# **Chat BI \- 模块依赖关系调整方案**

为了解决 chatbi-api 和 chatbi-agent-core 之间潜在的循环依赖问题，我们对代码工程架构进行如下优化调整。

### **1\. 问题分析**

原始设计中，chatbi-agent-core 实现了 chatbi-api 中定义的接口，因此 agent-core 依赖 api。同时，如果 chatbi-api 本身作为可执行的Spring Boot应用，它需要引入 agent-core 中的服务实现，这将导致循环依赖，这是构建工具所不允许的。

### **2\. 解决方案：引入 application 启动模块**

我们引入一个新的模块 chatbi-application 作为唯一的启动入口和打包单元。

#### **调整后的模块职责与依赖关系**

1. **chatbi-api**  
   * **职责**: 定义API层，包含Controller (@RestController)和Service接口 (ConversationApi)。  
   * **依赖**: chatbi-common。  
   * **注意**: 不再包含 main 方法启动类。  
2. **chatbi-agent-core**  
   * **职责**: 实现核心业务逻辑，包括实现 chatbi-api 中定义的Service接口 (ConversationServiceImpl)。  
   * **依赖**: chatbi-api (为了获取接口定义), chatbi-common。  
3. **chatbi-application (新)**  
   * **职责**: 作为整个应用的启动入口和打包单元。  
   * **包含**: ChatBiApplication.java (包含 public static void main(...))。  
   * **依赖**: 依赖所有需要运行的业务模块，如 chatbi-api, chatbi-agent-core。  
   * **打包**: Spring Boot的打包插件（如 spring-boot-maven-plugin）会配置在此模块的 pom.xml 中。

#### **依赖关系图**

graph TD  
    subgraph "最终运行的应用 (Fat JAR)"  
        App(chatbi-application)  
    end  
      
    API(chatbi-api)  
    Core(chatbi-agent-core)  
    Common(chatbi-common)

    App \-- 依赖 \--\> API  
    App \-- 依赖 \--\> Core  
      
    API \-- 依赖 \--\> Common  
    Core \-- 依赖 \--\> API\[依赖接口定义\]  
    Core \-- 依赖 \--\> Common

### **3\. 更新后的代码工程架构树**

我们主设计文档 chat\_bi\_product\_design\_v1 中的代码工程架构树应更新如下：

chatbi-service-parent/  
├── pom.xml  
│  
├── chatbi-application/  (新增: 启动和打包模块)  
│   ├── src/main/java/com/qding/chatbi/ChatBiApplication.java  
│   ├── src/main/resources/application.yml  
│   └── pom.xml (包含spring-boot-maven-plugin)  
│  
├── chatbi-api/  
│   ├── src/main/java/com/qding/chatbi/api/  
│   │   ├── controller/  
│   │   │   └── ConversationController.java  
│   │   └── service/  
│   │       └── ConversationApi.java  
│   └── pom.xml  
│  
├── chatbi-agent-core/  
│   ├── src/main/java/com/qding/chatbi/agent/  
│   │   ├── service/impl/  
│   │   │   └── ConversationServiceImpl.java  
│   │   └── ... (其他核心逻辑)  
│   └── pom.xml  
│  
├── chatbi-common/  
│   └── ...  
│  
└── ... (其他服务模块)

通过此项调整，我们彻底解决了循环依赖问题，使得项目结构更加清晰、健壮且符合标准实践。