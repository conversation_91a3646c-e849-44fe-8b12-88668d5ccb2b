# 澄清机制修复测试

## 🔍 问题分析

### 原问题

用户输入"收入"这样的单词查询时，系统没有要求澄清，而是直接：

- 自动推断 `expectedGranularity: ["city", "category"]`
- 返回了数据表格

### 根本原因

prompt 中的第 6 条关键要求有逻辑漏洞：

```
"当mergedContext已经描述了完整查询意图（包含指标+时间+其他维度），应设置intent为DATA_QUERY，不要要求澄清"
```

对于首次查询，没有历史消息可合并，AI 错误地认为单个"收入"就是完整意图。

## 🔧 修复方案

### 1. 修改关键要求

```diff
- 6. 当mergedContext已经描述了完整查询意图（包含指标+时间+其他维度），应设置intent为DATA_QUERY，不要要求澄清
+ 6. 🔴 澄清优先原则：无论是否有历史消息，当当前查询缺少关键信息（时间、维度、具体指标）时，必须要求澄清，不允许自动推断或补充
+ 7. 只有当前查询本身包含完整的指标+时间+维度信息时，才设置intent为DATA_QUERY
```

### 2. 添加单词查询强制澄清规则

- 触发条件：查询只有 1-2 个词
- 处理原则：绝对禁止自动推断 expectedGranularity
- 必须要求用户明确指定查询维度

### 3. 强化合并逻辑

在步骤 4 中添加澄清检查：

```
4. 🔴 澄清检查: 合并完成后，必须检查最终意图是否完整。如果缺少时间、维度或具体指标，无论是否有历史消息，都必须要求澄清
```

## 🧪 测试案例

### 测试 1：单词查询（应该澄清）

- **输入**：`"收入"`
- **期望**：返回澄清选项，询问用户想查询哪个维度的收入
- **禁止**：自动设置 expectedGranularity

### 测试 2：完整查询（不需澄清）

- **输入**：`"今年各城市的销售收入"`
- **期望**：直接处理，设置 intent="DATA_QUERY"

### 测试 3：部分信息查询（应该澄清）

- **输入**：`"北京的数据"`
- **期望**：要求澄清具体想查询什么指标

## 📋 验证清单

- [ ] "收入" 查询触发澄清
- [ ] "销售" 查询触发澄清
- [ ] "成本" 查询触发澄清
- [ ] "今年各城市收入" 直接处理
- [ ] "本月销售额趋势" 直接处理
- [ ] expectedGranularity 不会被自动推断

## 🎯 修复效果

修复后，系统将：

1. ✅ 对模糊查询强制要求澄清
2. ✅ 防止 AI 自作主张推断维度
3. ✅ 确保用户获得准确的查询结果
4. ✅ 无论是否有历史消息都能正确判断
