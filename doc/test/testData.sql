-- =================================================================
-- ChatBI 项目测试数据生成脚本
-- =================================================================
-- 目的: 为 ChatBI 系统提供一套完整的、用于开发和测试的基础元数据。
-- 数据库: chatbi_metadata
-- =================================================================

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS `chatbi_metadata` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `chatbi_test_data` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 确保我们操作的是正确的数据库
USE `chatbi_metadata`;

-- （可选）在插入新数据前清空旧数据，方便重复执行脚本
-- 注意：TRUNCATE会重置自增ID，DELETE不会。请根据需要选择使用。
-- SET FOREIGN_KEY_CHECKS = 0;
-- TRUNCATE TABLE `database_source_configs`;
-- TRUNCATE TABLE `queryable_datasets`;
-- TRUNCATE TABLE `dataset_columns`;
-- TRUNCATE TABLE `roles`;
-- TRUNCATE TABLE `dataset_access_permissions`;
-- TRUNCATE TABLE `business_terminology`;
-- TRUNCATE TABLE `query_examples`;
-- SET FOREIGN_KEY_CHECKS = 1;


-- 1. 插入数据源配置 (`database_source_configs`)
-- -----------------------------------------------------------------
INSERT INTO `database_source_configs` (`id`, `source_name`, `database_type`, `connection_parameters`, `description`, `status`)
VALUES
(1, '本地测试MySQL', 'MYSQL', '{ "host": "localhost", "port": 3306, "username": "root", "password": "your_password", "databaseName": "chatbi_test_data" }', '用于开发和测试的本地MySQL数据库。', 'active');


-- 2. 插入可查询数据集 (`queryable_datasets`)
-- -----------------------------------------------------------------
INSERT INTO `queryable_datasets` (`id`, `dataset_name`, `description`, `database_source_id`, `base_object_name`, `base_object_type`, `technical_definition`, `status`)
VALUES
(1, '每日销售汇总', '每日T+1生成的销售分析宽表，已聚合部分指标。包含累计分析逻辑。', 1, 'daily_sales_summary', 'TABLE', NULL, 'active'),
(2, '公司月度财务报表', '各部门月度财务核心指标报表。', 1, 'monthly_financials', 'TABLE', NULL, 'active'),
(3, '用户地域分布', '一个基于自定义SQL创建的数据集，用于分析用户分布。', 1, 'user_city_distribution_query', 'QUERY', 'SELECT city, COUNT(id) as user_count FROM users GROUP BY city', 'active');


-- 3. 插入数据集字段 (`dataset_columns`)
-- -----------------------------------------------------------------
-- 3.1 为 "每日销售汇总" (dataset_id = 1) 添加字段
INSERT INTO `dataset_columns` (`dataset_id`, `column_name`, `description`, `technical_name_or_expression`, `semantic_type`, `data_type`, `synonyms`, `is_filterable`, `is_groupable`)
VALUES
(1, '销售额', '商品的最终成交金额。', 'sale_amount', 'METRIC', 'NUMBER', '["GMV", "成交额"]', 1, 0),
(1, '订单量', '支付成功的订单数量。', 'order_count', 'METRIC', 'NUMBER', '["订单数"]', 1, 0),
(1, '日期', '销售发生的日期。', 'dt', 'DIMENSION', 'DATE', '["时间"]', 1, 1),
(1, '城市', '订单所属的城市。', 'city', 'DIMENSION', 'STRING', '["地区"]', 1, 1),
(1, '商品品类', '商品所属的分类。', 'category_name', 'DIMENSION', 'STRING', '["分类"]', 1, 1),
(1, '本周累计销售额', '从本周第一天到当前日期的累计销售额，可按城市分区。', 'SUM(sale_amount) OVER (PARTITION BY city, YEAR(dt), WEEK(dt, 1) ORDER BY dt)', 'METRIC', 'NUMBER', '["周累计GMV", "WTD"]', 0, 0),
(1, '本月累计销售额', '从本月第一天到当前日期的累计销售额，可按城市分区。', 'SUM(sale_amount) OVER (PARTITION BY city, YEAR(dt), MONTH(dt) ORDER BY dt)', 'METRIC', 'NUMBER', '["月累计GMV", "MTD"]', 0, 0),
(1, '本年累计销售额', '从本年第一天到当前日期的累计销售额，可按城市分区。', 'SUM(sale_amount) OVER (PARTITION BY city, YEAR(dt) ORDER BY dt)', 'METRIC', 'NUMBER', '["年累计GMV", "YTD"]', 0, 0);

-- 3.2 为 "公司月度财务报表" (dataset_id = 2) 添加字段
INSERT INTO `dataset_columns` (`dataset_id`, `column_name`, `description`, `technical_name_or_expression`, `semantic_type`, `data_type`, `synonyms`, `is_filterable`, `is_groupable`)
VALUES
(2, '总收入', '公司在指定月份的总营业收入。', 'revenue', 'METRIC', 'NUMBER', '["营收"]', 1, 0),
(2, '净利润', '公司的税后净利润。', 'net_profit', 'METRIC', 'NUMBER', '["纯利"]', 1, 0),
(2, '成本费用', '公司总的运营成本和费用。', 'expenses', 'METRIC', 'NUMBER', '["开销"]', 1, 0),
(2, '净利率', '净利润占总收入的百分比。', 'net_profit / revenue', 'METRIC', 'PERCENTAGE', '[]', 0, 0),
(2, '部门', '产生财务数据的业务部门。', 'department', 'DIMENSION', 'STRING', '["业务线"]', 1, 1),
(2, '月份', '财务数据所属的月份。', 'month', 'DIMENSION', 'DATE', '["期间"]', 1, 1);


-- 4. 插入角色 (`roles`)
-- -----------------------------------------------------------------
INSERT INTO `roles` (`id`, `role_name`, `description`)
VALUES
(1, '全国销售总监', '可以查看全国所有销售数据。'),
(2, '华北区销售经理', '只能查看华北区（北京、天津）的销售数据。'),
(3, '财务分析师', '可以查看公司的核心财务报表。');


-- 5. 插入数据集访问权限 (`dataset_access_permissions`)
-- -----------------------------------------------------------------
INSERT INTO `dataset_access_permissions` (`role_id`, `dataset_id`)
VALUES
(1, 1), -- 全国销售总监 -> 每日销售汇总
(2, 1), -- 华北区销售经理 -> 每日销售汇总
(3, 2); -- 财务分析师 -> 公司月度财务报表


-- 6. 插入业务术语 (`business_terminology`)
-- -----------------------------------------------------------------
INSERT INTO `business_terminology` (`business_term`, `standard_reference_type`, `standard_reference_name`, `context_description`)
VALUES
('GMV', 'DatasetColumn', '销售额', 'Gross Merchandise Volume，即商品交易总额，是我们公司的核心指标。'),
('MTD', 'DatasetColumn', '本月累计销售额', 'Month-To-Date，指从本月第一天到当前日期的累计值。'),
('YTD', 'DatasetColumn', '本年累计销售额', 'Year-To-Date，指从本年第一天到当前日期的累计值。');


-- 7. 插入查询范例 (`query_examples`)
-- -----------------------------------------------------------------
INSERT INTO `query_examples` (`user_question`, `target_dataset_id`, `target_query_representation`)
VALUES
('查一下昨天北京的GMV', 1, '{"metrics":["销售额"], "filters":[{"fieldName":"日期", "value":"昨天"}, {"fieldName":"城市", "value":"北京"}]}'),
('看看北京本月至今的累计销售额趋势', 1, '{"metrics":["本月累计销售额"], "dimensions":["日期"], "filters":[{"fieldName":"日期", "value":"本月"}, {"fieldName":"城市", "value":"北京"}]}'),
('全国今年以来的总销售额是多少？', 1, '{"metrics":["本年累计销售额"], "filters":[{"fieldName":"日期", "value":"今年"}], "aggregation":"TOTAL"}'),
('查询财务部上个季度的净利润', 2, '{"metrics":["净利润"], "filters":[{"fieldName":"部门", "value":"财务部"}, {"fieldName":"月份", "value":"上个季度"}]}');

-- =================================================================
-- 脚本执行完毕
-- =================================================================





-- =================================================================
-- ChatBI 业务数据表及模拟数据生成脚本 (增强版 v2)
-- =================================================================
-- 目的: 创建 ChatBI 所需的底层业务表，并填充足够多样性的数据以满足各类测试场景。
-- 更新: 城市数量增加到20个，销售数据增加到近100条。
-- 数据库: chatbi_test_data (请确保已创建此数据库: CREATE DATABASE chatbi_test_data;)
-- =================================================================

-- 确保我们操作的是正确的数据库
USE `chatbi_test_data`;

-- --- 表结构创建 ---

-- 1. 创建 每日销售汇总表 (daily_sales_summary)
DROP TABLE IF EXISTS `daily_sales_summary`;
CREATE TABLE `daily_sales_summary` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `dt` DATE NOT NULL COMMENT '日期',
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `category_name` VARCHAR(100) NOT NULL COMMENT '商品品类',
  `sale_amount` DECIMAL(15, 2) NOT NULL COMMENT '销售额',
  `order_count` INT NOT NULL COMMENT '订单量',
  INDEX `idx_dt` (`dt`),
  INDEX `idx_city` (`city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日销售汇总表';

-- 2. 创建 公司月度财务报表 (monthly_financials)
DROP TABLE IF EXISTS `monthly_financials`;
CREATE TABLE `monthly_financials` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `month` DATE NOT NULL COMMENT '月份 (以当月第一天表示)',
  `department` VARCHAR(100) NOT NULL COMMENT '部门',
  `revenue` DECIMAL(18, 2) NOT NULL COMMENT '总收入',
  `net_profit` DECIMAL(18, 2) NOT NULL COMMENT '净利润',
  `expenses` DECIMAL(18, 2) NOT NULL COMMENT '成本费用',
  UNIQUE KEY `uk_month_dept` (`month`, `department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司月度财务报表';

-- 3. 创建 用户表 (users) - 用于支持自定义SQL数据集
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_name` VARCHAR(100) NOT NULL,
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `registration_date` DATE NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';


-- --- 模拟数据插入 ---

-- 1. 插入 用户表 (users) 数据 - 扩展到20个城市
TRUNCATE TABLE `users`;
INSERT INTO `users` (`user_name`, `city`, `registration_date`) VALUES
('用户A', '北京', '2024-10-01'), ('用户B', '上海', '2024-10-05'),
('用户C', '北京', '2024-11-11'), ('用户D', '广州', '2024-11-20'),
('用户E', '深圳', '2024-12-01'), ('用户F', '上海', '2024-12-15'),
('用户G', '北京', '2025-01-08'), ('用户H', '天津', '2025-01-22'),
('用户I', '广州', '2025-02-03'), ('用户J', '天津', '2025-02-18'),
('用户K', '杭州', '2025-01-10'), ('用户L', '成都', '2025-01-12'),
('用户M', '重庆', '2025-01-15'), ('用户N', '武汉', '2025-01-18'),
('用户O', '南京', '2025-02-05'), ('用户P', '苏州', '2025-02-08'),
('用户Q', '西安', '2025-02-11'), ('用户R', '长沙', '2025-02-14'),
('用户S', '郑州', '2025-03-01'), ('用户T', '青岛', '2025-03-03'),
('用户U', '大连', '2025-03-05'), ('用户V', '厦门', '2025-03-08'),
('用户W', '沈阳', '2025-03-10'), ('用户X', '哈尔滨', '2025-03-12'),
('用户Y', '济南', '2025-03-15'), ('用户Z', '合肥', '2025-03-18');


-- 2. 插入 公司月度财务报表 (monthly_financials) 数据
TRUNCATE TABLE `monthly_financials`;
INSERT INTO `monthly_financials` (`month`, `department`, `revenue`, `net_profit`, `expenses`) VALUES
('2024-10-01', '销售部', 550000.00, 120000.00, 430000.00),
('2024-10-01', '市场部', 80000.00, 15000.00, 65000.00),
('2024-11-01', '销售部', 620000.00, 155000.00, 465000.00),
('2024-11-01', '市场部', 95000.00, 20000.00, 75000.00),
('2024-12-01', '销售部', 800000.00, 250000.00, 550000.00),
('2024-12-01', '市场部', 120000.00, 30000.00, 90000.00),
('2024-12-01', '研发部', 200000.00, -50000.00, 250000.00),
('2025-01-01', '销售部', 750000.00, 210000.00, 540000.00),
('2025-01-01', '市场部', 110000.00, 25000.00, 85000.00),
('2025-01-01', '研发部', 220000.00, -40000.00, 260000.00),
('2025-02-01', '销售部', 780000.00, 225000.00, 555000.00),
('2025-02-01', '市场部', 115000.00, 28000.00, 87000.00);


-- 3. 插入 每日销售汇总表 (daily_sales_summary) 数据 - 扩展到近100条
TRUNCATE TABLE `daily_sales_summary`;
INSERT INTO `daily_sales_summary` (`dt`, `city`, `category_name`, `sale_amount`, `order_count`) VALUES
-- 2024年11月
('2024-11-15', '北京', '电子产品', 12000.50, 8),
('2024-11-15', '上海', '服装', 8500.00, 25),
('2024-11-16', '北京', '家居用品', 6200.75, 18),
('2024-11-20', '广州', '电子产品', 21000.00, 15),
('2024-11-25', '深圳', '食品饮料', 4500.00, 50),
('2024-11-30', '杭州', '服装', 9800.20, 30),

-- 2024年12月
('2024-12-01', '成都', '户外运动', 11000.00, 12),
('2024-12-02', '重庆', '电子产品', 19500.80, 14),
('2024-12-03', '武汉', '家居用品', 7800.00, 22),
('2024-12-05', '南京', '食品饮料', 5200.00, 60),
('2024-12-10', '北京', '电子产品', 25000.00, 18),
('2024-12-11', '上海', '户外运动', 13000.00, 15),
('2024-12-12', '广州', '服装', 11000.40, 35),
('2024-12-15', '苏州', '家居用品', 8800.00, 28),
('2024-12-20', '天津', '电子产品', 16000.00, 11),
('2024-12-25', '西安', '食品饮料', 6000.00, 70),
('2024-12-28', '北京', '电子产品', 15000.00, 10),
('2024-12-28', '上海', '家居用品', 8000.00, 20),
('2024-12-29', '北京', '服装', 7500.00, 15),
('2024-12-29', '深圳', '电子产品', 22000.00, 12),
('2024-12-30', '上海', '电子产品', 30000.00, 18),
('2024-12-30', '北京', '电子产品', 18000.00, 11),
('2024-12-31', '北京', '家居用品', 9500.00, 25),
('2024-12-31', '广州', '服装', 12000.00, 30),
('2024-12-31', '长沙', '户外运动', 9900.00, 14),

-- 2025年1月
('2025-01-01', '北京', '电子产品', 25000.00, 20),
('2025-01-01', '上海', '服装', 15000.00, 22),
('2025-01-01', '郑州', '食品饮料', 5500.00, 65),
('2025-01-02', '北京', '电子产品', 28000.00, 25),
('2025-01-02', '天津', '家居用品', 6000.00, 18),
('2025-01-02', '青岛', '服装', 8900.00, 28),
('2025-01-03', '深圳', '家居用品', 11000.00, 30),
('2025-01-03', '杭州', '电子产品', 32000.00, 20),
('2025-01-04', '大连', '食品饮料', 4800.00, 55),
('2025-01-05', '厦门', '户外运动', 14000.00, 16),
('2025-01-06', '北京', '电子产品', 19000.00, 15),
('2025-01-06', '上海', '家居用品', 10500.00, 25),
('2025-01-07', '北京', '电子产品', 21000.00, 17),
('2025-01-07', '广州', '食品饮料', 6200.00, 75),
('2025-01-08', '上海', '服装', 18000.00, 28),
('2025-01-08', '沈阳', '家居用品', 7200.00, 20),
('2025-01-10', '哈尔滨', '户外运动', 12500.00, 13),
('2025-01-12', '济南', '电子产品', 17500.00, 12),
('2025-01-15', '合肥', '服装', 9200.00, 32),
('2025-01-18', '成都', '食品饮料', 6800.00, 80),
('2025-01-20', '北京', '家居用品', 11500.00, 30),
('2025-01-22', '天津', '服装', 8200.00, 26),
('2025-01-25', '深圳', '电子产品', 45000.00, 30),
('2025-01-28', '武汉', '户外运动', 13500.00, 17),
('2025-01-30', '南京', '家居用品', 9800.00, 29),
('2025-01-31', '北京', '电子产品', 38000.00, 28),

-- 2025年2月
('2025-02-01', '北京', '服装', 13000.00, 20),
('2025-02-01', '广州', '电子产品', 35000.00, 22),
('2025-02-01', '苏州', '食品饮料', 7000.00, 85),
('2025-02-02', '北京', '服装', 14500.00, 23),
('2025-02-02', '上海', '电子产品', 42000.00, 28),
('2025-02-03', '西安', '家居用品', 8500.00, 24),
('2025-02-05', '长沙', '服装', 10800.00, 33),
('2025-02-07', '杭州', '户外运动', 16000.00, 18),
('2025-02-10', '重庆', '电子产品', 23000.00, 16),
('2025-02-11', '武汉', '食品饮料', 6500.00, 78),
('2025-02-14', '北京', '电子产品', 31000.00, 24),
('2025-02-15', '天津', '家居用品', 7800.00, 21),
('2025-02-18', '青岛', '户外运动', 11500.00, 11),
('2025-02-20', '大连', '服装', 9500.00, 31),
('2025-02-22', '厦门', '电子产品', 29000.00, 20),
('2025-02-25', '沈阳', '食品饮料', 5800.00, 68),
('2025-02-28', '北京', '电子产品', 48000.00, 35),
('2025-02-28', '上海', '家居用品', 12800.00, 32);


-- =================================================================
-- 脚本执行完毕
-- =================================================================
