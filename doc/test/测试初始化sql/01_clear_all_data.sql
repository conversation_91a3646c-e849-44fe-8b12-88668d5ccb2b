-- ============================================
-- ChatBI 测试数据清空脚本
-- 执行前请确保您了解此操作的影响
-- ============================================

-- 清空元数据库 chatbi_metadata 的所有表数据
USE `chatbi_metadata`;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空所有元数据表（按依赖关系倒序清空）
TRUNCATE TABLE `chat_messages`;
TRUNCATE TABLE `query_examples`;
TRUNCATE TABLE `business_terminology`;
TRUNCATE TABLE `dataset_access_permissions`;
TRUNCATE TABLE `dataset_columns`;
TRUNCATE TABLE `queryable_datasets`;
TRUNCATE TABLE `roles`;
TRUNCATE TABLE `database_source_configs`;
TRUNCATE TABLE `admin_users`;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 清空测试业务数据库 chatbi_test_data 的所有表数据
DROP DATABASE IF EXISTS `chatbi_test_data`;
CREATE DATABASE IF NOT EXISTS `chatbi_test_data` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 注意：如果您使用向量数据库（如 Milvus、Pinecone、Chroma 等），
-- 请根据您使用的向量数据库类型，添加相应的清空命令
-- 
-- 示例（如果使用 Chroma）：
-- DELETE FROM chroma_collections WHERE collection_name LIKE 'chatbi_%';
-- 
-- 示例（如果使用 Milvus）：
-- 需要通过 Milvus 客户端执行：
-- collection.drop()
-- 
-- 请根据实际使用的向量数据库进行相应的清空操作

COMMIT;

SELECT 'All data cleared successfully!' AS result; 