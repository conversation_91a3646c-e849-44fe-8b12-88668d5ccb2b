-- ============================================
-- ChatBI 测试数据初始化主脚本
-- 按顺序执行所有初始化脚本
-- ============================================

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 记录开始时间
SELECT CONCAT('开始执行ChatBI测试数据初始化... 时间: ', NOW()) AS start_info;

-- ============================================
-- 步骤1: 清空所有数据
-- ============================================
SELECT '=== 步骤1: 清空现有数据 ===' AS step_info;
SOURCE sql/01_clear_all_data.sql;

-- ============================================
-- 步骤2: 初始化测试业务数据
-- ============================================
SELECT '=== 步骤2: 初始化测试业务数据 ===' AS step_info;
SOURCE sql/02_create_test_business_data.sql;

-- ============================================
-- 步骤3: 初始化元数据
-- ============================================
SELECT '=== 步骤3: 初始化元数据配置 ===' AS step_info;
SOURCE sql/03_init_metadata.sql;

-- ============================================
-- 最终验证
-- ============================================
SELECT '=== 最终数据验证 ===' AS step_info;

-- 验证业务数据
SELECT 'chatbi_test_data 数据验证:' AS validation_info;
USE `chatbi_test_data`;
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'daily_sales_summary', COUNT(*) FROM daily_sales_summary  
UNION ALL
SELECT 'monthly_financials', COUNT(*) FROM monthly_financials;

-- 验证元数据
SELECT 'chatbi_metadata 数据验证:' AS validation_info;
USE `chatbi_metadata`;
SELECT 'database_source_configs' as table_name, COUNT(*) as record_count FROM database_source_configs
UNION ALL
SELECT 'roles', COUNT(*) FROM roles
UNION ALL  
SELECT 'queryable_datasets', COUNT(*) FROM queryable_datasets
UNION ALL
SELECT 'dataset_columns', COUNT(*) FROM dataset_columns
UNION ALL
SELECT 'dataset_access_permissions', COUNT(*) FROM dataset_access_permissions
UNION ALL
SELECT 'business_terminology', COUNT(*) FROM business_terminology
UNION ALL
SELECT 'query_examples', COUNT(*) FROM query_examples
UNION ALL
SELECT 'admin_users', COUNT(*) FROM admin_users;

-- 记录完成时间
SELECT CONCAT('ChatBI测试数据初始化完成! 时间: ', NOW()) AS completion_info;

-- ============================================
-- 测试建议
-- ============================================
SELECT '=== 测试建议 ===' AS test_suggestions;
SELECT '1. 可以使用以下三个测试角色进行功能测试:' AS suggestion1;
SELECT '   - 全国销售总监 (ID: 1): 可查询所有销售数据' AS suggestion2;
SELECT '   - 华北区销售经理 (ID: 2): 可查询销售数据(需要地域限制)' AS suggestion3;
SELECT '   - 财务分析师 (ID: 3): 只能查询财务数据' AS suggestion4;
SELECT '2. 可以测试的查询类型:' AS suggestion5;
SELECT '   - 基础指标查询: "昨天北京的销售额是多少?"' AS suggestion6;
SELECT '   - 业务术语查询: "昨天北京的GMV是多少?"' AS suggestion7;
SELECT '   - 计算指标查询: "北京1月份的MTD销售额"' AS suggestion8;
SELECT '   - 对比分析查询: "对比北京和上海的销售额"' AS suggestion9;
SELECT '   - 权限测试: 财务分析师询问销售数据应被拒绝' AS suggestion10; 