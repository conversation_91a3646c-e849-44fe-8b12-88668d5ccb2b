-- ============================================
-- ChatBI 测试业务数据初始化脚本
-- 创建 chatbi_test_data 数据库的表结构和测试数据
-- ============================================

USE `chatbi_test_data`;

-- ============================================
-- 1. 创建业务数据表结构
-- ============================================

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_name` VARCHAR(100) NOT NULL COMMENT '用户姓名',
  `email` VARCHAR(255) NOT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) COMMENT '手机号',
  `city` VARCHAR(50) NOT NULL COMMENT '所在城市',
  `registration_date` DATE NOT NULL COMMENT '注册日期',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '用户状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  INDEX `idx_city` (`city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 每日销售汇总表
CREATE TABLE IF NOT EXISTS `daily_sales_summary` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `dt` DATE NOT NULL COMMENT '日期',
  `city` VARCHAR(50) NOT NULL COMMENT '城市',
  `category` VARCHAR(100) NOT NULL COMMENT '商品品类',
  `sales_amount` DECIMAL(15,2) NOT NULL COMMENT '销售额',
  `order_count` INT NOT NULL COMMENT '订单数',
  `customer_count` INT NOT NULL COMMENT '客户数',
  `avg_order_value` DECIMAL(10,2) NOT NULL COMMENT '客单价',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dt_city_category` (`dt`, `city`, `category`),
  INDEX `idx_dt` (`dt`),
  INDEX `idx_city` (`city`),
  INDEX `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日销售汇总表';

-- 月度财务报表
CREATE TABLE IF NOT EXISTS `monthly_financials` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `month` DATE NOT NULL COMMENT '月份(当月第一天)',
  `department` VARCHAR(100) NOT NULL COMMENT '部门',
  `revenue` DECIMAL(15,2) NOT NULL COMMENT '收入',
  `cost` DECIMAL(15,2) NOT NULL COMMENT '成本',
  `expense` DECIMAL(15,2) NOT NULL COMMENT '费用',
  `profit` DECIMAL(15,2) NOT NULL COMMENT '利润',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_month_department` (`month`, `department`),
  INDEX `idx_month` (`month`),
  INDEX `idx_department` (`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='月度财务报表';

-- ============================================
-- 2. 插入测试数据
-- ============================================

-- 插入用户数据（覆盖多个城市，确保与销售数据的城市匹配）
INSERT INTO `users` (`user_name`, `email`, `phone`, `city`, `registration_date`, `status`) VALUES
-- 华北区城市
('张三', '<EMAIL>', '13800138001', '北京', '2024-01-15', 'active'),
('李四', '<EMAIL>', '13800138002', '天津', '2024-01-20', 'active'),
('王五', '<EMAIL>', '13800138003', '石家庄', '2024-02-10', 'active'),
('赵六', '<EMAIL>', '13800138004', '太原', '2024-02-15', 'active'),
('孙七', '<EMAIL>', '13800138005', '呼和浩特', '2024-03-01', 'active'),

-- 华东区城市
('周八', '<EMAIL>', '13800138006', '上海', '2024-01-10', 'active'),
('吴九', '<EMAIL>', '13800138007', '南京', '2024-01-25', 'active'),
('郑十', '<EMAIL>', '13800138008', '杭州', '2024-02-05', 'active'),
('钱一', '<EMAIL>', '13800138009', '合肥', '2024-02-20', 'active'),
('孙二', '<EMAIL>', '13800138010', '济南', '2024-03-10', 'active'),

-- 华南区城市
('李三', '<EMAIL>', '13800138011', '广州', '2024-01-05', 'active'),
('王四', '<EMAIL>', '13800138012', '深圳', '2024-01-30', 'active'),
('张五', '<EMAIL>', '13800138013', '南宁', '2024-02-25', 'active'),
('赵六', '<EMAIL>', '13800138014', '海口', '2024-03-05', 'active'),

-- 西南区城市
('刘七', '<EMAIL>', '13800138015', '成都', '2024-01-12', 'active'),
('陈八', '<EMAIL>', '13800138016', '重庆', '2024-02-08', 'active'),
('杨九', '<EMAIL>', '13800138017', '昆明', '2024-02-18', 'active'),
('黄十', '<EMAIL>', '13800138018', '贵阳', '2024-03-15', 'active'),

-- 西北区城市
('林一', '<EMAIL>', '13800138019', '西安', '2024-01-08', 'active'),
('徐二', '<EMAIL>', '13800138020', '兰州', '2024-02-12', 'active');

-- 插入每日销售汇总数据（2024年12月-2025年2月，覆盖多个城市和品类）
INSERT INTO `daily_sales_summary` (`dt`, `city`, `category`, `sales_amount`, `order_count`, `customer_count`, `avg_order_value`) VALUES
-- 2024年12月数据
('2024-12-01', '北京', '电子产品', 125000.00, 250, 180, 500.00),
('2024-12-01', '北京', '服装', 85000.00, 340, 260, 250.00),
('2024-12-01', '北京', '家居用品', 45000.00, 150, 120, 300.00),
('2024-12-01', '上海', '电子产品', 135000.00, 270, 200, 500.00),
('2024-12-01', '上海', '服装', 92000.00, 368, 280, 250.00),
('2024-12-01', '上海', '家居用品', 48000.00, 160, 128, 300.00),
('2024-12-01', '广州', '电子产品', 95000.00, 190, 140, 500.00),
('2024-12-01', '广州', '服装', 68000.00, 272, 200, 250.00),
('2024-12-01', '天津', '电子产品', 75000.00, 150, 110, 500.00),
('2024-12-01', '天津', '服装', 52000.00, 208, 160, 250.00),

('2024-12-02', '北京', '电子产品', 128000.00, 256, 185, 500.00),
('2024-12-02', '北京', '服装', 87000.00, 348, 265, 250.00),
('2024-12-02', '北京', '家居用品', 46000.00, 153, 122, 300.00),
('2024-12-02', '上海', '电子产品', 138000.00, 276, 205, 500.00),
('2024-12-02', '上海', '服装', 94000.00, 376, 285, 250.00),
('2024-12-02', '广州', '电子产品', 97000.00, 194, 143, 500.00),
('2024-12-02', '天津', '电子产品', 77000.00, 154, 112, 500.00),

-- 2024年12月中旬数据
('2024-12-15', '北京', '电子产品', 145000.00, 290, 210, 500.00),
('2024-12-15', '北京', '服装', 98000.00, 392, 300, 250.00),
('2024-12-15', '北京', '家居用品', 52000.00, 173, 138, 300.00),
('2024-12-15', '上海', '电子产品', 155000.00, 310, 230, 500.00),
('2024-12-15', '上海', '服装', 105000.00, 420, 320, 250.00),
('2024-12-15', '上海', '家居用品', 55000.00, 183, 146, 300.00),
('2024-12-15', '广州', '电子产品', 108000.00, 216, 160, 500.00),
('2024-12-15', '广州', '服装', 78000.00, 312, 230, 250.00),
('2024-12-15', '深圳', '电子产品', 125000.00, 250, 185, 500.00),
('2024-12-15', '深圳', '服装', 85000.00, 340, 260, 250.00),
('2024-12-15', '天津', '电子产品', 85000.00, 170, 125, 500.00),
('2024-12-15', '天津', '服装', 58000.00, 232, 180, 250.00),
('2024-12-15', '成都', '电子产品', 92000.00, 184, 135, 500.00),
('2024-12-15', '成都', '服装', 65000.00, 260, 200, 250.00),

-- 2024年12月底数据
('2024-12-31', '北京', '电子产品', 165000.00, 330, 240, 500.00),
('2024-12-31', '北京', '服装', 115000.00, 460, 350, 250.00),
('2024-12-31', '北京', '家居用品', 60000.00, 200, 160, 300.00),
('2024-12-31', '上海', '电子产品', 175000.00, 350, 260, 500.00),
('2024-12-31', '上海', '服装', 125000.00, 500, 380, 250.00),
('2024-12-31', '上海', '家居用品', 65000.00, 216, 173, 300.00),
('2024-12-31', '广州', '电子产品', 125000.00, 250, 185, 500.00),
('2024-12-31', '广州', '服装', 88000.00, 352, 260, 250.00),
('2024-12-31', '深圳', '电子产品', 135000.00, 270, 200, 500.00),
('2024-12-31', '深圳', '服装', 95000.00, 380, 290, 250.00),
('2024-12-31', '天津', '电子产品', 95000.00, 190, 140, 500.00),
('2024-12-31', '天津', '服装', 68000.00, 272, 210, 250.00),

-- 2025年1月数据
('2025-01-01', '北京', '电子产品', 98000.00, 196, 145, 500.00),
('2025-01-01', '北京', '服装', 68000.00, 272, 210, 250.00),
('2025-01-01', '北京', '家居用品', 36000.00, 120, 96, 300.00),
('2025-01-01', '上海', '电子产品', 108000.00, 216, 160, 500.00),
('2025-01-01', '上海', '服装', 75000.00, 300, 230, 250.00),
('2025-01-01', '上海', '家居用品', 39000.00, 130, 104, 300.00),
('2025-01-01', '广州', '电子产品', 78000.00, 156, 115, 500.00),
('2025-01-01', '广州', '服装', 55000.00, 220, 170, 250.00),
('2025-01-01', '天津', '电子产品', 65000.00, 130, 95, 500.00),
('2025-01-01', '天津', '服装', 45000.00, 180, 140, 250.00),

('2025-01-15', '北京', '电子产品', 135000.00, 270, 200, 500.00),
('2025-01-15', '北京', '服装', 92000.00, 368, 280, 250.00),
('2025-01-15', '北京', '家居用品', 48000.00, 160, 128, 300.00),
('2025-01-15', '上海', '电子产品', 145000.00, 290, 215, 500.00),
('2025-01-15', '上海', '服装', 98000.00, 392, 300, 250.00),
('2025-01-15', '上海', '家居用品', 52000.00, 173, 138, 300.00),
('2025-01-15', '广州', '电子产品', 105000.00, 210, 155, 500.00),
('2025-01-15', '广州', '服装', 75000.00, 300, 230, 250.00),
('2025-01-15', '深圳', '电子产品', 115000.00, 230, 170, 500.00),
('2025-01-15', '深圳', '服装', 78000.00, 312, 240, 250.00),
('2025-01-15', '天津', '电子产品', 82000.00, 164, 120, 500.00),
('2025-01-15', '天津', '服装', 58000.00, 232, 180, 250.00),

('2025-01-31', '北京', '电子产品', 148000.00, 296, 220, 500.00),
('2025-01-31', '北京', '服装', 105000.00, 420, 320, 250.00),
('2025-01-31', '北京', '家居用品', 55000.00, 183, 146, 300.00),
('2025-01-31', '上海', '电子产品', 158000.00, 316, 235, 500.00),
('2025-01-31', '上海', '服装', 112000.00, 448, 340, 250.00),
('2025-01-31', '上海', '家居用品', 58000.00, 193, 154, 300.00),
('2025-01-31', '广州', '电子产品', 118000.00, 236, 175, 500.00),
('2025-01-31', '广州', '服装', 85000.00, 340, 260, 250.00),
('2025-01-31', '天津', '电子产品', 88000.00, 176, 130, 500.00),
('2025-01-31', '天津', '服装', 62000.00, 248, 190, 250.00),

-- 2025年2月数据
('2025-02-01', '北京', '电子产品', 142000.00, 284, 210, 500.00),
('2025-02-01', '北京', '服装', 98000.00, 392, 300, 250.00),
('2025-02-01', '北京', '家居用品', 52000.00, 173, 138, 300.00),
('2025-02-01', '上海', '电子产品', 152000.00, 304, 225, 500.00),
('2025-02-01', '上海', '服装', 105000.00, 420, 320, 250.00),
('2025-02-01', '上海', '家居用品', 55000.00, 183, 146, 300.00),
('2025-02-01', '广州', '电子产品', 112000.00, 224, 165, 500.00),
('2025-02-01', '广州', '服装', 78000.00, 312, 240, 250.00),
('2025-02-01', '天津', '电子产品', 85000.00, 170, 125, 500.00),
('2025-02-01', '天津', '服装', 58000.00, 232, 180, 250.00),

('2025-02-15', '北京', '电子产品', 138000.00, 276, 205, 500.00),
('2025-02-15', '北京', '服装', 95000.00, 380, 290, 250.00),
('2025-02-15', '北京', '家居用品', 50000.00, 166, 133, 300.00),
('2025-02-15', '上海', '电子产品', 148000.00, 296, 220, 500.00),
('2025-02-15', '上海', '服装', 102000.00, 408, 310, 250.00),
('2025-02-15', '上海', '家居用品', 53000.00, 176, 141, 300.00),
('2025-02-15', '广州', '电子产品', 108000.00, 216, 160, 500.00),
('2025-02-15', '广州', '服装', 75000.00, 300, 230, 250.00),
('2025-02-15', '天津', '电子产品', 82000.00, 164, 120, 500.00),
('2025-02-15', '天津', '服装', 55000.00, 220, 170, 250.00);

-- 插入月度财务数据
INSERT INTO `monthly_financials` (`month`, `department`, `revenue`, `cost`, `expense`, `profit`) VALUES
-- 2024年第四季度
('2024-10-01', '销售部', 12500000.00, 8750000.00, 1250000.00, 2500000.00),
('2024-10-01', '市场部', 2500000.00, 1750000.00, 500000.00, 250000.00),
('2024-10-01', '研发部', 5000000.00, 4000000.00, 800000.00, 200000.00),
('2024-10-01', '运营部', 3000000.00, 2100000.00, 450000.00, 450000.00),

('2024-11-01', '销售部', 13200000.00, 9240000.00, 1320000.00, 2640000.00),
('2024-11-01', '市场部', 2640000.00, 1848000.00, 528000.00, 264000.00),
('2024-11-01', '研发部', 5280000.00, 4224000.00, 844800.00, 211200.00),
('2024-11-01', '运营部', 3168000.00, 2217600.00, 475200.00, 475200.00),

('2024-12-01', '销售部', 15800000.00, 11060000.00, 1580000.00, 3160000.00),
('2024-12-01', '市场部', 3160000.00, 2212000.00, 632000.00, 316000.00),
('2024-12-01', '研发部', 6320000.00, 5056000.00, 1012800.00, 253200.00),
('2024-12-01', '运营部', 3792000.00, 2654400.00, 569280.00, 568320.00),

-- 2025年第一季度
('2025-01-01', '销售部', 14500000.00, 10150000.00, 1450000.00, 2900000.00),
('2025-01-01', '市场部', 2900000.00, 2030000.00, 580000.00, 290000.00),
('2025-01-01', '研发部', 5800000.00, 4640000.00, 928000.00, 232000.00),
('2025-01-01', '运营部', 3480000.00, 2436000.00, 522000.00, 522000.00),

('2025-02-01', '销售部', 13800000.00, 9660000.00, 1380000.00, 2760000.00),
('2025-02-01', '市场部', 2760000.00, 1932000.00, 552000.00, 276000.00),
('2025-02-01', '研发部', 5520000.00, 4416000.00, 883200.00, 220800.00),
('2025-02-01', '运营部', 3312000.00, 2318400.00, 496800.00, 496800.00);

COMMIT;

SELECT 'Test business data initialized successfully!' AS result; 