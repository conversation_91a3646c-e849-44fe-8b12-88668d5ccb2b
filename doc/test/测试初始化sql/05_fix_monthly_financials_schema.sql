-- =================================================================
-- 修复 monthly_financials 表结构：将 month 字段从 VARCHAR 改为 DATE
-- =================================================================
-- 目的: 统一时间字段类型，避免SQL生成时的类型兼容问题
-- 执行前请确保已备份数据
-- =================================================================

USE `chatbi_test_data`;

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS `monthly_financials_backup` AS 
SELECT * FROM `monthly_financials`;

-- 2. 删除旧表
DROP TABLE IF EXISTS `monthly_financials`;

-- 3. 重新创建表结构（使用DATE类型）
CREATE TABLE `monthly_financials` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `month` DATE NOT NULL COMMENT '月份(当月第一天)',
  `department` VARCHAR(100) NOT NULL COMMENT '部门',
  `revenue` DECIMAL(15,2) NOT NULL COMMENT '收入',
  `cost` DECIMAL(15,2) NOT NULL COMMENT '成本',
  `expense` DECIMAL(15,2) NOT NULL COMMENT '费用',
  `profit` DECIMAL(15,2) NOT NULL COMMENT '利润',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_month_department` (`month`, `department`),
  INDEX `idx_month` (`month`),
  INDEX `idx_department` (`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='月度财务报表';

-- 4. 重新插入数据（转换格式）
INSERT INTO `monthly_financials` (`month`, `department`, `revenue`, `cost`, `expense`, `profit`) VALUES
-- 2024年第四季度
('2024-10-01', '销售部', 12500000.00, 8750000.00, 1250000.00, 2500000.00),
('2024-10-01', '市场部', 2500000.00, 1750000.00, 500000.00, 250000.00),
('2024-10-01', '研发部', 5000000.00, 4000000.00, 800000.00, 200000.00),
('2024-10-01', '运营部', 3000000.00, 2100000.00, 450000.00, 450000.00),

('2024-11-01', '销售部', 13200000.00, 9240000.00, 1320000.00, 2640000.00),
('2024-11-01', '市场部', 2640000.00, 1848000.00, 528000.00, 264000.00),
('2024-11-01', '研发部', 5280000.00, 4224000.00, 844800.00, 211200.00),
('2024-11-01', '运营部', 3168000.00, 2217600.00, 475200.00, 475200.00),

('2024-12-01', '销售部', 15800000.00, 11060000.00, 1580000.00, 3160000.00),
('2024-12-01', '市场部', 3160000.00, 2212000.00, 632000.00, 316000.00),
('2024-12-01', '研发部', 6320000.00, 5056000.00, 1012800.00, 253200.00),
('2024-12-01', '运营部', 3792000.00, 2654400.00, 569280.00, 568320.00),

-- 2025年第一季度
('2025-01-01', '销售部', 14500000.00, 10150000.00, 1450000.00, 2900000.00),
('2025-01-01', '市场部', 2900000.00, 2030000.00, 580000.00, 290000.00),
('2025-01-01', '研发部', 5800000.00, 4640000.00, 928000.00, 232000.00),
('2025-01-01', '运营部', 3480000.00, 2436000.00, 522000.00, 522000.00),

('2025-02-01', '销售部', 13800000.00, 9660000.00, 1380000.00, 2760000.00),
('2025-02-01', '市场部', 2760000.00, 1932000.00, 552000.00, 276000.00),
('2025-02-01', '研发部', 5520000.00, 4416000.00, 883200.00, 220800.00),
('2025-02-01', '运营部', 3312000.00, 2318400.00, 496800.00, 496800.00);

-- 5. 验证数据
SELECT '数据迁移完成' as status;
SELECT 'monthly_financials', COUNT(*) as record_count FROM monthly_financials;
SELECT 'monthly_financials_backup', COUNT(*) as backup_record_count FROM monthly_financials_backup;

-- 6. 显示新表结构
DESCRIBE monthly_financials;

-- =================================================================
-- 迁移完成！现在 month 字段是标准的 DATE 类型
-- ================================================================= 