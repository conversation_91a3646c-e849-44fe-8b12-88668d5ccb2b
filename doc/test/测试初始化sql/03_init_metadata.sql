-- ============================================
-- ChatBI 元数据初始化脚本 (最终修正版)
-- 使用实际数据库表的字段名称
-- ============================================

USE `chatbi_metadata`;

-- 禁用外键检查，方便数据插入
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================
-- 1. 数据库源配置 (database_source_configs)
-- ============================================
INSERT INTO `database_source_configs` (
  `source_name`, 
  `database_type`, 
  `host`, 
  `port`, 
  `username`, 
  `password`, 
  `database_name`, 
  `connection_parameters`, 
  `description`, 
  `status`
) VALUES (
  'ChatBI测试数据源', 
  'MYSQL', 
  'localhost', 
  3306, 
  'root', 
  NULL, 
  'chatbi_test_data', 
  '{"ssl_mode": "DISABLED", "charset": "utf8mb4"}', 
  '用于ChatBI功能测试的MySQL数据库连接配置', 
  'active'
);

-- ============================================
-- 2. 角色配置 (roles)
-- ============================================
INSERT INTO `roles` (`role_name`, `description`) VALUES
('全国销售总监', '拥有全国销售数据的完整查询权限，可以查看所有城市和区域的销售指标'),
('华北区销售经理', '华北区域销售数据查询权限，主要负责北京、天津、河北、山西、内蒙古等地区的销售业务'),
('财务分析师', '公司财务数据分析权限，可查询各部门的收入、成本、费用和利润等财务指标');

-- ============================================
-- 3. 可查询数据集 (queryable_datasets)
-- ============================================
INSERT INTO `queryable_datasets` (
  `dataset_name`, 
  `description`, 
  `database_source_id`, 
  `base_object_name`, 
  `base_object_type`, 
  `synonyms`, 
  `refresh_frequency`, 
  `data_owner`, 
  `status`
) VALUES
('每日销售汇总', 
 '按日期、城市、商品品类汇总的销售数据，包含销售额、订单数、客户数等核心销售指标。适用于日常销售分析、区域对比、品类分析等场景。',
 1, 
 'daily_sales_summary', 
 'TABLE',
 '["销售数据", "日销售", "销售汇总", "销售报表"]',
 '每日更新',
 '销售运营团队',
 'active'),

('公司月度财务报表', 
 '按月份和部门汇总的财务数据，包含收入、成本、费用、利润等核心财务指标。适用于部门绩效分析、成本控制、利润分析等场景。',
 1, 
 'monthly_financials', 
 'TABLE',
 '["财务数据", "月度财务", "财务报表", "部门财务"]',
 '每月更新',
 '财务管理部',
 'active'),

('用户信息查询', 
 '用户基础信息数据，包含用户姓名、邮箱、电话、城市、注册日期等。支持用户画像分析和地域分布统计。',
 1, 
 'users', 
 'TABLE',
 '["用户数据", "客户信息", "用户画像"]',
 '实时更新',
 '用户运营团队',
 'active');

-- ============================================
-- 4. 数据集字段配置 (dataset_columns)
-- ============================================

-- 4.1 每日销售汇总数据集字段
INSERT INTO `dataset_columns` (
  `dataset_id`, 
  `column_name`, 
  `description`, 
  `technical_name_or_expression`, 
  `semantic_type`, 
  `data_type`, 
  `allowed_aggregations`, 
  `synonyms`, 
  `is_filterable`, 
  `is_groupable`, 
  `is_default_metric`, 
  `is_computed`, 
  `formatting_hint`, 
  `status`
) VALUES

-- 时间维度
(1, '日期', '交易发生的日期', 'dt', 'TIME_DIMENSION', 'DATE', NULL, '["date", "时间", "交易日期"]', TRUE, TRUE, FALSE, 0, '{"format": "YYYY-MM-DD"}', 'active'),

-- 地理维度
(1, '城市', '销售发生的城市', 'city', 'Dimension', 'VARCHAR', NULL, '["地区", "区域", "city"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

-- 商品维度  
(1, '商品品类', '商品的分类', 'category', 'Dimension', 'VARCHAR', NULL, '["category", "品类", "分类", "商品类别"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

-- 基础指标
(1, '销售额', '总的销售金额', 'sales_amount', 'Metric', 'DECIMAL', '["SUM", "AVG", "MAX", "MIN"]', '["GMV", "营收", "收入", "sales", "销售金额"]', TRUE, FALSE, TRUE, 0, '{"format": "currency", "precision": 2}', 'active'),

(1, '订单数', '订单的总数量', 'order_count', 'Metric', 'INT', '["SUM", "AVG", "MAX", "MIN"]', '["订单量", "单量", "orders", "订单总数"]', TRUE, FALSE, FALSE, 0, '{"format": "number"}', 'active'),

(1, '客户数', '独立客户的数量', 'customer_count', 'Metric', 'INT', '["SUM", "AVG", "MAX", "MIN"]', '["客户量", "用户数", "customers", "买家数"]', TRUE, FALSE, FALSE, 0, '{"format": "number"}', 'active'),

(1, '客单价', '平均每个订单的金额', 'avg_order_value', 'Metric', 'DECIMAL', '["AVG", "MAX", "MIN"]', '["AOV", "平均订单价值", "订单均价"]', TRUE, FALSE, FALSE, 0, '{"format": "currency", "precision": 2}', 'active'),

-- 计算指标 - 本月累计销售额 (MTD)
(1, '本月累计销售额', '从月初到当前日期的累计销售额', 'SUM(sales_amount) OVER (PARTITION BY DATE_FORMAT(dt, "%Y-%m") ORDER BY dt ROWS UNBOUNDED PRECEDING)', 'Metric', 'DECIMAL', '["SUM"]', '["MTD销售额", "MTD", "月度累计", "本月累计"]', TRUE, FALSE, FALSE, 1, '{"format": "currency", "precision": 2}', 'active'),

-- 计算指标 - 日均销售额
(1, '日均销售额', '指定时间段内的平均每日销售额', 'AVG(sales_amount)', 'Metric', 'DECIMAL', '["AVG"]', '["日平均销售额", "每日平均销售额"]', TRUE, FALSE, FALSE, 1, '{"format": "currency", "precision": 2}', 'active');

-- 4.2 财务报表数据集字段
INSERT INTO `dataset_columns` (
  `dataset_id`, 
  `column_name`, 
  `description`, 
  `technical_name_or_expression`, 
  `semantic_type`, 
  `data_type`, 
  `allowed_aggregations`, 
  `synonyms`, 
  `is_filterable`, 
  `is_groupable`, 
  `is_default_metric`, 
  `is_computed`, 
  `formatting_hint`, 
  `status`
) VALUES

-- 时间维度
(2, '月份', '财务数据所属的月份', 'month', 'TIME_DIMENSION', 'VARCHAR', NULL, '["month", "时间", "会计月份"]', TRUE, TRUE, FALSE, 0, '{"format": "YYYY-MM"}', 'active'),

-- 组织维度
(2, '部门', '财务数据所属的部门', 'department', 'Dimension', 'VARCHAR', NULL, '["dept", "组织", "业务部门"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

-- 财务指标
(2, '收入', '部门的总收入', 'revenue', 'Metric', 'DECIMAL', '["SUM", "AVG", "MAX", "MIN"]', '["营收", "销售收入", "revenue"]', TRUE, FALSE, TRUE, 0, '{"format": "currency", "precision": 2}', 'active'),

(2, '成本', '部门的总成本', 'cost', 'Metric', 'DECIMAL', '["SUM", "AVG", "MAX", "MIN"]', '["费用成本", "cost"]', TRUE, FALSE, FALSE, 0, '{"format": "currency", "precision": 2}', 'active'),

(2, '费用', '部门的运营费用', 'expense', 'Metric', 'DECIMAL', '["SUM", "AVG", "MAX", "MIN"]', '["运营费用", "expense"]', TRUE, FALSE, FALSE, 0, '{"format": "currency", "precision": 2}', 'active'),

(2, '利润', '部门的净利润', 'profit', 'Metric', 'DECIMAL', '["SUM", "AVG", "MAX", "MIN"]', '["净利润", "盈利", "profit", "net_profit"]', TRUE, FALSE, FALSE, 0, '{"format": "currency", "precision": 2}', 'active'),

-- 计算指标 - 利润率
(2, '利润率', '利润占收入的百分比', 'ROUND((profit / revenue) * 100, 2)', 'Metric', 'DECIMAL', '["AVG"]', '["盈利率", "净利润率"]', TRUE, FALSE, FALSE, 1, '{"format": "percentage", "precision": 2}', 'active'),

-- 计算指标 - 季度累计收入
(2, '季度累计收入', '当前季度从第一个月到当前月的累计收入', 'SUM(revenue) OVER (PARTITION BY CONCAT(YEAR(STR_TO_DATE(CONCAT(month, "-01"), "%Y-%m-%d")), "-Q", QUARTER(STR_TO_DATE(CONCAT(month, "-01"), "%Y-%m-%d"))) ORDER BY month ROWS UNBOUNDED PRECEDING)', 'Metric', 'DECIMAL', '["SUM"]', '["QTD收入", "季度累计"]', TRUE, FALSE, FALSE, 1, '{"format": "currency", "precision": 2}', 'active');

-- 4.3 用户信息数据集字段
INSERT INTO `dataset_columns` (
  `dataset_id`, 
  `column_name`, 
  `description`, 
  `technical_name_or_expression`, 
  `semantic_type`, 
  `data_type`, 
  `allowed_aggregations`, 
  `synonyms`, 
  `is_filterable`, 
  `is_groupable`, 
  `is_default_metric`, 
  `is_computed`, 
  `formatting_hint`, 
  `status`
) VALUES

-- 用户标识
(3, '用户ID', '用户的唯一标识符', 'id', 'Dimension', 'BIGINT', NULL, '["用户编号", "user_id"]', TRUE, FALSE, FALSE, 0, NULL, 'active'),

(3, '用户姓名', '用户的姓名', 'user_name', 'Dimension', 'VARCHAR', NULL, '["姓名", "用户名", "name"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

(3, '邮箱', '用户的电子邮箱', 'email', 'Dimension', 'VARCHAR', NULL, '["email", "电子邮箱", "邮件地址"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

(3, '城市', '用户所在的城市', 'city', 'Dimension', 'VARCHAR', NULL, '["地区", "区域", "city"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

(3, '注册日期', '用户注册的日期', 'registration_date', 'TIME_DIMENSION', 'DATE', NULL, '["注册时间", "reg_date"]', TRUE, TRUE, FALSE, 0, '{"format": "YYYY-MM-DD"}', 'active'),

(3, '用户状态', '用户当前的状态', 'status', 'Dimension', 'VARCHAR', NULL, '["状态", "user_status"]', TRUE, TRUE, FALSE, 0, NULL, 'active'),

-- 统计指标
(3, '用户数量', '统计用户的数量', 'COUNT(*)', 'Metric', 'INT', '["COUNT"]', '["用户总数", "用户量", "user_count"]', TRUE, FALSE, TRUE, 1, '{"format": "number"}', 'active');

-- ============================================
-- 5. 数据集访问权限 (dataset_access_permissions)
-- ============================================
INSERT INTO `dataset_access_permissions` (`role_id`, `dataset_id`) VALUES
-- 全国销售总监：可以查询销售数据和用户数据
(1, 1), -- 每日销售汇总
(1, 3), -- 用户信息查询

-- 华北区销售经理：可以查询销售数据和用户数据（应用层需要根据角色加地域限制）
(2, 1), -- 每日销售汇总
(2, 3), -- 用户信息查询

-- 财务分析师：只能查询财务数据
(3, 2); -- 公司月度财务报表

-- ============================================
-- 6. 业务术语对照 (business_terminology)
-- ============================================
INSERT INTO `business_terminology` (
  `business_term`, 
  `standard_reference_type`, 
  `standard_reference_id`, 
  `standard_reference_name`, 
  `context_description`, 
  `status`
) VALUES

-- 销售相关术语
('GMV', 'DatasetColumn', '4', '销售额', '电商行业常用术语，指商品交易总额(Gross Merchandise Volume)', 'active'),
('营收', 'DatasetColumn', '4', '销售额', '企业营业收入的简称', 'active'),
('订单量', 'DatasetColumn', '5', '订单数', '订单数量的常用说法', 'active'),
('单量', 'DatasetColumn', '5', '订单数', '订单数量的简称', 'active'),
('客户量', 'DatasetColumn', '6', '客户数', '客户数量的常用说法', 'active'),
('用户数', 'DatasetColumn', '6', '客户数', '用户数量的说法，在销售语境下通常指客户数', 'active'),
('AOV', 'DatasetColumn', '7', '客单价', 'Average Order Value的缩写，平均订单价值', 'active'),
('客单均价', 'DatasetColumn', '7', '客单价', '客单价的另一种说法', 'active'),

-- 时间相关术语
('MTD', 'DatasetColumn', '8', '本月累计销售额', 'Month To Date的缩写，本月累计', 'active'),
('本月累计', 'DatasetColumn', '8', '本月累计销售额', '从月初到当前日期的累计值', 'active'),
('月度累计', 'DatasetColumn', '8', '本月累计销售额', '月度累计值的说法', 'active'),

-- 财务相关术语
('净利润', 'DatasetColumn', '15', '利润', '净利润的标准说法', 'active'),
('盈利', 'DatasetColumn', '15', '利润', '利润的通俗说法', 'active'),
('利润率', 'DatasetColumn', '16', '利润率', '利润占收入的比例', 'active'),
('盈利率', 'DatasetColumn', '16', '利润率', '盈利率的说法', 'active'),
('QTD', 'DatasetColumn', '17', '季度累计收入', 'Quarter To Date的缩写，季度累计', 'active'),

-- 地域相关术语
('华北', 'Custom', NULL, '北京,天津,石家庄,太原,呼和浩特', '华北地区包含的主要城市', 'active'),
('华东', 'Custom', NULL, '上海,南京,杭州,合肥,济南', '华东地区包含的主要城市', 'active'),
('华南', 'Custom', NULL, '广州,深圳,南宁,海口', '华南地区包含的主要城市', 'active');

-- ============================================
-- 7. 查询示例 (query_examples)
-- ============================================
INSERT INTO `query_examples` (
  `user_question`, 
  `target_query_representation`, 
  `target_dataset_id`, 
  `involved_column_ids`, 
  `notes`, 
  `difficulty_level`, 
  `status`
) VALUES

-- 简单查询示例
('昨天北京的销售额是多少？', 
 '{"intent": "simple_metric", "dataset": "每日销售汇总", "metrics": ["销售额"], "filters": [{"column": "日期", "value": "YESTERDAY"}, {"column": "城市", "value": "北京"}]}',
 1, 
 '[1, 2, 4]',
 '基础的单指标查询，包含时间和地域筛选条件',
 'Easy', 
 'verified'),

('查一下上海的订单量', 
 '{"intent": "simple_metric", "dataset": "每日销售汇总", "metrics": ["订单数"], "filters": [{"column": "城市", "value": "上海"}]}',
 1, 
 '[2, 5]',
 '基础查询，测试同义词理解（订单量->订单数）',
 'Easy', 
 'verified'),

-- 业务术语测试示例
('昨天北京的GMV是多少？', 
 '{"intent": "terminology", "dataset": "每日销售汇总", "metrics": ["销售额"], "terms": ["GMV"], "filters": [{"column": "日期", "value": "YESTERDAY"}, {"column": "城市", "value": "北京"}]}',
 1, 
 '[1, 2, 4]',
 '测试业务术语理解，GMV应该映射到销售额',
 'Medium', 
 'verified'),

('看看北京1月份的MTD销售额', 
 '{"intent": "computed_metric", "dataset": "每日销售汇总", "metrics": ["本月累计销售额"], "terms": ["MTD"], "filters": [{"column": "城市", "value": "北京"}]}',
 1, 
 '[1, 2, 8]',
 '计算指标查询，测试MTD术语理解和窗口函数',
 'Hard', 
 'verified');

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

COMMIT;

SELECT 'Metadata initialization completed successfully!' AS result; 