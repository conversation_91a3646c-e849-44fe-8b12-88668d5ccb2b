# **ChatBI 对话查询功能测试数据准备计划**

## **1\. 测试目标**

本计划旨在创建一套完整的端到端测试数据，用以全面验证 ChatBI 的核心对话查询功能，主要覆盖以下目标：

* **功能正确性**: 验证系统能否正确理解用户的简单查询、带筛选条件的查询、聚合查询，并返回准确的数据结果。  
* **AI 理解能力**:  
  * **同义词/业务术语**: 测试 AI 能否通过 synonyms 和 business\_terminology 来理解用户的“黑话”（如 GMV）。  
  * **复杂意图**: 测试 AI 能否通过 query\_examples 的 RAG 检索，理解更复杂的分析意图（如对比、趋势、累计值查询）。  
* **权限控制**: 验证不同角色的用户，是否只能查询到其被授权访问的数据集。  
* **计算指标**: 验证 AI 能否正确调用在元数据中定义的、包含复杂 SQL 表达式的计算指标（如“月度累计销售额”）。  
* **边界与异常**: 测试系统在查询不到数据或遇到无效问题时的反馈是否友好和合理。

## **2\. 测试场景与角色设定**

* **业务场景**: 我们将继续使用之前讨论的虚拟电商公司场景，包含**销售**和**财务**两大核心业务。  
* **数据源**: 所有数据都存放在您本机的 MySQL 数据库 chatbi\_test\_data 中。  
* **测试角色 (Test Personas)**: 我们将设定三个不同权限的测试用户角色，用于登录和发起对话查询。

| 测试角色 | 对应 roles.role\_name | 核心测试点 |
| :---- | :---- | :---- |
| **张总 (全国销售总监)** | 全国销售总监 | **高权限**: 应能查询到所有销售数据，包括全国范围和任意城市的指标。 |
| **李经理 (华北区经理)** | 华北区销售经理 | **数据权限**: 同样访问“每日销售汇总”数据集，但其查询结果理论上应被限制在华北区（这需要应用层或AI强制加 WHERE 条件，是重要的测试点）。 |
| **王分析师 (财务分析师)** | 财务分析师 | **数据集隔离**: 应只能查询“公司月度财务报表”，无法访问任何销售相关的数据。 |

## **3\. 【新增】业务数据表关联关系与数据完整性说明**

为了保证测试的有效性和数据的真实性，我们在创建测试数据时，将严格遵循以下原则：

### **3.1 表间逻辑关联**

尽管为了简化 AI 的查询，我们的核心分析表（如 daily\_sales\_summary）大多是预先处理好的宽表，但它们之间依然存在清晰的业务逻辑关联：

* **daily\_sales\_summary 与 users**:  
  * **关联字段**: city (城市)。  
  * **关系说明**: daily\_sales\_summary 表中的每一条销售记录都发生在某个城市，这个城市必然是 users 表中用户所在的城市之一。这模拟了“有用户的地方才可能有销售”的业务逻辑。  
* **daily\_sales\_summary 与 monthly\_financials**:  
  * **关联字段**: 时间维度 (通过 daily\_sales\_summary.dt 和 monthly\_financials.month 进行关联)。  
  * **关系说明**: 某个部门在某个月的财务数据（如销售部的收入），在逻辑上应该与 daily\_sales\_summary 表中该月所有销售记录的总和存在关联性。

### **3.2 数据完整性与外键逻辑**

1. **非空原则**: 所有在数据库表结构中定义为 NOT NULL 的字段，在生成的测试数据中都**必须有值**，不允许出现 NULL 或空字符串。  
2. **逻辑外键**:  
   * 虽然我们为了测试方便，可能不会在 chatbi\_test\_data 数据库的表之间设置严格的物理外键约束，但在生成数据时，会**严格保证逻辑上的外键关系**。  
   * 例如，daily\_sales\_summary 中出现的任何一个 city，都必须是 users 表中已经存在的 city。  
   * 这样做可以确保所有基于关联维度的查询（如 JOIN 或子查询）都能返回合理的结果。

## **4\. 测试数据准备清单**

为了支持上述测试，我们需要准备两类数据：**系统元数据**（存放在 chatbi\_metadata 库）和**底层业务数据**（存放在 chatbi\_test\_data 库）。

### **4.1 底层业务数据 (chatbi\_test\_data)**

这是 AI 查询的最终数据来源。

* **daily\_sales\_summary 表**: 需要包含足够多样的销售记录。  
  * **时间跨度**: 覆盖多个月、多个季度，并跨越年份（如2024年12月 \- 2025年2月），以测试“本月”、“上季度”、“YTD”等查询。  
  * **城市维度**: 包含至少20个城市的数据，特别是“北京”、“天津”等华北区城市，以及其他区域的城市。  
  * **品类维度**: 包含多个商品品类（如电子产品、服装、家居用品）。  
* **monthly\_financials 表**: 包含财务数据。  
  * **时间跨度**: 覆盖多个季度。  
  * **部门维度**: 包含多个部门（如销售部、市场部、研发部）。  
* **users 表**: 包含不同城市的用户信息，用于支撑自定义 SQL 数据集。

### **4.2 系统元数据 (chatbi\_metadata)**

这是 AI 的“大脑”，决定了它能理解什么、如何查询。

* **database\_source\_configs**: 需包含一条指向 chatbi\_test\_data 数据库的配置。  
* **queryable\_datasets**: 需创建“每日销售汇总”和“公司月度财务报表”两个核心数据集。  
* **dataset\_columns**:  
  * 为“每日销售汇总”详细配置所有字段，包括**简单指标**（销售额）、**维度**（城市、品类）以及**计算指标**（如 本月累计销售额）。  
  * 为“公司月度财务报表”配置财务相关字段。  
* **roles**: 创建“全国销售总监”、“华北区销售经理”、“财务分析师”三个角色。  
* **dataset\_access\_permissions**:  
  * 为“全国销售总监”和“华北区销售经理”授权访问“每日销售汇总”。  
  * 为“财务分析师”授权访问“公司月度财务报表”。  
* **business\_terminology**: 至少包含 GMV \-\> 销售额 和 MTD \-\> 本月累计销售额 的映射。  
* **query\_examples**: 包含至少 5-10 条高质量的查询范例，覆盖简单查询、带筛选的查询、聚合查询和涉及累计值的复杂查询。

## **5\. 测试用例建议 (Test Case Suggestions)**

准备好数据后，我们可以设计以下几类测试用例，由测试人员分别扮演不同角色进行对话。

1. **基础查询 (角色: 张总)**  
   * 昨天北京的销售额是多少？  
   * 查一下上海的订单量  
2. **同义词/术语测试 (角色: 张总)**  
   * 昨天北京的GMV是多少？ (测试 business\_terminology)  
   * 查一下上海的订单数 (测试 synonyms)  
3. **权限隔离测试**  
   * **\[失败场景\]** 以 **王分析师** (财务) 身份登录，提问: 查一下销售额。**预期结果**: AI 应礼貌地拒绝，并告知其没有查询该数据的权限。  
   * **\[成功场景\]** 以 **王分析师** (财务) 身份登录，提问: 上个季度研发部的净利润是多少？。**预期结果**: 成功返回数据。  
4. **复杂查询/RAG 测试 (角色: 张总)**  
   * 对比一下北京和上海上个月的GMV (测试AI是否能理解对比意图，可能参考范例)  
   * 按城市列出上周的销售额，从高到低排 (测试排序)  
5. **计算指标测试 (角色: 张总)**  
   * 看看北京1月份的MTD销售额 (测试AI能否调用定义好的窗口函数表达式)

## **6\. 执行计划**

1. **确认方案**: 请您审阅此测试数据准备计划，确认是否覆盖了您关心的所有测试点。  
2. **生成脚本**: 待您确认后，我将为您生成两份 SQL 脚本：一份用于创建和填充 chatbi\_test\_data 业务数据库，另一份用于填充 chatbi\_metadata 元数据数据库。  
3. **执行测试**: 您在环境中执行完脚本后，即可开始进行端到端的对话查询功能测试。