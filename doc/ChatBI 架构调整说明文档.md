# **ChatBI 项目架构调整说明文档**

## **1\. 调整背景 (Adjustment Background)**

在项目初始设计和开发过程中，我们发现 chatbi-admin-backend 模块和 chatbi-metadata-service 模块中，存在对同一批数据库表（如 database\_source\_configs, queryable\_datasets 等）的实体类（Entity）和数据访问仓库（Repository）的重复定义。

这种重复违反了软件设计的 DRY (Don't Repeat Yourself) 原则，会导致维护成本增加、代码冗余以及潜在的数据模型不一致问题。为了优化项目结构，提升代码质量和可维护性，我们决定进行本次架构调整。

## **2\. 核心调整方案 (Core Adjustment Plan)**

本次调整的核心思想是\*\*“关注点分离”**与**“单一职责”\*\*。我们将对数据持久化层进行统一管理。

**核心决策：**

**将所有与 chatbi\_metadata 数据库相关的 JPA 实体 (Entity) 和数据仓库 (Repository) 类，全部集中定义在 chatbi-metadata-service 模块中。**

其他需要访问这些数据的业务模块（如 chatbi-admin-backend 和 chatbi-agent-core），将不再维护自己的 Entity 和 Repository，而是通过直接添加对 chatbi-metadata-service 模块的依赖来获取数据访问能力。

## **3\. 模块职责变更 (Module Responsibility Changes)**

* **chatbi-metadata-service**:  
  * **变更后职责**: 成为项目中**唯一的数据持久化中心**。它不仅定义了所有与 chatbi\_metadata 数据库交互的 Entity 和 Repository，还向上层业务模块提供基础的、通用的数据服务（Service）。  
  * **移除**: (无)  
  * **增加**: 统一管理所有相关的 Entity 和 Repository。  
* **chatbi-admin-backend**:  
  * **变更后职责**: 完全聚焦于后台管理的**业务逻辑**和 **API 接口**。它将作为一个**消费者**，调用 chatbi-metadata-service 提供的服务或直接注入其 Repository 来完成数据操作。  
  * **移除**: 删除其包下的所有重复的 entity 和 repository 子包及其中的所有类。  
  * **增加**: 在其 pom.xml 中增加对 chatbi-metadata-service 模块的依赖。  
* **chatbi-agent-core**:  
  * **变更后职责**: (同 admin-backend) 完全聚焦于 AI Agent 的核心对话和查询规划逻辑。  
  * **移除**: (如果存在) 删除其包下的所有重复的 entity 和 repository。  
  * **增加**: 在其 pom.xml 中增加对 chatbi-metadata-service 模块的依赖。

## **4\. 依赖关系变更 (Dependency Relationship Changes)**

调整后的 Maven/Gradle 模块依赖关系将更加清晰：

graph TD  
    subgraph "业务逻辑层"  
        Admin\["chatbi-admin-backend"\]  
        Agent\["chatbi-agent-core"\]  
    end

    subgraph "数据持久化层"  
        Metadata\["chatbi-metadata-service"\]  
    end

    subgraph "基础库"  
        Common\["chatbi-common"\]  
    end

    Admin \--\> Metadata  
    Agent \--\> Metadata  
    Admin \--\> Common  
    Agent \--\> Common  
    Metadata \--\> Common

    style Metadata fill:\#c6f6d5,stroke:\#2f855a,stroke-width:2px

## **5\. 受影响的架构树展示 (Affected Architecture Tree)**

为了更直观地展示本次调整，以下是调整后核心模块的包结构变化：

chatbi-service/  
├── ...  
│  
├── 🧠 chatbi-agent-core/  
│   ├── pom.xml                   \<\!-- 新增: \<dependency\>chatbi-metadata-service\</dependency\> \--\>  
│   └── src/main/java/com/qding/chatbi/agent/  
│       └── service/  
│           └── impl/  
│               └── ConversationServiceImpl.java \<\!-- Service层现在会注入metadata模块的Repository \--\>  
│  
├── 🛠️ chatbi-admin-backend/  
│   ├── pom.xml                   \<\!-- 新增: \<dependency\>chatbi-metadata-service\</dependency\> \--\>  
│   └── src/main/java/com/qding/chatbi/admin/  
│       ├── controller/               
│       ├── service/                \<\!-- Service层现在会注入metadata模块的Repository \--\>  
│       └── vo/  
│       └── entity/                 \<\!-- 【移除】此包及其所有内容 \--\>  
│       └── repository/             \<\!-- 【移除】此包及其所有内容 \--\>  
│  
└── 💿 chatbi-metadata-service/  
    ├── pom.xml  
    └── src/main/java/com/qding/chatbi/metadata/  
        ├── entity/                 \<\!-- 【核心】统一管理所有JPA实体 \--\>  
        │   ├── DatabaseSourceConfig.java  
        │   ├── QueryableDataset.java  
        │   └── ...  
        ├── repository/             \<\!-- 【核心】统一管理所有JPA仓库 \--\>  
        │   ├── DataSourceRepository.java  
        │   ├── DatasetRepository.java  
        │   └── ...  
        └── service/                \<\!-- 提供基础的数据服务 \--\>  
            ├── impl/  
            ├── DatasetMetadataService.java  
            └── PermissionService.java

**说明**:

* chatbi-agent-core 和 chatbi-admin-backend 模块的 pom.xml 文件需要更新，以包含对 chatbi-metadata-service 的依赖。  
* chatbi-agent-core 和 chatbi-admin-backend 模块内部原有的 entity 和 repository 包将被完全移除。  
* chatbi-metadata-service 模块成为项目中唯一的、权威的数据持久化层，包含了所有的 Entity 和 Repository 定义。  
* 所有需要进行数据库操作的 Service 类（无论它们位于哪个模块），现在都将注入 chatbi-metadata-service 中定义的 Repository 接口。

## **6\. 代码实现示例 (Code Implementation Example)**

以 ModelManagementService 为例，展示其调用方式的变化。

**调整前 (Before)**: ModelManagementService 在 chatbi-admin-backend 内部，并注入**本模块**的 DataSourceRepository。

// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/ModelManagementService.java  
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.repository.DataSourceRepository; // 引用本模块的Repository

@Service  
public class ModelManagementService {  
    @Autowired  
    private DataSourceRepository dataSourceRepository; // 注入本模块的Repository

    public void someMethod() {  
        dataSourceRepository.findAll();  
    }  
}

**调整后 (After)**: chatbi-admin-backend 依赖 chatbi-metadata-service，ModelManagementService 注入\*\*metadata-service 模块\*\*的 DataSourceRepository。

// 首先，在 chatbi-admin-backend/pom.xml 中添加依赖:  
// \<dependency\>  
//     \<groupId\>com.qding.chatbi\</groupId\>  
//     \<artifactId\>chatbi-metadata-service\</artifactId\>  
//     \<version\>${project.version}\</version\>  
// \</dependency\>

// 然后，修改 Service 实现:  
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/ModelManagementService.java  
package com.qding.chatbi.admin.service;

import com.qding.chatbi.metadata.repository.DataSourceRepository; // 引用 metadata 模块的 Repository

@Service  
public class ModelManagementService {  
    @Autowired  
    private DataSourceRepository dataSourceRepository; // 注入 metadata 模块的 Repository

    public void someMethod() {  
        dataSourceRepository.findAll();  
    }  
}

## **7\. 总结 (Summary)**

本次架构调整通过集中化管理数据持久化层，解决了代码冗余和维护困难的问题。调整后的架构职责更清晰、耦合度更低，为项目的长期健康发展奠定了坚实的基础，同时也依然保留了未来将 metadata-service 拆分为独立微服务的可能性。