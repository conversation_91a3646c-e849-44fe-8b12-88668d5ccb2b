# ChatBI 查询范例 JSON 格式更新说明

## 📋 概述

由于 NLU Agent 提示词模板的调整，查询范例管理中的 JSON 结构需要与新的模板保持一致。本工具提供了完整的解决方案，可以：

1. 生成符合新提示词模板的查询范例数据
2. 同步更新数据库和向量库
3. 使用 1024 维向量（text-embedding-v4 模型）

## 🎯 新的 JSON 格式

### DATA_QUERY 格式

```json
{
  "intent": "DATA_QUERY",
  "entities": {
    "metrics": ["销售额"],
    "dimensions_to_group": ["城市"],
    "filters": [
      { "fieldName": "城市", "operator": "IN", "value": ["北京", "上海"] },
      {
        "fieldName": "日期",
        "operator": "BETWEEN",
        "value": ["2024-12-01", "2024-12-31"]
      }
    ],
    "sort_by": [{ "fieldName": "销售额", "order": "DESC" }],
    "limit": 10
  },
  "queryType": "AGGREGATION_QUERY",
  "targetDatasetId": 1,
  "datasetMatchConfidence": "HIGH",
  "internal_context": {
    "originalQuery": "北京上海本月的销售额",
    "mergedContext": "北京上海本月的销售额"
  }
}
```

### CLARIFICATION_NEEDED 格式

```json
{
  "intent": "CLARIFICATION_NEEDED",
  "response": "我了解您想查看北京的收入数据。请问您想查看哪个时间段的数据？",
  "clarificationOptions": [
    { "optionText": "查看北京本月的收入" },
    { "optionText": "查看北京上个月的收入" },
    { "optionText": "查看北京最近一年的收入" }
  ],
  "internal_context": {
    "originalQuery": "北京的收入",
    "mergedContext": "北京的收入"
  }
}
```

### DATA_UNAVAILABLE 格式

```json
{
  "intent": "DATA_UNAVAILABLE",
  "response": "您查询的'用户增长指标'不在可查询的数据范围内，或者您没有访问该数据的权限。",
  "internal_context": {
    "originalQuery": "用户增长指标",
    "mergedContext": "用户增长指标"
  }
}
```

### GREETING 格式

```json
{
  "intent": "GREETING",
  "response": "您好！很高兴为您服务，请问有什么可以帮您分析的吗？",
  "internal_context": {
    "originalQuery": "你好",
    "mergedContext": "你好"
  }
}
```

## 🚀 使用方法

### 方式一：一键运行（推荐）

```bash
./run_update_query_examples.sh
```

这个脚本会自动：

- 检查 Python 环境和依赖
- 检查 MySQL 和 Milvus 服务状态
- 确认用户操作意图
- 执行完整的更新流程

### 方式二：直接运行 Python 脚本

```bash
python3 update_query_examples_json_format.py
```

## 📋 前置条件

### 1. 服务环境

- **MySQL 服务**：确保`chatbi_metadata`数据库可访问
- **Milvus 服务**：确保端口 19530 可达
- **Dashscope API**：确保 API 密钥有效

### 2. Python 环境

- **Python 3.7+**
- **依赖包**：
  - `mysql-connector-python`
  - `pymilvus`
  - `requests`

脚本会自动检查并安装缺失的依赖包。

### 3. 权限要求

- MySQL root 用户访问权限
- Milvus 数据库操作权限

## 🔄 更新流程

### 步骤 1：环境检查

- 验证 Python 环境
- 检查依赖包
- 测试 MySQL 连接
- 测试 Milvus 连接
- 验证 Dashscope API

### 步骤 2：数据准备

- 加载新格式的查询范例模板
- 包含 4 种意图类型：DATA_QUERY, CLARIFICATION_NEEDED, DATA_UNAVAILABLE, GREETING
- 共 8 个示例，覆盖各种查询场景

### 步骤 3：清空旧数据

- 清空`query_examples`表
- 清空`query_examples`向量集合

### 步骤 4：插入新数据

- 插入符合新格式的 JSON 数据到数据库
- 生成 1024 维向量并插入 Milvus
- 创建 COSINE 相似度索引

## 📊 生成的示例数据

脚本将生成以下 8 个查询范例：

| 用户问题               | 意图类型             | 难度   | 说明           |
| ---------------------- | -------------------- | ------ | -------------- |
| 北京上海本月的销售额   | DATA_QUERY           | Easy   | 多城市聚合查询 |
| 销售额最高的 10 个产品 | DATA_QUERY           | Medium | 产品排名查询   |
| 最近 30 天销售额趋势   | DATA_QUERY           | Medium | 时间趋势查询   |
| 北京的收入             | CLARIFICATION_NEEDED | Easy   | 缺少时间信息   |
| 各城市销售额           | CLARIFICATION_NEEDED | Easy   | 缺少时间信息   |
| 用户增长指标           | DATA_UNAVAILABLE     | Easy   | 数据不可用     |
| 你好                   | GREETING             | Easy   | 问候语         |
| 谢谢                   | GREETING             | Easy   | 感谢回复       |

## 🔧 配置说明

### 向量配置

- **模型**：text-embedding-v4
- **维度**：1024 维
- **相似度算法**：COSINE
- **索引类型**：IVF_FLAT

### 数据库配置

- **主机**：localhost:3306
- **数据库**：chatbi_metadata
- **表**：query_examples
- **编码**：UTF-8

### 向量库配置

- **主机**：localhost:19530
- **集合**：query_examples
- **字段**：id (主键), vector (向量)

## 📝 注意事项

1. **数据备份**：更新前会完全清空现有数据，请确保已做好备份
2. **网络要求**：需要访问 Dashscope API 生成向量
3. **API 限流**：脚本已内置 API 限流保护（每次调用间隔 0.1 秒）
4. **日志记录**：执行过程会生成详细日志文件便于调试

## 🛠️ 故障排除

### 常见问题

1. **MySQL 连接失败**

   - 检查 MySQL 服务是否启动
   - 验证 root 密码是否正确

2. **Milvus 连接失败**

   - 检查 Milvus 服务是否启动
   - 验证端口 19530 是否可访问

3. **向量生成失败**

   - 检查网络连接
   - 验证 Dashscope API 密钥
   - 检查 API 使用配额

4. **依赖包安装失败**
   - 使用`pip3 install`手动安装
   - 检查 Python 版本兼容性

### 日志文件

执行过程中会生成日志文件：

- 文件名格式：`generate_query_examples_YYYYMMDD_HHMMSS.log`
- 包含详细的执行信息和错误提示

## ✅ 验证结果

更新完成后，可以通过以下方式验证：

1. **数据库验证**

```sql
SELECT id, user_question, JSON_EXTRACT(target_query_representation, '$.intent') as intent
FROM query_examples
ORDER BY id;
```

2. **向量库验证**

```python
from pymilvus import Collection
collection = Collection("query_examples")
print(f"向量数量: {collection.num_entities}")
```

3. **前端页面验证**

- 访问查询范例管理页面
- 检查 JSON 格式是否正确显示
- 验证向量搜索功能

## 🎉 完成

更新完成后，系统中的查询范例数据将完全符合新的 NLU Agent 提示词模板要求，为智能查询服务提供高质量的示例支持。
