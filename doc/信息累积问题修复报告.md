# ChatBI 多轮对话信息累积问题修复报告

## 🚨 **问题描述**

用户报告了一个严重的多轮对话信息丢失问题：

**问题场景**：

1. 用户问："成本"
2. 用户问："上海"
3. 用户问："去年的"
4. **问题**：这时地点"上海"被丢了，AI 又开始问城市
5. 用户问："上海"
6. **问题**：时间"去年的"又丢了

## 🔍 **根本原因分析**

### **原因 1：NLU 提示词模板语法错误**

- **问题**：NLU 提示词中使用了错误的模板语法 `{{chatHistory}}`
- **现实**：系统使用 AiServices + Function Calling 架构，历史消息通过方法参数传递
- **后果**：AI 可能无法正确接收历史对话信息

### **原因 2：信息累积规则不完整**

- **问题**：NLU 系统在处理多轮对话时，只考虑最近 1-2 轮历史，没有维护完整的累积状态
- **表现**：
  - 第 3 轮："去年的" → 只与"上海"合并，丢失了核心指标"成本"
  - 第 5 轮："上海" → 只与"去年的"合并，又丢失了核心指标"成本"
- **后果**：形成无限循环的澄清请求

## 🔧 **解决方案**

### **修复 1：模板语法修正**

**文件**：`chatbi-agent-core/src/main/resources/prompts/nlu_function_calling_agent.txt`

**修改前**：

```
**对话历史记录：**
{{chatHistory}}
```

**修改后**：

```
**对话历史记录：**
（历史对话将通过方法参数自动传递）
```

### **修复 2：强化信息累积规则**

**新增核心规则**：

1. **完整历史扫描**：分析完整对话历史，提取**所有轮次**中累积的查询信息
2. **核心信息追踪**：**优先保持原始核心意图**（如"成本"、"销售额"等指标）
3. **增量信息叠加**：将新的维度信息（时间、地理、筛选条件）叠加到核心意图上
4. **信息完整性维护**：确保每次合并都保留**所有**已收集的有效信息

**关键强制规则**：

- **核心意图保持**：如果历史中存在明确的指标查询，该指标必须在后续所有轮次中保持
- **维度信息累积**：所有历史轮次中提供的有效维度信息都应该累积保持
- **逐步完善机制**：每轮对话应该在保持已有信息的基础上，增加新的维度信息
- **信息丢失检测**：严格检查是否有历史信息在当前轮次中被意外丢失

### **修复 3：添加完整累积示例**

**正确的对话流程**：

1. 用户："成本" → 累积=[指标:成本] → 缺少[时间,地理] → AI 要求澄清
2. 用户："上海" → 累积=[指标:成本, 地理:上海] → 缺少[时间] → AI 确认"上海的成本"，要求时间
3. 用户："去年的" → 累积=[指标:成本, 地理:上海, 时间:去年] → **完整** → AI 执行查询

**关键 mergedContext 示例**：

```
"从历史累积的'上海的成本'和当前输入'去年的'合并为'上海去年的成本'"
```

## 🎯 **期望修复效果**

修复后，用户的原问题场景应该变成：

1. 用户："成本" → AI："需要时间和地区信息"
2. 用户："上海" → AI："明白了，您想查询上海的成本。请问希望查看哪个时间段？"
3. 用户："去年的" → AI：**直接执行查询**，返回"上海去年的成本"数据

**不再出现**：

- ❌ 地理信息丢失
- ❌ 时间信息丢失
- ❌ 无限循环的澄清请求

## 🧪 **测试验证**

建议使用以下测试案例验证修复效果：

**测试案例 1：基础累积**

1. "销售额" → AI 要求澄清
2. "北京" → AI 确认"北京的销售额"，要求时间
3. "本月" → AI 返回数据，不再循环

**测试案例 2：复杂累积**

1. "成本" → AI 要求澄清
2. "上海" → AI 确认"上海的成本"，要求时间
3. "去年" → AI 返回数据
4. "深圳" → AI 应该理解为新查询或对比查询

**测试案例 3：信息完整性**
每一轮的`mergedContext`字段都应该体现完整的累积过程，不应该出现信息丢失。

## 📝 **相关文件**

- **核心修改**：`chatbi-agent-core/src/main/resources/prompts/nlu_function_calling_agent.txt`
- **系统架构**：`chatbi-agent-core/src/main/java/com/qding/chatbi/agent/service/impl/ConversationManagerImpl.java`
- **测试建议**：通过前端测试界面进行多轮对话验证

## ⚠️ **注意事项**

1. **重启应用**：修改提示词后需要重启 ChatBI 应用
2. **监控日志**：观察`mergedContext`字段的输出，确认信息累积逻辑正确
3. **边界情况**：测试用户输入完全无关内容时的处理逻辑
