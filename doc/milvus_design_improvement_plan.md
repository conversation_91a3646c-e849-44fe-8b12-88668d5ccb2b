# Milvus 向量库设计优化方案

## 🎯 改进目标

1. 提高查询准确性和召回率
2. 优化索引性能
3. 增强数据一致性
4. 支持多维度检索策略

## 🔧 具体改进方案

### 方案 1：多层次向量化策略

#### 1.1 增强向量化内容

**当前策略**：

- 业务术语：`term + "\n" + description`
- 查询示例：仅`user_question`

**改进策略**：

```java
// 业务术语增强向量化
String termVector = String.format("%s\n%s\n同义词:%s\n语义类型:%s",
    term.getBusinessTerm(),
    term.getContextDescription(),
    term.getSynonyms(),           // 新增同义词
    term.getSemanticType());      // 新增语义类型

// 查询示例增强向量化
String exampleVector = String.format("%s\n难度:%s\n查询类型:%s\n关键指标:%s",
    example.getUserQuestion(),
    example.getDifficultyLevel(),
    extractQueryType(example),     // 提取查询类型（排名/趋势/对比等）
    extractMetrics(example));      // 提取核心指标
```

#### 1.2 多向量存储策略

```sql
-- 新的集合设计
CREATE COLLECTION enhanced_query_examples (
    id BIGINT PRIMARY KEY,
    question_vector FLOAT_VECTOR(1536),    -- 原始问题向量
    intent_vector FLOAT_VECTOR(1536),      -- 意图向量（从target_query_representation生成）
    context_vector FLOAT_VECTOR(1536),     -- 上下文向量（难度+类型+指标）
    difficulty_level VARCHAR(20),          -- 支持过滤
    query_type VARCHAR(50),                 -- 支持过滤
    dataset_id BIGINT                       -- 支持过滤
);
```

### 方案 2：高性能索引优化

#### 2.1 索引类型升级

**当前配置**：

```python
index_params = {
    "metric_type": "L2",
    "index_type": "IVF_FLAT",
    "params": {"nlist": 1024}
}
```

**优化配置**：

```python
# 方案A：高精度配置
index_params = {
    "metric_type": "COSINE",        # 余弦相似度更适合文本向量
    "index_type": "HNSW",           # 更高性能的图索引
    "params": {
        "M": 16,                    # 连接数
        "efConstruction": 200       # 构建参数
    }
}

# 方案B：均衡配置
index_params = {
    "metric_type": "COSINE",
    "index_type": "IVF_PQ",         # 乘积量化压缩
    "params": {
        "nlist": 2048,             # 增加聚类数
        "m": 8,                    # PQ参数
        "nbits": 8
    }
}
```

#### 2.2 查询参数优化

```java
// 当前查询参数
SearchParam searchParam = SearchParam.newBuilder()
    .withCollectionName(COLLECTION_NAME)
    .withVectorFieldName("vector")
    .withVectors(List.of(queryEmbedding.vectorAsList()))
    .withTopK(topK)
    .build();

// 优化查询参数
SearchParam enhancedSearchParam = SearchParam.newBuilder()
    .withCollectionName(COLLECTION_NAME)
    .withVectorFieldName("vector")
    .withVectors(List.of(queryEmbedding.vectorAsList()))
    .withTopK(topK * 2)                           // 检索更多候选
    .withSearchParams(Map.of(
        "ef", 200,                                // HNSW搜索参数
        "nprobe", 32                              // IVF搜索参数
    ))
    .withMetricType(MetricType.COSINE)
    .withExpr(buildFilterExpression(filters))     // 支持条件过滤
    .withOutFields(List.of("id", "difficulty_level", "query_type"))
    .build();
```

### 方案 3：智能查询策略

#### 3.1 多阶段检索策略

```java
public class EnhancedMilvusSearchService {

    public List<QueryExample> searchWithReranking(String queryText, int topK, SearchContext context) {
        // 阶段1：粗排 - 向量相似度搜索
        List<SearchCandidate> candidates = vectorSearch(queryText, topK * 3);

        // 阶段2：细排 - 多因子重排序
        List<SearchCandidate> reranked = rerank(candidates, context);

        // 阶段3：后处理 - 多样性和相关性平衡
        return postProcess(reranked, topK);
    }

    private List<SearchCandidate> rerank(List<SearchCandidate> candidates, SearchContext context) {
        return candidates.stream()
            .map(candidate -> {
                float finalScore = calculateEnhancedScore(candidate, context);
                candidate.setFinalScore(finalScore);
                return candidate;
            })
            .sorted((a, b) -> Float.compare(b.getFinalScore(), a.getFinalScore()))
            .collect(Collectors.toList());
    }

    private float calculateEnhancedScore(SearchCandidate candidate, SearchContext context) {
        float vectorScore = candidate.getVectorSimilarity();     // 向量相似度
        float difficultyScore = calculateDifficultyMatch(candidate, context);  // 难度匹配
        float typeScore = calculateTypeMatch(candidate, context);              // 查询类型匹配
        float datasetScore = calculateDatasetRelevance(candidate, context);    // 数据集相关性

        // 加权计算最终分数
        return 0.5f * vectorScore + 0.2f * difficultyScore + 0.2f * typeScore + 0.1f * datasetScore;
    }
}
```

#### 3.2 上下文感知搜索

```java
public class ContextAwareSearch {

    public List<QueryExample> searchWithContext(String userQuery, ConversationContext context) {
        // 构建增强查询文本
        String enhancedQuery = buildEnhancedQuery(userQuery, context);

        // 根据上下文构建过滤条件
        String filterExpr = buildContextualFilter(context);

        // 动态调整搜索参数
        SearchParams params = adaptSearchParams(context);

        return search(enhancedQuery, filterExpr, params);
    }

    private String buildEnhancedQuery(String userQuery, ConversationContext context) {
        StringBuilder enhanced = new StringBuilder(userQuery);

        // 添加对话历史信息
        if (context.hasRecentMetrics()) {
            enhanced.append(" 涉及指标:").append(String.join(",", context.getRecentMetrics()));
        }

        // 添加数据集上下文
        if (context.getCurrentDataset() != null) {
            enhanced.append(" 数据集:").append(context.getCurrentDataset().getName());
        }

        return enhanced.toString();
    }
}
```

### 方案 4：数据一致性保障

#### 4.1 强一致性同步机制

```java
@Service
@Transactional
public class ConsistentDataSyncService {

    public void syncWithConsistencyCheck() {
        // 1. 获取MySQL数据快照
        List<QueryExample> mysqlData = getAllMySQLData();

        // 2. 获取Milvus数据快照
        Set<Long> milvusIds = getAllMilvusIds();

        // 3. 计算差异
        SyncPlan plan = calculateSyncPlan(mysqlData, milvusIds);

        // 4. 执行同步
        executeSyncPlan(plan);

        // 5. 验证同步结果
        validateSyncResult();
    }

    private void executeSyncPlan(SyncPlan plan) {
        // 删除多余数据
        for (Long id : plan.getIdsToDelete()) {
            milvusClient.delete(DeleteParam.newBuilder()
                .withCollectionName(COLLECTION_NAME)
                .withExpr("id == " + id)
                .build());
        }

        // 添加缺失数据
        for (QueryExample example : plan.getDataToInsert()) {
            saveOrUpdateExample(example);
        }

        // 更新不一致数据
        for (QueryExample example : plan.getDataToUpdate()) {
            saveOrUpdateExample(example);
        }
    }
}
```

#### 4.2 实时一致性监控

```java
@Component
@Scheduled(fixedRate = 300000) // 5分钟检查一次
public class DataConsistencyMonitor {

    public void checkConsistency() {
        ConsistencyReport report = generateConsistencyReport();

        if (!report.isConsistent()) {
            log.warn("数据不一致检测到: {}", report);

            // 发送告警
            alertService.sendAlert("Milvus数据不一致", report.getSummary());

            // 自动修复（可选）
            if (autoFixEnabled) {
                syncService.autoFix(report);
            }
        }
    }
}
```

### 方案 5：查询性能优化

#### 5.1 缓存策略

```java
@Service
public class CachedSearchService {

    @Cacheable(value = "milvus-search", key = "#queryText + '_' + #topK")
    public List<QueryExample> search(String queryText, int topK) {
        return milvusSearchService.search(queryText, topK);
    }

    @CacheEvict(value = "milvus-search", allEntries = true)
    public void evictCache() {
        log.info("清除Milvus搜索缓存");
    }
}
```

#### 5.2 批量查询优化

```java
public class BatchSearchService {

    public Map<String, List<QueryExample>> batchSearch(List<String> queries, int topK) {
        // 并行向量化
        List<Embedding> embeddings = queries.parallelStream()
            .map(embeddingModel::embed)
            .map(Embedding::content)
            .collect(Collectors.toList());

        // 批量搜索
        SearchParam batchSearchParam = SearchParam.newBuilder()
            .withCollectionName(COLLECTION_NAME)
            .withVectorFieldName("vector")
            .withVectors(embeddings.stream()
                .map(Embedding::vectorAsList)
                .collect(Collectors.toList()))
            .withTopK(topK)
            .build();

        R<SearchResults> response = milvusClient.search(batchSearchParam);

        // 解析批量结果
        return parseBatchResults(queries, response);
    }
}
```

## 📈 预期改进效果

1. **查询精度提升**：30-50% (通过多向量和重排序)
2. **查询速度提升**：20-40% (通过索引优化和缓存)
3. **数据一致性**：99.9% (通过强一致性保障)
4. **系统稳定性**：显著提升 (通过监控和自动修复)

## 🛠 实施路径

### 阶段 1：基础优化（1-2 周）

1. 索引配置优化
2. 查询参数调优
3. 数据一致性修复

### 阶段 2：策略增强（2-3 周）

1. 多向量存储实施
2. 重排序算法实现
3. 上下文感知搜索

### 阶段 3：性能优化（1-2 周）

1. 缓存机制实现
2. 批量查询优化
3. 监控体系完善

## 📊 评估指标

1. **准确性指标**：

   - Top-1 准确率
   - Top-5 召回率
   - MRR (Mean Reciprocal Rank)

2. **性能指标**：

   - 平均查询延迟
   - P99 查询延迟
   - QPS 支持能力

3. **一致性指标**：
   - 数据同步准确率
   - 不一致检测时间
   - 自动修复成功率
