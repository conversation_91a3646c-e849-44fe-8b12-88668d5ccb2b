# 权限中心功能测试指南

## 概述

权限中心模块包含两个主要功能：
1. **角色管理** - 创建、编辑、删除和搜索角色
2. **权限分配** - 为角色分配数据集访问权限

## 前置条件

1. **后端服务**：确保后端服务运行在 `http://localhost:8081`
2. **前端服务**：确保前端服务运行在 `http://localhost:3004`
3. **数据库**：确保MySQL数据库已创建相关表结构
4. **登录凭据**：`superadmin` / `111111`

## 测试步骤

### 1. 登录系统

1. 访问 `http://localhost:3004`
2. 使用凭据登录：
   - 用户名：`superadmin`
   - 密码：`111111`
3. 验证登录成功，进入管理后台

### 2. 角色管理功能测试

#### 2.1 访问角色管理页面
1. 在左侧菜单中点击 **权限中心** → **角色管理**
2. 验证页面正常加载，显示角色列表

#### 2.2 查看现有角色
验证页面显示以下预设角色：
- 全国销售总监
- 华北区销售经理  
- 财务分析师

#### 2.3 搜索功能测试
1. 在搜索框中输入"销售"
2. 点击搜索或按回车
3. 验证只显示包含"销售"的角色
4. 清空搜索框，验证显示所有角色

#### 2.4 新建角色测试
1. 点击 **新建角色** 按钮
2. 填写角色信息：
   - 角色名称：`数据分析师`
   - 描述：`负责全公司数据分析工作，可查看大部分数据集`
3. 点击确定
4. 验证角色创建成功，列表中显示新角色

#### 2.5 编辑角色测试
1. 点击某个角色的 **编辑** 链接
2. 修改角色描述
3. 点击确定
4. 验证修改成功

#### 2.6 删除角色测试
1. 点击某个角色的 **删除** 链接
2. 在确认对话框中点击确定
3. 验证角色删除成功

### 3. 权限分配功能测试

#### 3.1 访问权限分配页面
1. 在左侧菜单中点击 **权限中心** → **权限分配**
2. 验证页面正常加载，显示权限矩阵

#### 3.2 查看权限矩阵
验证页面显示：
- 左侧列：数据集列表（每日销售汇总、公司月度财务报表等）
- 顶部行：角色列表（全国销售总监、华北区销售经理、财务分析师等）
- 矩阵中的复选框表示权限关系

#### 3.3 权限分配测试
1. 勾选某个角色对某个数据集的访问权限
2. 验证 **保存更改** 按钮变为可用状态
3. 点击 **保存更改** 按钮
4. 验证保存成功提示
5. 刷新页面，验证权限设置已保存

#### 3.4 批量权限设置测试
1. 为一个角色勾选多个数据集的权限
2. 为多个角色设置不同的权限组合
3. 点击保存
4. 验证所有权限设置都已正确保存

## API测试（可选）

### 使用curl命令测试后端API

#### 1. 获取JWT Token
```bash
curl -X POST http://localhost:8081/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"superadmin","password":"111111"}'
```

#### 2. 测试角色列表API
```bash
curl -X GET "http://localhost:8081/api/admin/permissions/roles?page=0&size=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 3. 测试权限分配API
```bash
curl -X GET "http://localhost:8081/api/admin/permissions/assignments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 4. 测试创建角色API
```bash
curl -X POST http://localhost:8081/api/admin/permissions/roles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"roleName":"测试角色","description":"这是一个测试角色"}'
```

## 预期结果

### 角色管理
- ✅ 能够查看角色列表
- ✅ 能够搜索角色
- ✅ 能够创建新角色
- ✅ 能够编辑角色信息
- ✅ 能够删除角色
- ✅ 分页功能正常

### 权限分配
- ✅ 能够查看权限矩阵
- ✅ 能够设置角色权限
- ✅ 能够保存权限更改
- ✅ 权限设置持久化
- ✅ 界面响应正常

## 故障排除

### 1. CORS错误
如果遇到CORS错误，检查：
- 后端服务是否正常运行
- API路径是否正确（应包含 `/api/admin` 前缀）
- 前端请求URL是否正确

### 2. 认证错误
如果遇到401错误：
- 检查JWT token是否有效
- 检查登录状态
- 重新登录获取新token

### 3. 权限错误
如果遇到403错误：
- 检查用户角色是否为SUPER_ADMIN
- 检查API端点的权限配置

### 4. 数据加载问题
如果数据不显示：
- 检查数据库连接
- 检查数据库中是否有测试数据
- 查看浏览器控制台错误信息

## 数据库验证

可以通过以下SQL查询验证数据：

```sql
-- 查看角色数据
SELECT * FROM roles;

-- 查看数据集数据  
SELECT * FROM queryable_datasets;

-- 查看权限分配数据
SELECT r.role_name, d.dataset_name 
FROM dataset_access_permissions p
JOIN roles r ON p.role_id = r.id
JOIN queryable_datasets d ON p.dataset_id = d.id;
```

## 总结

权限中心模块提供了完整的角色管理和权限分配功能，支持：
- 灵活的角色定义
- 直观的权限矩阵管理
- 完整的CRUD操作
- 搜索和分页功能
- 实时权限更新

通过以上测试步骤，可以全面验证权限中心模块的功能完整性和稳定性。 