# **Chat BI 数据库表结构设计 (MySQL)**

## **1\. 引言**

本文档详细定义了 Chat BI 项目元数据与配置存储所需的所有数据库表结构。这些表构成了系统的核心配置，支撑着数据源管理、逻辑数据模型、权限控制、知识库以及对话历史等关键功能。所有表设计均基于 MySQL 5.7+，并建议使用 utf8mb4 字符集以支持各类字符。

### **1.1 数据库定义**

本项目所使用的元数据及配置存储数据库名为 chatbi_metadata。

CREATE DATABASE IF NOT EXISTS \`chatbi_metadata\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;  
USE \`chatbi_metadata\`;

## **2\. 表结构定义**

以下是本项目涉及的所有核心表的详细设计和 DDL 语句。

### **2.1 数据库源配置表 (database_source_configs)**

- **用途**: 统一管理所有可连接的外部数据库源（如数据仓库、业务数据库）的配置信息。

#### **字段说明**

| 字段名                | 数据类型     | 约束                         | 注释                                                                |
| :-------------------- | :----------- | :--------------------------- | :------------------------------------------------------------------ |
| id                    | BIGINT       | 主键, 自增                   | 唯一标识 ID                                                         |
| source_name           | VARCHAR(255) | 非空, 唯一                   | 数据库源的业务名称，如 "生产环境 MySQL 集群"                        |
| database_type         | VARCHAR(50)  | 非空                         | 数据库类型，如 "MYSQL", "POSTGRESQL", "HIVE"                        |
| connection_parameters | JSON         | 非空                         | 连接数据库所需的全部参数（JSON 格式），敏感信息应加密或引用外部密钥 |
| description           | TEXT         | 可空                         | 关于此数据源的描述                                                  |
| status                | VARCHAR(50)  | 非空, 默认 'active'          | 数据源当前状态 ('active', 'inactive', 'maintenance')                |
| created_at            | TIMESTAMP    | 非空, 默认当前时间           | 记录创建时间                                                        |
| updated_at            | TIMESTAMP    | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                                                    |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`database_source_configs\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`source_name\` VARCHAR(255) NOT NULL COMMENT '数据库源的业务名称',  
 \`database_type\` VARCHAR(50) NOT NULL COMMENT '数据库类型 (MYSQL, HIVE, etc.)',  
 \`connection_parameters\` JSON NOT NULL COMMENT '连接数据库所需的全部参数 (JSON 格式)',  
 \`description\` TEXT COMMENT '关于此数据源的描述',  
 \`status\` VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT '数据源状态 (active, inactive, maintenance)',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`),  
 UNIQUE KEY \`uk_source_name\` (\`source_name\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库源配置表';

### **2.2 可查询数据集表 (queryable_datasets)**

- **用途**: 定义用户可以通过自然语言直接查询的"数据集"。每个数据集代表了一个业务主题或一个已经处理了内部关联的数据视图。

#### **字段说明**

| 字段名               | 数据类型     | 约束                         | 注释                                            |
| :------------------- | :----------- | :--------------------------- | :---------------------------------------------- |
| id                   | BIGINT       | 主键, 自增                   | 唯一标识 ID                                     |
| dataset_name         | VARCHAR(255) | 非空, 唯一                   | 数据集的业务名称，如 "每日产品销售汇总"         |
| description          | TEXT         | 可空                         | 数据集的详细业务描述                            |
| database_source_id   | BIGINT       | 非空                         | 外键，关联 database_source_configs.id           |
| base_object_name     | VARCHAR(255) | 非空                         | 此数据集在数据库中的基础对象名称 (表名/视图名)  |
| base_object_type     | VARCHAR(50)  | 非空                         | 基础对象的类型 ('TABLE', 'VIEW', 'QUERY')       |
| technical_definition | TEXT         | 可空                         | 当类型为 QUERY 时，存储定义数据集的 SQL         |
| synonyms             | JSON         | 可空                         | 数据集的别名或常用称呼 (JSON 数组)              |
| refresh_frequency    | VARCHAR(100) | 可空                         | 数据更新频率描述，如 "每日", "实时"             |
| data_owner           | VARCHAR(100) | 可空                         | 数据责任人或团队                                |
| status               | VARCHAR(50)  | 非空, 默认 'active'          | 数据集状态 ('active', 'inactive', 'deprecated') |
| created_at           | TIMESTAMP    | 非空, 默认当前时间           | 记录创建时间                                    |
| updated_at           | TIMESTAMP    | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                                |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`queryable_datasets\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`dataset_name\` VARCHAR(255) NOT NULL COMMENT '数据集的业务名称',  
 \`description\` TEXT COMMENT '数据集的详细业务描述',  
 \`database_source_id\` BIGINT NOT NULL COMMENT '外键, 关联 database_source_configs.id',  
 \`base_object_name\` VARCHAR(255) NOT NULL COMMENT '此数据集在数据库中的基础对象名称 (表名/视图名)',  
 \`base_object_type\` VARCHAR(50) NOT NULL COMMENT '基础对象的类型 (TABLE, VIEW, QUERY)',  
 \`technical_definition\` TEXT COMMENT '当类型为 QUERY 时, 存储定义数据集的 SQL',  
 \`synonyms\` JSON COMMENT '数据集的别名或常用称呼 (JSON 数组)',  
 \`refresh_frequency\` VARCHAR(100) COMMENT '数据更新频率描述',  
 \`data_owner\` VARCHAR(100) COMMENT '数据责任人或团队',  
 \`status\` VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT '数据集状态 (active, inactive, deprecated)',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`),  
 UNIQUE KEY \`uk_dataset_name\` (\`dataset_name\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='可查询数据集表';

### **2.3 数据集字段表 (dataset_columns)**

- **用途**: 定义每个"可查询数据集"中可供用户查询的具体字段，包括其业务含义、技术定义和语义类型。

#### **字段说明**

| 字段名                       | 数据类型      | 约束                         | 注释                                                                |
| :--------------------------- | :------------ | :--------------------------- | :------------------------------------------------------------------ |
| id                           | BIGINT        | 主键, 自增                   | 唯一标识 ID                                                         |
| dataset_id                   | BIGINT        | 非空, 索引                   | 外键，关联 queryable_datasets.id                                    |
| column_name                  | VARCHAR(255)  | 非空                         | 字段的业务名称，如 "总销售额"                                       |
| description                  | TEXT          | 可空                         | 字段的详细描述及计算口径                                            |
| technical_name_or_expression | VARCHAR(1024) | 非空                         | 物理列名或计算表达式                                                |
| semantic_type                | VARCHAR(50)   | 非空                         | 语义类型 ('METRIC', 'DIMENSION', 'TIME_DIMENSION'，'GEO_DIMENSION') |
| data_type                    | VARCHAR(50)   | 非空                         | 呈现给用户的数据类型                                                |
| allowed_aggregations         | JSON          | 可空                         | 对于指标，允许的聚合函数列表 (JSON 数组)                            |
| synonyms                     | JSON          | 可空                         | 字段的别名 (JSON 数组)                                              |
| is_filterable                | BOOLEAN       | 非空, 默认 true              | 是否可用于查询的过滤条件                                            |
| is_groupable                 | BOOLEAN       | 非空, 默认 true              | 是否可用于查询的分组依据                                            |
| is_default_metric            | BOOLEAN       | 非空, 默认 false             | 是否为该数据集的默认度量                                            |
| possible_values_query        | TEXT          | 可空                         | 获取该维度可选值的查询语句                                          |
| formatting_hint              | JSON          | 可空                         | 如何展示此字段值的建议                                              |
| status                       | VARCHAR(50)   | 非空, 默认 'active'          | 字段状态 ('active', 'inactive')                                     |
| created_at                   | TIMESTAMP     | 非空, 默认当前时间           | 记录创建时间                                                        |
| updated_at                   | TIMESTAMP     | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                                                    |
| is_computed                  | bit(1)        | 非空, 默认 0                 | 是否为计算字段                                                      |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`dataset_columns\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`dataset_id\` BIGINT NOT NULL COMMENT '外键, 关联 queryable_datasets.id',  
 \`column_name\` VARCHAR(255) NOT NULL COMMENT '字段的业务名称',  
 \`description\` TEXT COMMENT '字段的详细描述及计算口径',  
 \`technical_name_or_expression\` VARCHAR(1024) NOT NULL COMMENT '物理列名或计算表达式',  
 \`semantic_type\` VARCHAR(50) NOT NULL COMMENT '语义类型 (METRIC, DIMENSION, TIME_DIMENSION, GEO_DIMENSION)',  
 \`data_type\` VARCHAR(50) NOT NULL COMMENT '呈现给用户的数据类型',  
 \`allowed_aggregations\` JSON COMMENT '对于指标, 允许的聚合函数列表 (JSON 数组)',  
 \`synonyms\` JSON COMMENT '字段的别名，同义词，简称 (JSON 数组)',  
 \`is_filterable\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否可用于查询的过滤条件',  
 \`is_groupable\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否可用于查询的分组依据',  
 \`is_default_metric\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为该数据集的默认度量',  
 \`possible_values_query\` TEXT COMMENT '获取该维度可选值的查询语句',  
 \`formatting_hint\` JSON COMMENT '如何展示此字段值的建议',  
 \`status\` VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT '字段状态 (active, inactive)',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
\`is_computed\` bit(1) NOT NULL DEFAULT 0 COMMENT '是否为计算字段',
PRIMARY KEY (\`id\`),  
 INDEX \`idx_dataset_id\` (\`dataset_id\`),  
 UNIQUE KEY \`uk_dataset_id_column_name\` (\`dataset_id\`, \`column_name\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据集字段表';

### **2.4 角色表 (roles)**

- **用途**: 定义系统中的用户角色，用于权限控制。

#### **字段说明**

| 字段名      | 数据类型     | 约束                         | 注释                        |
| :---------- | :----------- | :--------------------------- | :-------------------------- |
| id          | BIGINT       | 主键, 自增                   | 唯一标识 ID                 |
| role_name   | VARCHAR(100) | 非空, 唯一                   | 角色名称，如 "SalesManager" |
| description | TEXT         | 可空                         | 角色的描述                  |
| created_at  | TIMESTAMP    | 非空, 默认当前时间           | 记录创建时间                |
| updated_at  | TIMESTAMP    | 非空, 默认当前时间, 自动更新 | 记录最后更新时间            |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`roles\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`role_name\` VARCHAR(100) NOT NULL COMMENT '角色名称',  
 \`description\` TEXT COMMENT '角色的描述',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`),  
 UNIQUE KEY \`uk_role_name\` (\`role_name\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

### **2.5 数据集访问权限表 (dataset_access_permissions)**

- **用途**: 定义哪个角色可以访问哪个"可查询数据集"。

#### **字段说明**

| 字段名     | 数据类型  | 约束                         | 注释                             |
| :--------- | :-------- | :--------------------------- | :------------------------------- |
| id         | BIGINT    | 主键, 自增                   | 唯一标识 ID                      |
| role_id    | BIGINT    | 非空                         | 外键，关联 roles.id              |
| dataset_id | BIGINT    | 非空                         | 外键，关联 queryable_datasets.id |
| created_at | TIMESTAMP | 非空, 默认当前时间           | 记录创建时间                     |
| updated_at | TIMESTAMP | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                 |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`dataset_access_permissions\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`role_id\` BIGINT NOT NULL COMMENT '外键, 关联 roles.id',  
 \`dataset_id\` BIGINT NOT NULL COMMENT '外键, 关联 queryable_datasets.id',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`),  
 UNIQUE KEY \`uk_role_dataset\` (\`role_id\`, \`dataset_id\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据集访问权限表';

### **2.6 业务述词对照表 (business_terminology)**

- **用途**: 存储企业内部的专业术语、缩写、别名（"黑话"）及其与标准数据概念的对应关系。

#### **字段说明**

| 字段名                  | 数据类型     | 约束                         | 注释                                    |
| :---------------------- | :----------- | :--------------------------- | :-------------------------------------- |
| id                      | BIGINT       | 主键, 自增                   | 唯一标识 ID                             |
| business_term           | VARCHAR(255) | 非空, 唯一                   | 业务术语/黑话，如 "GMV"                 |
| standard_reference_type | VARCHAR(50)  | 非空                         | 映射到的标准参考类型                    |
| standard_reference_id   | VARCHAR(255) | 可空                         | 映射到的标准参考对象的 ID               |
| standard_reference_name | VARCHAR(255) | 可空                         | 映射到的标准参考对象的名称 (用于可读性) |
| context_description     | TEXT         | 可空                         | 此术语使用的上下文或补充说明            |
| status                  | VARCHAR(50)  | 非空, 默认 'active'          | 术语状态 ('active', 'pending_review')   |
| created_at              | TIMESTAMP    | 非空, 默认当前时间           | 记录创建时间                            |
| updated_at              | TIMESTAMP    | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                        |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`business_terminology\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`business_term\` VARCHAR(255) NOT NULL COMMENT '业务术语/黑话',  
 \`standard_reference_type\` VARCHAR(50) NOT NULL COMMENT '映射到的标准参考类型 (DatasetColumn, Custom)',  
 \`standard_reference_id\` VARCHAR(255) COMMENT '映射到的标准参考对象的 ID',  
 \`standard_reference_name\` VARCHAR(255) COMMENT '映射到的标准参考对象的名称 (用于可读性)',  
 \`context_description\` TEXT COMMENT '此术语使用的上下文或补充说明',  
 \`status\` VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT '术语状态 (active, pending_review)',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`),  
 UNIQUE KEY \`uk_business_term\` (\`business_term\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务述词对照表';

### **2.7 业务查询示例表 (query_examples)**

- **用途**: 存储高质量的"用户自然语言问题"到"结构化查询意图"或"目标 SQL"的映射示例，用于 AI 模型训练和 RAG。

#### **字段说明**

| 字段名                      | 数据类型    | 约束                         | 注释                                                                                 |
| :-------------------------- | :---------- | :--------------------------- | :----------------------------------------------------------------------------------- |
| id                          | BIGINT      | 主键, 自增                   | 唯一标识 ID                                                                          |
| user_question               | TEXT        | 非空                         | 用户提出的自然语言问题                                                               |
| target_query_representation | JSON        | 非空                         | 对应的结构化查询意图或最终的 SQL 语句                                                |
| target_dataset_id           | BIGINT      | 可空                         | 此查询主要关联的数据集 ID                                                            |
| involved_column_ids         | JSON        | 可空                         | 查询涉及的字段 ID 列表 (JSON 数组)                                                   |
| notes                       | TEXT        | 可空                         | 关于此示例的说明、难点或应用场景，以及当前查询是否完整有没有缺少什么，为空表示已完整 |
| difficulty_level            | VARCHAR(50) | 可空                         | 示例难度 ('Easy', 'Medium', 'Hard')                                                  |
| status                      | VARCHAR(50) | 非空, 默认 'verified'        | 示例状态 ('verified', 'needs_review')，说明这条示例是否可用                          |
| created_at                  | TIMESTAMP   | 非空, 默认当前时间           | 记录创建时间                                                                         |
| updated_at                  | TIMESTAMP   | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                                                                     |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`query_examples\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 \`user_question\` TEXT NOT NULL COMMENT '用户提出的自然语言问题',  
 \`target_query_representation\` JSON NOT NULL COMMENT '对应的结构化查询意图或最终的 SQL 语句',  
 \`target_dataset_id\` BIGINT COMMENT '此查询主要关联的数据集 ID',  
 \`involved_column_ids\` JSON COMMENT '查询涉及的字段 ID 列表 (JSON 数组)',  
 \`notes\` TEXT COMMENT '关于此示例的说明、难点或应用场景',  
 \`difficulty_level\` VARCHAR(50) COMMENT '示例难度 (Easy, Medium, Hard)',  
 \`status\` VARCHAR(50) NOT NULL DEFAULT 'verified' COMMENT '示例状态 (verified, needs_review)',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务查询示例表';

### **2.8 聊天消息历史表 (chat_messages)**

- **用途**: 存储用户与 AI Agent 之间的每一条交互消息，用于审计、问题排查、AI 性能分析等。

#### **字段说明**

| 字段名                   | 数据类型     | 约束                         | 注释                                |
| :----------------------- | :----------- | :--------------------------- | :---------------------------------- |
| id                       | BIGINT       | 主键, 自增                   | 消息唯一标识                        |
| session_id               | VARCHAR(255) | 非空, 索引                   | 标识该消息所属的会话                |
| user_id                  | VARCHAR(255) | 可空, 索引                   | 发起或参与此次对话的用户标识        |
| sender_type              | VARCHAR(50)  | 非空                         | 消息发送者类型 ('USER', 'AI_AGENT') |
| message_text             | TEXT         | 非空                         | 消息文本内容                        |
| message_timestamp        | TIMESTAMP    | 非空, 默认当前时间, 索引     | 消息创建或接收的时间戳              |
| response_type            | VARCHAR(50)  | 可空                         | AI 回复类型                         |
| structured_response_data | JSON         | 可空                         | AI 回复的结构化数据                 |
| error_code               | VARCHAR(50)  | 可空                         | 错误码                              |
| error_message_detail     | TEXT         | 可空                         | 详细错误信息                        |
| processing_duration_ms   | INT          | 可空                         | AI 处理耗时(毫秒)                   |
| llm_model_used           | VARCHAR(100) | 可空                         | 调用的 LLM 模型名称                 |
| created_at               | TIMESTAMP    | 非空, 默认当前时间           | 记录创建时间                        |
| updated_at               | TIMESTAMP    | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                    |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS \`chat_messages\` (  
 \`id\` BIGINT NOT NULL AUTO_INCREMENT COMMENT '消息唯一标识',  
 \`session_id\` VARCHAR(255) NOT NULL COMMENT '会话 ID',  
 \`user_id\` VARCHAR(255) COMMENT '用户 ID',  
 \`sender_type\` VARCHAR(50) NOT NULL COMMENT '消息发送者类型 (USER, AI_AGENT)',  
 \`message_text\` TEXT NOT NULL COMMENT '消息文本内容',  
 \`message_timestamp\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间戳',  
 \`response_type\` VARCHAR(50) COMMENT 'AI 回复类型 (DATA_RESULT, CLARIFICATION_NEEDED, ERROR 等)',  
 \`structured_response_data\` JSON COMMENT 'AI 回复的结构化数据 (如查询结果, 澄清选项)',  
 \`error_code\` VARCHAR(50) COMMENT '错误码 (如果 AI 回复是错误类型)',  
 \`error_message_detail\` TEXT COMMENT '详细错误信息',  
 \`processing_duration_ms\` INT COMMENT 'AI 处理耗时(毫秒)',  
 \`llm_model_used\` VARCHAR(100) COMMENT '调用的 LLM 模型名称',  
 \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (\`id\`),  
 INDEX \`idx_session_id\` (\`session_id\`),  
 INDEX \`idx_user_id\` (\`user_id\`),  
 INDEX \`idx_message_timestamp\` (\`message_timestamp\`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息历史表';

### **2.9 管理员用户表 (admin_users)**

- **用途**: 存储系统管理员用户信息，用于系统管理和维护。

#### **字段说明**

| 字段名        | 数据类型     | 约束                         | 注释                                      |
| :------------ | :----------- | :--------------------------- | :---------------------------------------- |
| id            | BIGINT       | 主键, 自增                   | 唯一标识 ID                               |
| username      | VARCHAR(100) | 非空, 唯一                   | 管理员用户名                              |
| password_hash | VARCHAR(255) | 非空                         | 密码哈希值                                |
| email         | VARCHAR(255) | 非空, 唯一                   | 管理员邮箱                                |
| phone_number  | VARCHAR(20)  | 可空, 唯一                   | 管理员手机号                              |
| full_name     | VARCHAR(100) | 可空                         | 管理员全名                                |
| role          | VARCHAR(50)  | 非空, 默认 'admin'           | 管理员角色 ('admin', 'super_admin')       |
| last_login_at | TIMESTAMP    | 可空                         | 最后登录时间                              |
| status        | VARCHAR(50)  | 非空, 默认 'active'          | 账号状态 ('active', 'inactive', 'locked') |
| created_at    | TIMESTAMP    | 非空, 默认当前时间           | 记录创建时间                              |
| updated_at    | TIMESTAMP    | 非空, 默认当前时间, 自动更新 | 记录最后更新时间                          |

#### **DDL 定义**

CREATE TABLE IF NOT EXISTS `admin_users` (  
 `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',  
 `username` VARCHAR(100) NOT NULL COMMENT '管理员用户名',  
 `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希值',  
 `email` VARCHAR(255) NOT NULL COMMENT '管理员邮箱',  
 `phone_number` VARCHAR(20) COMMENT '管理员手机号',  
 `full_name` VARCHAR(100) COMMENT '管理员全名',  
 `role` VARCHAR(50) NOT NULL DEFAULT 'admin' COMMENT '管理员角色 (admin, super_admin)',  
 `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',  
 `status` VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT '账号状态 (active, inactive, locked)',  
 `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',  
 `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',  
 PRIMARY KEY (`id`),  
 UNIQUE KEY `uk_username` (`username`),  
 UNIQUE KEY `uk_email` (`email`),
UNIQUE KEY `uk_phone_number` (`phone_number`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';
