# -*- coding: utf-8 -*-
"""
ChatBI Milvus Initialization Script

This script connects to a Milvus instance and sets up the necessary collections
for the ChatBI knowledge base, based on the project's database schema design.

It creates two collections:
1.  business_terms: To store embeddings for business terminology.
2.  query_examples: To store embeddings for user questions from query examples.
"""

from pymilvus import (
    connections,
    utility,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType,
)

# --- Milvus Connection Configuration ---
# These should match your docker-compose.yml setup
_HOST = "127.0.0.1"
_PORT = "19530"

# --- Collection Schemas Configuration ---
# 1. Business Terminology Collection
TERM_COLLECTION_NAME = "business_terms"
# This dimension must match the output dimension of your embedding model.
# As per application.yml, <PERSON><PERSON>'s embedding model has a dimension of 1024.
TERM_VECTOR_DIMENSION = 1024

# 2. Query Examples Collection
EXAMPLE_COLLECTION_NAME = "query_examples"
EXAMPLE_VECTOR_DIMENSION = 1024

def create_milvus_collection(
    collection_name, pk_field_name, vector_field_name, vector_dimension
):
    """
    Creates a new collection in Milvus with a specified schema.

    Args:
        collection_name (str): The name of the collection to create.
        pk_field_name (str): The name of the primary key field.
        vector_field_name (str): The name of the vector field.
        vector_dimension (int): The dimension of the vector field.
    """
    if utility.has_collection(collection_name):
        print(f"集合 '{collection_name}' 已存在，跳过创建。")
        return Collection(collection_name)

    print(f"集合 '{collection_name}' 不存在，开始创建...")

    # Define fields
    pk_field = FieldSchema(
        name=pk_field_name,
        dtype=DataType.INT64,
        is_primary=True,
        auto_id=False,  # We will manually insert IDs from MySQL
    )
    vector_field = FieldSchema(
        name=vector_field_name,
        dtype=DataType.FLOAT_VECTOR,
        dim=vector_dimension,
    )

    # Create schema
    schema = CollectionSchema(
        fields=[pk_field, vector_field],
        description=f"ChatBI collection for {collection_name}",
        enable_dynamic_field=False,
    )

    # Create collection
    collection = Collection(name=collection_name, schema=schema)
    print(f"集合 '{collection_name}' 创建成功。")

    # Create index for the vector field for efficient searching
    print(f"为 '{collection_name}' 的向量字段 '{vector_field_name}' 创建索引...")
    index_params = {
        "metric_type": "L2",  # L2 (Euclidean distance) is a common choice
        "index_type": "IVF_FLAT",
        "params": {"nlist": 1024},  # nlist is a trade-off between accuracy and speed
    }
    collection.create_index(field_name=vector_field_name, index_params=index_params)
    print(f"索引创建成功。")

    return collection

def main():
    """
    Main function to initialize all necessary Milvus collections for ChatBI.
    """
    print(f"正在连接 Milvus (Host: {_HOST}, Port: {_PORT})...")
    try:
        connections.connect("default", host=_HOST, port=_PORT)
        print("Milvus 连接成功。")

        # Initialize Business Terminology Collection
        term_collection = create_milvus_collection(
            collection_name=TERM_COLLECTION_NAME,
            pk_field_name="id",
            vector_field_name="vector",
            vector_dimension=TERM_VECTOR_DIMENSION,
        )
        # Load collection into memory for searching
        term_collection.load()
        print(f"集合 '{TERM_COLLECTION_NAME}' 已加载到内存。")

        # Initialize Query Examples Collection
        example_collection = create_milvus_collection(
            collection_name=EXAMPLE_COLLECTION_NAME,
            pk_field_name="id",
            vector_field_name="vector",
            vector_dimension=EXAMPLE_VECTOR_DIMENSION,
        )
        # Load collection into memory for searching
        example_collection.load()
        print(f"集合 '{EXAMPLE_COLLECTION_NAME}' 已加载到内存。")

        print("\nMilvus 初始化成功完成！")

    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        connections.disconnect("default")
        print("已断开与 Milvus 的连接。")


if __name__ == "__main__":
    main()
