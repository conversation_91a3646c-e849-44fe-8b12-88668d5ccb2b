# ChatBI 向量库重建说明文档

## 📋 概述

本文档说明如何使用 text-embedding-v4 模型重新生成 ChatBI 系统的向量库数据。

## 🎯 重建目标

- **删除现有向量数据**：清理 `business_terms` 和 `query_examples` 集合中的所有向量
- **重新生成向量**：使用最新的 `text-embedding-v4` 模型重新生成所有向量
- **应用优化策略**：使用上下文丰富化向量策略提升搜索精度

## 🔧 使用方法

### 方式一：一键运行（推荐）

```bash
./run_vector_rebuild.sh
```

该脚本会自动：

- 检查 Python 环境和依赖
- 检查 MySQL 和 Milvus 服务状态
- 确认用户操作意图
- 执行完整的重建流程

### 方式二：直接运行 Python 脚本

```bash
python3 rebuild_vectors_with_text_embedding_v4.py
```

## 📋 前置条件

### 1. 服务环境

- **MySQL 服务**：确保 `chatbi_metadata` 数据库可访问
- **Milvus 服务**：确保端口 19530 可达
- **Dashscope API**：确保 API 密钥有效

### 2. Python 环境

- **Python 3.7+**
- **依赖包**：
  - `pymilvus`
  - `mysql-connector-python`
  - `requests`

运行脚本会自动检查并安装缺失的依赖包。

### 3. 权限要求

- MySQL root 用户访问权限
- Milvus 数据库操作权限

## 🔄 重建流程

### 步骤 1: 配置数据库连接

- 输入 MySQL root 密码
- 测试数据库连接

### 步骤 2: 测试服务连接

- 验证 MySQL 连接状态
- 验证 Milvus 连接状态
- 验证 Dashscope API 访问

### 步骤 3: 清理旧向量数据

- 清理 `business_terms` 集合
- 清理 `query_examples` 集合

### 步骤 4: 重建业务术语向量

- 从 `business_terminology` 表读取数据
- 构建向量化文本：`术语名称 + 术语描述`
- 调用 text-embedding-v4 生成向量
- 批量插入 Milvus

### 步骤 5: 重建查询示例向量

- 从 `query_examples` 表读取数据
- 构建丰富向量化文本：
  ```
  用户问题: [用户问题]
  关联数据集: [数据集名称]
  查询类型: [难度级别]
  核心指标: [指标列表]
  分析维度: [维度列表]
  ```
- 调用 text-embedding-v4 生成向量
- 批量插入 Milvus

### 步骤 6: 测试向量搜索功能

- 执行示例搜索
- 验证搜索结果
- 确认重建成功

## 📊 向量化配置

### 模型参数

- **模型名称**：`text-embedding-v4`
- **API 地址**：`https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding`
- **向量维度**：1024
- **API 密钥**：从 `application.yml` 读取

### 搜索参数

- **相似度算法**：COSINE
- **索引类型**：IVF_FLAT
- **搜索参数**：nprobe=10

## 📁 生成文件

### 日志文件

- **文件名格式**：`vector_rebuild_YYYYMMDD_HHMMSS.log`
- **内容**：详细的重建过程日志
- **位置**：脚本运行目录

### 输出信息

- 连接测试结果
- 数据处理进度
- 向量生成统计
- 错误信息和警告

## ⚠️ 注意事项

### 1. 数据安全

- **不可逆操作**：清理向量数据后无法恢复
- **生产环境**：建议在维护窗口期执行
- **数据备份**：重建前确保 MySQL 数据已备份

### 2. 性能影响

- **API 调用频率**：包含限流保护（每次调用间隔 0.1 秒）
- **网络依赖**：需要稳定的 Dashscope API 网络连接
- **时间消耗**：根据数据量，可能需要几分钟到几十分钟

### 3. 错误处理

- **部分失败**：记录失败的条目，不影响其他数据处理
- **连接中断**：自动重试机制，避免网络波动影响
- **回滚机制**：如果重建失败，需要手动检查和修复

## 🧪 验证方法

### 1. 数据量验证

```bash
# 检查向量数量是否符合预期
python3 -c "
from pymilvus import connections, Collection
connections.connect('default', host='localhost', port='19530')
business_terms = Collection('business_terms')
query_examples = Collection('query_examples')
business_terms.load()
query_examples.load()
print(f'业务术语向量: {business_terms.num_entities}')
print(f'查询示例向量: {query_examples.num_entities}')
"
```

### 2. 搜索功能验证

- 启动 ChatBI 应用
- 执行几个测试查询
- 观察搜索匹配的准确性
- 对比重建前后的效果

### 3. 性能测试

- 测量查询响应时间
- 检查相似度分数分布
- 验证上下文丰富化效果

## 🔧 故障排查

### 常见问题

#### 1. MySQL 连接失败

```
❌ MySQL连接失败: Access denied for user 'root'@'localhost'
```

**解决方案**：

- 检查 MySQL 密码是否正确
- 确认 root 用户有访问权限
- 检查 MySQL 服务是否运行

#### 2. Milvus 连接失败

```
❌ Milvus连接失败: failed to connect to all addresses
```

**解决方案**：

- 检查 Milvus 服务是否启动
- 确认端口 19530 可达
- 检查防火墙设置

#### 3. API 调用失败

```
❌ API调用失败，状态码: 401
```

**解决方案**：

- 检查 Dashscope API 密钥是否有效
- 确认账户余额充足
- 检查网络连接

#### 4. 集合不存在

```
❌ 集合 query_examples 不存在
```

**解决方案**：

- 运行 Milvus 初始化脚本
- 创建必要的集合结构
- 检查集合名称配置

## 📈 效果监控

### 重建后建议监控的指标

1. **搜索准确性**：

   - 相似查询的匹配度
   - 语义理解的准确性
   - 用户反馈质量

2. **性能指标**：

   - 向量搜索响应时间
   - 查询匹配成功率
   - 系统资源使用情况

3. **业务指标**：
   - 用户查询成功率
   - 澄清问题的频率
   - 整体用户满意度

## 📞 技术支持

如果在重建过程中遇到问题：

1. **查看详细日志**：检查生成的日志文件
2. **检查系统状态**：确认所有服务正常运行
3. **验证配置**：检查 API 密钥和连接参数
4. **分步调试**：可以分别测试各个步骤

重建完成后，建议进行充分的功能测试，确保新的向量搜索效果符合预期。
