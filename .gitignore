# Java / Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
#*.class

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws
out/

# Eclipse
.classpath
.project
.settings/
bin/
tmp/
.metadata/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac OS
.DS_Store
.AppleDouble
.LSOverride

# Node.js / Vue.js
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Log files
*.log
logs/

# Other
*.zip
*.tar.gz
*.tgz
*.rar
*.war
*.ear

# Operating System files
Thumbs.db
ehthumbs.db
Desktop.ini

# Environment files
.env
.env.local
.env.*.local

# IDE-specific files for other editors (e.g. Sublime Text, Atom)
*.sublime-project
*.sublime-workspace
.atom/

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Distribution / Build folders
build/
dist/
# For frontend specifically
/chatbi-admin-frontend/dist
/chatbi-admin-frontend/node_modules/

# Temporary files
*.tmp
*~
*.bak
*.swp

# Docker volumes
volumes/
