-- =================================================================
-- 权限中心数据库表结构
-- =================================================================

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(100) NOT NULL UNIQUE COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='角色表';

-- 数据集访问权限表
CREATE TABLE IF NOT EXISTS dataset_access_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    dataset_id BIGINT NOT NULL COMMENT '数据集ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_role_dataset (role_id, dataset_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (dataset_id) REFERENCES queryable_datasets(id) ON DELETE CASCADE
) COMMENT='数据集访问权限表';

-- 插入一些示例角色数据
INSERT INTO roles (role_name, description) VALUES 
('华北区销售经理', '负责华北区域的销售业务管理，可查看销售相关数据'),
('财务分析师', '负责财务数据分析，可查看财务相关数据集'),
('数据分析师', '负责全公司数据分析工作，可查看大部分数据集'),
('高级管理层', '公司高级管理人员，可查看所有数据集')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- 创建索引
CREATE INDEX idx_dataset_permissions_role_id ON dataset_access_permissions(role_id);
CREATE INDEX idx_dataset_permissions_dataset_id ON dataset_access_permissions(dataset_id); 