-- ========================================
-- 数据迁移：统一 semantic_type 值格式
-- 将所有 semantic_type 值统一为大写格式
-- ========================================

USE chatbi_dev;

-- 开始事务
START TRANSACTION;

-- 备份当前状态（记录到日志表）
CREATE TABLE IF NOT EXISTS migration_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    migration_name VARCHAR(100),
    old_value VARCHAR(50),
    new_value VARCHAR(50),
    affected_columns TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 记录迁移开始
INSERT INTO migration_log (migration_name, old_value, new_value, affected_columns) 
SELECT 
    'semantic_type_standardization',
    semantic_type,
    CASE 
        WHEN semantic_type = 'Time' THEN 'TIME_DIMENSION'
        WHEN semantic_type = 'Metric' THEN 'METRIC' 
        WHEN semantic_type = 'Dimension' THEN 'DIMENSION'
        ELSE semantic_type
    END,
    GROUP_CONCAT(column_name)
FROM dataset_columns 
WHERE semantic_type IN ('Time', 'Metric', 'Dimension')
GROUP BY semantic_type;

-- 执行迁移
-- 1. 修复时间维度
UPDATE dataset_columns 
SET semantic_type = 'TIME_DIMENSION' 
WHERE semantic_type = 'Time';

-- 2. 修复指标类型  
UPDATE dataset_columns 
SET semantic_type = 'METRIC' 
WHERE semantic_type = 'Metric';

-- 3. 修复维度类型
UPDATE dataset_columns 
SET semantic_type = 'DIMENSION' 
WHERE semantic_type = 'Dimension';

-- 验证迁移结果
SELECT 
    '迁移验证' as step,
    semantic_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT column_name LIMIT 5) as sample_columns
FROM dataset_columns 
GROUP BY semantic_type
ORDER BY semantic_type;

-- 检查关键字段是否正确迁移
SELECT 
    '关键字段验证' as step,
    dc.column_name,
    dc.semantic_type,
    qd.dataset_name
FROM dataset_columns dc
JOIN queryable_datasets qd ON dc.dataset_id = qd.id
WHERE dc.column_name IN ('成本', '收入', '销售额', '日期', '月份', '注册日期')
ORDER BY qd.dataset_name, dc.column_name;

-- 如果验证通过，提交事务
-- 如果有问题，可以执行 ROLLBACK; 来回滚
COMMIT;

SELECT '数据迁移完成！semantic_type 已统一为标准格式。' as result; 