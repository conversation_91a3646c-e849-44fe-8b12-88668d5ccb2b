-- ========================================
-- 修复 semantic_type 数据不一致问题
-- ========================================

USE chatbi_metadata;

-- 1. 检查当前的semantic_type值分布
SELECT 
    semantic_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT column_name) as example_columns
FROM dataset_columns 
GROUP BY semantic_type
ORDER BY semantic_type;

-- 2. 修复时间维度类型：Time -> TIME_DIMENSION
UPDATE dataset_columns 
SET semantic_type = 'TIME_DIMENSION' 
WHERE semantic_type = 'Time';

-- 3. 修复指标类型：Metric -> METRIC
UPDATE dataset_columns 
SET semantic_type = 'METRIC' 
WHERE semantic_type = 'Metric';

-- 4. 修复维度类型：Dimension -> DIMENSION  
UPDATE dataset_columns 
SET semantic_type = 'DIMENSION' 
WHERE semantic_type = 'Dimension';

-- 5. 检查修复结果
SELECT 
    semantic_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT column_name) as example_columns
FROM dataset_columns 
GROUP BY semantic_type
ORDER BY semantic_type;

-- 6. 验证关键字段
SELECT 
    dc.column_name,
    dc.semantic_type,
    qd.dataset_name,
    dc.description
FROM dataset_columns dc
JOIN queryable_datasets qd ON dc.dataset_id = qd.id
WHERE dc.column_name IN ('成本', '收入', '销售额', '日期', '月份', '注册日期')
ORDER BY qd.dataset_name, dc.column_name;

-- ========================================
-- 7. 添加销售数据集的"成本"计算字段
-- 解决用户询问"成本"时的字段缺失问题
-- ========================================

-- 检查销售数据集ID
SELECT id, dataset_name FROM queryable_datasets WHERE dataset_name LIKE '%销售%';

-- 为销售数据集（ID=1）添加"成本"计算字段
-- 使用常见的成本估算公式：成本 = 销售额 * 0.7
INSERT INTO dataset_columns (
    dataset_id, 
    column_name, 
    description, 
    technical_name_or_expression, 
    semantic_type, 
    data_type, 
    allowed_aggregations, 
    synonyms, 
    is_filterable, 
    is_groupable, 
    is_default_metric, 
    is_computed, 
    formatting_hint, 
    status
) VALUES (
    1, 
    '成本', 
    '基于销售额估算的商品成本，计算公式为销售额的70%', 
    'sales_amount * 0.7', 
    'METRIC', 
    'DECIMAL', 
    '["SUM", "AVG", "MAX", "MIN"]', 
    '["费用", "cost", "商品成本"]', 
    TRUE, 
    FALSE, 
    FALSE, 
    1, 
    '{"format": "currency", "precision": 2}', 
    'active'
) ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    technical_name_or_expression = VALUES(technical_name_or_expression),
    is_computed = VALUES(is_computed);

-- 验证新添加的成本字段
SELECT 
    dc.column_name,
    dc.technical_name_or_expression,
    dc.semantic_type,
    dc.is_computed,
    qd.dataset_name
FROM dataset_columns dc
JOIN queryable_datasets qd ON dc.dataset_id = qd.id
WHERE dc.column_name = '成本' AND qd.dataset_name LIKE '%销售%';

-- 显示完成信息
SELECT '成本字段添加完成，现在用户可以直接查询销售数据集中的成本字段了！' AS status; 