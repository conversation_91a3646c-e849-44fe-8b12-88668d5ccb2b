# **chatbi-data-access-service 详细设计文档**

## **1\. 模块定位与职责**

chatbi-data-access-service 是 ChatBI 项目的数据访问层。在当前的“模块化单体”架构下，它作为一个独立的Maven模块存在，其核心职责是：

* **动态数据源管理**: 能够根据传入的dataSourceId，动态地创建并管理到不同物理数据库（如MySQL, ClickHouse, PostgreSQL等）的连接池。  
* **SQL安全执行**: 接收上游模块（主要是chatbi-agent-core）传递来的SQL查询语句，并在指定的数据源上安全地执行。  
* **结果集标准化**: 将从各种不同数据库返回的结果集（ResultSet）转换成项目统一的、与具体数据库无关的数据传输对象（QueryResult DTO）。  
* **隔离复杂性**: 将所有与JDBC、数据库驱动、连接池管理相关的复杂性和配置细节都封装在此模块内部，使上层业务模块可以完全不必关心底层的数据库交互细节。

该模块是整个系统连接真实业务数据的唯一通道，其设计的健壮性、安全性和性能直接关系到整个ChatBI系统的稳定。

## **2\. 核心组件与实现**

### **2.1. DataAccessService (接口)**

这是该模块暴露给其他模块的**唯一公共契约**。

// File: src/main/java/com/qding/chatbi/dataaccess/service/DataAccessService.java  
package com.qding.chatbi.dataaccess.service;

import com.qding.chatbi.common.dto.QueryResult;

public interface DataAccessService {  
    QueryResult executeQuery(String dataSourceId, String sql);  
}

### **2.2. DataAccessServiceImpl (实现类)**

这是接口的核心实现，包含了所有业务逻辑。

* **依赖注入**:  
  * 它需要注入 chatbi-metadata-service 提供的 DatasetMetadataService (或一个更具体的 DataSourceConfigService)，以便能根据 dataSourceId 查询到数据库的连接配置。  
* **动态数据源缓存**:  
  * 这是本模块的实现精髓。为了避免每次查询都重新创建昂贵的数据库连接池，我们需要一个缓存机制。  
  * 我们将使用一个线程安全的 ConcurrentMap\<String, DataSource\> 来作为缓存。Map的key是dataSourceId，value是已经创建好的 javax.sql.DataSource 实例（通常是一个HikariCP连接池）。  
  * 实现上，会采用 computeIfAbsent 原子操作，确保在多线程环境下，对于同一个dataSourceId，连接池只会被创建一次。

### **2.3. DataSourceFactory (设想的内部组件)**

为了让 DataAccessServiceImpl 的代码更清晰，我们可以将创建 DataSource 的逻辑封装到一个私有的工厂方法或一个独立的内部类中。

* **职责**: 接收一个DatabaseSourceConfig对象（从元数据服务获取），然后创建一个配置好的 HikariDataSource 实例。  
* **配置**: 在这里可以统一配置连接池的参数，如最大连接数（maximumPoolSize）、最小空闲数（minimumIdle）、连接超时时间（connectionTimeout）等。这些参数可以写入到 application.yml 中，实现外部化配置。

## **3\. 核心工作流程**

当 DataAccessServiceImpl.executeQuery(dataSourceId, sql) 方法被调用时，内部的执行流程如下：

1. **记录日志**: 记录接收到的 dataSourceId 和 sql（SQL内容建议记录在DEBUG级别）。  
2. **获取数据源**:  
   * 调用 dataSourceCache.computeIfAbsent(dataSourceId, ...)。  
   * **缓存命中 (Cache Hit)**: 如果缓存中已存在该dataSourceId对应的DataSource实例，直接返回。  
   * 缓存未命中 (Cache Miss):  
     a. 通过注入的metadataService，调用getDatabaseSourceConfigById(dataSourceId)方法，从数据库获取DatabaseSourceConfig对象。  
     b. 如果配置不存在，抛出ChatBiException(ErrorCode.RESOURCE\_NOT\_FOUND)。  
     c. 调用内部的createHikariDataSource(config)方法，创建一个新的HikariDataSource实例。  
     d. computeIfAbsent方法会自动将新创建的DataSource放入缓存，并返回它。  
3. **创建JdbcTemplate**: 使用获取到的DataSource实例，创建一个新的JdbcTemplate对象。JdbcTemplate是Spring提供的强大的JDBC工具类，能自动处理资源的开关。  
4. **执行查询与结果映射**:  
   * 调用 jdbcTemplate.query(sql, resultSetExtractor) 方法。  
   * 提供一个 ResultSetExtractor 的匿名内部类或Lambda表达式实现。  
   * 在实现中：  
     a. 通过ResultSetMetaData获取列数和所有列的名称（getColumnLabel）。  
     b. 循环遍历 ResultSet (while (rs.next()))。  
     c. 在循环中，为每一行创建一个List\<Object\>，并逐列读取数据（rs.getObject(i))。  
     d. 将所有行数据收集到一个List\<List\<Object\>\>中。  
     e. 最后，使用列名列表和行数据列表，创建一个QueryResult对象并返回。  
5. **异常处理**:  
   * 用一个大的try-catch块包裹步骤3和4。  
   * 捕获Spring的DataAccessException。这是所有JDBC相关异常的根异常。  
   * 在catch块中，记录详细的错误日志，然后将其包装成一个ChatBiException(ErrorCode.DATABASE\_QUERY\_ERROR)并向上抛出，同时将原始异常作为cause传入，以便于问题追溯。

## **4\. pom.xml 依赖**

chatbi-data-access-service模块的pom.xml需要包含以下关键依赖：

\<dependencies\>  
    \<\!-- Spring Boot Starter for JDBC, includes HikariCP and JdbcTemplate \--\>  
    \<dependency\>  
        \<groupId\>org.springframework.boot\</groupId\>  
        \<artifactId\>spring-boot-starter-jdbc\</artifactId\>  
    \</dependency\>

    \<\!-- 依赖我们自己的 common 和 metadata-service 模块 \--\>  
    \<dependency\>  
        \<groupId\>com.qding.chatbi\</groupId\>  
        \<artifactId\>chatbi-common\</artifactId\>  
        \<version\>${project.version}\</version\>  
    \</dependency\>  
    \<dependency\>  
        \<groupId\>com.qding.chatbi\</groupId\>  
        \<artifactId\>chatbi-metadata-service\</artifactId\>  
        \<version\>${project.version}\</version\>  
    \</dependency\>

    \<\!-- 数据库驱动 (根据需要支持的数据库添加) \--\>  
    \<dependency\>  
        \<groupId\>mysql\</groupId\>  
        \<artifactId\>mysql-connector-java\</artifactId\>  
        \<scope\>runtime\</scope\>  
    \</dependency\>  
    \<\!--  
    \<dependency\>  
        \<groupId\>ru.yandex.clickhouse\</groupId\>  
        \<artifactId\>clickhouse-jdbc\</artifactId\>  
        \<version\>...\</version\>  
    \</dependency\>  
    \--\>  
\</dependencies\>

通过以上设计，chatbi-data-access-service模块成为了一个职责单一、性能高效、易于扩展且与上层业务完全解耦的坚实数据底座。