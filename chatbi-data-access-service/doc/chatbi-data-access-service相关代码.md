// =========================================================================
// 模块: chatbi-data-access-service
// 职责: 提供数据访问的核心服务，管理动态数据源并执行SQL。
// =========================================================================

/**
 * 声明:
 * 1. DatabaseSourceConfig 类被假定存在于 chatbi-metadata-service 模块中，
 * 并包含 getJdbcUrl(), getUsername(), getPassword(), getDriverClassName() 等方法。
 * 2. DatasetMetadataService 接口被假定存在于 chatbi-metadata-service 模块中，
 * 并提供 getDatabaseSourceConfigById(String id) 方法来获取数据源配置。
 */

package com.qding.chatbi.dataaccess.service;

import com.qding.chatbi.common.dto.QueryResult;

/**
 * DataAccessService 接口
 * <p>
 * 这是数据访问模块暴露给其他模块的唯一公共契约。
 * 上游模块 (如 chatbi-agent-core) 将通过Spring依赖注入来使用此接口。
 */
public interface DataAccessService {

    /**
     * 根据指定的数据源ID和SQL语句执行查询。
     *
     * @param dataSourceId 数据源的唯一标识符, 由元数据模块管理。
     * @param sql          需要被执行的、已经过安全验证的SQL查询语句。
     * @return 查询结果, 封装在统一的 QueryResult DTO 中。
     * @throws com.qding.chatbi.common.exception.ChatBiException 如果发生任何数据访问错误。
     */
    QueryResult executeQuery(String dataSourceId, String sql);
}

// -------------------------------------------------------------------------

package com.qding.chatbi.dataaccess.service.impl;

import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.enums.ErrorCode;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.dataaccess.service.DataAccessService;
import com.qding.chatbi.metadata.entity.DatabaseSourceConfig;
import com.qding.chatbi.metadata.service.DatasetMetadataService; // 假设从元数据服务获取配置
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * DataAccessService 的核心实现类。
 * <p>
 * 实现了动态数据源的创建、缓存和管理，并使用 Spring 的 JdbcTemplate 来安全、高效地执行SQL。
 */
@Service
@Slf4j
public class DataAccessServiceImpl implements DataAccessService {

    // 注入元数据服务，用于根据ID获取数据源的详细连接配置
    @Autowired
    private DatasetMetadataService metadataService;

    // 使用 ConcurrentHashMap 作为线程安全的数据源缓存 (连接池缓存)
    // Key: dataSourceId, Value: HikariDataSource 实例
    private final ConcurrentMap<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();

    // 从 application.yml 中读取可配置的连接池参数
    @Value("${chatbi.datasource.pool.max-size:10}")
    private int maxPoolSize;

    @Value("${chatbi.datasource.pool.min-idle:2}")
    private int minIdle;

    @Value("${chatbi.datasource.pool.connection-timeout:30000}")
    private long connectionTimeout;

    @Override
    public QueryResult executeQuery(String dataSourceId, String sql) {
        log.info("Executing query on dataSourceId: '{}'", dataSourceId);
        log.debug("Executing SQL: {}", sql);

        try {
            // 步骤 1: 获取或创建数据源实例
            DataSource dataSource = getDataSource(dataSourceId);

            // 步骤 2: 基于数据源创建 JdbcTemplate
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

            // 步骤 3: 执行查询并使用 ResultSetExtractor 将结果直接映射到 QueryResult DTO
            return jdbcTemplate.query(sql, (ResultSetExtractor<QueryResult>) rs -> {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // 提取列名
                List<String> columnNames = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(metaData.getColumnLabel(i));
                }

                // 提取所有行数据
                List<List<Object>> rows = new ArrayList<>();
                while (rs.next()) {
                    List<Object> row = new ArrayList<>(columnCount);
                    for (int i = 1; i <= columnCount; i++) {
                        row.add(rs.getObject(i));
                    }
                    rows.add(row);
                }
                log.info("Query successful for dataSourceId: '{}', fetched {} rows.", dataSourceId, rows.size());
                return new QueryResult(columnNames, rows);
            });

        } catch (DataAccessException e) {
            // 步骤 4: 捕获所有 Spring JDBC 相关的异常
            log.error("Database query failed for dataSourceId: '{}'. Error: {}", dataSourceId, e.getMessage(), e);
            // 将技术性异常包装成对上层更友好的业务异常
            throw new ChatBiException(ErrorCode.DATABASE_QUERY_ERROR, "数据库查询执行失败: " + e.getMostSpecificCause().getMessage(), e);
        } catch (Exception e) {
            // 捕获其他所有未知异常，例如创建数据源失败等
            log.error("An unexpected error occurred during query execution for dataSourceId: '{}'", dataSourceId, e);
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "数据检索服务发生未知错误。", e);
        }
    }

    /**
     * 从缓存中获取数据源，如果不存在则创建并缓存一个新的。
     * 使用 ConcurrentMap.computeIfAbsent 保证线程安全和原子性。
     *
     * @param dataSourceId 数据源的唯一ID
     * @return 配置好的 DataSource (连接池) 实例
     */
    private DataSource getDataSource(String dataSourceId) {
        return dataSourceCache.computeIfAbsent(dataSourceId, id -> {
            log.info("DataSource for id '{}' not found in cache. Creating a new one.", id);

            // 从元数据服务获取连接配置
            DatabaseSourceConfig config = metadataService.getDatabaseSourceConfigById(id);
            if (config == null) {
                log.error("Configuration for dataSourceId '{}' not found in metadata.", id);
                throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, "数据源配置不存在 (ID: " + id + ")");
            }
            
            // 创建新的连接池实例
            return createHikariDataSource(config);
        });
    }

    /**
     * 根据数据库配置信息创建一个 HikariCP 数据源(连接池)。
     *
     * @param dbConfig 数据库连接配置
     * @return HikariDataSource 实例
     */
    private DataSource createHikariDataSource(DatabaseSourceConfig dbConfig) {
        try {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setPoolName("HikariPool-" + dbConfig.getId());
            hikariConfig.setJdbcUrl(dbConfig.getJdbcUrl());
            hikariConfig.setUsername(dbConfig.getUsername());
            hikariConfig.setPassword(dbConfig.getPassword());
            hikariConfig.setDriverClassName(dbConfig.getDriverClassName());
            
            // 应用外部化配置
            hikariConfig.setMaximumPoolSize(maxPoolSize);
            hikariConfig.setMinimumIdle(minIdle);
            hikariConfig.setConnectionTimeout(connectionTimeout);
            
            // 其他推荐的默认配置
            hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
            hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
            hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

            log.info("Created new HikariDataSource for pool: {}", hikariConfig.getPoolName());
            return new HikariDataSource(hikariConfig);
        } catch (Exception e) {
            log.error("Failed to create HikariDataSource for dataSourceId: {}", dbConfig.getId(), e);
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "创建数据库连接池失败。", e);
        }
    }
}
