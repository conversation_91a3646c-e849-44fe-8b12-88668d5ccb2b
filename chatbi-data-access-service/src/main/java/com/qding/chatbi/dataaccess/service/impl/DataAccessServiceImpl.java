package com.qding.chatbi.dataaccess.service.impl;

import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.enums.ErrorCode;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.dataaccess.service.DataAccessService;
import com.qding.chatbi.metadata.entity.DatabaseSourceConfig;
import com.qding.chatbi.metadata.service.DatasetMetadataService;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**  
 * 数据访问服务实现类
 * 
 * 职责：
 * 1. 管理多个数据源的连接池（HikariCP）
 * 2. 根据数据源ID动态创建和缓存数据库连接
 * 3. 执行SQL查询并返回标准化的结果
 * 4. 处理数据库连接异常和查询异常
 * 
 * 数据流转：
 * DataSourceId + SQL -> 数据库连接池 -> 数据库 -> 原始ResultSet -> QueryResult
 * 
 * 调试说明：
 * - 记录每次查询的数据源ID和SQL语句
 * - 记录连接池的创建和使用情况
 * - 记录查询结果的详细统计信息
 * - 记录数据库异常的完整上下文
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
@Service
@Slf4j
public class DataAccessServiceImpl implements DataAccessService {

    @Autowired
    private DatasetMetadataService metadataService;

    private final ConcurrentMap<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();

    @Value("${chatbi.datasource.pool.max-size:10}")
    private int maxPoolSize;

    @Value("${chatbi.datasource.pool.min-idle:2}")
    private int minIdle;

    @Value("${chatbi.datasource.pool.connection-timeout:30000}")
    private long connectionTimeout;

    /**
     * 执行SQL查询
     * 
     * @param dataSourceId 数据源ID，用于标识目标数据库
     * @param sql SQL查询语句
     * @return QueryResult 标准化的查询结果对象
     * @throws ChatBiException 当数据库操作失败时抛出
     */
    @Override
    public QueryResult executeQuery(String dataSourceId, String sql) {
        log.info("========== DataAccessService.executeQuery 开始执行数据库查询 ==========");
        log.info("📥 接收到的查询请求:");
        log.info("   - 数据源ID: '{}'", dataSourceId);
        log.info("   - SQL长度: {} 字符", sql != null ? sql.length() : 0);
        log.info("   - SQL内容: {}", sql != null ? sql.replace("\n", " ").replaceAll("\\s+", " ") : "NULL");
        log.info("   - 请求时间戳: {}", System.currentTimeMillis());

        long startTime = System.currentTimeMillis();

        try {
            // 🔗 获取数据源连接
            log.info("🔗 获取数据源连接...");
            DataSource dataSource = getDataSource(dataSourceId);
            log.info("✅ 数据源连接获取成功, 类型: {}", dataSource.getClass().getSimpleName());
            
            // 🔍 增强调试：获取连接详细信息
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) dataSource;
                log.debug("🔍 HikariDataSource详细信息:");
                log.debug("   - JDBC URL: {}", hikariDS.getJdbcUrl());
                log.debug("   - 用户名: {}", hikariDS.getUsername());
                log.debug("   - 连接池名称: {}", hikariDS.getPoolName());
                log.debug("   - 最大连接数: {}", hikariDS.getMaximumPoolSize());
                log.debug("   - 当前活跃连接: {}", hikariDS.getHikariPoolMXBean() != null ? hikariDS.getHikariPoolMXBean().getActiveConnections() : "N/A");
            }
            
            // 📞 创建JdbcTemplate并执行查询
            log.info("📞 创建JdbcTemplate并执行查询...");
            log.debug("🔍 即将执行的SQL详情:");
            log.debug("   - 数据源ID: {}", dataSourceId);
            log.debug("   - SQL长度: {} 字符", sql.length());
            log.debug("   - 完整SQL内容:\n{}", sql);
            log.debug("   - SQL（单行格式）: {}", sql.replace("\n", " ").replaceAll("\\s+", " "));
            
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            
            // 🧪 测试数据库连接
            try {
                log.debug("🧪 测试数据库连接...");
                String testQuery = "SELECT 1 as test_connection";
                jdbcTemplate.queryForObject(testQuery, Integer.class);
                log.debug("✅ 数据库连接测试成功");
            } catch (Exception testEx) {
                log.error("❌ 数据库连接测试失败: {}", testEx.getMessage());
                throw testEx;
            }

            return jdbcTemplate.query(sql, (ResultSetExtractor<QueryResult>) rs -> {
                log.debug("🔍 开始处理ResultSet...");
                
                // 📊 获取结果集元数据
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                log.debug("   - 结果列数: {}", columnCount);

                // 📋 提取列名
                List<String> columnNames = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    columnNames.add(columnName);
                    log.debug("   - 第{}列: {} (类型: {})", i, columnName, metaData.getColumnTypeName(i));
                }

                // 📈 提取数据行
                List<List<Object>> rows = new ArrayList<>();
                int rowCount = 0;
                while (rs.next()) {
                    List<Object> row = new ArrayList<>(columnCount);
                    for (int i = 1; i <= columnCount; i++) {
                        Object value = rs.getObject(i);
                        row.add(value);
                    }
                    rows.add(row);
                    rowCount++;
                    
                    // 记录前几行数据作为样本（仅在debug级别）
                    if (rowCount <= 3) {
                        log.debug("   - 第{}行数据: {}", rowCount, row);
                    }
                }
                
                if (rowCount > 3) {
                    log.debug("   - ... 还有{}行数据未显示详情", rowCount - 3);
                }

                long queryTime = System.currentTimeMillis() - startTime;
                
                log.info("✅ 查询执行成功:");
                log.info("   - 数据源ID: '{}'", dataSourceId);
                log.info("   - 查询耗时: {}ms", queryTime);
                log.info("   - 返回列数: {}", columnNames.size());
                log.info("   - 返回行数: {}", rows.size());
                log.info("   - 列名列表: {}", columnNames);
                
                // 🏗️ 构建查询结果对象
                QueryResult result = new QueryResult();
                result.setColumnHeaders(columnNames);
                result.setRows(rows);
                result.setTotalRows((long) rows.size());
                
                log.info("🏗️ QueryResult对象构建完成");
                return result;
            });

        } catch (DataAccessException e) {
            long failedTime = System.currentTimeMillis() - startTime;
            log.error("❌ 数据库查询失败:");
            log.error("   - 数据源ID: '{}'", dataSourceId);
            log.error("   - 失败时间: {}ms 后", failedTime);
            log.error("   - 异常类型: {}", e.getClass().getSimpleName());
            log.error("   - 异常消息: {}", e.getMessage());
            log.error("   - 根本原因: {}", e.getMostSpecificCause().getMessage());
            log.error("   - SQL语句: {}", sql);
            log.error("   - 完整异常堆栈:", e);
            
            throw new ChatBiException(ErrorCode.DATABASE_ERROR, 
                    "数据库查询执行失败: " + e.getMostSpecificCause().getMessage());
        } catch (Exception e) {
            long failedTime = System.currentTimeMillis() - startTime;
            log.error("❌ 查询执行过程中发生未知异常:");
            log.error("   - 数据源ID: '{}'", dataSourceId);
            log.error("   - 失败时间: {}ms 后", failedTime);
            log.error("   - 异常类型: {}", e.getClass().getSimpleName());
            log.error("   - 异常消息: {}", e.getMessage());
            log.error("   - SQL语句: {}", sql);
            log.error("   - 完整异常堆栈:", e);
            
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "数据检索服务发生未知错误。");
        }
    }

    /**
     * 获取数据源连接（带缓存）
     * 
     * @param dataSourceId 数据源ID
     * @return DataSource 数据源连接对象
     */
    private DataSource getDataSource(String dataSourceId) {
        log.debug("🔍 从缓存获取数据源, ID: '{}'", dataSourceId);
        
        return dataSourceCache.computeIfAbsent(dataSourceId, id -> {
            log.info("💾 数据源 '{}' 不在缓存中，创建新的连接池...", id);

            // 📋 从元数据服务获取数据库配置
            log.debug("📋 从元数据服务获取数据库配置...");
            log.debug("   - 请求的数据源ID: {}", id);
            log.debug("   - 数据源ID类型: {}", id.getClass().getSimpleName());
            
            DatabaseSourceConfig config;
            try {
                config = metadataService.getDatabaseSourceConfigById(Long.parseLong(id));
                log.debug("✅ 成功获取数据源配置: {}", config != null ? "配置存在" : "配置为空");
            } catch (NumberFormatException e) {
                log.error("❌ 数据源ID格式错误: '{}' 不是有效的数字", id);
                throw new ChatBiException(ErrorCode.INVALID_INPUT, "数据源ID格式错误: " + id);
            }
            
            if (config == null) {
                log.error("❌ 数据源配置未找到:");
                log.error("   - 请求的数据源ID: '{}'", id);
                log.error("   - 这可能表示数据源不存在或已被删除");
                throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, 
                        "数据源配置不存在 (ID: " + id + ")");
            }
            
            log.info("✅ 找到数据源配置:");
            log.info("   - 数据源ID: {}", config.getId());
            log.info("   - 数据库类型: {}", config.getDatabaseType());
            log.debug("   - 主机地址: {}", config.getHost());
            log.debug("   - 端口: {}", config.getPort());
            log.debug("   - 数据库名: {}", config.getDatabaseName());
            
            // 🏗️ 创建HikariDataSource连接池
            return createHikariDataSource(config);
        });
    }

    /**
     * 创建HikariCP数据库连接池
     * 
     * @param dbConfig 数据库源配置
     * @return DataSource HikariCP数据源对象
     */
    private DataSource createHikariDataSource(DatabaseSourceConfig dbConfig) {
        log.info("🏗️ 创建HikariCP连接池:");
        log.info("   - 数据源ID: {}", dbConfig.getId());
        log.info("   - 数据库类型: {}", dbConfig.getDatabaseType());
        log.info("   - 主机: {}", dbConfig.getHost());
        log.info("   - 端口: {}", dbConfig.getPort());
        log.info("   - 数据库名: {}", dbConfig.getDatabaseName());
        log.info("   - 用户名: {}", dbConfig.getUsername());
        
        // 🔍 增强调试：显示完整的数据库配置
        log.debug("🔍 完整数据库配置详情:");
        log.debug("   - 数据源配置对象: {}", dbConfig);
        log.debug("   - 连接参数: {}", dbConfig.getConnectionParameters());
        log.debug("   - 密码是否为空: {}", dbConfig.getPassword() == null ? "是" : "否");
        
        try {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setPoolName("HikariPool-" + dbConfig.getId());
            
            // 根据数据库类型构建JDBC URL和设置驱动
            String jdbcUrl = buildJdbcUrl(dbConfig);
            log.debug("🔍 构建的JDBC URL: {}", jdbcUrl);
            
            hikariConfig.setJdbcUrl(jdbcUrl);
            hikariConfig.setUsername(dbConfig.getUsername());
            hikariConfig.setPassword(dbConfig.getPassword());
            hikariConfig.setDriverClassName(getDriverClassName(dbConfig.getDatabaseType()));

            // 📊 连接池配置
            hikariConfig.setMaximumPoolSize(maxPoolSize);
            hikariConfig.setMinimumIdle(minIdle);
            hikariConfig.setConnectionTimeout(connectionTimeout);
            
            // 🚀 性能优化配置
            hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
            hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
            hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

            log.info("✅ HikariCP连接池配置完成:");
            log.info("   - 连接池名称: {}", hikariConfig.getPoolName());
            log.info("   - JDBC URL: {}", jdbcUrl);
            log.info("   - 最大连接数: {}", maxPoolSize);
            log.info("   - 最小空闲连接数: {}", minIdle);
            log.info("   - 连接超时时间: {}ms", connectionTimeout);

            // 🧪 测试连接配置
            log.debug("🧪 测试HikariConfig配置...");
            HikariDataSource dataSource = new HikariDataSource(hikariConfig);
            log.info("🎯 HikariDataSource创建成功，连接池: {}", hikariConfig.getPoolName());
            
            // 🔍 验证连接池创建后的状态
            log.debug("🔍 连接池创建后状态:");
            log.debug("   - 连接池是否关闭: {}", dataSource.isClosed());
            log.debug("   - 连接池是否运行: {}", dataSource.isRunning());
            
            return dataSource;
        } catch (Exception e) {
            log.error("❌ 创建HikariDataSource失败:");
            log.error("   - 数据源ID: {}", dbConfig.getId());
            log.error("   - 异常类型: {}", e.getClass().getSimpleName());
            log.error("   - 异常消息: {}", e.getMessage());
            log.error("   - 完整异常堆栈:", e);
            
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "创建数据库连接池失败。");
        }
    }

    /**
     * 根据数据库配置构建JDBC URL
     */
    private String buildJdbcUrl(DatabaseSourceConfig config) {
        switch (config.getDatabaseType()) {
            case MYSQL:
                return String.format("**********************************************************************************", 
                        config.getHost(), config.getPort(), config.getDatabaseName());
            case POSTGRESQL:
                return String.format("jdbc:postgresql://%s:%d/%s", 
                        config.getHost(), config.getPort(), config.getDatabaseName());
            case SQLSERVER:
                return String.format("**************************************", 
                        config.getHost(), config.getPort(), config.getDatabaseName());
            case ORACLE:
                return String.format("**************************", 
                        config.getHost(), config.getPort(), config.getDatabaseName());
            default:
                throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, 
                        "不支持的数据库类型: " + config.getDatabaseType());
        }
    }

    /**
     * 根据数据库类型获取对应的JDBC驱动类名
     */
    private String getDriverClassName(com.qding.chatbi.common.enums.DatabaseType databaseType) {
        switch (databaseType) {
            case MYSQL:
                return "com.mysql.cj.jdbc.Driver";
            case POSTGRESQL:
                return "org.postgresql.Driver";
            case SQLSERVER:
                return "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            case ORACLE:
                return "oracle.jdbc.driver.OracleDriver";
            default:
                throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, 
                        "不支持的数据库类型: " + databaseType);
        }
    }
} 