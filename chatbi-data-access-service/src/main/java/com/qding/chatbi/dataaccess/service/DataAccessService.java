package com.qding.chatbi.dataaccess.service;

import com.qding.chatbi.common.dto.QueryResult;

/**
 * DataAccessService 接口
 * 定义了数据访问模块对外提供的核心能力。
 * chatbi-agent-core 模块将会注入此接口的实现。
 */
public interface DataAccessService {

    /**
     * 根据指定的数据源ID和SQL语句执行查询。
     *
     * @param dataSourceId 数据源的唯一标识符
     * @param sql          要执行的SQL查询语句
     * @return 查询结果，封装在QueryResult DTO中
     */
    QueryResult executeQuery(String dataSourceId, String sql);
} 