# Spring Boot application configuration for chatbi-data-access-service
# Example:
# server:
#   port: 8084
# spring:
#   application:
#     name: chatbi-data-access-service
# This service might configure multiple data sources dynamically or via properties
# Example for a primary data source (actual connections would be dynamic)
# primary_datasource:
#   jdbc-url: *********************************************************
#   username: db_user
#   password: db_password
#   driver-class-name: org.postgresql.Driver

# ChatBI Data Access Service Specific Configurations
spring:
  application:
    name: chatbi-data-access-service

chatbi:
  datasource:
    pool:
      max-size: 15
      min-idle: 3
      connection-timeout: 30000
