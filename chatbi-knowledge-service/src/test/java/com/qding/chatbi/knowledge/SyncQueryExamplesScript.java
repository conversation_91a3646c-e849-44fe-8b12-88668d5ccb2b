package com.qding.chatbi.knowledge;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.*;

/**
 * 通过调用管理后台API来同步QueryExample数据到Milvus
 * 前提：
 * 1. 管理后台服务（chatbi-admin-backend）已经启动
 * 2. Milvus服务已经启动
 */
@SpringBootApplication
public class SyncQueryExamplesScript {

    private static final String ADMIN_API_BASE = "http://localhost:8080/api/admin";
    private static final String USERNAME = "admin";
    private static final String PASSWORD = "password";

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(SyncQueryExamplesScript.class, args);

        try {
            // 获取数据源
            DataSource dataSource = context.getBean(DataSource.class);
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            RestTemplate restTemplate = new RestTemplate();

            // 1. 获取JWT token
            String token = getAuthToken(restTemplate);
            if (token == null) {
                System.err.println("无法获取认证token，请确保管理后台服务已启动");
                return;
            }
            System.out.println("成功获取认证token");

            // 2. 查询所有QueryExample
            String sql = "SELECT id, user_question, target_query_representation, target_dataset_id, " +
                    "involved_column_ids, notes, difficulty_level, status " +
                    "FROM query_examples";

            List<Map<String, Object>> examples = jdbcTemplate.queryForList(sql);
            System.out.println("从数据库中找到 " + examples.size() + " 条QueryExample记录");

            // 3. 逐条通过API更新（触发同步到Milvus）
            int successCount = 0;
            int failCount = 0;
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            for (Map<String, Object> example : examples) {
                try {
                    Long id = ((Number) example.get("id")).longValue();

                    // 构建更新请求
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("userQuestion", example.get("user_question"));
                    requestBody.put("targetQueryRepresentation", example.get("target_query_representation"));
                    requestBody.put("targetDatasetId", example.get("target_dataset_id"));
                    requestBody.put("difficultyLevel", example.get("difficulty_level"));
                    requestBody.put("notes", example.get("notes"));

                    HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

                    // 调用更新API（会触发同步到Milvus）
                    String url = ADMIN_API_BASE + "/knowledge-base/examples/" + id;
                    ResponseEntity<String> response = restTemplate.exchange(
                            url, HttpMethod.PUT, request, String.class);

                    if (response.getStatusCode().is2xxSuccessful()) {
                        successCount++;
                        System.out.println("✓ 成功同步ID: " + id + " - " + example.get("user_question"));
                    } else {
                        failCount++;
                        System.err.println("✗ 同步失败ID: " + id + " - 状态码: " + response.getStatusCode());
                    }

                } catch (Exception e) {
                    failCount++;
                    System.err.println("✗ 同步失败: " + e.getMessage());
                }
            }

            // 4. 输出统计结果
            System.out.println("\n=== 同步完成 ===");
            System.out.println("成功: " + successCount + " 条");
            System.out.println("失败: " + failCount + " 条");
            System.out.println("总计: " + examples.size() + " 条");

        } catch (Exception e) {
            System.err.println("同步过程出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            context.close();
        }
    }

    private static String getAuthToken(RestTemplate restTemplate) {
        try {
            Map<String, String> loginRequest = new HashMap<>();
            loginRequest.put("username", USERNAME);
            loginRequest.put("password", PASSWORD);

            ResponseEntity<Map> response = restTemplate.postForEntity(
                    ADMIN_API_BASE + "/auth/login",
                    loginRequest,
                    Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return (String) response.getBody().get("token");
            }
        } catch (Exception e) {
            System.err.println("登录失败: " + e.getMessage());
        }
        return null;
    }
}