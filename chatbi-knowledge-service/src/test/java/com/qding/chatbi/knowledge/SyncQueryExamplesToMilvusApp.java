package com.qding.chatbi.knowledge;

import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import com.qding.chatbi.metadata.entity.QueryExample;
import com.qding.chatbi.metadata.repository.QueryExampleRepository;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.List;

/**
 * 独立应用程序：同步QueryExample数据到Milvus
 * 运行方式：直接执行main方法
 */
@SpringBootApplication
public class SyncQueryExamplesToMilvusApp {

    public static void main(String[] args) {
        // 启动Spring应用上下文
        ConfigurableApplicationContext context = SpringApplication.run(SyncQueryExamplesToMilvusApp.class, args);

        try {
            // 获取必要的服务
            QueryExampleRepository repository = context.getBean(QueryExampleRepository.class);
            KnowledgePersistenceService knowledgeService = context.getBean(KnowledgePersistenceService.class);

            System.out.println("=== 开始同步QueryExample数据到Milvus ===");

            // 1. 从数据库读取所有QueryExample
            List<QueryExample> allExamples = repository.findAll();
            System.out.println("找到 " + allExamples.size() + " 条QueryExample记录");

            // 2. 逐条同步到Milvus
            int successCount = 0;
            int failCount = 0;

            for (QueryExample example : allExamples) {
                try {
                    System.out.println("\n正在同步ID=" + example.getId() + ": " + example.getUserQuestion());
                    knowledgeService.saveOrUpdateExample(example);
                    successCount++;
                    System.out.println("✓ 成功");
                } catch (Exception e) {
                    failCount++;
                    System.err.println("✗ 失败: " + e.getMessage());
                }
            }

            // 3. 输出统计结果
            System.out.println("\n=== 同步完成 ===");
            System.out.println("成功: " + successCount + " 条");
            System.out.println("失败: " + failCount + " 条");
            System.out.println("总计: " + allExamples.size() + " 条");

            // 4. 测试搜索功能
            if (successCount > 0) {
                System.out.println("\n=== 测试向量搜索功能 ===");
                testVectorSearch(knowledgeService);
            }

        } catch (Exception e) {
            System.err.println("同步过程出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭应用上下文
            context.close();
        }
    }

    private static void testVectorSearch(KnowledgePersistenceService knowledgeService) {
        String[] testQueries = {
                "北京的销售额",
                "同比增长",
                "排名前五",
                "各城市销售额",
                "累计"
        };

        for (String query : testQueries) {
            System.out.println("\n搜索: '" + query + "'");
            try {
                List<QueryExample> results = knowledgeService.searchSimilarExamples(query, 3);
                if (results.isEmpty()) {
                    System.out.println("  未找到相似示例");
                } else {
                    for (int i = 0; i < results.size(); i++) {
                        QueryExample result = results.get(i);
                        System.out.println("  " + (i + 1) + ". " + result.getUserQuestion() +
                                " (难度: " + result.getDifficultyLevel() + ")");
                    }
                }
            } catch (Exception e) {
                System.err.println("  搜索失败: " + e.getMessage());
            }
        }
    }
}