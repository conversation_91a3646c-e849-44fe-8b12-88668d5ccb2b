package com.qding.chatbi.knowledge;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.R;
import io.milvus.param.dml.QueryParam;
import io.milvus.response.QueryResultsWrapper;
import io.milvus.grpc.QueryResults;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 详细对比MySQL和Milvus中的query_examples数据
 */
public class DataComparisonTest {

    private static final String MYSQL_URL = "*******************************************";
    private static final String MYSQL_USER = "root";
    private static final String MYSQL_PASSWORD = "";

    private static final String MILVUS_HOST = "localhost";
    private static final int MILVUS_PORT = 19530;
    private static final String COLLECTION_NAME = "query_examples";

    public static void main(String[] args) {
        System.out.println("============================================================");
        System.out.println("           详细数据对比分析 (Java版本)");
        System.out.println("============================================================");

        try {
            // 获取MySQL数据
            Map<Long, String> mysqlData = getMySQLData();
            System.out.println("✓ MySQL数据获取成功，记录数: " + mysqlData.size());

            // 获取Milvus数据
            Set<Long> milvusIds = getMilvusData();
            System.out.println("✓ Milvus数据获取成功，记录数: " + milvusIds.size());

            // 进行对比
            compareData(mysqlData, milvusIds);

        } catch (Exception e) {
            System.err.println("❌ 数据对比失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Map<Long, String> getMySQLData() throws Exception {
        Map<Long, String> data = new HashMap<>();

        try (Connection conn = DriverManager.getConnection(MYSQL_URL, MYSQL_USER, MYSQL_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT id, user_question FROM query_examples ORDER BY id")) {

            while (rs.next()) {
                Long id = rs.getLong("id");
                String question = rs.getString("user_question");
                data.put(id, question);
            }
        }

        return data;
    }

    private static Set<Long> getMilvusData() throws Exception {
        Set<Long> ids = new HashSet<>();

        MilvusServiceClient milvusClient = new MilvusServiceClient(
                ConnectParam.newBuilder()
                        .withHost(MILVUS_HOST)
                        .withPort(MILVUS_PORT)
                        .build());

        try {
            R<QueryResults> response = milvusClient.query(
                    QueryParam.newBuilder()
                            .withCollectionName(COLLECTION_NAME)
                            .withExpr("id >= 0")
                            .withOutFields(Arrays.asList("id"))
                            .withLimit(1000L)
                            .build());

            if (response.getStatus() == 0 && response.getData() != null) {
                QueryResultsWrapper wrapper = new QueryResultsWrapper(response.getData());
                List<QueryResultsWrapper.RowRecord> records = wrapper.getRowRecords();

                for (QueryResultsWrapper.RowRecord record : records) {
                    Object idObj = record.get("id");
                    if (idObj instanceof Long) {
                        ids.add((Long) idObj);
                    } else if (idObj instanceof Integer) {
                        ids.add(((Integer) idObj).longValue());
                    }
                }
            } else {
                throw new RuntimeException("Milvus查询失败: " + response.getMessage());
            }
        } finally {
            milvusClient.close();
        }

        return ids;
    }

    private static void compareData(Map<Long, String> mysqlData, Set<Long> milvusIds) {
        Set<Long> mysqlIds = mysqlData.keySet();

        System.out.println("\n📊 数据统计:");
        System.out.println("   MySQL记录数: " + mysqlIds.size());
        System.out.println("   Milvus记录数: " + milvusIds.size());

        System.out.println("\n📋 对比分析:");

        // 在MySQL中但不在Milvus中
        Set<Long> missingInMilvus = new HashSet<>(mysqlIds);
        missingInMilvus.removeAll(milvusIds);

        if (!missingInMilvus.isEmpty()) {
            System.out.println("   ⚠️  在Milvus中缺失的记录 (" + missingInMilvus.size() + "条):");
            List<Long> sortedMissing = new ArrayList<>(missingInMilvus);
            Collections.sort(sortedMissing);
            for (Long id : sortedMissing) {
                String question = mysqlData.get(id);
                String preview = question.length() > 50 ? question.substring(0, 50) + "..." : question;
                System.out.println("      ID " + id + ": " + preview);
            }
        } else {
            System.out.println("   ✅ 所有MySQL记录都在Milvus中存在");
        }

        // 在Milvus中但不在MySQL中
        Set<Long> extraInMilvus = new HashSet<>(milvusIds);
        extraInMilvus.removeAll(mysqlIds);

        if (!extraInMilvus.isEmpty()) {
            System.out.println("   ⚠️  在Milvus中多余的记录 (" + extraInMilvus.size() + "条):");
            List<Long> sortedExtra = new ArrayList<>(extraInMilvus);
            Collections.sort(sortedExtra);
            System.out.println("      ID列表: " + sortedExtra);
        } else {
            System.out.println("   ✅ Milvus中没有多余记录");
        }

        // 共同存在的记录
        Set<Long> commonIds = new HashSet<>(mysqlIds);
        commonIds.retainAll(milvusIds);
        System.out.println("   ✅ 两边都存在的记录: " + commonIds.size() + "条");

        // 总结和建议
        System.out.println("\n🎯 同步状态总结:");
        if (!missingInMilvus.isEmpty() || !extraInMilvus.isEmpty()) {
            System.out.println("   ❌ 数据不同步!");
            System.out.println("      - 缺失: " + missingInMilvus.size() + "条");
            System.out.println("      - 多余: " + extraInMilvus.size() + "条");
            System.out.println("      - 匹配: " + commonIds.size() + "条");

            System.out.println("\n💡 建议解决方案:");
            if (!missingInMilvus.isEmpty()) {
                System.out.println("   1. 同步缺失的" + missingInMilvus.size() + "条记录到Milvus");
                System.out.println("      缺失的ID: " + missingInMilvus.stream()
                        .sorted().map(String::valueOf).collect(Collectors.joining(", ")));
            }
            if (!extraInMilvus.isEmpty()) {
                System.out.println("   2. 清理Milvus中多余的" + extraInMilvus.size() + "条记录");
                System.out.println("      多余的ID: " + extraInMilvus.stream()
                        .sorted().map(String::valueOf).collect(Collectors.joining(", ")));
            }
        } else {
            System.out.println("   ✅ 数据完全同步! 两边都有" + commonIds.size() + "条记录");
        }

        System.out.println("\n============================================================");
    }
}