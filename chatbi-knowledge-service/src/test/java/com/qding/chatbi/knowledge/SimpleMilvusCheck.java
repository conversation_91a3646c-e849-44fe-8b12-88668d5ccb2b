package com.qding.chatbi.knowledge;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.R;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.collection.ShowCollectionsParam;
import io.milvus.grpc.ShowCollectionsResponse;

/**
 * 简单检查Milvus连接和集合状态
 */
public class SimpleMilvusCheck {

    private static final String MILVUS_HOST = "localhost";
    private static final int MILVUS_PORT = 19530;
    private static final String COLLECTION_NAME = "query_examples";

    public static void main(String[] args) {
        System.out.println("=== 简单检查Milvus状态 ===");

        MilvusServiceClient milvusClient = null;
        try {
            // 连接Milvus
            System.out.println("1. 连接Milvus服务器 (" + MILVUS_HOST + ":" + MILVUS_PORT + ")...");
            milvusClient = new MilvusServiceClient(
                    ConnectParam.newBuilder()
                            .withHost(MILVUS_HOST)
                            .withPort(MILVUS_PORT)
                            .build());
            System.out.println("   ✓ 连接成功");

            // 查看所有集合
            System.out.println("\n2. 查看所有集合:");
            R<ShowCollectionsResponse> showResp = milvusClient.showCollections(
                    ShowCollectionsParam.newBuilder().build());

            if (showResp.getStatus() == 0) {
                ShowCollectionsResponse data = showResp.getData();
                System.out.println("   现有集合数量: " + data.getCollectionNamesCount());
                for (String name : data.getCollectionNamesList()) {
                    System.out.println("   - " + name);
                }
            } else {
                System.out.println("   ✗ 查看集合失败: " + showResp.getMessage());
            }

            // 检查目标集合
            System.out.println("\n3. 检查集合 '" + COLLECTION_NAME + "':");
            R<Boolean> hasResp = milvusClient.hasCollection(
                    HasCollectionParam.newBuilder()
                            .withCollectionName(COLLECTION_NAME)
                            .build());

            if (hasResp.getStatus() == 0) {
                boolean exists = hasResp.getData();
                if (exists) {
                    System.out.println("   ✓ 集合存在");
                } else {
                    System.out.println("   ✗ 集合不存在");
                }
            } else {
                System.out.println("   ✗ 检查失败: " + hasResp.getMessage());
            }

        } catch (Exception e) {
            System.err.println("✗ 检查失败: " + e.getMessage());
        } finally {
            if (milvusClient != null) {
                milvusClient.close();
                System.out.println("\n4. 已断开连接");
            }
        }

        System.out.println("\n=== 检查完成 ===");
    }
}