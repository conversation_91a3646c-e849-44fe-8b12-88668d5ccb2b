package com.qding.chatbi.knowledge.config;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KnowledgeServiceConfig {

    // 从 application.yml 注入 Milvus 的配置
    @Value("${langchain4j.embedding-store.milvus.host:localhost}")
    private String milvusHost;
    @Value("${langchain4j.embedding-store.milvus.port:19530}")
    private Integer milvusPort;

    @Value("${langchain4j.embedding-store.milvus.username:}")
    private String milvusUsername;
    @Value("${langchain4j.embedding-store.milvus.password:}")
    private String milvusPassword;

    /**
     * 创建一个 MilvusServiceClient Bean，用于与 Milvus 服务器交互。
     * @return MilvusServiceClient 实例
     */
    @Bean(destroyMethod = "close")
    public MilvusServiceClient milvusServiceClient() {
        return new MilvusServiceClient(
                ConnectParam.newBuilder()
                        .withHost(milvusHost)
                        .withPort(milvusPort)
                        .withAuthorization(milvusUsername, milvusPassword)
                        .build());
    }
} 