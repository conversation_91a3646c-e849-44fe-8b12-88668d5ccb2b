<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <parent>
        <groupId>com.qding.chatbi</groupId>
        <artifactId>chatbi-service</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-knowledge-service</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-metadata-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- Milvus client dependency (example) -->
        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.3.2</version> <!-- Check for the latest version -->
        </dependency>
        <!-- LangChain4j -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-dashscope</artifactId>
        </dependency>
    </dependencies>
</project>
