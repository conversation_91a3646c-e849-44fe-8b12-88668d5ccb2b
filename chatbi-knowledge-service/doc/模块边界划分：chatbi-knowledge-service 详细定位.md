# **模块边界划分：chatbi-knowledge-service 详细定位**

## **1\. 核心定位：AI 的“长期记忆”与“知识顾问”**

首先，我们需要给 chatbi-knowledge-service 一个清晰的定位。在整个系统中，它扮演着 **AI Agent 的“长期记忆”和“知识顾问”** 的角色。

它的核心职责是**封装所有与知识库（业务术语、查询范例等）存储和检索相关的底层操作**。

1. **存储知识**: 提供统一的接口，用于将“业务术语”和“查询范例”等知识**写入**其后端存储中。这个过程包括：  
   * 将结构化数据存入关系型数据库（如 MySQL）。  
   * 将需要被语义检索的文本（如术语、用户问题）通过 Embedding 模型**向量化**后，存入向量数据库（如 Milvus）。  
2. **提供检索服务**: 为 chatbi-agent-core 提供高效的知识检索接口，回答诸如“和这个问题最相似的范例是什么？”或者“‘GMV’这个词是什么意思？”这类问题。

它就像一个黑盒子，对上层调用者屏蔽了底层是使用 MySQL、Milvus 还是其他存储技术的复杂性。

## **2\. 使用场景与边界 (MySQL vs. Milvus)**

为了保证数据一致性，我们首先必须明确两个数据库各自的、不可替代的角色：

* **MySQL (事实之源 \- Source of Truth)**  
  * **定位**: 它是知识的**主存储**和**最终权威**。所有业务术语和查询范例的完整、结构化信息（ID、文本内容、元数据、创建/更新时间等）都**必须**存储在 MySQL 的表中（如 business\_terminology, query\_examples）。  
  * **使用场景**:  
    1. **精确查找**: 当需要根据 ID 获取某个术语的完整信息时。  
    2. **结构化查询**: 当后台需要根据条件（如状态、创建者）筛选、分页、展示知识列表时。  
    3. **内容获取**: 当 AI 通过 Milvus 找到相关知识的 ID 后，**必须回到 MySQL** 来获取这些 ID 对应的完整、可读的文本内容。  
* **Milvus (语义索引 \- Semantic Index)**  
  * **定位**: 它是知识的**语义索引**，本身**不存储**完整的知识内容。它只存储知识文本的数学表示（向量）和指向 MySQL 中对应记录的 ID。  
  * **使用场景**:  
    1. **相似性搜索**: 这是它**唯一**的、不可替代的用途。当 AI 需要回答“和这个问题最相似的查询范例是什么？”时，它会将用户问题的向量发送给 Milvus，Milvus 会返回最接近的几个知识点的 **ID 列表**。  
  * **关键原则**: Milvus 中的每一条向量记录，都必须能在一个 MySQL 的表中找到其对应的、完整的原始记录。Milvus 永远是 MySQL 的“附属索引”。

## **3\. 数据一致性保障策略**

所有对知识库的写操作（增、删、改）都必须被封装在 chatbi-knowledge-service 的一个事务性方法中，以确保对两个数据库的操作要么同时成功，要么都不产生影响。

### **3.1. 创建 (Create) 操作流程**

这是一个**两阶段提交**的过程，**MySQL 必须先行**。

1. **第一步：写入 MySQL**  
   * KnowledgePersistenceService 接收到业务数据（如一个新的业务术语）。  
   * 将完整的术语信息（文本、描述等）插入到 MySQL 的 business\_terminology 表中。  
   * **获取数据库自增的 ID**。这是至关重要的一步，因为这个 ID 是连接两个库的唯一桥梁。  
2. **第二步：向量化并写入 Milvus**  
   * 使用 Embedding 模型，将术语的文本内容转换为向量（Embedding）。  
   * 将**第一步获取的 ID** 和生成的向量，一同插入到 Milvus 的对应集合（Collection）中。

为什么必须先写 MySQL？  
因为 MySQL 是事实之源。如果先写 Milvus 成功，但写 MySQL 失败了，我们就会在向量库中产生一个无法找到原始文本的“孤儿向量”，这将导致后续检索出错。反之，如果写 MySQL 成功而写 Milvus 失败，我们最多只是暂时无法通过语义搜索找到这条新知识，但这不会造成数据错乱，并且我们可以通过后台任务轻松地进行数据补偿和同步。

### **3.2. 更新 (Update) 操作流程**

1. **第一步：更新 MySQL**  
   * 更新 MySQL 表中对应记录的文本或其他元数据。  
2. **第二步：更新 Milvus**  
   * 如果被更新的文本内容是用于生成向量的，则需要**重新生成向量**。  
   * 使用记录的 ID，在 Milvus 中执行 delete 操作，删除旧的向量。  
   * 执行 insert 操作，插入新的向量和相同的 ID。  
   * （注意：一些向量数据库支持 upsert 操作，可以简化这一步）。

### **3.3. 删除 (Delete) 操作流程**

这是一个**两阶段删除**的过程，顺序可以灵活，但建议**先删 Milvus**。

1. **第一步：删除 Milvus 向量**  
   * 根据要删除记录的 ID，在 Milvus 中删除对应的向量。  
2. **第二步：删除 MySQL 记录**  
   * 在 MySQL 中删除对应的整行记录。

为什么建议先删 Milvus？  
如果在删除 MySQL 记录后，删除 Milvus 向量失败，我们会产生一个“孤儿向量”。虽然这不会在界面上直接出错，但会占用向量库空间并可能在某些情况下被错误地检索到。先删索引再删主数据是更稳妥的做法。

### **3.4. 异常处理与事务**

KnowledgePersistenceService 中的写操作方法应该被 @Transactional 注解标记。如果对 MySQL 的操作失败，整个事务会回滚。如果对 Milvus 的操作失败，我们需要在 catch 块中捕获异常，并可以选择：

* **A) 立即回滚**: 手动抛出一个运行时异常，让 Spring 的 @Transactional 来回滚已经完成的 MySQL 操作。这是最能保证强一致性的做法。  
* **B) 记录并补偿**: 将失败的操作（例如，“ID为123的记录需要同步到Milvus”）记录到一个专门的失败日志表或消息队列中，由后台的定时任务进行重试和补偿。这种方式可以提高主流程的可用性，但会带来数据的短暂不一致（最终一致性）。

对于我们的项目，**方案 A (立即回滚)** 是初期更简单、更可靠的选择。

## **4\. 总结**

通过上述设计，我们为 MySQL 和 Milvus 这两个异构数据源的数据一致性问题提供了明确的解决方案：

* **定位清晰**: MySQL 是**事实**，Milvus 是**索引**。  
* **流程标准**: 所有写操作都封装在 KnowledgePersistenceService 中，并遵循“**先写事实（MySQL），后写索引（Milvus）**”的原则。  
* **事务保障**: 利用 Spring 的事务和异常处理机制，确保操作的原子性，最大限度地保证数据一致。