# **chatbi-knowledge-service 模块详细设计**

## **1\. 模块定位与职责**

在 ChatBI 的单体架构中，chatbi-knowledge-service 模块扮演着系统\*\*“知识库持久化与检索层”\*\*的核心角色。它并非一个独立的、可运行的应用，而是一个专业的服务库，为上层业务模块（如 admin-backend 和 agent-core）提供统一的知识存取能力。

**核心职责**:

1. **封装底层存储**: 作为一个“黑盒子”，完全封装对 **MySQL**（存储结构化知识）和 **Milvus**（存储语义向量）这两个异构数据源的所有 CRUD 操作。上层调用者无需关心知识的具体存储方式和位置。  
2. **提供写操作接口 (Write API)**: 向 chatbi-admin-backend 模块提供**增、删、改**知识的接口。例如，当管理员在后台新建一个业务术语时，admin-backend 会调用本模块的方法，由本模块负责将该术语的结构化数据存入 MySQL，并将其文本向量化后存入 Milvus。  
3. **提供读操作接口 (Read API)**: 向 chatbi-agent-core 模块提供**高效的知识检索接口**。例如，当 AI Agent 需要理解用户问题时，会调用本模块的方法，由本模块负责从 Milvus 中进行语义搜索，并从 MySQL 中获取匹配项的详细内容。  
4. **保障数据一致性**: 通过统一的业务封装和事务管理，确保 MySQL 和 Milvus 之间的数据状态保持一致。

## **2\. 详细设计**

### **2.1. 包结构**

com.qding.chatbi.knowledge  
├── config/  
│   └── KnowledgeServiceConfig.java      \# 配置 EmbeddingModel 和 MilvusClient  
├── service/  
│   └── KnowledgePersistenceService.java \# 核心服务类，封装所有CRUD逻辑  
└── dto/ (可选)  
    └── SearchResultDTO.java             \# 定义检索结果的数据结构

### **2.2. 配置 (KnowledgeServiceConfig.java)**

此类负责创建和配置与知识库交互所需的核心 Bean。

package com.qding.chatbi.knowledge.config;

import dev.langchain4j.model.embedding.EmbeddingModel;  
import dev.langchain4j.model.dashscope.QwenEmbeddingModel; // 示例  
import io.milvus.client.MilvusServiceClient;  
import io.milvus.param.ConnectParam;  
import org.springframework.beans.factory.annotation.Value;  
import org.springframework.context.annotation.Bean;  
import org.springframework.context.annotation.Configuration;

@Configuration  
public class KnowledgeServiceConfig {

    // 从 application.yml 注入 Embedding Model 的配置  
    @Value("${langchain4j.dashscope.embedding-model.api-key}")  
    private String embeddingApiKey;

    // 从 application.yml 注入 Milvus 的配置  
    @Value("${langchain4j.embedding-store.milvus.host}")  
    private String milvusHost;  
    @Value("${langchain4j.embedding-store.milvus.port}")  
    private Integer milvusPort;

    /\*\*  
     \* 创建一个 EmbeddingModel Bean，用于将文本转换为向量。  
     \* @return EmbeddingModel 实例  
     \*/  
    @Bean  
    public EmbeddingModel embeddingModel() {  
        return QwenEmbeddingModel.builder()  
                .apiKey(embeddingApiKey)  
                .build();  
    }

    /\*\*  
     \* 创建一个 MilvusServiceClient Bean，用于与 Milvus 服务器交互。  
     \* @return MilvusServiceClient 实例  
     \*/  
    @Bean(destroyMethod \= "close")  
    public MilvusServiceClient milvusServiceClient() {  
        return new MilvusServiceClient(  
                ConnectParam.newBuilder()  
                        .withHost(milvusHost)  
                        .withPort(milvusPort)  
                        .build());  
    }  
}

### **2.3. 核心服务 (KnowledgePersistenceService.java)**

这是本模块的**唯一入口和核心**，封装了所有操作，并着重处理数据一致性。

package com.qding.chatbi.knowledge.service;

import com.qding.chatbi.metadata.entity.BusinessTerminology; // 从metadata模块引入实体  
import com.qding.chatbi.metadata.repository.BusinessTerminologyRepository; // 从metadata模块引入仓库  
import dev.langchain4j.data.embedding.Embedding;  
import dev.langchain4j.model.embedding.EmbeddingModel;  
import io.milvus.client.MilvusServiceClient;  
import io.milvus.param.dml.InsertParam;  
import io.milvus.param.dml.SearchParam;  
import org.springframework.beans.factory.annotation.Autowired;  
import org.springframework.stereotype.Service;  
import org.springframework.transaction.annotation.Transactional;  
import java.util.List;  
import java.util.stream.Collectors;

@Service  
public class KnowledgePersistenceService {

    private static final String TERM\_COLLECTION\_NAME \= "business\_terms";

    @Autowired  
    private BusinessTerminologyRepository termRepository; // MySQL操作  
    @Autowired  
    private MilvusServiceClient milvusClient; // Milvus操作  
    @Autowired  
    private EmbeddingModel embeddingModel; // 向量化模型

    /\*\*  
     \* 创建一个新的业务术语，确保MySQL和Milvus的数据一致性。  
     \* 此方法应由 admin-backend 的服务调用。  
     \* @param term 待创建的业务术语实体  
     \*/  
    @Transactional // 确保对MySQL的操作是事务性的  
    public void createTerm(BusinessTerminology term) {  
        try {  
            // 步骤1: 先将结构化数据写入MySQL  
            BusinessTerminology savedTerm \= termRepository.save(term);  
            Long primaryKey \= savedTerm.getId();

            // 步骤2: 将术语文本向量化  
            Embedding embedding \= embeddingModel.embed(savedTerm.getBusinessTerm()).content();

            // 步骤3: 将向量和主键ID写入Milvus  
            List\<List\<?\>\> vectors \= List.of(embedding.vectorAsList());  
            List\<Long\> primaryKeys \= List.of(primaryKey);  
              
            InsertParam insertParam \= InsertParam.newBuilder()  
                    .withCollectionName(TERM\_COLLECTION\_NAME)  
                    .withPrimaryKeys(primaryKeys)  
                    .withVectors(vectors)  
                    .build();  
              
            milvusClient.insert(insertParam);

        } catch (Exception e) {  
            // 如果任何一步（特别是Milvus操作）失败，则抛出运行时异常  
            // @Transactional注解会捕获此异常，并回滚已经完成的MySQL操作  
            throw new RuntimeException("创建业务术语并同步到向量库失败", e);  
        }  
    }

    /\*\*  
     \* 根据文本进行语义搜索，返回最相似的术语列表。  
     \* 此方法应由 agent-core 的服务调用。  
     \* @param queryText 用于搜索的文本  
     \* @param topK 返回结果的数量  
     \* @return 匹配的业务术语实体列表  
     \*/  
    public List\<BusinessTerminology\> searchSimilarTerms(String queryText, int topK) {  
        // 步骤1: 将查询文本向量化  
        Embedding queryEmbedding \= embeddingModel.embed(queryText).content();

        // 步骤2: 在Milvus中执行相似性搜索  
        SearchParam searchParam \= SearchParam.newBuilder()  
                .withCollectionName(TERM\_COLLECTION\_NAME)  
                .withVectors(List.of(queryEmbedding.vectorAsList()))  
                .withTopK(topK)  
                .build();  
          
        List\<Long\> resultIds \= milvusClient.search(searchParam)  
                                    .getResults()  
                                    .get(0) // 获取第一个查询向量的结果  
                                    .getIds()  
                                    .stream()  
                                    .map(id \-\> (Long) id)  
                                    .collect(Collectors.toList());  
          
        if (resultIds.isEmpty()) {  
            return List.of();  
        }

        // 步骤3: 使用从Milvus获取的ID，回到MySQL查询完整的术语信息  
        return termRepository.findAllById(resultIds);  
    }  
      
    // ... 此处应包含 updateTerm 和 deleteTerm 的实现，同样遵循数据一致性策略 ...  
}

## **4\. 总结**

chatbi-knowledge-service 模块通过将底层存储操作（MySQL \+ Milvus）封装在一个统一的 Service 中，成功地为上层应用（admin-backend 和 agent-core）提供了清晰、简单且可靠的知识存取接口。

* **对于 admin-backend**: 它只需要关心业务逻辑，调用 knowledgeService.createTerm() 即可，无需知道背后复杂的向量化和双写过程。  
* **对于 agent-core**: 它只需要关心如何提问，调用 knowledgeService.searchSimilarTerms() 即可获得知识，无需关心底层的向量检索和数据库查询。  
* **数据一致性**: 通过**先写MySQL、后写Milvus**的策略，并结合Spring的**事务管理**，最大限度地保证了两个异构数据库之间的数据同步和一致性。

这个设计确保了本模块的高度内聚和专业性，是构建一个健壮的 RAG 系统的坚实基础。