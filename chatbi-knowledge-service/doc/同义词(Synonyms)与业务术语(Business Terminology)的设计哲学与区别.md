# **同义词(Synonyms)与业务术语(Business Terminology)的设计哲学与区别**

## **1\. 核心区别：一个是谁，一个是什么**

为了快速理解，我们可以用一个简单的类比：

* **synonyms (同义词)**: 相当于一个实体（数据集或字段）的\*\*“小名”或“别名”**。它的作用是告诉 AI：“当用户提到‘GMV’、‘成交额’或‘销售额’时，它们指的都是同一个东西——sale\_amount 这个字段”。它解决的是**识别 (Identification)\*\* 的问题。  
* **business\_terminology (业务术语)**: 相当于为某个“概念”（比如 GMV）建立的一张\*\*“知识卡片”或“百科词条”**。它不仅告诉 AI “GMV”最终指向哪个字段，更重要的是，它首先解释了**“GMV 是什么”**（例如：“商品交易总额，是公司的核心指标”）。它解决的是**理解 (Comprehension)\*\* 和**消歧 (Disambiguation)** 的问题。

## **2\. 详细定位与职责**

### **2.1. synonyms (同义词) \- 紧密绑定的“别名”**

* **定位**:  
  * **范围**: 它的作用域是**局部的 (Local)**，紧密地绑定在某一个具体的数据集或字段上。  
  * **关系**: 是一种**直接且强映射**的关系。\["GMV", "成交额"\] 就是 sale\_amount，不存在歧义。  
* **核心价值**:  
  1. **提升查询效率和召回率**: 这是它的主要价值。当 AI 的 NLU 模块解析用户问题时，它可以非常快速地通过匹配 synonyms 列表来定位到具体的字段。例如，用户问“查成交额”，系统可以直接匹配到 sale\_amount 字段，无需进行复杂的推理。  
  2. **简化管理**: 管理员在配置一个字段时，可以顺手把它所有常见的叫法都加上，配置路径很短。  
* **例子**:  
  * 在“每日销售汇总”数据集中，sale\_amount 字段的 synonyms 设置为 \["GMV", "成交额"\]。  
  * 当用户提问：“查一下北京的成交额和订单数”，AI 能迅速识别“成交额”就是sale\_amount，“订单数”是order\_count（假设其同义词包含“订单数”）。

### **2.2. business\_terminology (业务术语) \- 解耦的“知识卡片”**

* **定位**:  
  * **范围**: 它的作用域是**全局的 (Global)**，是整个公司层面统一的知识库。  
  * **关系**: 是一种\*\*“概念-实体”的间接映射\*\*，并附带了丰富的上下文。它首先定义一个概念，然后再将这个概念链接到系统中的具体实现。  
* **核心价值**:  
  1. **处理复杂和抽象的术语**: 对于一些复杂的、有特定计算口径的术语（如“月活留存率”、“客单价”、“净利率”），简单地添加同义词是不够的。business\_terminology 可以提供详细的业务描述和计算逻辑，帮助 AI 更好地理解这些概念。  
  2. **支持问答和澄清**: 当用户直接提问“什么是GMV？”时，AI 可以直接从 business\_terminology 表中查询 context\_description 字段来回答用户，使 AI 表现得更像一个真正的业务专家。  
  3. **解决歧义**: 在复杂的场景下，同一个术语可能在不同数据集中有不同的含义。例如，“活跃用户”在“App日活数据”集中可能指启动App的用户，而在“交易数据”集中可能指下单的用户。business\_terminology 可以记录这些不同上下文的定义，帮助 AI 在对话中进行澄清：“您是指App的活跃用户，还是指下单的活跃用户？”  
  4. **跨数据集的知识链接**: 它可以将一个全局的业务概念（如“核心客户”）链接到多个数据集中相关的字段上。

## **3\. 协同工作的流程**

synonyms 和 business\_terminology 并非互相替代，而是协同工作，构成了一个由浅入深的知识体系。

**一个典型的 AI 处理流程如下：**

**用户提问**: “对比一下北京和上海上个月的GMV和客单价”

1. **快速匹配 (Synonyms)**: AI 的 NLU 模块首先进行快速的关键词匹配。它可能会在 dataset\_columns 的 synonyms 字段中直接找到“客单价”这个别名，从而快速定位到一个计算指标。  
2. **概念查询 (Business Terminology)**: 对于“GMV”，AI 可能无法在任何一个 column\_name 或 synonyms 中直接找到完全匹配的项。此时，它会查询 business\_terminology 表。  
3. **获取知识**: AI 在 business\_terminology 表中找到了 business\_term \= 'GMV' 的记录，并从中了解到：  
   * **它的含义**: “商品交易总额...”  
   * **它的映射**: 它映射到标准概念“销售额”。  
4. **二次定位**: 现在，AI 的任务变成了“寻找代表‘销售额’的字段”。它会再次扫描所有可访问数据集的字段，这次它会去匹配 column\_name 为“销售额”或 synonyms 中包含“销售额”的字段。  
5. **组合结果**: 最终，AI 成功地将“GMV”和“客单价”都定位到了“每日销售汇总”数据集中的具体字段或计算表达式上，从而完成后续的 SQL 生成。

## **4\. 总结**

| 特性 | synonyms (同义词) | business\_terminology (业务术语) |
| :---- | :---- | :---- |
| **定位** | 字段/数据集的“**小名**” | 公司级的“**百科卡片**” |
| **作用域** | **局部** (绑定到单个实体) | **全局** (跨数据集的知识) |
| **核心功能** | **快速识别**和召回 | **深度理解**和消歧 |
| **解决问题** | “它还叫什么？” | “它是什么？它意味着什么？” |
| **存在价值** | **效率**: 让 AI 快速处理常见、无歧义的别名。 | **智能**: 让 AI 能处理复杂、抽象的业务概念，并能与用户进行知识问答。 |

因此，同时拥有这两个字段，能让我们的 ChatBI 系统在**效率**和**智能**两个维度上都达到一个更高的水平，是构建一个强大、专业的对话式分析系统的必要设计。