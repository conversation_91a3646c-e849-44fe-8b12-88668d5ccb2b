#!/bin/bash

# ChatBI Application 测试环境启动脚本

echo "正在启动 ChatBI Application (测试环境)..."

# 设置JVM参数
export JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"

# 设置Spring Profile
export SPRING_PROFILES_ACTIVE=test

# 创建日志目录
mkdir -p /data/logs/

# 启动应用
java $JAVA_OPTS -jar chatbi-application-1.0-SNAPSHOT.jar \
  --spring.profiles.active=test \
  --server.port=8081 \
  > /data/app.log 2>&1 < /dev/null &

echo "ChatBI Application (测试环境) 启动完成!"
