#!/bin/bash

# ChatBI Application 生产环境启动脚本

echo "正在启动 ChatBI Application (生产环境)..."

# 检查必需的环境变量
if [ -z "$DB_USERNAME" ] || [ -z "$DB_PASSWORD" ] || [ -z "$DASHSCOPE_API_KEY" ] || [ -z "$ACTUATOR_PASSWORD" ]; then
    echo "错误: 缺少必需的环境变量"
    echo "请设置: DB_USERNAME, DB_PASSWORD, DASHSCOPE_API_KEY, ACTUATOR_PASSWORD"
    echo "可选设置: ACTUATOR_USERNAME (默认: admin)"
    exit 1
fi

# 设置JVM参数 (生产环境)
export JAVA_OPTS="-Xms1g -Xmx4g -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/chatbi/"

# 设置Spring Profile
export SPRING_PROFILES_ACTIVE=prod

# 创建日志目录
sudo mkdir -p /var/log/chatbi
sudo chown $(whoami):$(whoami) /var/log/chatbi

# 启动应用
nohup java $JAVA_OPTS -jar chatbi-application-1.0-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  --server.port=8080 \
  > /var/log/chatbi/startup.log 2>&1 < /dev/null &

# 获取进程ID
PID=$!
echo $PID > /var/run/chatbi-application.pid

echo "ChatBI Application (生产环境) 启动完成! PID: $PID"
echo "日志文件: /var/log/chatbi/"
echo "启动日志: /var/log/chatbi/startup.log"
