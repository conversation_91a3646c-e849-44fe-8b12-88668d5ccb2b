#!/bin/bash

# ChatBI SSE真实进度测试脚本
# 测试完全基于真实处理时长的SSE流式接口（零模拟时间）

echo "=========================================="
echo "ChatBI SSE真实进度测试 - 零模拟时间版本"
echo "=========================================="

# 服务器配置
SERVER_URL="http://localhost:8080"
SSE_ENDPOINT="/api/v1/chatbi/conversation/query-stream"

# 测试数据
USER_ID="test_user_$(date +%s)"
SESSION_ID="test_session_$(date +%s)"

echo "测试配置:"
echo "- 服务器: $SERVER_URL"
echo "- 用户ID: $USER_ID"
echo "- 会话ID: $SESSION_ID"
echo "- 特性: 真实进度反馈，不使用模拟时间"
echo ""

# 测试用例
declare -a test_queries=(
    "查询销售数据"
    "显示最近一个月的订单统计"
    "按城市分组显示用户数量"
    "查看产品销售排行榜"
    "分析用户行为趋势"
)

# 测试角色
declare -a test_roles=(
    ""
    "数据分析师"
    "业务人员"
    "管理层"
)

# 函数：测试SSE查询
test_sse_query() {
    local query="$1"
    local test_num="$2"
    local role="$3"

    echo "----------------------------------------"
    echo "测试 $test_num: $query"
    if [[ -n "$role" ]]; then
        echo "角色: $role"
    else
        echo "角色: 游客 (无角色)"
    fi
    echo "----------------------------------------"

    # 构建请求体
    local additional_params='{"timeZone": "Asia/Shanghai"}'
    if [[ -n "$role" ]]; then
        additional_params='{"timeZone": "Asia/Shanghai", "userRole": "'$role'"}'
    fi

    local request_body=$(cat <<EOF
{
    "userId": "$USER_ID",
    "sessionId": "$SESSION_ID",
    "queryText": "$query",
    "additionalParams": $additional_params
}
EOF
)
    
    echo "发送SSE请求..."
    echo "请求体: $request_body"
    echo ""
    
    # 发送SSE请求并处理响应
    curl -N -H "Accept: text/event-stream" \
         -H "Content-Type: application/json" \
         -H "Cache-Control: no-cache" \
         -X POST \
         -d "$request_body" \
         "$SERVER_URL$SSE_ENDPOINT" \
         --max-time 60 \
         --connect-timeout 10 \
         2>/dev/null | while IFS= read -r line; do
        
        # 处理SSE事件
        if [[ $line == data:* ]]; then
            # 提取JSON数据
            json_data="${line#data: }"
            
            # 解析事件类型和消息
            event_type=$(echo "$json_data" | jq -r '.type // "unknown"' 2>/dev/null)
            message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
            progress=$(echo "$json_data" | jq -r '.progress // -1' 2>/dev/null)
            timestamp=$(echo "$json_data" | jq -r '.timestamp // 0' 2>/dev/null)
            
            # 格式化时间戳
            if [[ $timestamp != "0" && $timestamp != "null" ]]; then
                formatted_time=$(date -r $((timestamp/1000)) '+%H:%M:%S' 2>/dev/null || echo "unknown")
            else
                formatted_time=$(date '+%H:%M:%S')
            fi
            
            # 根据事件类型显示不同的信息
            case $event_type in
                "progress")
                    if [[ $progress != "-1" && $progress != "null" ]]; then
                        printf "[%s] 进度: %s%% - %s\n" "$formatted_time" "$progress" "$message"
                    else
                        printf "[%s] 状态: %s\n" "$formatted_time" "$message"
                    fi
                    ;;
                "thinking")
                    printf "[%s] 💭 思考: %s\n" "$formatted_time" "$message"
                    ;;
                "sql_generation")
                    printf "[%s] 🔧 SQL生成: %s\n" "$formatted_time" "$message"
                    ;;
                "data_retrieval")
                    printf "[%s] 📊 数据检索: %s\n" "$formatted_time" "$message"
                    ;;
                "response_generation")
                    printf "[%s] 📝 响应生成: %s\n" "$formatted_time" "$message"
                    ;;
                "result")
                    printf "[%s] ✅ 结果: 查询完成\n" "$formatted_time"
                    # 解析响应类型
                    response_type=$(echo "$json_data" | jq -r '.responseType // "unknown"' 2>/dev/null)
                    message_to_user=$(echo "$json_data" | jq -r '.messageToUser // ""' 2>/dev/null)
                    thought_process=$(echo "$json_data" | jq -r '.thoughtProcess // ""' 2>/dev/null)
                    printf "    响应类型: %s\n" "$response_type"
                    if [[ $message_to_user != "" && $message_to_user != "null" ]]; then
                        printf "    消息: %s\n" "$message_to_user"
                    fi
                    if [[ $thought_process != "" && $thought_process != "null" ]]; then
                        printf "    🧠 思考过程: 已包含 (%d 字符)\n" "${#thought_process}"
                    fi
                    ;;
                "performance")
                    printf "[%s] 📈 性能统计:\n" "$formatted_time"
                    total_time=$(echo "$json_data" | jq -r '.totalTime // 0' 2>/dev/null)
                    query_time=$(echo "$json_data" | jq -r '.queryTime // 0' 2>/dev/null)
                    overhead=$(echo "$json_data" | jq -r '.overhead // 0' 2>/dev/null)
                    printf "    总耗时: %sms, 查询耗时: %sms, 处理开销: %sms\n" "$total_time" "$query_time" "$overhead"
                    ;;
                "error")
                    printf "[%s] ❌ 错误: %s\n" "$formatted_time" "$message"
                    ;;
                *)
                    printf "[%s] 未知事件: %s - %s\n" "$formatted_time" "$event_type" "$message"
                    ;;
            esac
        elif [[ $line == event:* ]]; then
            # 事件名称行，可以忽略或记录
            event_name="${line#event: }"
            # printf "事件: %s\n" "$event_name"
        fi
    done
    
    echo ""
    echo "测试 $test_num 完成"
    echo ""
}

# 检查服务器是否可用
echo "检查服务器连接..."
if ! curl -s --connect-timeout 5 "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo "❌ 无法连接到服务器: $SERVER_URL"
    echo "请确保ChatBI服务正在运行"
    exit 1
fi
echo "✅ 服务器连接正常"
echo ""

# 检查jq是否安装
if ! command -v jq &> /dev/null; then
    echo "⚠️  警告: 未安装jq，JSON解析可能不完整"
    echo "建议安装jq: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
    echo ""
fi

# 执行测试用例
test_count=0
for i in "${!test_queries[@]}"; do
    for j in "${!test_roles[@]}"; do
        test_count=$((test_count + 1))
        query="${test_queries[$i]}"
        role="${test_roles[$j]}"

        test_sse_query "$query" "$test_count" "$role"

        # 测试间隔
        if [[ $test_count -lt $((${#test_queries[@]} * ${#test_roles[@]})) ]]; then
            echo "等待3秒后进行下一个测试..."
            sleep 3
        fi
    done
done

echo "=========================================="
echo "所有测试完成"
echo "=========================================="

# 测试总结
echo ""
echo "测试总结:"
echo "- 测试了 ${#test_queries[@]} 个查询"
echo "- 测试了 ${#test_roles[@]} 个角色"
echo "- 总共执行了 $((${#test_queries[@]} * ${#test_roles[@]})) 个测试用例"
echo "- 验证了SSE实时进度反馈功能"
echo "- 检查了各个处理阶段的时间统计"
echo "- 验证了AI思考过程的显示"
echo "- 测试了角色权限功能"
echo ""
echo "如果看到完整的进度更新、性能统计和思考过程，说明SSE功能正常工作"
