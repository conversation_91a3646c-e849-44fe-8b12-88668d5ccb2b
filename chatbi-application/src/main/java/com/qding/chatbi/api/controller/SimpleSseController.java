package com.qding.chatbi.api.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 简单的SSE示例控制器
 * 展示SSE（Server-Sent Events）的基本用法
 */
@RestController
@RequestMapping("/api/v1/chatbi/sse-demo")
public class SimpleSseController {

    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(2);

    /**
     * 简单的SSE示例
     * 发送几条消息然后结束连接
     *
     * @param name 用户名
     * @return SseEmitter对象
     */
    @GetMapping(value = "/simple", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter simpleSse(@RequestParam(required = false, defaultValue = "User") String name) {
        // 创建SseEmitter实例，设置超时时间为10秒
        SseEmitter emitter = new SseEmitter(10000L);

        // 异步发送消息
        executor.submit(() -> {
            try {
                // 发送欢迎消息
                emitter.send("Hello, " + name + "! Welcome to SSE demo.");

                // 发送几条示例消息
                for (int i = 1; i <= 5; i++) {
                    Thread.sleep(1000); // 模拟处理时间
                    emitter.send("Message #" + i + " from server");
                }

                // 发送结束消息
                emitter.send("Demo completed. Connection will close now.");
                
                // 关闭连接
                emitter.complete();
            } catch (IOException e) {
                emitter.completeWithError(e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 实时计数器示例
     * 每秒发送递增的数字
     *
     * @param limit 计数上限
     * @return SseEmitter对象
     */
    @GetMapping(value = "/counter", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter counter(@RequestParam(required = false, defaultValue = "10") int limit) {
        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        executor.scheduleAtFixedRate(() -> {
            try {
                // 发送当前时间戳作为计数
                long count = System.currentTimeMillis() % 1000000;
                emitter.send("Count: " + count);
                
                // 如果达到限制则完成连接
                if (count % limit == 0) {
                    emitter.send("Counter reached limit. Closing connection.");
                    emitter.complete();
                }
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }, 0, 1, TimeUnit.SECONDS); // 立即开始，每1秒执行一次

        return emitter;
    }

    /**
     * 自定义事件类型示例
     * 展示如何发送不同类型的事件
     *
     * @return SseEmitter对象
     */
    @GetMapping(value = "/events", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter customEvents() {
        SseEmitter emitter = new SseEmitter(20000L);

        executor.submit(() -> {
            try {
                // 发送状态事件
                emitter.send(SseEmitter.event()
                        .name("status")
                        .data("Connection established"));

                Thread.sleep(1000);

                // 发送进度事件
                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data("Task 1 completed")
                        .id("task-1"));

                Thread.sleep(1000);

                // 发送数据事件
                emitter.send(SseEmitter.event()
                        .name("data")
                        .data("{\"message\": \"This is sample data\", \"value\": 123}"));

                Thread.sleep(1000);

                // 发送完成事件
                emitter.send(SseEmitter.event()
                        .name("complete")
                        .data("All tasks completed successfully"));

                emitter.complete();
            } catch (IOException | InterruptedException e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }
}