package com.qding.chatbi.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 展示 CompletableFuture.runAsync 和 SSE 结合使用的示例控制器
 * 
 * 这个控制器演示了如何在 ChatBI 项目中结合使用 CompletableFuture.runAsync() 和 SSE (Server-Sent Events)
 * 来处理长时间运行的任务，同时向客户端提供实时进度更新。
 */
@RestController
@RequestMapping("/api/v1/chatbi/sse-async-demo")
public class SseWithCompletableFutureController {

    private static final Logger log = LoggerFactory.getLogger(SseWithCompletableFutureController.class);
    
    // 创建一个专用的线程池来处理异步任务
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 基本的 SSE + CompletableFuture 示例
     * 
     * 这个端点演示了如何使用 CompletableFuture.runAsync() 在后台执行长时间运行的任务，
     * 同时通过 SSE 向客户端发送实时进度更新。
     * 
     * @param taskId 任务ID，用于标识不同的任务
     * @return SseEmitter 对象，用于发送事件流
     */
    @GetMapping(value = "/basic", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter basicExample(@RequestParam(defaultValue = "task-1") String taskId) {
        // 创建一个 SseEmitter，设置超时时间为5分钟
        SseEmitter emitter = new SseEmitter(300000L);
        
        log.info("开始处理任务: {}", taskId);
        
        // 使用 CompletableFuture.runAsync() 异步执行任务
        CompletableFuture.runAsync(() -> {
            try {
                // 发送开始事件
                sendEvent(emitter, "status", "任务开始执行: " + taskId);
                
                // 模拟一个多阶段的长时间运行任务
                for (int i = 1; i <= 5; i++) {
                    // 模拟工作负载
                    Thread.sleep(1000);
                    
                    // 计算进度
                    int progress = i * 20;
                    
                    // 发送进度更新事件
                    sendEvent(emitter, "progress", 
                        String.format("任务 %s 完成进度: %d%%", taskId, progress));
                    
                    log.info("任务 {} 进度: {}%", taskId, progress);
                }
                
                // 发送完成事件
                sendEvent(emitter, "result", 
                    String.format("任务 %s 执行完成，结果: SUCCESS", taskId));
                sendEvent(emitter, "status", "任务执行完毕");
                
                // 完成 SSE 连接
                emitter.complete();
                
                log.info("任务 {} 执行完成", taskId);
            } catch (Exception e) {
                log.error("任务 {} 执行过程中发生错误", taskId, e);
                
                try {
                    // 发送错误事件
                    sendEvent(emitter, "error", "任务执行失败: " + e.getMessage());
                } catch (IOException ioException) {
                    log.error("发送错误事件失败", ioException);
                }
                
                // 标记 SSE 连接完成但带有错误
                emitter.completeWithError(e);
            }
        }, executorService); // 使用自定义线程池
        
        // 立即返回 emitter，不阻塞主线程
        return emitter;
    }

    /**
     * 复杂任务处理示例
     * 
     * 这个端点演示了更复杂的场景，其中包含多个阶段的任务处理，
     * 每个阶段都可以向客户端发送不同类型的事件。
     * 
     * @param taskId 任务ID
     * @param duration 任务持续时间（秒）
     * @return SseEmitter 对象
     */
    @GetMapping(value = "/complex", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter complexExample(
            @RequestParam(defaultValue = "complex-task-1") String taskId,
            @RequestParam(defaultValue = "10") int duration) {
        
        SseEmitter emitter = new SseEmitter(300000L);
        
        log.info("开始处理复杂任务: {}, 预计耗时: {}秒", taskId, duration);
        
        // 使用 CompletableFuture.runAsync() 异步执行复杂任务
        CompletableFuture.runAsync(() -> {
            try {
                // 发送任务开始事件
                sendEvent(emitter, "status", 
                    String.format("开始处理复杂任务: %s，预计耗时: %d秒", taskId, duration));
                
                // 阶段1: 数据准备 (20%)
                sendEvent(emitter, "phase", "阶段1: 数据准备");
                simulateWork(emitter, taskId, 1, duration / 4);
                sendEvent(emitter, "progress", "任务进度: 20%");
                
                // 阶段2: 数据处理 (50%)
                sendEvent(emitter, "phase", "阶段2: 数据处理");
                simulateWork(emitter, taskId, 2, duration / 4);
                sendEvent(emitter, "progress", "任务进度: 50%");
                
                // 阶段3: 数据分析 (80%)
                sendEvent(emitter, "phase", "阶段3: 数据分析");
                simulateWork(emitter, taskId, 3, duration / 4);
                sendEvent(emitter, "progress", "任务进度: 80%");
                
                // 阶段4: 结果生成 (100%)
                sendEvent(emitter, "phase", "阶段4: 结果生成");
                simulateWork(emitter, taskId, 4, duration / 4);
                sendEvent(emitter, "progress", "任务进度: 100%");
                
                // 发送最终结果
                sendEvent(emitter, "result", 
                    String.format("复杂任务 %s 执行完成，生成了1000条记录", taskId));
                sendEvent(emitter, "status", "任务执行完毕");
                
                emitter.complete();
                
                log.info("复杂任务 {} 执行完成", taskId);
            } catch (Exception e) {
                log.error("复杂任务 {} 执行过程中发生错误", taskId, e);
                
                try {
                    sendEvent(emitter, "error", "任务执行失败: " + e.getMessage());
                } catch (IOException ioException) {
                    log.error("发送错误事件失败", ioException);
                }
                
                emitter.completeWithError(e);
            }
        }, executorService);
        
        return emitter;
    }

    /**
     * 并行任务处理示例
     * 
     * 这个端点演示了如何并行处理多个子任务，并将它们的进度汇总后发送给客户端。
     * 
     * @param taskId 主任务ID
     * @param subTaskCount 子任务数量
     * @return SseEmitter 对象
     */
    @GetMapping(value = "/parallel", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter parallelExample(
            @RequestParam(defaultValue = "parallel-task-1") String taskId,
            @RequestParam(defaultValue = "3") int subTaskCount) {
        
        SseEmitter emitter = new SseEmitter(300000L);
        
        log.info("开始处理并行任务: {}, 子任务数量: {}", taskId, subTaskCount);
        
        // 使用 CompletableFuture.runAsync() 异步执行并行任务
        CompletableFuture.runAsync(() -> {
            try {
                sendEvent(emitter, "status", 
                    String.format("开始处理并行任务: %s，包含 %d 个子任务", taskId, subTaskCount));
                
                // 创建多个并行的子任务
                CompletableFuture<?>[] futures = new CompletableFuture[subTaskCount];
                
                for (int i = 0; i < subTaskCount; i++) {
                    final int subTaskIndex = i;
                    futures[i] = CompletableFuture.runAsync(() -> {
                        try {
                            String subTaskId = taskId + "-sub-" + subTaskIndex;
                            
                            // 发送子任务开始事件
                            sendEvent(emitter, "subtask-start", 
                                String.format("子任务 %s 开始执行", subTaskId));
                            
                            // 模拟子任务执行
                            Thread.sleep(2000 + (subTaskIndex * 1000));
                            
                            // 发送子任务完成事件
                            sendEvent(emitter, "subtask-complete", 
                                String.format("子任务 %s 执行完成", subTaskId));
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        } catch (IOException e) {
                            log.error("发送子任务事件失败", e);
                        }
                    }, executorService);
                }
                
                // 等待所有子任务完成
                CompletableFuture.allOf(futures).join();
                
                // 发送主任务完成事件
                sendEvent(emitter, "result", 
                    String.format("并行任务 %s 执行完成，所有 %d 个子任务已完成", taskId, subTaskCount));
                sendEvent(emitter, "status", "任务执行完毕");
                
                emitter.complete();
                
                log.info("并行任务 {} 执行完成", taskId);
            } catch (Exception e) {
                log.error("并行任务 {} 执行过程中发生错误", taskId, e);
                
                try {
                    sendEvent(emitter, "error", "任务执行失败: " + e.getMessage());
                } catch (IOException ioException) {
                    log.error("发送错误事件失败", ioException);
                }
                
                emitter.completeWithError(e);
            }
        }, executorService);
        
        return emitter;
    }

    /**
     * 模拟工作负载
     * 
     * @param emitter SseEmitter 对象
     * @param taskId 任务ID
     * @param phase 阶段编号
     * @param seconds 工作时间（秒）
     * @throws InterruptedException 如果线程被中断
     * @throws IOException 如果发送事件失败
     */
    private void simulateWork(SseEmitter emitter, String taskId, int phase, int seconds) 
            throws InterruptedException, IOException {
        for (int i = 1; i <= seconds; i++) {
            Thread.sleep(1000);
            sendEvent(emitter, "work", 
                String.format("任务 %s 阶段 %d 执行中... (%d/%d秒)", taskId, phase, i, seconds));
        }
    }

    /**
     * 发送 SSE 事件的辅助方法
     * 
     * @param emitter SseEmitter 对象
     * @param eventType 事件类型
     * @param data 事件数据
     * @throws IOException 如果发送事件失败
     */
    private void sendEvent(SseEmitter emitter, String eventType, String data) throws IOException {
        emitter.send(SseEmitter.event()
                .name(eventType)
                .data(data));
    }

    /**
     * 应用关闭时关闭线程池
     */
    public void shutdown() {
        executorService.shutdown();
    }
}