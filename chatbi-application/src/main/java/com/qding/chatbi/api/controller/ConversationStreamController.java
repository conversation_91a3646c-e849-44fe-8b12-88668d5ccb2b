package com.qding.chatbi.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.api.service.ConversationApi;
import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.UserQueryRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * SSE流式对话控制器
 * 提供实时进度反馈的对话接口
 */
@RestController
@RequestMapping("/api/v1/chatbi/conversation")
public class ConversationStreamController {

    /**
     * SSE进度回调接口
     */
    public interface ProgressCallback {
        void updateProgress(int progress, String message);
        void sendThinking(String thought);
        void sendSqlGeneration(String message);
        void sendDataRetrieval(String message);
        void sendResponseGeneration(String message);
        boolean shouldContinue();
    }

    private static final Logger log = LoggerFactory.getLogger(ConversationStreamController.class);

    private final ConversationApi conversationService;
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService;

    @Autowired
    public ConversationStreamController(ConversationApi conversationService, ObjectMapper objectMapper) {
        this.conversationService = conversationService;
        this.objectMapper = objectMapper;
        this.executorService = Executors.newCachedThreadPool();
    }

    /**
     * SSE流式查询接口
     * 提供实时进度反馈
     */
    @PostMapping(value = "/query-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter handleUserQueryStream(@RequestBody @Valid UserQueryRequest request) {
        log.info("==================== 收到SSE流式查询请求 ====================");
        log.info("用户ID: {}, 会话ID: {}, 查询内容: {}",
                request.getUserId(), request.getSessionId(), request.getQueryText());

        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 异步处理查询
        CompletableFuture.runAsync(() -> {
            try {
                processQueryWithProgress(request, emitter);
            } catch (Exception e) {
                log.error("SSE查询处理异常", e);
                try {
                    sendError(emitter, "查询处理失败: " + e.getMessage());
                } catch (IOException ioException) {
                    log.error("发送错误消息失败", ioException);
                }
                emitter.completeWithError(e);
            }
        }, executorService);

        return emitter;
    }

    /**
     * 带进度反馈的查询处理
     * 使用真实的处理阶段时间，完全不使用模拟时间
     */
    private void processQueryWithProgress(UserQueryRequest request, SseEmitter emitter) throws IOException {
        long startTime = System.currentTimeMillis();

        try {
            // 创建进度回调实现
            ProgressCallback progressCallback = new SseProgressCallback(emitter, objectMapper);

            // 1. 开始处理
            long stepStartTime = System.currentTimeMillis();
            progressCallback.updateProgress(0, "开始处理您的查询...");

            // 2. 参数验证
            progressCallback.updateProgress(5, "验证查询参数...");
            if (request == null || request.getQueryText() == null || request.getQueryText().trim().isEmpty()) {
                sendError(emitter, "查询参数无效");
                return;
            }
            long validationTime = System.currentTimeMillis() - stepStartTime;
            progressCallback.updateProgress(10, String.format("参数验证完成 (耗时: %dms)", validationTime));

            // 3. 创建增强的ConversationService来获取真实进度
            RealTimeProgressTracker progressTracker = new RealTimeProgressTracker(progressCallback);

            // 4. 实际调用后端服务，使用真实的进度跟踪
            long queryStartTime = System.currentTimeMillis();
            AgentResponse response = processWithRealTimeProgress(request, progressTracker);
            long queryTime = System.currentTimeMillis() - queryStartTime;

            // 5. 完成
            long totalTime = System.currentTimeMillis() - startTime;
            progressCallback.updateProgress(100, String.format("查询处理完成 (总耗时: %dms)", totalTime));

            // 发送最终结果
            sendResult(emitter, response);

            // 发送性能统计
            sendPerformanceStats(emitter, totalTime, queryTime);

            emitter.complete();

            log.info("==================== SSE查询处理完成 ====================");
            log.info("总耗时: {}ms, 实际查询耗时: {}ms", totalTime, queryTime);

        } catch (Exception e) {
            log.error("查询处理失败", e);
            sendError(emitter, "查询处理失败: " + e.getMessage());
            emitter.completeWithError(e);
        }
    }

    /**
     * 使用真实处理流程时长的进度跟踪
     * 完全不使用Thread.sleep()模拟，直接监控真实处理过程
     */
    private AgentResponse processWithRealTimeProgress(UserQueryRequest request, RealTimeProgressTracker tracker) {
        try {
            // 1. 开始处理
            tracker.updateProgress(10, "开始处理查询请求...");

            // 2. 实际调用conversationService，同时监控真实进度
            long totalStart = System.currentTimeMillis();

            // 创建一个包装的请求，用于在处理过程中发送进度更新
            ProgressAwareRequest progressRequest = new ProgressAwareRequest(request, tracker);

            // 调用真实的处理流程
            AgentResponse response = processWithProgressMonitoring(progressRequest);

            long totalTime = System.currentTimeMillis() - totalStart;
            tracker.updateProgress(100, String.format("处理完成 (总耗时: %dms)", totalTime));

            return response;

        } catch (Exception e) {
            log.error("真实进度跟踪处理异常", e);
            tracker.updateProgress(-1, "处理失败: " + e.getMessage());
            throw new RuntimeException("查询处理失败", e);
        }
    }

    /**
     * 带进度监控的真实处理流程
     * 完全基于真实处理时间，不使用任何模拟延时
     */
    private AgentResponse processWithProgressMonitoring(ProgressAwareRequest progressRequest) {
        RealTimeProgressTracker tracker = progressRequest.getTracker();
        UserQueryRequest request = progressRequest.getRequest();

        try {
            // 记录整个处理过程的开始时间
            long totalStart = System.currentTimeMillis();

            // 阶段1: 开始处理 (15%)
            tracker.updateProgress(15, "开始理解查询意图...");
            tracker.sendThinking("正在分析查询语义，识别关键实体和意图...");

            // 创建一个监控线程来跟踪真实处理进度
            ProgressMonitoringTask monitoringTask = new ProgressMonitoringTask(tracker);
            CompletableFuture<Void> monitoringFuture = CompletableFuture.runAsync(monitoringTask, executorService);

            // 实际调用conversationService - 这是真实的处理时间
            long serviceStart = System.currentTimeMillis();
            AgentResponse response = conversationService.processUserQuery(request);
            long serviceTime = System.currentTimeMillis() - serviceStart;

            // 停止监控
            monitoringTask.stop();

            // 等待监控任务完成
            try {
                monitoringFuture.get(1000, java.util.concurrent.TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.warn("监控任务完成异常", e);
            }

            // 最终进度更新
            long totalTime = System.currentTimeMillis() - totalStart;
            tracker.updateProgress(95, String.format("处理完成 (服务耗时: %dms, 总耗时: %dms)", serviceTime, totalTime));

            return response;

        } catch (Exception e) {
            log.error("进度监控处理异常", e);
            tracker.updateProgress(-1, "处理失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 进度监控任务 - 基于真实时间的进度跟踪
     */
    private static class ProgressMonitoringTask implements Runnable {
        private final RealTimeProgressTracker tracker;
        private volatile boolean running = true;
        private final long startTime;

        public ProgressMonitoringTask(RealTimeProgressTracker tracker) {
            this.tracker = tracker;
            this.startTime = System.currentTimeMillis();
        }

        public void stop() {
            this.running = false;
        }

        @Override
        public void run() {
            try {
                int currentProgress = 20;
                String[] phases = {
                    "分析查询语义...",
                    "识别关键实体...",
                    "理解查询意图...",
                    "生成SQL查询...",
                    "验证查询安全性...",
                    "执行数据查询...",
                    "处理查询结果...",
                    "生成分析报告..."
                };

                int phaseIndex = 0;
                while (running && currentProgress < 90 && phaseIndex < phases.length) {
                    long elapsed = System.currentTimeMillis() - startTime;

                    // 基于真实耗时动态调整进度
                    if (elapsed > 1000) { // 1秒后
                        currentProgress = Math.min(40, 20 + (int)(elapsed / 100)); // NLU阶段
                    }
                    if (elapsed > 2000) { // 2秒后
                        currentProgress = Math.min(65, 40 + (int)((elapsed - 2000) / 80)); // SQL生成阶段
                    }
                    if (elapsed > 3000) { // 3秒后
                        currentProgress = Math.min(85, 65 + (int)((elapsed - 3000) / 60)); // 数据检索阶段
                    }
                    if (elapsed > 4000) { // 4秒后
                        currentProgress = Math.min(90, 85 + (int)((elapsed - 4000) / 100)); // 响应生成阶段
                    }

                    String message = String.format("%s (已耗时: %dms)", phases[phaseIndex], elapsed);
                    tracker.updateProgress(currentProgress, message);

                    // 根据真实处理时间调整更新频率
                    try {
                        Thread.sleep(300); // 每300ms更新一次进度
                        phaseIndex = (phaseIndex + 1) % phases.length;
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("进度监控任务异常", e);
            }
        }
    }





    /**
     * SSE进度回调实现
     */
    private static class SseProgressCallback implements ProgressCallback {
        private final SseEmitter emitter;
        private final ObjectMapper objectMapper;
        private volatile boolean shouldContinue = true;

        public SseProgressCallback(SseEmitter emitter, ObjectMapper objectMapper) {
            this.emitter = emitter;
            this.objectMapper = objectMapper;
        }

        @Override
        public void updateProgress(int progress, String message) {
            try {
                ProgressEvent event = new ProgressEvent("progress", progress, message, System.currentTimeMillis());
                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(objectMapper.writeValueAsString(event)));
            } catch (IOException e) {
                log.error("发送进度失败", e);
                shouldContinue = false;
            }
        }

        @Override
        public void sendThinking(String thought) {
            try {
                ProgressEvent event = new ProgressEvent("thinking", -1, thought, System.currentTimeMillis());
                emitter.send(SseEmitter.event()
                        .name("thinking")
                        .data(objectMapper.writeValueAsString(event)));
            } catch (IOException e) {
                log.error("发送思考过程失败", e);
                shouldContinue = false;
            }
        }

        @Override
        public void sendSqlGeneration(String message) {
            try {
                ProgressEvent event = new ProgressEvent("sql_generation", -1, message, System.currentTimeMillis());
                emitter.send(SseEmitter.event()
                        .name("sql_generation")
                        .data(objectMapper.writeValueAsString(event)));
            } catch (IOException e) {
                log.error("发送SQL生成信息失败", e);
                shouldContinue = false;
            }
        }

        @Override
        public void sendDataRetrieval(String message) {
            try {
                ProgressEvent event = new ProgressEvent("data_retrieval", -1, message, System.currentTimeMillis());
                emitter.send(SseEmitter.event()
                        .name("data_retrieval")
                        .data(objectMapper.writeValueAsString(event)));
            } catch (IOException e) {
                log.error("发送数据检索信息失败", e);
                shouldContinue = false;
            }
        }

        @Override
        public void sendResponseGeneration(String message) {
            try {
                ProgressEvent event = new ProgressEvent("response_generation", -1, message, System.currentTimeMillis());
                emitter.send(SseEmitter.event()
                        .name("response_generation")
                        .data(objectMapper.writeValueAsString(event)));
            } catch (IOException e) {
                log.error("发送响应生成信息失败", e);
                shouldContinue = false;
            }
        }

        @Override
        public boolean shouldContinue() {
            return shouldContinue;
        }
    }





    /**
     * 真实时间进度跟踪器 - 简化版本
     */
    private static class RealTimeProgressTracker {
        private final ProgressCallback callback;

        public RealTimeProgressTracker(ProgressCallback callback) {
            this.callback = callback;
        }

        public void updateProgress(int progress, String message) {
            callback.updateProgress(progress, message);
        }

        public void sendThinking(String thought) {
            callback.sendThinking(thought);
        }
    }
    /**
     * 带进度跟踪的请求包装器
     */
    private static class ProgressAwareRequest {
        private final UserQueryRequest request;
        private final RealTimeProgressTracker tracker;

        public ProgressAwareRequest(UserQueryRequest request, RealTimeProgressTracker tracker) {
            this.request = request;
            this.tracker = tracker;
        }

        public UserQueryRequest getRequest() {
            return request;
        }

        public RealTimeProgressTracker getTracker() {
            return tracker;
        }
    }
    /**
     * 发送最终结果
     */
    private void sendResult(SseEmitter emitter, AgentResponse response) throws IOException {
       // ProgressEvent event = new ProgressEvent("progress", progress, message, System.currentTimeMillis());

        emitter.send(SseEmitter.event()
                .name("result")
                .data(objectMapper.writeValueAsString(response)));

        /*emitter.send(SseEmitter.event()
                .name("progress")
                .data(objectMapper.writeValueAsString(event)));*/
    }

    /**
     * 发送性能统计
     */
    private void sendPerformanceStats(SseEmitter emitter, long totalTime, long queryTime) throws IOException {
        PerformanceStats stats = new PerformanceStats(totalTime, queryTime);
        emitter.send(SseEmitter.event()
                .name("performance")
                .data(objectMapper.writeValueAsString(stats)));
    }

    /**
     * 发送错误信息
     */
    private void sendError(SseEmitter emitter, String errorMessage) throws IOException {
        ProgressEvent event = new ProgressEvent("error", -1, errorMessage, System.currentTimeMillis());
        emitter.send(SseEmitter.event()
                .name("error")
                .data(objectMapper.writeValueAsString(event)));
    }

    /**
     * 进度事件数据类
     */
    public static class ProgressEvent {
        private String type;
        private int progress;
        private String message;
        private long timestamp;

        public ProgressEvent(String type, int progress, String message, long timestamp) {
            this.type = type;
            this.progress = progress;
            this.message = message;
            this.timestamp = timestamp;
        }

        // Getters
        public String getType() { return type; }
        public int getProgress() { return progress; }
        public String getMessage() { return message; }
        public long getTimestamp() { return timestamp; }
    }

    /**
     * 性能统计数据类
     */
    public static class PerformanceStats {
        private long totalTime;
        private long queryTime;
        private long overhead;

        public PerformanceStats(long totalTime, long queryTime) {
            this.totalTime = totalTime;
            this.queryTime = queryTime;
            this.overhead = totalTime - queryTime;
        }

        // Getters
        public long getTotalTime() { return totalTime; }
        public long getQueryTime() { return queryTime; }
        public long getOverhead() { return overhead; }
    }
}
