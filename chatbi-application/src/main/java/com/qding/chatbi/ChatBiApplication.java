package com.qding.chatbi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@SpringBootApplication(exclude = {
    ManagementWebSecurityAutoConfiguration.class
})
@ComponentScan(basePackages = {
    "com.qding.chatbi.admin",
    "com.qding.chatbi.agent", 
    "com.qding.chatbi.metadata",
    "com.qding.chatbi.knowledge",
    "com.qding.chatbi.common",
    "com.qding.chatbi.api"
})
@EnableJpaRepositories(basePackages = {
    "com.qding.chatbi.admin.repository",
    "com.qding.chatbi.agent.repository",
    "com.qding.chatbi.metadata.repository"
})
@EntityScan(basePackages = {
    "com.qding.chatbi.admin.entity",
    "com.qding.chatbi.agent.entity",
    "com.qding.chatbi.metadata.entity"
})
public class ChatBiApplication {

    public static void main(String[] args) {
        ApplicationContext context = SpringApplication.run(ChatBiApplication.class, args);
        printAllControllers(context);
    }

    /**
     * 启动后扫描并打印所有RestController
     */
    private static void printAllControllers(ApplicationContext context) {
        Map<String, Object> controllerBeans = context.getBeansWithAnnotation(RestController.class);
        System.out.println("==================== 扫描到的RestController ====================");
        for (Map.Entry<String, Object> entry : controllerBeans.entrySet()) {
            System.out.println("Controller: " + entry.getKey() + " -> " + entry.getValue().getClass().getName());
        }
        System.out.println("==================== 总共找到 " + controllerBeans.size() + " 个Controller ====================");
    }
} 