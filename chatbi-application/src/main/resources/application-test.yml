# 测试环境配置
server:
  port: 8081

spring:
  application:
    name: chatbi-application-test

  # 数据库配置 - 测试环境
  datasource:
    url: ***********************************************************************************************************************************************************
    username: root
    password: root123
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update  #允许自动更新表结构
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        #format_sql: true

# LangChain4j 配置 - 测试环境
langchain4j:
  dashscope:
    chat-model:
      api-key: sk-d37335951c6744ba80c147a8110acced
      model-name: qwen-max
      temperature: 0.5  # 测试环境使用较低的随机性
    embedding-model:
      api-key: sk-d37335951c6744ba80c147a8110acced
      model-name: text-embedding-v4
  embedding-store:
    milvus:
      host: ***********
      port: 19530
      collection-name: chatbi_embeddings
      dimension: 1024
      username: root
      password: Milvus

# 日志配置 - 测试环境
logging:
  level:
    com.qding.chatbi: DEBUG
    dev.langchain4j: INFO
    com.zaxxer.hikari: WARN
    org.springframework: INFO
    org.springframework.test: DEBUG
    root: INFO
  
  # 测试环境日志文件配置
  file:
    path: /data/log
  
  logback:
    rollingpolicy:
      file-name-pattern: ${logging.file.path}/chatbi-application-test.%d{yyyy-MM-dd}.%i.log
      max-file-size: 50MB   # 测试环境文件更小
      max-history: 3        # 测试环境只保留3天
      total-size-cap: 500MB
      clean-history-on-start: true

# Milvus配置 - 测试环境
milvus:
  host: ***********
  port: 19530
  search:
    similarity-threshold: 0.5  # 测试环境阈值最低

# Actuator 配置 - 测试环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,beans,mappings,env
  endpoint:
    health:
      show-details: when-authorized  # 只有授权用户才能看到详情
      roles: ACTUATOR
    info:
      enabled: true
      roles: ACTUATOR
  security:
    enabled: true

# 测试环境特殊配置
debug: true
