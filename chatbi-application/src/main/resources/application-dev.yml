# 开发环境配置
server:
  port: 8081

spring:
  application:
    name: chatbi-application-dev

  # Spring Security 配置 - 开发环境
  security:
    user:
      name: admin
      password: dev123456
      roles: ACTUATOR,ADMIN

  # 数据库配置 - 开发环境
  datasource:
    url: ***********************************************************************************************************************************************************
    username: root
    password: root123
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境允许自动更新表结构
    show-sql: true      # 开发环境显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        #format_sql: true  # 格式化SQL输出

# LangChain4j 配置 - 开发环境
langchain4j:
  dashscope:
    chat-model:
      api-key: sk-d37335951c6744ba80c147a8110acced
      model-name: qwen-max
      temperature: 0.8
    embedding-model:
      api-key: sk-d37335951c6744ba80c147a8110acced
      model-name: text-embedding-v4
  embedding-store:
    milvus:
      host: ***********
      port: 19530
      collection-name: chatbi_embeddings
      dimension: 1024
      username: root
      password: Milvus

# 日志配置 - 开发环境
logging:
  level:
    # 开发环境更详细的日志
    com.qding.chatbi: DEBUG
    dev.langchain4j: DEBUG
    com.zaxxer.hikari: INFO
    org.springframework: INFO
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    root: INFO
  
  # 开发环境日志文件配置
  file:
    path: ./logs/
  
  logback:
    rollingpolicy:
      file-name-pattern: ${logging.file.path}/chatbi-application-dev.%d{yyyy-MM-dd}.%i.log
      max-file-size: 100MB  # 开发环境文件稍小
      max-history: 7        # 开发环境保留7天
      total-size-cap: 1GB
      clean-history-on-start: true

# Milvus配置 - 开发环境
milvus:
  host: ***********
  port: 19530
  search:
    similarity-threshold: 0.5  # 开发环境阈值稍低，便于测试

# Actuator 配置 - 开发环境（更多端点暴露）
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always
    env:
      show-values: always

# 开发环境特殊配置
debug: true  # 启用调试模式
