# 生产环境配置
server:
  port: 8081
  servlet:
    session:
      timeout: 60m  # 生产环境会话超时时间更长

spring:
  application:
    name: chatbi-application-prod

  # 数据库配置 - 生产环境
  datasource:
    url: *****************************************************************************************************************************************************************
    username: ${DB_USERNAME:chatbi_user}  # 使用环境变量
    password: ${DB_PASSWORD:}             # 使用环境变量
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
    
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境不允许自动修改表结构
    show-sql: false       # 生产环境不显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

# LangChain4j 配置 - 生产环境
langchain4j:
  dashscope:
    chat-model:
      api-key: ${DASHSCOPE_API_KEY:}  # 使用环境变量
      model-name: qwen-max
      temperature: 0.8  # 生产环境稍微保守一些
    embedding-model:
      api-key: ${DASHSCOPE_API_KEY:}  # 使用环境变量
      model-name: text-embedding-v4
  embedding-store:
    milvus:
      host: ${MILVUS_HOST:prod-milvus-host}
      port: ${MILVUS_PORT:19530}
      collection-name: chatbi_embeddings
      dimension: 1024
      username: ${MILVUS_USERNAME:root}
      password: ${MILVUS_PASSWORD:}

# 日志配置 - 生产环境
logging:
  level:
    # 生产环境较少的日志输出
    com.qding.chatbi: INFO
    dev.langchain4j: INFO
    com.zaxxer.hikari: WARN
    org.springframework: INFO
    org.hibernate: WARN
    root: WARN
  
  # 生产环境日志文件配置
  file:
    path: /var/log/chatbi  # 生产环境使用标准日志路径
  
  logback:
    rollingpolicy:
      file-name-pattern: ${logging.file.path}/chatbi-application-prod.%d{yyyy-MM-dd}.%i.log
      max-file-size: 200MB
      max-history: 30       # 生产环境保留30天
      total-size-cap: 5GB   # 生产环境更大的存储空间
      clean-history-on-start: true

# Milvus配置 - 生产环境
milvus:
  host: ${MILVUS_HOST:prod-milvus-host}
  port: ${MILVUS_PORT:19530}
  search:
    similarity-threshold: 0.5  # 生产环境更严格的阈值

# Actuator 配置 - 生产环境（限制端点暴露）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 生产环境只暴露必要端点
  endpoint:
    health:
      show-details: when-authorized  # 只有授权用户才能看到详情

# 生产环境安全配置
debug: false  # 关闭调试模式
