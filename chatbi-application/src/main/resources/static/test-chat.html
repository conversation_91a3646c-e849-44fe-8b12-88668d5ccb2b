<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBi数据查询助手</title>
    <!--<script src="https://cdn.tailwindcss.com"></script>-->
    <script src="js/tailwindcss.js"></script>
    <!--<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>-->
    <script src="js/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        #chat-window {
            height: calc(100vh - 12rem);
        }
        .message-bubble {
            max-width: 80%;
        }
        .clarification-btn {
            transition: background-color 0.2s;
        }
        .message-input-container {
            padding: 10px;
            background-color: #ffffff;
            border-top: 1px solid #eeeeee;
            display: flex;
        }

        .data-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
            font-size: 14px;
            border: 1px solid #dfe2e5;
        }

        .data-table th, .data-table td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }

        .data-table th {
            background-color: #f6f8fa;
            font-weight: bold;
        }

        .data-table tr:nth-child(even) {
            background-color: #f6f8fa;
        }

        #message-input {
            flex-grow: 1;
        }

        .analysis-content {
            line-height: 1.6;
            margin: 8px 0;
        }

        .analysis-content strong {
            color: #1f2937;
            font-weight: 600;
        }

        .analysis-content br + br {
            margin-top: 8px;
        }

        .clarification-btn {
            transition: all 0.2s ease;
            border: 1px solid #3b82f6;
        }

        .clarification-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="w-full max-w-2xl mx-auto bg-white rounded-2xl shadow-2xl flex flex-col h-[95vh]">
        <header class="bg-gray-800 text-white p-4 rounded-t-2xl">
            <h1 class="text-xl font-bold text-center">ChatBi数据查询助手</h1>
            <div class="mt-3">
                <label for="role-selector" class="text-sm font-medium text-gray-300 mr-2">当前角色:</label>
                <select id="role-selector" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2">
                    <!-- 角色将在这里动态加载 -->
                </select>
            </div>
        </header>

        <main id="chat-window" class="flex-1 p-6 overflow-y-auto space-y-6">
            <!-- 聊天消息将在这里动态添加 -->
            <div class="flex justify-start">
                <div class="message-bubble bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none">
                    <p class="text-sm">您好！我是您的BI助手，请问有什么可以帮您查询的吗？例如，您可以问我："上个月北京地区的GMV是多少？"</p>
                </div>
            </div>
        </main>

        <footer class="p-4 border-t border-gray-200">
            <div class="flex items-center space-x-3">
                <input type="text" id="user-input" class="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入您的问题...">
                <button id="send-btn" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300">
                    发送
                </button>
            </div>
        </footer>
    </div>

    <script>
        // --- 核心变量 ---
        const chatWindow = document.getElementById('chat-window');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        let userId = 'test-user-001'; // 固定的测试用户ID
        let sessionId = null; // 初始化会话ID
        const roleSelector = document.getElementById('role-selector');

        // --- 初始化 ---
        document.addEventListener('DOMContentLoaded', fetchAndPopulateRoles);

        // --- 事件监听 ---
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // --- 函数定义 ---

        // 获取并填充角色
        async function fetchAndPopulateRoles() {
            try {
                const response = await fetch('/api/admin/permissions/roles/all');
                if (!response.ok) {
                    throw new Error(`无法获取角色列表: ${response.status}`);
                }
                const roles = await response.json();

                // 添加一个无角色的默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '游客 (无角色)';
                roleSelector.appendChild(defaultOption);

                // 动态填充从API获取的角色
                roles.forEach(roleName => {
                    const option = document.createElement('option');
                    option.value = roleName;
                    option.textContent = roleName;
                    roleSelector.appendChild(option);
                });

            } catch (error) {
                console.error('获取角色失败:', error);
                const errorOption = document.createElement('option');
                errorOption.value = '';
                errorOption.textContent = '无法加载角色';
                roleSelector.appendChild(errorOption);
                roleSelector.disabled = true;
            }
        }

        // 发送消息
        async function sendMessage() {
            const queryText = userInput.value.trim();
            if (!queryText) return;

            // 禁用输入和按钮
            toggleInput(false);
            addMessage(queryText, 'user');
            userInput.value = '';

            try {
                const selectedRole = roleSelector.value; // 获取选中的角色
                const additionalParams = {
                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                };
                if (selectedRole) {
                    additionalParams.userRole = selectedRole; // 如果有角色，则添加到参数中
                }

                const requestBody = {
                    userId: userId,
                    sessionId: sessionId,
                    queryText: queryText,
                    additionalParams: additionalParams
                };

                const response = await fetch('/api/v1/chatbi/conversation/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `HTTP错误! 状态: ${response.status}` }));
                    throw new Error(errorData.message || `HTTP错误! 状态: ${response.status}`);
                }

                const data = await response.json();
                handleAgentResponse(data);

            } catch (error) {
                console.error('发送消息时出错:', error);
                addMessage(`抱歉，连接服务器时发生错误: ${error.message}`, 'agent', 'ERROR');
            } finally {
                toggleInput(true);
            }
        }

        // 处理澄清选项点击
        async function handleClarificationClick(optionId, optionText, payload) {
            addMessage(`我选择了: "${optionText}"`, 'user');
            toggleInput(false);

            try {
                const selectedRole = roleSelector.value; // 获取选中的角色
                const additionalParams = {
                    clarifiedOptionId: optionId
                };
                
                // 如果有payload，将其内容合并到additionalParams中
                if (payload) {
                    Object.assign(additionalParams, payload);
                }
                
                 if (selectedRole) {
                    additionalParams.userRole = selectedRole; // 如果有角色，则添加到参数中
                }

                const requestBody = {
                    userId: userId,
                    sessionId: sessionId,
                    queryText: `基于澄清选项 "${optionText}"，请继续处理我的原始查询`, // 更有意义的查询文本
                    additionalParams: additionalParams
                };

                console.log('发送澄清请求:', requestBody);

                // 统一使用 /query 接口
                const response = await fetch('/api/v1/chatbi/conversation/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });

                 if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `HTTP错误! 状态: ${response.status}` }));
                    throw new Error(errorData.message || `HTTP错误! 状态: ${response.status}`);
                }

                const data = await response.json();
                handleAgentResponse(data);

            } catch (error) {
                 console.error('发送澄清响应时出错:', error);
                addMessage(`抱歉，处理您的选择时发生错误: ${error.message}`, 'agent', 'ERROR');
            } finally {
                toggleInput(true);
            }
        }

        // 处理AI Agent的响应
        function handleAgentResponse(data) {
            console.log('处理AI响应，完整数据:', data);
            
            // 更新会话ID
            if (data.sessionId) {
                sessionId = data.sessionId;
            }

            // --- 优化消息文案 ---
            let messageToDisplay = data.messageToUser;
            const isDataEmpty = data.responseType === 'DATA' && 
                                (!data.queryResult || !data.queryResult.rows || data.queryResult.rows.length === 0);

            if (isDataEmpty && !data.clarificationOptions) {
                // 如果是空数据结果且没有澄清选项，替换为更友好的提示
                const match = (data.messageToUser || '').match(/查询结果:\s*(.*?)\s*=/);
                const queryTopic = match && match[1] ? `"${match[1]}"` : '您查询的';
                messageToDisplay = `抱歉，没有查询到${queryTopic}相关数据。`;
            }

            // --- 统一的消息和内容渲染逻辑 ---
            // 1. 创建一个（可能为空的）消息气泡
            const messageBubble = addMessage(null, 'agent', data.responseType);

            // NEW ORDER: Summary -> Table -> Chart -> Analysis
            
            // 2. 添加总结 (如果存在)
            if (data.summary) {
                const summaryElement = document.createElement('div');
                summaryElement.className = 'text-sm mb-3'; // 与其他元素保持间距
                summaryElement.textContent = data.summary;
                messageBubble.appendChild(summaryElement);
            }

            // 3. 处理数据和可视化 (如果存在)
            if (data.responseType === 'DATA' && data.queryResult && data.queryResult.rows && data.queryResult.rows.length > 0) {
                let displayStrategy = 'both';
                if (data.displaySuggestion && data.displaySuggestion.dataVisualizationConfig) {
                    displayStrategy = data.displaySuggestion.dataVisualizationConfig.displayStrategy || 'both';
                }
                
                let tableConfig = null, chartConfig = null;
                if (data.displaySuggestion && data.displaySuggestion.dataVisualizationConfig && data.displaySuggestion.dataVisualizationConfig.success) {
                    try {
                        if (data.displaySuggestion.dataVisualizationConfig.tableConfigJson) {
                            tableConfig = JSON.parse(data.displaySuggestion.dataVisualizationConfig.tableConfigJson);
                        }
                        if (data.displaySuggestion.dataVisualizationConfig.chartConfigJson) {
                            chartConfig = JSON.parse(data.displaySuggestion.dataVisualizationConfig.chartConfigJson);
                        }
                    } catch(e) { console.error("解析 table/chart config JSON 失败", e); }
                }

                // 按 表格 -> 图表 的顺序添加
                if (displayStrategy === 'both' || displayStrategy === 'table_primary' || displayStrategy === 'table_only') {
                    const tableElement = createTableElement(data.queryResult, tableConfig, data.displaySuggestion);
                    if (tableElement) messageBubble.appendChild(tableElement);
                }

                if (displayStrategy === 'both' || displayStrategy === 'chart_only') {
                    const chartElement = createChartElement(chartConfig, data.displaySuggestion);
                    if (chartElement) messageBubble.appendChild(chartElement);
                }
            }

            // 4. 最后添加文本解读 (analysis) 和澄清选项
            const textContent = document.createElement('div');
            textContent.className = 'text-sm whitespace-pre-wrap';

            if (data.messageToUser) {
                if (data.messageToUser.includes('📊') || data.messageToUser.includes('🔍') || data.messageToUser.includes('📈') || data.messageToUser.includes('💡')) {
                    textContent.innerHTML = formatAnalysisText(data.messageToUser);
                    textContent.className = 'text-sm analysis-content';
                } else {
                    textContent.textContent = data.messageToUser;
                }
                messageBubble.appendChild(textContent);
            }
            
            if ((data.responseType === 'CLARIFICATION_NEEDED' || data.responseType === 'DATA_UNAVAILABLE') && data.clarificationOptions && data.clarificationOptions.length > 0) {
                const clarificationContainer = createClarificationOptions(data.clarificationOptions);
                messageBubble.appendChild(clarificationContainer);
            }
        }

        // 添加消息到聊天窗口
        function addMessage(text, sender, responseType = 'TEXT') {
            const messageContainer = document.createElement('div');
            messageContainer.className = `flex ${sender === 'user' ? 'justify-end' : 'justify-start'}`;

            const messageBubble = document.createElement('div');
            messageBubble.className = 'message-bubble p-3 rounded-lg';
            
            if (sender === 'user') {
                messageBubble.classList.add('bg-blue-600', 'text-white', 'rounded-br-none');
            } else {
                if (responseType === 'ERROR') {
                    messageBubble.classList.add('bg-red-100', 'text-red-700', 'rounded-bl-none');
                } else if (responseType === 'DATA_UNAVAILABLE') {
                    messageBubble.classList.add('bg-orange-100', 'text-orange-700', 'rounded-bl-none');
                } else {
                    messageBubble.classList.add('bg-gray-200', 'text-gray-800', 'rounded-bl-none');
                }
            }

            // 仅当提供了文本时才创建并添加文本节点
            if (text) {
                const textContent = document.createElement('div');
                textContent.className = 'text-sm whitespace-pre-wrap';
                
                if (text.includes('📊') || text.includes('🔍') || text.includes('📈') || text.includes('💡')) {
                    textContent.innerHTML = formatAnalysisText(text);
                    textContent.className = 'text-sm analysis-content';
                } else {
                    textContent.textContent = text;
                }
                
                messageBubble.appendChild(textContent);
            }

            messageContainer.appendChild(messageBubble);
            chatWindow.appendChild(messageContainer);
            chatWindow.scrollTop = chatWindow.scrollHeight;

            return messageBubble;
        }

        // 格式化分析文本，支持简单的Markdown语法
        function formatAnalysisText(text) {
            return text
                // 处理粗体文本 **text** -> <strong>text</strong>
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 处理列表项 - text -> • text
                .replace(/^- (.*$)/gm, '• $1')
                // 保持换行
                .replace(/\n/g, '<br/>');
        }

        // 创建澄清选项按钮
        function createClarificationOptions(options) {
            console.log('createClarificationOptions 被调用，选项详情:', options);
            const container = document.createElement('div');
            container.className = 'mt-3 space-y-2';

            options.forEach((option, index) => {
                console.log(`创建第 ${index + 1} 个澄清选项:`, option);
                const button = document.createElement('button');
                button.className = 'clarification-btn w-full text-left text-sm bg-blue-100 text-blue-800 p-2 rounded-md hover:bg-blue-200';
                const buttonText = option.optionText || option.displayText; // 兼容两种字段名
                button.textContent = buttonText;
                button.onclick = () => handleClarificationClick(option.optionId, buttonText, option.payload);
                container.appendChild(button);
                console.log(`澄清选项按钮已创建: ${buttonText}`);
            });
            console.log('所有澄清选项按钮创建完成，容器返回');
            return container;
        }

        // 切换输入框和发送按钮的状态
        function toggleInput(enabled) {
            userInput.disabled = !enabled;
            sendBtn.disabled = !enabled;
            if (enabled) {
                userInput.focus();
            }
        }

        // 创建表格元素
        function createTableElement(queryResult, tableConfig, displaySuggestion) {
            if (!queryResult) return null;

            const container = document.createElement('div');
            container.className = 'overflow-x-auto my-4'; // 增加上下边距

            // 添加表格标题
            const tableTitle = displaySuggestion ? displaySuggestion.title : '查询结果';
            if (tableTitle) {
                const titleElement = document.createElement('h3');
                titleElement.textContent = tableTitle;
                titleElement.className = 'text-md font-semibold text-gray-700 mb-2'; // 标题样式
                container.appendChild(titleElement);
            }

            if (tableConfig && tableConfig.tableType === 'pivot') {
                console.log("检测到透视表配置，开始创建透视表...");
                const pivotTable = createPivotTable(queryResult, tableConfig);
                container.appendChild(pivotTable);
                return container;
            }

            console.log("创建标准表格...");
            const table = document.createElement('table');
            table.className = 'data-table w-full text-sm border-collapse border border-gray-300';

            // 表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            queryResult.columnHeaders.forEach(headerText => {
                const th = document.createElement('th');
                th.className = 'p-2 border border-gray-300 bg-gray-100 font-semibold text-left';
                th.textContent = headerText;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // 表体
            const tbody = document.createElement('tbody');
            queryResult.rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.className = 'hover:bg-gray-50';
                row.forEach(cellData => {
                    const td = document.createElement('td');
                    td.className = 'p-2 border border-gray-300';
                    td.textContent = cellData;
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
            table.appendChild(tbody);
            
            container.appendChild(table);

            return container;
        }
        
        // 创建透视表 (Pivot Table)
        function createPivotTable(queryResult, tableConfig) {
            if (!tableConfig.pivotConfig) return createTableElement(queryResult, null);
            const { rowFields, columnFields, valueFields } = tableConfig.pivotConfig;
            
            if (!rowFields || !columnFields || !valueFields || rowFields.length === 0 || columnFields.length === 0 || valueFields.length === 0) {
                 console.error("透视表配置不完整！");
                 return createTableElement(queryResult, null);
            }

            const rowIndex = queryResult.columnHeaders.indexOf(rowFields[0]);
            const colIndex = queryResult.columnHeaders.indexOf(columnFields[0]);
            const valIndex = queryResult.columnHeaders.indexOf(valueFields[0]);

            if (rowIndex === -1 || colIndex === -1 || valIndex === -1) {
                console.error("透视表字段在查询结果中未找到！");
                return createTableElement(queryResult, null); // 回退到标准表格
            }

            // 数据透视
            const pivotData = {};
            const colHeaders = new Set();
            queryResult.rows.forEach(row => {
                const rowKey = row[rowIndex];
                const colKey = row[colIndex];
                const val = row[valIndex];

                if (!pivotData[rowKey]) {
                    pivotData[rowKey] = {};
                }
                pivotData[rowKey][colKey] = val;
                colHeaders.add(colKey);
            });

            const sortedColHeaders = Array.from(colHeaders).sort();

            const table = document.createElement('table');
            table.className = 'data-table w-full text-sm border-collapse border border-gray-300';

            // 表头
            const thead = table.createTHead();
            const headerRow = thead.insertRow();
            headerRow.className = 'bg-gray-100';
            const firstHeader = document.createElement('th');
            firstHeader.className = 'p-2 border border-gray-300 font-semibold text-left';
            firstHeader.textContent = rowFields[0];
            headerRow.appendChild(firstHeader);
            sortedColHeaders.forEach(header => {
                const th = document.createElement('th');
                th.className = 'p-2 border border-gray-300 font-semibold text-left';
                th.textContent = header;
                headerRow.appendChild(th);
            });

            // 表体
            const tbody = table.createTBody();
            Object.keys(pivotData).sort().forEach(rowKey => {
                const row = tbody.insertRow();
                row.className = 'hover:bg-gray-50';
                
                const rowKeyCell = row.insertCell();
                rowKeyCell.className = 'p-2 border border-gray-300 font-semibold';
                rowKeyCell.textContent = rowKey;

                sortedColHeaders.forEach(colKey => {
                    const cell = row.insertCell();
                    cell.className = 'p-2 border border-gray-300 text-right';
                    const value = pivotData[rowKey][colKey] || 0;
                    cell.textContent = formatTableCell(value, tableConfig.formatting ? tableConfig.formatting[valueFields[0]] : null);
                });
            });

            return table;
        }

        // 格式化表格单元格内容
        function formatTableCell(data, formatOptions) {
            if (data === null || data === undefined) return '';
            
            if (formatOptions) {
                if (formatOptions.type === 'currency' || formatOptions.type === 'number') {
                    let value = Number(data);
                    if (isNaN(value)) return data;
                    
                    return value.toLocaleString('en-US', {
                        minimumFractionDigits: formatOptions.decimals || 0,
                        maximumFractionDigits: formatOptions.decimals || 0,
                    });
                }
            }
            
            if (typeof data === 'number' || (typeof data === 'string' && /^\d+(\.\d+)?$/.test(data))) {
                 return Number(data).toLocaleString('en-US');
            }

            return data;
        }

        // 创建图表元素
        function createChartElement(chartConfig, displaySuggestion) {
            // displaySuggestion 保持，但优先使用 chartConfig
            if (chartConfig && chartConfig.type && chartConfig.data) {
                console.log("检测到完整的 chartConfig，直接渲染图表。");
                const canvasContainer = document.createElement('div');
                canvasContainer.className = 'mt-4 p-4 bg-white rounded-lg shadow-md';
                const canvas = document.createElement('canvas');
                canvasContainer.appendChild(canvas);
                console.log("Chart是否引入:"+typeof Chart);
                new Chart(canvas, chartConfig);
                return canvasContainer;
            }

            // --- Fallback/Legacy Logic ---
            console.warn("未找到有效的 chartConfig，回退到旧的 displaySuggestion 逻辑。");
            if (!displaySuggestion || !displaySuggestion.chartType || !displaySuggestion.transformedData) {
                return null;
            }
            const { chartType, transformedData, chartTitle } = displaySuggestion;

            const canvasContainer = document.createElement('div');
            canvasContainer.className = 'mt-4 p-4 bg-white rounded-lg shadow-md';
            const canvas = document.createElement('canvas');
            canvasContainer.appendChild(canvas);

            new Chart(canvas, {
                type: chartType,
                data: transformedData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: chartTitle || '图表展示'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            return canvasContainer;
        }
    </script>
</body>
</html>
