<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE简单示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #messages {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-top: 10px;
        }
        .message {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .event {
            background-color: #e8f4f8;
        }
        .status {
            background-color: #fff3cd;
        }
        .data {
            background-color: #d4edda;
        }
        .progress {
            background-color: #cce7ff;
        }
        .complete {
            background-color: #d4edda;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>SSE (Server-Sent Events) 简单示例</h1>
    
    <div class="container">
        <h2>1. 简单消息示例</h2>
        <div>
            <label for="nameInput">用户名:</label>
            <input type="text" id="nameInput" value="TestUser">
            <button onclick="startSimpleSse()">开始简单SSE</button>
            <button onclick="closeConnection()" id="closeBtn" disabled>关闭连接</button>
        </div>
    </div>

    <div class="container">
        <h2>2. 实时计数器示例</h2>
        <div>
            <label for="limitInput">计数上限:</label>
            <input type="number" id="limitInput" value="10" min="1" max="100">
            <button onclick="startCounterSse()">开始计数器SSE</button>
        </div>
    </div>

    <div class="container">
        <h2>3. 自定义事件示例</h2>
        <div>
            <button onclick="startCustomEventsSse()">开始自定义事件SSE</button>
        </div>
    </div>

    <div class="container">
        <h2>消息输出</h2>
        <div id="messages"></div>
    </div>

    <script>
        let eventSource = null;

        // 添加消息到显示区域
        function addMessage(message, type = '') {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 开始简单SSE连接
        function startSimpleSse() {
            closeConnection();
            
            const name = document.getElementById('nameInput').value || 'User';
            eventSource = new EventSource(`/api/v1/chatbi/sse-demo/simple?name=${encodeURIComponent(name)}`);
            
            document.getElementById('closeBtn').disabled = false;
            addMessage(`开始连接到简单SSE端点，用户名: ${name}`, 'status');
            
            eventSource.onopen = function(event) {
                addMessage('SSE连接已建立', 'status');
            };
            
            eventSource.onmessage = function(event) {
                addMessage(`收到消息: ${event.data}`);
            };
            
            eventSource.onerror = function(event) {
                addMessage('SSE连接发生错误或已关闭', 'status');
                closeConnection();
            };
        }

        // 开始计数器SSE连接
        function startCounterSse() {
            closeConnection();
            
            const limit = document.getElementById('limitInput').value || 10;
            eventSource = new EventSource(`/api/v1/chatbi/sse-demo/counter?limit=${limit}`);
            
            addMessage(`开始连接到计数器SSE端点，限制: ${limit}`, 'status');
            
            eventSource.onmessage = function(event) {
                addMessage(`计数: ${event.data}`, 'data');
            };
            
            eventSource.onerror = function(event) {
                addMessage('计数器SSE连接已关闭', 'status');
                closeConnection();
            };
        }

        // 开始自定义事件SSE连接
        function startCustomEventsSse() {
            closeConnection();
            
            eventSource = new EventSource('/api/v1/chatbi/sse-demo/events');
            
            addMessage('开始连接到自定义事件SSE端点', 'status');
            
            // 监听自定义事件
            eventSource.addEventListener('status', function(event) {
                addMessage(`状态: ${event.data}`, 'status');
            });
            
            eventSource.addEventListener('progress', function(event) {
                addMessage(`进度: ${event.data}`, 'progress');
            });
            
            eventSource.addEventListener('data', function(event) {
                addMessage(`数据: ${event.data}`, 'data');
            });
            
            eventSource.addEventListener('complete', function(event) {
                addMessage(`完成: ${event.data}`, 'complete');
                closeConnection();
            });
            
            eventSource.onerror = function(event) {
                addMessage('自定义事件SSE连接发生错误或已完成', 'status');
                closeConnection();
            };
        }

        // 关闭SSE连接
        function closeConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                document.getElementById('closeBtn').disabled = true;
                addMessage('SSE连接已手动关闭', 'status');
            }
        }

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            closeConnection();
        });
    </script>
</body>
</html>