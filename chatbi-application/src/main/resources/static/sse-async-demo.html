<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE + CompletableFuture 示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .stop-btn {
            background-color: #f44336;
        }
        .stop-btn:hover {
            background-color: #d32f2f;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-top: 10px;
        }
        .message {
            padding: 5px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        .status {
            background-color: #e8f4f8;
            font-weight: bold;
        }
        .progress {
            background-color: #d4edda;
        }
        .phase {
            background-color: #fff3cd;
            font-weight: bold;
        }
        .work {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .result {
            background-color: #d1ecf1;
            font-weight: bold;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .subtask-start {
            background-color: #e2e3e5;
        }
        .subtask-complete {
            background-color: #d4edda;
        }
        .progress-bar-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress-bar {
            height: 20px;
            background-color: #4CAF50;
            border-radius: 5px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>SSE + CompletableFuture 结合使用示例</h1>
    
    <div class="container">
        <h2>1. 基本示例</h2>
        <div>
            <label for="basicTaskId">任务ID:</label>
            <input type="text" id="basicTaskId" value="task-1" placeholder="输入任务ID">
            <button onclick="startBasicExample()">开始基本示例</button>
            <button class="stop-btn" onclick="closeConnection()" id="closeBtn" disabled>关闭连接</button>
            <p>演示使用 CompletableFuture.runAsync() 异步执行任务并通过 SSE 发送实时进度更新</p>
        </div>
    </div>

    <div class="container">
        <h2>2. 复杂任务示例</h2>
        <div>
            <label for="complexTaskId">任务ID:</label>
            <input type="text" id="complexTaskId" value="complex-task-1" placeholder="输入任务ID">
            
            <label for="duration">持续时间(秒):</label>
            <input type="number" id="duration" value="10" min="1" max="60">
            
            <button onclick="startComplexExample()">开始复杂任务示例</button>
            <p>演示多阶段任务处理，每个阶段都有不同的处理逻辑和进度更新</p>
        </div>
    </div>

    <div class="container">
        <h2>3. 并行任务示例</h2>
        <div>
            <label for="parallelTaskId">主任务ID:</label>
            <input type="text" id="parallelTaskId" value="parallel-task-1" placeholder="输入主任务ID">
            
            <label for="subTaskCount">子任务数量:</label>
            <input type="number" id="subTaskCount" value="3" min="1" max="10">
            
            <button onclick="startParallelExample()">开始并行任务示例</button>
            <p>演示如何并行处理多个子任务并将进度汇总后发送给客户端</p>
        </div>
    </div>

    <div class="container">
        <h2>进度条</h2>
        <div class="progress-bar-container">
            <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
        </div>
    </div>

    <div class="container">
        <h2>事件输出</h2>
        <div id="messages"></div>
    </div>

    <script>
        let eventSource = null;
        let currentProgress = 0;

        // 添加消息到显示区域
        function addMessage(message, type = '') {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 更新进度条
        function updateProgressBar(progress, text = '') {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress + '%';
            progressBar.textContent = text || progress + '%';
            currentProgress = progress;
        }

        // 开始基本示例
        function startBasicExample() {
            closeConnection();
            
            const taskId = document.getElementById('basicTaskId').value || 'task-1';
            const url = `/api/v1/chatbi/sse-async-demo/basic?taskId=${encodeURIComponent(taskId)}`;
            
            eventSource = new EventSource(url);
            
            document.getElementById('closeBtn').disabled = false;
            addMessage(`开始连接到基本示例端点，任务ID: ${taskId}`, 'status');
            updateProgressBar(0, '任务初始化');
            
            setupEventHandlers();
        }

        // 开始复杂任务示例
        function startComplexExample() {
            closeConnection();
            
            const taskId = document.getElementById('complexTaskId').value || 'complex-task-1';
            const duration = document.getElementById('duration').value || 10;
            const url = `/api/v1/chatbi/sse-async-demo/complex?taskId=${encodeURIComponent(taskId)}&duration=${duration}`;
            
            eventSource = new EventSource(url);
            
            addMessage(`开始连接到复杂任务示例端点，任务ID: ${taskId}，预计耗时: ${duration}秒`, 'status');
            updateProgressBar(0, '任务初始化');
            
            setupEventHandlers();
        }

        // 开始并行任务示例
        function startParallelExample() {
            closeConnection();
            
            const taskId = document.getElementById('parallelTaskId').value || 'parallel-task-1';
            const subTaskCount = document.getElementById('subTaskCount').value || 3;
            const url = `/api/v1/chatbi/sse-async-demo/parallel?taskId=${encodeURIComponent(taskId)}&subTaskCount=${subTaskCount}`;
            
            eventSource = new EventSource(url);
            
            addMessage(`开始连接到并行任务示例端点，主任务ID: ${taskId}，子任务数量: ${subTaskCount}`, 'status');
            updateProgressBar(0, '任务初始化');
            
            setupEventHandlers();
        }

        // 设置事件处理器
        function setupEventHandlers() {
            // 通用消息处理
            eventSource.onmessage = function(event) {
                addMessage(`收到消息: ${event.data}`);
            };
            
            // 连接打开事件
            eventSource.onopen = function(event) {
                addMessage('SSE连接已建立', 'status');
            };
            
            // 状态事件
            eventSource.addEventListener('status', function(event) {
                addMessage(`状态: ${event.data}`, 'status');
            });
            
            // 进度事件
            eventSource.addEventListener('progress', function(event) {
                addMessage(`进度: ${event.data}`, 'progress');
                
                // 从消息中提取进度百分比
                const match = event.data.match(/(\d+)%/);
                if (match) {
                    const progress = parseInt(match[1]);
                    updateProgressBar(progress);
                }
            });
            
            // 阶段事件
            eventSource.addEventListener('phase', function(event) {
                addMessage(`阶段: ${event.data}`, 'phase');
            });
            
            // 工作事件
            eventSource.addEventListener('work', function(event) {
                addMessage(`工作中: ${event.data}`, 'work');
            });
            
            // 子任务开始事件
            eventSource.addEventListener('subtask-start', function(event) {
                addMessage(`子任务开始: ${event.data}`, 'subtask-start');
            });
            
            // 子任务完成事件
            eventSource.addEventListener('subtask-complete', function(event) {
                addMessage(`子任务完成: ${event.data}`, 'subtask-complete');
            });
            
            // 结果事件
            eventSource.addEventListener('result', function(event) {
                addMessage(`结果: ${event.data}`, 'result');
                updateProgressBar(100, '任务完成');
            });
            
            // 错误事件
            eventSource.addEventListener('error', function(event) {
                addMessage(`错误: ${event.data}`, 'error');
                closeConnection();
            });
            
            // 连接错误
            eventSource.onerror = function(event) {
                addMessage('SSE连接发生错误或已关闭', 'error');
                closeConnection();
            };
        }

        // 关闭SSE连接
        function closeConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                document.getElementById('closeBtn').disabled = true;
                addMessage('SSE连接已手动关闭', 'status');
            }
        }

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            closeConnection();
        });
    </script>
</body>
</html>