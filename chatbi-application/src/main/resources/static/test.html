<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI 管理后台 - 数据集管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
        }
        .ant-menu-item-selected {
            background-color: #e6f7ff;
            color: #1890ff;
            border-right: 3px solid #1890ff;
        }
        .ant-btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;
        }
        .ant-btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        .ant-table-thead > tr > th {
            background-color: #fafafa;
        }
        .ant-tag {
            display: inline-block;
            padding: 0 7px;
            font-size: 12px;
            line-height: 20px;
            border-radius: 2px;
            border: 1px solid;
        }
        .ant-tag-blue {
            color: #1890ff;
            background: #e6f7ff;
            border-color: #91d5ff;
        }
        .ant-tag-green {
            color: #52c41a;
            background: #f6ffed;
            border-color: #b7eb8f;
        }
    </style>
</head>
<body>
    <div class="flex h-screen bg-gray-100">
        <!-- 侧边栏 -->
        <div class="w-56 bg-white shadow-md">
            <div class="h-16 flex items-center justify-center text-2xl font-bold text-gray-800">
                <span>ChatBI</span>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3 text-gray-700 font-medium ant-menu-item-selected">
                        <span class="mr-3">📊</span> 模型管理
                    </li>
                    <li class="px-4 py-3 text-gray-500 font-medium hover:bg-gray-100 cursor-pointer">
                        <span class="mr-3">📚</span> 知识库
                    </li>
                    <li class="px-4 py-3 text-gray-500 font-medium hover:bg-gray-100 cursor-pointer">
                        <span class="mr-3">🛡️</span> 权限中心
                    </li>
                    <li class="px-4 py-3 text-gray-500 font-medium hover:bg-gray-100 cursor-pointer">
                        <span class="mr-3">⚙️</span> 系统管理
                    </li>
                </ul>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部 Header -->
            <header class="h-16 bg-white shadow-sm flex items-center justify-between px-6">
                <div>
                    <h1 class="text-lg font-semibold text-gray-800">数据集管理</h1>
                </div>
                <div>
                    <span class="text-gray-600">欢迎, superadmin</span>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-6">
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <!-- 操作区域 -->
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex space-x-2">
                            <input type="text" placeholder="搜索数据集名称..." class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 w-64">
                            <button class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                查询
                            </button>
                        </div>
                        <button class="px-4 py-2 text-white font-semibold rounded-md ant-btn-primary hover:bg-blue-500">
                            + 新建数据集
                        </button>
                    </div>

                    <!-- 表格 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full text-sm text-left text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase ant-table-thead">
                                <tr>
                                    <th scope="col" class="px-6 py-3">数据集名称</th>
                                    <th scope="col" class="px-6 py-3">所属数据源</th>
                                    <th scope="col" class="px-6 py-3">类型</th>
                                    <th scope="col" class="px-6 py-3">状态</th>
                                    <th scope="col" class="px-6 py-3">创建时间</th>
                                    <th scope="col" class="px-6 py-3">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-white border-b hover:bg-gray-50">
                                    <td class="px-6 py-4 font-medium text-gray-900">每日产品销售汇总</td>
                                    <td class="px-6 py-4">生产环境MySQL集群</td>
                                    <td class="px-6 py-4"><span class="ant-tag ant-tag-blue">VIEW</span></td>
                                    <td class="px-6 py-4"><span class="ant-tag ant-tag-green">ACTIVE</span></td>
                                    <td class="px-6 py-4">2024-05-10 10:30:00</td>
                                    <td class="px-6 py-4 flex space-x-3">
                                        <a href="#" class="font-medium text-blue-600 hover:underline">配置</a>
                                        <a href="#" class="font-medium text-blue-600 hover:underline">编辑</a>
                                        <a href="#" class="font-medium text-red-600 hover:underline">删除</a>
                                    </td>
                                </tr>
                                <tr class="bg-white border-b hover:bg-gray-50">
                                    <td class="px-6 py-4 font-medium text-gray-900">用户行为日志</td>
                                    <td class="px-6 py-4">数据仓库Hive</td>
                                    <td class="px-6 py-4"><span class="ant-tag ant-tag-blue">TABLE</span></td>
                                    <td class="px-6 py-4"><span class="ant-tag ant-tag-green">ACTIVE</span></td>
                                    <td class="px-6 py-4">2024-04-20 15:00:00</td>
                                    <td class="px-6 py-4 flex space-x-3">
                                        <a href="#" class="font-medium text-blue-600 hover:underline">配置</a>
                                        <a href="#" class="font-medium text-blue-600 hover:underline">编辑</a>
                                        <a href="#" class="font-medium text-red-600 hover:underline">删除</a>
                                    </td>
                                </tr>
                                <!-- More rows... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
