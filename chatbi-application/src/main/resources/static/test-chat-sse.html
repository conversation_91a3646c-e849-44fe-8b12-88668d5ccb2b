<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBi数据查询助手 - SSE实时版</title>
    <script src="js/tailwindcss.js"></script>
    <script src="js/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        #chat-window {
            height: calc(100vh - 16rem);
        }

        .message-bubble {
            max-width: 85%;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .message-bubble:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .clarification-btn {
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .clarification-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .message-input-container {
            padding: 16px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(229, 231, 235, 0.5);
            display: flex;
        }

        .data-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
            font-size: 14px;
            border: 1px solid #dfe2e5;
        }

        .data-table th, .data-table td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }

        .data-table th {
            background-color: #f6f8fa;
            font-weight: bold;
        }

        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .data-table tr:hover {
            background-color: #f0f8ff;
        }

        /* 进度条样式 */
        .progress-container {
            background-color: #f3f4f6;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            border-left: 4px solid #3b82f6;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);
            border-radius: 8px;
            overflow: hidden;
            margin: 12px 0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
            background-size: 200% 100%;
            border-radius: 8px;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            width: 0%;
            position: relative;
            animation: progressGlow 2s infinite;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
            animation: progressShine 1.5s infinite;
        }

        @keyframes progressGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-size: 15px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .progress-percentage {
            font-size: 16px;
            font-weight: 700;
            color: #3b82f6;
            float: right;
            text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
        }

        /* 实时状态样式 */
        .status-item {
            padding: 12px 16px;
            margin: 8px 0;
            border-radius: 10px;
            font-size: 14px;
            border-left: 4px solid;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            position: relative;
            overflow: hidden;
        }

        .status-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .status-item.active::before {
            transform: translateX(100%);
        }

        .status-thinking {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #f59e0b;
            color: #92400e;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
        }

        .status-sql {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;
            color: #1e40af;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .status-data {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-color: #10b981;
            color: #065f46;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
        }

        .status-response {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            border-color: #6366f1;
            color: #4338ca;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
        }

        .status-error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border-color: #ef4444;
            color: #dc2626;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .status-item.active {
            transform: translateX(4px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* 性能统计样式 */
        .performance-stats {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            margin-top: 8px;
            font-size: 12px;
            color: #64748b;
        }

        .performance-item {
            display: inline-block;
            margin-right: 16px;
        }

        /* 思考过程样式 */
        .thought-process {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: #475569;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .thought-process-header {
            font-size: 13px;
            font-weight: 600;
            color: #334155;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .thought-process-toggle {
            background: none;
            border: none;
            color: #3b82f6;
            cursor: pointer;
            font-size: 12px;
            margin-left: 8px;
            text-decoration: underline;
        }

        .thought-process-content {
            display: none;
        }

        .thought-process-content.expanded {
            display: block;
        }

        /* 动画效果 */
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto max-w-5xl h-screen flex flex-col">
        <!-- 头部 -->
        <header class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white p-8 relative overflow-hidden">
            <!-- 背景装饰 -->
            <div class="absolute inset-0">
                <div class="absolute top-4 left-4 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                <div class="absolute top-8 right-8 w-16 h-16 bg-white/10 rounded-full blur-lg animate-pulse delay-75"></div>
                <div class="absolute bottom-4 left-1/3 w-12 h-12 bg-white/10 rounded-full blur-md animate-pulse delay-150"></div>
            </div>

            <div class="relative z-10">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">ChatBi数据查询助手</h1>
                        <p class="text-blue-100">SSE实时进度版 - 真实反馈各阶段处理情况</p>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                            <label for="role-selector" class="text-sm font-medium text-blue-100 mr-3">当前角色:</label>
                            <select id="role-selector" class="px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg text-sm text-white focus:outline-none focus:ring-2 focus:ring-white/50">
                                <!-- 角色将在这里动态加载 -->
                            </select>
                        </div>
                        <div class="text-sm text-blue-100 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                            会话ID: <span id="session-display" class="font-mono text-white">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 聊天窗口 -->
        <div id="chat-window" class="flex-1 overflow-y-auto p-6 space-y-6 bg-gradient-to-b from-gray-50 to-white">
            <!-- 欢迎消息 -->
            <div class="flex justify-start">
                <div class="message-bubble bg-gradient-to-r from-white to-blue-50 rounded-2xl p-6 shadow-lg border border-blue-100 max-w-4xl">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="text-sm font-medium text-gray-700">ChatBi助手</div>
                    </div>
                    <div class="text-gray-800 leading-relaxed">
                        <div class="mb-4">
                            <span class="text-lg font-semibold text-gray-900">👋 您好！我是ChatBi数据查询助手</span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div class="bg-white/70 rounded-lg p-4 border border-blue-100">
                                <h4 class="font-semibold text-blue-700 mb-2">🔍 核心功能</h4>
                                <ul class="text-sm space-y-1">
                                    <li>• 查询和分析业务数据</li>
                                    <li>• 生成数据可视化图表</li>
                                    <li>• 提供数据洞察和建议</li>
                                </ul>
                            </div>
                            <div class="bg-white/70 rounded-lg p-4 border border-purple-100">
                                <h4 class="font-semibold text-purple-700 mb-2">⚡ SSE实时特性</h4>
                                <ul class="text-sm space-y-1">
                                    <li>• 实时显示查询处理进度</li>
                                    <li>• 展示各阶段真实处理时间</li>
                                    <li>• 显示AI的完整思考过程</li>
                                    <li>• 支持角色权限管理</li>
                                </ul>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border-l-4 border-blue-400">
                            <p class="text-sm text-gray-700">
                                <strong>💡 使用提示：</strong> 请先选择您的角色，然后输入数据查询需求，我会实时反馈处理进度和思考过程。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="message-input-container bg-white/95 backdrop-blur-lg border-t border-gray-200/50">
            <div class="flex-1 flex space-x-4">
                <input
                    type="text"
                    id="user-input"
                    placeholder="请输入您的数据查询需求..."
                    class="flex-1 px-6 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-300 hover:shadow-md"
                >
                <button
                    id="send-btn"
                    class="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        发送
                    </span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const chatWindow = document.getElementById('chat-window');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const roleSelector = document.getElementById('role-selector');
        const sessionDisplay = document.getElementById('session-display');

        // 生成会话ID
        const userId = 'user_' + Math.random().toString(36).substr(2, 9);
        let sessionId = 'session_' + Math.random().toString(36).substr(2, 9);
        sessionDisplay.textContent = sessionId;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            fetchAndPopulateRoles();

            // 添加测试按钮（开发调试用）
            if (window.location.search.includes('debug=true')) {
                addDebugTestButton();
            }

            // 根据URL参数自动测试不同功能
            const urlParams = new URLSearchParams(window.location.search);
            const testType = urlParams.get('test');

            if (testType) {
                setTimeout(() => {
                    switch (testType) {
                        case 'connection':
                            testConnection();
                            break;
                        case 'sse':
                            testSSEConnection();
                            break;
                        case 'basic':
                            testBasicMessage();
                            break;
                        case 'clarification':
                            testClarificationOptions();
                            break;
                        case 'acknowledgement':
                            testAcknowledgementResponse();
                            break;
                        case 'data':
                            testDataResponse();
                            break;
                        case 'all':
                            testAllResponseTypes();
                            break;
                        default:
                            console.log('未知测试类型:', testType);
                    }
                }, 1000);
            }
        });

        // 事件监听
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // 获取并填充角色
        async function fetchAndPopulateRoles() {
            try {
                const response = await fetch('/api/admin/permissions/roles/all');
                if (!response.ok) {
                    throw new Error(`无法获取角色列表: ${response.status}`);
                }
                const roles = await response.json();

                // 添加一个无角色的默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '游客 (无角色)';
                roleSelector.appendChild(defaultOption);

                // 动态填充从API获取的角色
                roles.forEach(roleName => {
                    const option = document.createElement('option');
                    option.value = roleName;
                    option.textContent = roleName;
                    roleSelector.appendChild(option);
                });

            } catch (error) {
                console.error('获取角色失败:', error);
                const errorOption = document.createElement('option');
                errorOption.value = '';
                errorOption.textContent = '无法加载角色';
                roleSelector.appendChild(errorOption);
                roleSelector.disabled = true;
            }
        }

        // 发送消息
        async function sendMessage() {
            const queryText = userInput.value.trim();
            if (!queryText) return;

            // 禁用输入
            toggleInput(false);
            addMessage(queryText, 'user');
            userInput.value = '';

            // 创建进度容器
            const progressContainer = createProgressContainer();
            
            try {
                const selectedRole = roleSelector.value;
                const additionalParams = {
                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                };
                if (selectedRole) {
                    additionalParams.userRole = selectedRole;
                }

                const requestBody = {
                    userId: userId,
                    sessionId: sessionId,
                    queryText: queryText,
                    additionalParams: additionalParams
                };

                // 使用SSE接口
                await handleSSEQuery(requestBody, progressContainer);

            } catch (error) {
                console.error('发送消息时出错:', error);
                addMessage(`抱歉，连接服务器时发生错误: ${error.message}`, 'agent', 'ERROR');
                progressContainer.remove();
            } finally {
                toggleInput(true);
            }
        }

        // 处理SSE查询
        async function handleSSEQuery(requestBody, progressContainer) {
            return new Promise((resolve, reject) => {
                console.log('🚀 开始SSE查询请求');
                console.log('📝 请求体:', requestBody);

                // 创建AbortController用于超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    console.log('⏰ 请求超时，取消请求');
                    controller.abort();
                }, 120000); // 2分钟超时

                // 使用fetch API处理SSE流，因为EventSource不支持POST请求
                fetch('/api/v1/chatbi/conversation/query-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                }).then(response => {
                    clearTimeout(timeoutId);
                    console.log('📡 收到响应:', response.status, response.statusText);
                    console.log('响应:', response);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';

                    function readStream() {
                        return reader.read().then(({ done, value }) => {
                            if (done) {
                                // 延迟移除进度容器，让用户看到完成状态
                                setTimeout(() => {
                                    // progressContainer.remove();
                                }, 2000);
                                resolve();
                                return;
                            }


                            // 将新数据添加到缓冲区
                            buffer += decoder.decode(value, { stream: true });

                            // 按行分割处理
                            const lines = buffer.split('\n');
                            // 保留最后一行（可能不完整）
                            buffer = lines.pop() || '';

                            for (const line of lines) {
                                const trimmedLine = line.trim();
                               // console.log("trimmedLine:","#"+trimmedLine+"#")
                                if (trimmedLine.startsWith('data:')) {
                                    try {
                                        const jsonData = trimmedLine.substring(5).trim();
                                        if (jsonData && jsonData !== '') {
                                            const data = JSON.parse(jsonData);
                                            handleSSEEvent(data, progressContainer);
                                        }
                                    } catch (e) {
                                        console.error('解析SSE数据失败:', e, 'Line:', trimmedLine);
                                    }
                                } else if (trimmedLine.startsWith('event:')) {
                                    // 处理事件类型行（如果需要）
                                    const eventType = trimmedLine.substring(6).trim();
                                    // console.log('SSE事件类型:', eventType);
                                } else if (trimmedLine === '') {
                                    // 空行，SSE事件分隔符
                                    continue;
                                }
                            }

                            return readStream();
                        });
                    }

                    return readStream();
                }).catch(error => {
                    clearTimeout(timeoutId);
                    console.error('❌ SSE连接失败:', error);
                    console.error('错误类型:', error.name);
                    console.error('错误消息:', error.message);

                    let errorMessage = 'SSE连接失败';
                    if (error.name === 'AbortError') {
                        errorMessage = '请求超时，请检查网络连接或稍后重试';
                    } else if (error.message.includes('Failed to fetch')) {
                        errorMessage = '网络连接失败，请检查服务是否正常运行';
                    } else {
                        errorMessage = `连接失败: ${error.message}`;
                    }

                    // 更新进度容器显示错误
                    updateProgress(progressContainer, 0, `❌ ${errorMessage}`);
                    setTimeout(() => {
                        progressContainer.remove();
                    }, 3000);

                    addMessage(errorMessage, 'agent', 'ERROR');
                    reject(error);
                });
            });
        }

        // 处理SSE事件 - 实时显示进度内容
        function handleSSEEvent(data, progressContainer) {
            const eventType = data.type || 'unknown';
            const timestamp = data.timestamp ? new Date(data.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

            // console.log(`[SSE事件] ${eventType}:`, data);

            switch (eventType) {
                case 'progress':
                    // 实时显示进度更新
                    // console.log(`[进度] ${data.progress}% - ${data.message} (时间戳: ${timestamp})`);
                    updateProgress(progressContainer, data.progress, data.message, data.timestamp);

                    // 如果进度达到100%，准备折叠
                    if (data.progress >= 100) {
                        collapseProgressOnComplete(progressContainer);
                    }
                    break;

                case 'thinking':
                    console.log(`[思考] ${data.message}`);
                    addStatusItem(progressContainer, `💭 ${data.message}`, 'thinking');
                    break;

                case 'sql_generation':
                    console.log(`[SQL生成] ${data.message}`);
                    addStatusItem(progressContainer, `🔧 ${data.message}`, 'sql');
                    break;

                case 'data_retrieval':
                    console.log(`[数据检索] ${data.message}`);
                    addStatusItem(progressContainer, `📊 ${data.message}`, 'data');
                    break;

                case 'response_generation':
                    console.log(`[响应生成] ${data.message}`);
                    addStatusItem(progressContainer, `📝 ${data.message}`, 'response');
                    break;

                case 'error':
                    console.error(`[错误] ${data.message}`);
                    addStatusItem(progressContainer, `❌ ${data.message}`, 'error');
                    addMessage(`查询失败: ${data.message}`, 'agent', 'ERROR');
                    break;

                case 'result':
                    //console.log('[最终结果]', data);
                    //console.log('响应类型:', data.responseType);
                    //console.log('消息内容:', data.messageToUser);

                    // 确保进度容器已折叠（不隐藏）
                    if (progressContainer) {
                        collapseProgressOnComplete(progressContainer);

                        // 更新进度容器状态为完成
                        const header = progressContainer.querySelector('.progress-header');
                        if (header) {
                            header.classList.add('bg-green-50', 'border-green-200');
                            header.classList.remove('animate-pulse');

                            // 停止动画
                            const icon = header.querySelector('.w-10');
                            if (icon) {
                                icon.classList.remove('animate-pulse');
                                icon.innerHTML = `
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                `;
                            }
                        }
                    }

                    // 处理最终结果
                    try {
                        handleAgentResponse(data);
                        console.log('✅ handleAgentResponse 执行成功');
                    } catch (error) {
                        console.error('❌ handleAgentResponse 执行失败:', error);
                        // 降级处理：显示基本消息
                        addMessage(data.messageToUser || '收到响应', 'agent', data.responseType);
                    }
                    break;

                case 'performance':
                    console.log('[性能统计]', data);
                    // 添加性能统计
                    addPerformanceStats(progressContainer, data);
                    break;

                default:
                    console.warn('未知SSE事件类型:', eventType, data);
                    // 显示未知事件
                    addStatusItem(progressContainer, `❓ 未知事件: ${data.message || JSON.stringify(data)}`, 'unknown');
            }
        }

        // 创建优化的进度容器
        function createProgressContainer() {
            const container = document.createElement('div');
            container.className = 'flex justify-start fade-in';

            const bubble = document.createElement('div');
            bubble.className = 'message-bubble bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 shadow-lg border border-blue-200 backdrop-blur-sm max-w-5xl';

            // 创建可折叠的进度容器
            const progressContainer = createCollapsibleProgressContainer();
            bubble.appendChild(progressContainer);

            container.appendChild(bubble);
            chatWindow.appendChild(container);
            chatWindow.scrollTop = chatWindow.scrollHeight;

            return progressContainer;
        }

        // 创建可折叠的进度容器
        function createCollapsibleProgressContainer() {
            const container = document.createElement('div');
            container.className = 'progress-container';

            // 头部区域
            const header = document.createElement('div');
            header.className = 'progress-header flex items-center justify-between p-4 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors';
            header.innerHTML = `
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3 animate-pulse">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-blue-700">ChatBi智能助手</div>
                        <div class="text-xs text-gray-500 progress-status">正在处理您的查询...</div>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="progress-percentage text-lg font-bold text-blue-600 mr-3">0%</div>
                    <svg class="w-5 h-5 text-gray-400 collapse-icon transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            `;

            // 进度条
            const progressBarContainer = document.createElement('div');
            progressBarContainer.className = 'progress-bar-container mt-3 px-4';
            progressBarContainer.innerHTML = `
                <div class="progress-bar bg-gray-200 rounded-full h-3 overflow-hidden shadow-inner">
                    <div class="progress-fill bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 h-full rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                        <div class="absolute inset-0 bg-white opacity-30 animate-pulse"></div>
                    </div>
                </div>
                <div class="progress-text text-xs text-gray-600 mt-2">准备开始处理...</div>
            `;

            // 详细进度区域（可折叠）
            const detailsContainer = document.createElement('div');
            detailsContainer.className = 'progress-details mt-4 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden transition-all duration-300';
            detailsContainer.style.maxHeight = '500px';

            const detailsContent = document.createElement('div');
            detailsContent.className = 'progress-details-content p-4';

            // 阶段进度
            const stagesContainer = document.createElement('div');
            stagesContainer.className = 'stages-container space-y-2';

            // 时间统计
            const timeStats = document.createElement('div');
            timeStats.className = 'time-stats mt-4 p-3 bg-white rounded border border-gray-200';
            timeStats.innerHTML = `
                <div class="text-xs font-medium text-gray-700 mb-2">⏱️ 处理时间统计</div>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div>开始时间: <span class="start-time font-mono">${new Date().toLocaleTimeString()}</span></div>
                    <div>当前耗时: <span class="elapsed-time font-mono text-blue-600">0ms</span></div>
                </div>
            `;

            detailsContent.appendChild(stagesContainer);
            detailsContent.appendChild(timeStats);
            detailsContainer.appendChild(detailsContent);

            container.appendChild(header);
            container.appendChild(progressBarContainer);
            container.appendChild(detailsContainer);

            // 添加折叠功能
            let isCollapsed = false;
            header.addEventListener('click', () => {
                isCollapsed = !isCollapsed;
                const icon = header.querySelector('.collapse-icon');

                if (isCollapsed) {
                    detailsContainer.style.maxHeight = '0';
                    detailsContainer.style.padding = '0';
                    icon.style.transform = 'rotate(-90deg)';
                } else {
                    detailsContainer.style.maxHeight = '500px';
                    detailsContainer.style.padding = '';
                    icon.style.transform = 'rotate(0deg)';
                }
            });

            return container;
        }

        // 更新进度 - 优化版本，支持阶段显示
        function updateProgress(container, progress, message, timestamp) {
            // console.log(`[进度更新] ${progress}% - ${message}`);

            const progressText = container.querySelector('.progress-text');
            const progressPercentage = container.querySelector('.progress-percentage');
            const progressFill = container.querySelector('.progress-fill');
            const progressStatus = container.querySelector('.progress-status');
            const elapsedTimeElement = container.querySelector('.elapsed-time');

            // 更新进度文本
            if (progressText) {
                progressText.textContent = message;
            }

            // 更新进度百分比
            if (progressPercentage) {
                progressPercentage.textContent = `${progress}%`;
            }

            // 更新进度条填充
            if (progressFill) {
                const clampedProgress = Math.max(0, Math.min(100, progress));
                progressFill.style.width = `${clampedProgress}%`;

                // 根据进度改变颜色
                if (clampedProgress < 30) {
                    progressFill.className = 'progress-fill bg-gradient-to-r from-blue-500 to-blue-600 h-full rounded-full transition-all duration-500 ease-out relative';
                } else if (clampedProgress < 70) {
                    progressFill.className = 'progress-fill bg-gradient-to-r from-blue-500 via-purple-500 to-purple-600 h-full rounded-full transition-all duration-500 ease-out relative';
                } else {
                    progressFill.className = 'progress-fill bg-gradient-to-r from-purple-500 via-pink-500 to-green-500 h-full rounded-full transition-all duration-500 ease-out relative';
                }
            }

            // 更新状态文本
            if (progressStatus) {
                if (progress >= 100) {
                    progressStatus.textContent = '处理完成 ✅';
                } else if (progress >= 90) {
                    progressStatus.textContent = '即将完成...';
                } else {
                    progressStatus.textContent = '正在处理您的查询...';
                }
            }

            // 更新耗时
            if (elapsedTimeElement && timestamp) {
                const startTime = container.querySelector('.start-time')?.textContent;
                if (startTime) {
                    const elapsed = timestamp - new Date(`1970-01-01 ${startTime}`).getTime();
                    elapsedTimeElement.textContent = `${elapsed}ms`;
                }
            }

            // 添加阶段记录
            addStageRecord(container, progress, message, timestamp);

            // 滚动到最新位置
            chatWindow.scrollTop = chatWindow.scrollHeight;
        }

        // 添加阶段记录
        function addStageRecord(container, progress, message, timestamp) {
            const stagesContainer = container.querySelector('.stages-container');
            if (!stagesContainer) return;

            // 检查是否是新阶段
            const lastStage = stagesContainer.lastElementChild;
            if (lastStage && lastStage.textContent.includes(message.split('(')[0].trim())) {
                // 更新现有阶段
                const timeSpan = lastStage.querySelector('.stage-time');
                if (timeSpan && timestamp) {
                    timeSpan.textContent = new Date(timestamp).toLocaleTimeString();
                }
                return;
            }

            // 创建新阶段记录
            const stageElement = document.createElement('div');
            stageElement.className = 'stage-item flex items-center justify-between p-2 bg-white rounded border border-gray-200 text-xs';

            const progressIcon = getProgressIcon(progress);
            const timeStr = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

            stageElement.innerHTML = `
                <div class="flex items-center">
                    <span class="stage-icon mr-2">${progressIcon}</span>
                    <span class="stage-message">${message}</span>
                </div>
                <span class="stage-time text-gray-500 font-mono">${timeStr}</span>
            `;

            stagesContainer.appendChild(stageElement);

            // 限制显示的阶段数量
            const stages = stagesContainer.children;
            if (stages.length > 10) {
                stagesContainer.removeChild(stages[0]);
            }
        }

        // 获取进度图标
        function getProgressIcon(progress) {
            if (progress >= 100) return '✅';
            if (progress >= 90) return '🎯';
            if (progress >= 70) return '⚡';
            if (progress >= 50) return '🔄';
            if (progress >= 30) return '⏳';
            return '🚀';
        }

        // 处理完成后折叠进度
        function collapseProgressOnComplete(container) {
            const header = container.querySelector('.progress-header');
            const detailsContainer = container.querySelector('.progress-details');
            const icon = container.querySelector('.collapse-icon');

            if (header && detailsContainer && icon) {
                // 添加完成样式
                header.classList.add('bg-green-50', 'border-green-200');
                header.classList.remove('hover:bg-blue-50');

                // 自动折叠
                setTimeout(() => {
                    detailsContainer.style.maxHeight = '0';
                    detailsContainer.style.padding = '0';
                    icon.style.transform = 'rotate(-90deg)';

                    // 更新状态
                    const progressStatus = container.querySelector('.progress-status');
                    if (progressStatus) {
                        progressStatus.textContent = '处理完成，点击展开查看详情';
                    }
                }, 2000); // 2秒后自动折叠
            }
        }

        // 添加性能统计
        function addPerformanceStats(container, data) {
            const timeStats = container.querySelector('.time-stats');
            if (!timeStats) return;

            // 更新时间统计
            const elapsedTimeElement = timeStats.querySelector('.elapsed-time');
            if (elapsedTimeElement && data.totalTime) {
                elapsedTimeElement.textContent = `${data.totalTime}ms`;
            }

            // 添加详细性能信息
            const existingPerf = timeStats.querySelector('.performance-details');
            if (existingPerf) {
                existingPerf.remove();
            }

            const perfDetails = document.createElement('div');
            perfDetails.className = 'performance-details mt-2 p-2 bg-gray-100 rounded text-xs';
            perfDetails.innerHTML = `
                <div class="font-medium text-gray-700 mb-1">📊 性能详情</div>
                <div class="grid grid-cols-2 gap-1">
                    <div>总耗时: <span class="font-mono text-blue-600">${data.totalTime || 0}ms</span></div>
                    <div>查询耗时: <span class="font-mono text-green-600">${data.queryTime || 0}ms</span></div>
                    <div>开销: <span class="font-mono text-orange-600">${data.overhead || 0}ms</span></div>
                    <div>效率: <span class="font-mono text-purple-600">${data.queryTime && data.totalTime ? Math.round((data.queryTime / data.totalTime) * 100) : 0}%</span></div>
                </div>
            `;

            timeStats.appendChild(perfDetails);
        }

        // 添加状态项
        function addStatusItem(container, message, type) {
            const statusContainer = container.querySelector('.status-container');
            if (!statusContainer) return;

            const statusItem = document.createElement('div');
            statusItem.className = `status-item status-${type} active fade-in`;

            // 添加图标
            const icon = getStatusIcon(type);
            statusItem.innerHTML = `
                <div class="flex items-center">
                    <div class="status-icon mr-3">${icon}</div>
                    <div class="flex-1">${message}</div>
                    <div class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</div>
                </div>
            `;

            // 移除之前状态项的active类
            const previousItems = statusContainer.querySelectorAll('.status-item.active');
            previousItems.forEach(item => {
                if (item !== statusItem) {
                    item.classList.remove('active');
                    item.classList.add('completed');
                }
            });

            statusContainer.appendChild(statusItem);
            chatWindow.scrollTop = chatWindow.scrollHeight;
        }

        // 获取状态图标
        function getStatusIcon(type) {
            const icons = {
                thinking: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
                sql: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',
                data: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',
                response: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>',
                error: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'
            };
            return icons[type] || icons.thinking;
        }

        // 添加性能统计
        function addPerformanceStats(container, stats) {
            const statusContainer = container.querySelector('.status-container');
            if (!statusContainer) return;

            const perfDiv = document.createElement('div');
            perfDiv.className = 'performance-stats fade-in';
            perfDiv.innerHTML = `
                <div class="performance-item"><strong>总耗时:</strong> ${stats.totalTime}ms</div>
                <div class="performance-item"><strong>查询耗时:</strong> ${stats.queryTime}ms</div>
                <div class="performance-item"><strong>处理开销:</strong> ${stats.overhead}ms</div>
            `;

            statusContainer.appendChild(perfDiv);
            chatWindow.scrollTop = chatWindow.scrollHeight;
        }

        // 切换输入状态
        function toggleInput(enabled) {
            userInput.disabled = !enabled;
            sendBtn.disabled = !enabled;
            if (enabled) {
                userInput.focus();
            }
        }

        // 添加消息
        function addMessage(content, sender, messageType = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `flex ${sender === 'user' ? 'justify-end' : 'justify-start'} fade-in`;

            const bubble = document.createElement('div');
            bubble.className = `message-bubble ${sender === 'user' ? 'bg-blue-600 text-white' : 'bg-white border border-gray-200'} rounded-lg p-4 shadow-sm`;

            if (sender === 'agent') {
                const senderLabel = document.createElement('div');
                senderLabel.className = 'text-sm text-gray-600 mb-1';
                senderLabel.textContent = 'ChatBi助手';
                bubble.appendChild(senderLabel);
            }

            if (content) {
                const contentDiv = document.createElement('div');
                contentDiv.className = sender === 'user' ? 'text-white' : 'text-gray-800';

                if (messageType === 'ERROR') {
                    contentDiv.className += ' text-red-600';
                }

                contentDiv.textContent = content;
                bubble.appendChild(contentDiv);
            }

            messageDiv.appendChild(bubble);

            chatWindow.appendChild(messageDiv);
            chatWindow.scrollTop = chatWindow.scrollHeight;

            // 返回气泡元素，以便后续添加表格、图表等内容
            return bubble;
        }

        // 处理Agent响应 - 美化显示所有类型的最终结果
        function handleAgentResponse(data) {
            console.log('处理AI响应，完整数据:', data);

            // 更新会话ID
            if (data.sessionId) {
                sessionId = data.sessionId;
                sessionDisplay.textContent = sessionId;
                console.log('🆔 更新会话ID:', sessionId);
            }

            // 显示思考过程（如果存在）
            if (data.thoughtProcess) {
                addThoughtProcess(data.thoughtProcess);
            }

            // 根据响应类型创建美化的消息气泡
            const messageBubble = createResponseBubble(data);

            // 添加到聊天窗口
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex justify-start fade-in';
            messageDiv.appendChild(messageBubble);

            chatWindow.appendChild(messageDiv);
            chatWindow.scrollTop = chatWindow.scrollHeight;
        }

        // 创建美化的响应气泡
        function createResponseBubble(data) {
            const bubble = document.createElement('div');
            bubble.className = 'message-bubble bg-gradient-to-r from-white to-blue-50 rounded-2xl p-6 shadow-lg border border-blue-100 max-w-5xl';

            // 添加头部
            const header = createResponseHeader(data);
            bubble.appendChild(header);

            // 根据响应类型添加内容
            switch (data.responseType) {
                case 'ACKNOWLEDGEMENT':
                    addAcknowledgementContent(bubble, data);
                    break;
                case 'DATA':
                    addDataContent(bubble, data);
                    break;
                case 'CLARIFICATION_NEEDED':
                    addClarificationContent(bubble, data);
                    break;
                case 'ERROR':
                    addErrorContent(bubble, data);
                    break;
                default:
                    addDefaultContent(bubble, data);
            }

            return bubble;
        }

        // 创建响应头部
        function createResponseHeader(data) {
            const header = document.createElement('div');
            header.className = 'flex items-center justify-between mb-4 pb-3 border-b border-blue-100';

            const leftSection = document.createElement('div');
            leftSection.className = 'flex items-center';

            // AI头像和标题
            const avatar = document.createElement('div');
            avatar.className = 'w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3';
            avatar.innerHTML = '<span class="text-white text-sm font-bold">AI</span>';

            const titleSection = document.createElement('div');
            const title = document.createElement('div');
            title.className = 'text-sm font-semibold text-gray-700';
            title.textContent = 'ChatBi助手';

            const responseTypeLabel = document.createElement('div');
            responseTypeLabel.className = 'text-xs text-gray-500';
            responseTypeLabel.textContent = getResponseTypeLabel(data.responseType);

            titleSection.appendChild(title);
            titleSection.appendChild(responseTypeLabel);

            leftSection.appendChild(avatar);
            leftSection.appendChild(titleSection);

            // 右侧状态
            const rightSection = document.createElement('div');
            rightSection.className = 'text-xs text-gray-500';
            rightSection.innerHTML = `
                <div>会话: ${data.sessionId || 'unknown'}</div>
                <div>${new Date().toLocaleTimeString()}</div>
            `;

            header.appendChild(leftSection);
            header.appendChild(rightSection);

            return header;
        }

        // 获取响应类型标签
        function getResponseTypeLabel(responseType) {
            const labels = {
                'ACKNOWLEDGEMENT': '系统消息',
                'DATA': '数据查询结果',
                'CLARIFICATION_NEEDED': '需要澄清',
                'ERROR': '错误信息',
                'DATA_UNAVAILABLE': '数据不可用'
            };
            return labels[responseType] || responseType;
        }

        // 添加ACKNOWLEDGEMENT类型内容
        function addAcknowledgementContent(bubble, data) {
            const container = document.createElement('div');
            container.className = 'bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-lg';

            const icon = document.createElement('div');
            icon.className = 'flex items-center mb-3';
            icon.innerHTML = `
                <svg class="w-6 h-6 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm font-medium text-yellow-800">系统提示</span>
            `;

            const message = document.createElement('div');
            message.className = 'text-sm text-yellow-700 leading-relaxed';
            message.textContent = data.messageToUser || data.responseText || '系统消息';

            container.appendChild(icon);
            container.appendChild(message);
            bubble.appendChild(container);
        }

        // 添加DATA类型内容
        function addDataContent(bubble, data) {
            console.log('🔵 开始添加DATA类型内容');
            console.log('📊 数据摘要:', data.summary);
            console.log('📋 查询结果:', data.queryResult);
            console.log('🎨 显示建议:', data.displaySuggestion);

            // 1. 添加摘要（如果存在）
            if (data.summary) {
                console.log('📝 添加数据摘要');
            } else {
                console.log('⚠️ 没有数据摘要');
            }
            if (data.summary) {
                const summaryContainer = document.createElement('div');
                summaryContainer.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4';

                const summaryTitle = document.createElement('div');
                summaryTitle.className = 'flex items-center mb-2';
                summaryTitle.innerHTML = `
                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="text-sm font-medium text-blue-800">数据摘要</span>
                `;

                const summaryText = document.createElement('div');
                summaryText.className = 'text-sm text-blue-700';
                summaryText.textContent = data.summary;

                summaryContainer.appendChild(summaryTitle);
                summaryContainer.appendChild(summaryText);
                bubble.appendChild(summaryContainer);
            }

            // 2. 添加数据表格和图表
            console.log('📊 检查查询结果数据');
            if (data.queryResult && data.queryResult.rows && data.queryResult.rows.length > 0) {
                console.log('✅ 有查询结果数据，行数:', data.queryResult.rows.length);
            } else {
                console.log('⚠️ 没有查询结果数据');
            }
            if (data.queryResult && data.queryResult.rows && data.queryResult.rows.length > 0) {
                let displayStrategy = 'both';
                if (data.displaySuggestion && data.displaySuggestion.dataVisualizationConfig) {
                    displayStrategy = data.displaySuggestion.dataVisualizationConfig.displayStrategy || 'both';
                }

                let tableConfig = null, chartConfig = null;
                if (data.displaySuggestion && data.displaySuggestion.dataVisualizationConfig && data.displaySuggestion.dataVisualizationConfig.success) {
                    try {
                        if (data.displaySuggestion.dataVisualizationConfig.tableConfigJson) {
                            tableConfig = JSON.parse(data.displaySuggestion.dataVisualizationConfig.tableConfigJson);
                        }
                        if (data.displaySuggestion.dataVisualizationConfig.chartConfigJson) {
                            chartConfig = JSON.parse(data.displaySuggestion.dataVisualizationConfig.chartConfigJson);
                        }
                    } catch(e) { console.error("解析 table/chart config JSON 失败", e); }
                }

                // 添加表格
                console.log('📋 显示策略:', displayStrategy);
                if (displayStrategy === 'both' || displayStrategy === 'table_primary' || displayStrategy === 'table_only') {
                    console.log('🔧 创建表格元素');
                    const tableElement = createTableElement(data.queryResult, tableConfig, data.displaySuggestion);
                    if (tableElement) {
                        console.log('✅ 表格元素创建成功，添加到气泡');
                        bubble.appendChild(tableElement);
                    } else {
                        console.log('❌ 表格元素创建失败');
                    }
                } else {
                    console.log('⚠️ 显示策略不包含表格');
                }

                // 添加图表
                if (displayStrategy === 'both' || displayStrategy === 'chart_only') {
                    const chartElement = createChartElement(chartConfig, data.displaySuggestion);
                    if (chartElement) bubble.appendChild(chartElement);
                }
            }

            // 3. 添加分析内容
            if (data.messageToUser) {
                const analysisContainer = document.createElement('div');
                analysisContainer.className = 'bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4';

                const analysisTitle = document.createElement('div');
                analysisTitle.className = 'flex items-center mb-3';
                analysisTitle.innerHTML = `
                    <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">智能分析</span>
                `;

                const analysisContent = document.createElement('div');
                analysisContent.className = 'text-sm text-gray-700 leading-relaxed';

                if (data.messageToUser.includes('📊') || data.messageToUser.includes('🔍') || data.messageToUser.includes('📈') || data.messageToUser.includes('💡')) {
                    analysisContent.innerHTML = formatAnalysisText(data.messageToUser);
                } else {
                    analysisContent.textContent = data.messageToUser;
                }

                analysisContainer.appendChild(analysisTitle);
                analysisContainer.appendChild(analysisContent);
                bubble.appendChild(analysisContainer);
            }
        }

        // 添加CLARIFICATION类型内容
        function addClarificationContent(bubble, data) {
            const container = document.createElement('div');
            container.className = 'bg-purple-50 border border-purple-200 rounded-lg p-4';

            const header = document.createElement('div');
            header.className = 'flex items-center mb-3';
            header.innerHTML = `
                <svg class="w-6 h-6 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm font-medium text-purple-800">需要澄清</span>
            `;

            const message = document.createElement('div');
            message.className = 'text-sm text-purple-700 mb-4';
            message.textContent = data.messageToUser || '请选择以下选项：';

            container.appendChild(header);
            container.appendChild(message);

            // 添加澄清选项
            if (data.clarificationOptions && data.clarificationOptions.length > 0) {
                const optionsContainer = createClarificationOptions(data.clarificationOptions);
                container.appendChild(optionsContainer);
            }

            bubble.appendChild(container);
        }

        // 添加ERROR类型内容
        function addErrorContent(bubble, data) {
            const container = document.createElement('div');
            container.className = 'bg-red-50 border-l-4 border-red-400 p-4 rounded-lg';

            const header = document.createElement('div');
            header.className = 'flex items-center mb-3';
            header.innerHTML = `
                <svg class="w-6 h-6 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm font-medium text-red-800">错误信息</span>
            `;

            const message = document.createElement('div');
            message.className = 'text-sm text-red-700';
            message.textContent = data.messageToUser || data.responseText || '发生了未知错误';

            container.appendChild(header);
            container.appendChild(message);
            bubble.appendChild(container);
        }

        // 添加默认类型内容
        function addDefaultContent(bubble, data) {
            const container = document.createElement('div');
            container.className = 'bg-gray-50 border border-gray-200 rounded-lg p-4';

            const message = document.createElement('div');
            message.className = 'text-sm text-gray-700';
            message.textContent = data.messageToUser || data.responseText || '收到响应';

            container.appendChild(message);
            bubble.appendChild(container);
        }





        // 添加思考过程显示
        function addThoughtProcess(thoughtProcess) {
            if (!thoughtProcess || thoughtProcess.trim() === '') {
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex justify-start fade-in';

            const bubble = document.createElement('div');
            bubble.className = 'message-bubble bg-white rounded-lg p-4 shadow-sm border border-gray-200';

            const senderLabel = document.createElement('div');
            senderLabel.className = 'text-sm text-gray-600 mb-2';
            senderLabel.textContent = 'ChatBi助手 - AI思考过程';
            bubble.appendChild(senderLabel);

            const thoughtContainer = document.createElement('div');
            thoughtContainer.className = 'thought-process';

            const header = document.createElement('div');
            header.className = 'thought-process-header';
            header.innerHTML = '🧠 AI思考过程 <button class="thought-process-toggle" onclick="toggleThoughtProcess(this)">展开</button>';

            const content = document.createElement('div');
            content.className = 'thought-process-content';
            content.textContent = thoughtProcess;

            thoughtContainer.appendChild(header);
            thoughtContainer.appendChild(content);
            bubble.appendChild(thoughtContainer);
            messageDiv.appendChild(bubble);

            chatWindow.appendChild(messageDiv);
            chatWindow.scrollTop = chatWindow.scrollHeight;
        }

        // 切换思考过程显示
        function toggleThoughtProcess(button) {
            const content = button.parentElement.nextElementSibling;
            const isExpanded = content.classList.contains('expanded');

            if (isExpanded) {
                content.classList.remove('expanded');
                button.textContent = '展开';
            } else {
                content.classList.add('expanded');
                button.textContent = '收起';
            }
        }

        // 创建表格元素 - 对照test-chat.html实现
        function createTableElement(queryResult, tableConfig, displaySuggestion) {
            if (!queryResult) return null;

            const container = document.createElement('div');
            container.className = 'overflow-x-auto my-4'; // 增加上下边距

            // 添加表格标题
            const tableTitle = displaySuggestion ? displaySuggestion.title : '查询结果';
            if (tableTitle) {
                const titleElement = document.createElement('h3');
                titleElement.textContent = tableTitle;
                titleElement.className = 'text-md font-semibold text-gray-700 mb-2'; // 标题样式
                container.appendChild(titleElement);
            }

            if (tableConfig && tableConfig.tableType === 'pivot') {
                console.log("检测到透视表配置，开始创建透视表...");
                const pivotTable = createPivotTable(queryResult, tableConfig);
                container.appendChild(pivotTable);
                return container;
            }

            console.log("创建标准表格...");
            const table = document.createElement('table');
            table.className = 'data-table w-full text-sm border-collapse border border-gray-300';

            // 表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            queryResult.columnHeaders.forEach(headerText => {
                const th = document.createElement('th');
                th.className = 'p-2 border border-gray-300 bg-gray-100 font-semibold text-left';
                th.textContent = headerText;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // 表体
            const tbody = document.createElement('tbody');
            queryResult.rows.forEach(row => {
                const tr = document.createElement('tr');
                tr.className = 'hover:bg-gray-50';
                row.forEach(cellData => {
                    const td = document.createElement('td');
                    td.className = 'p-2 border border-gray-300';
                    td.textContent = cellData;
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
            table.appendChild(tbody);

            container.appendChild(table);

            return container;
        }

        // 创建透视表 (Pivot Table) - 对照test-chat.html实现
        function createPivotTable(queryResult, tableConfig) {
            if (!tableConfig.pivotConfig) return createTableElement(queryResult, null);
            const { rowFields, columnFields, valueFields } = tableConfig.pivotConfig;

            if (!rowFields || !columnFields || !valueFields || rowFields.length === 0 || columnFields.length === 0 || valueFields.length === 0) {
                 console.error("透视表配置不完整！");
                 return createTableElement(queryResult, null);
            }

            const rowIndex = queryResult.columnHeaders.indexOf(rowFields[0]);
            const colIndex = queryResult.columnHeaders.indexOf(columnFields[0]);
            const valIndex = queryResult.columnHeaders.indexOf(valueFields[0]);

            if (rowIndex === -1 || colIndex === -1 || valIndex === -1) {
                console.error("透视表字段在查询结果中未找到！");
                return createTableElement(queryResult, null); // 回退到标准表格
            }

            // 数据透视
            const pivotData = {};
            const colHeaders = new Set();
            queryResult.rows.forEach(row => {
                const rowKey = row[rowIndex];
                const colKey = row[colIndex];
                const value = parseFloat(row[valIndex]) || 0;

                if (!pivotData[rowKey]) pivotData[rowKey] = {};
                pivotData[rowKey][colKey] = (pivotData[rowKey][colKey] || 0) + value;
                colHeaders.add(colKey);
            });

            const sortedColHeaders = Array.from(colHeaders).sort();

            // 创建透视表
            const table = document.createElement('table');
            table.className = 'data-table w-full text-sm border-collapse border border-gray-300';

            // 表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            const firstTh = document.createElement('th');
            firstTh.className = 'p-2 border border-gray-300 bg-gray-100 font-semibold text-left';
            firstTh.textContent = rowFields[0];
            headerRow.appendChild(firstTh);

            sortedColHeaders.forEach(colHeader => {
                const th = document.createElement('th');
                th.className = 'p-2 border border-gray-300 bg-gray-100 font-semibold text-center';
                th.textContent = colHeader;
                headerRow.appendChild(th);
            });

            thead.appendChild(headerRow);
            table.appendChild(thead);

            // 表体
            const tbody = document.createElement('tbody');
            Object.keys(pivotData).sort().forEach(rowKey => {
                const tr = document.createElement('tr');
                tr.className = 'hover:bg-gray-50';

                const rowTd = document.createElement('td');
                rowTd.className = 'p-2 border border-gray-300 font-medium';
                rowTd.textContent = rowKey;
                tr.appendChild(rowTd);

                sortedColHeaders.forEach(colKey => {
                    const td = document.createElement('td');
                    td.className = 'p-2 border border-gray-300 text-right';
                    const value = pivotData[rowKey][colKey] || 0;
                    td.textContent = value.toLocaleString();
                    tr.appendChild(td);
                });

                tbody.appendChild(tr);
            });
            table.appendChild(tbody);

            return table;
        }

        // 创建图表元素 - 对照test-chat.html实现
        function createChartElement(chartConfig, displaySuggestion) {
            // displaySuggestion 保持，但优先使用 chartConfig
            if (chartConfig && chartConfig.type && chartConfig.data) {
                console.log("检测到完整的 chartConfig，直接渲染图表。");
                const canvasContainer = document.createElement('div');
                canvasContainer.className = 'mt-4 p-4 bg-white rounded-lg shadow-md';
                const canvas = document.createElement('canvas');
                canvasContainer.appendChild(canvas);
                console.log("Chart是否引入:"+typeof Chart);
                new Chart(canvas, chartConfig);
                return canvasContainer;
            }

            // --- Fallback/Legacy Logic ---
            console.warn("未找到有效的 chartConfig，回退到旧的 displaySuggestion 逻辑。");
            if (!displaySuggestion || !displaySuggestion.chartType || !displaySuggestion.transformedData) {
                return null;
            }
            const { chartType, transformedData, chartTitle } = displaySuggestion;

            const canvasContainer = document.createElement('div');
            canvasContainer.className = 'mt-4 p-4 bg-white rounded-lg shadow-md';
            const canvas = document.createElement('canvas');
            canvasContainer.appendChild(canvas);

            new Chart(canvas, {
                type: chartType,
                data: transformedData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: chartTitle || '图表展示'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            return canvasContainer;
        }

        // 创建澄清选项 - 完全对照test-chat.html实现
        function createClarificationOptions(options) {
            console.log('createClarificationOptions 被调用，选项详情:', options);
            const container = document.createElement('div');
            container.className = 'mt-3 space-y-2';

            options.forEach((option, index) => {
                console.log(`创建第 ${index + 1} 个澄清选项:`, option);
                const button = document.createElement('button');
                button.className = 'clarification-btn w-full text-left text-sm bg-blue-100 text-blue-800 p-2 rounded-md hover:bg-blue-200';
                const buttonText = option.optionText || option.displayText; // 兼容两种字段名
                button.textContent = buttonText;
                button.onclick = () => handleClarificationClick(option.optionId, buttonText, option.payload);
                container.appendChild(button);
                console.log(`澄清选项按钮已创建: ${buttonText}`);
            });
            console.log('所有澄清选项按钮创建完成，容器返回');
            return container;
        }

        // 处理澄清选项点击 - 完全对照test-chat.html实现
        async function handleClarificationClick(optionId, optionText, payload) {
            addMessage(`我选择了: "${optionText}"`, 'user');
            toggleInput(false);

            try {
                const selectedRole = roleSelector.value; // 获取选中的角色
                const additionalParams = {
                    clarifiedOptionId: optionId
                };

                // 如果有payload，将其内容合并到additionalParams中
                if (payload) {
                    Object.assign(additionalParams, payload);
                }

                if (selectedRole) {
                    additionalParams.userRole = selectedRole; // 如果有角色，则添加到参数中
                }

                const requestBody = {
                    userId: userId,
                    sessionId: sessionId,
                    queryText: `基于澄清选项 "${optionText}"，请继续处理我的原始查询`, // 更有意义的查询文本
                    additionalParams: additionalParams
                };

                console.log('发送澄清请求:', requestBody);

                // 创建进度容器
                const progressContainer = createProgressContainer();

                // 使用SSE流式接口处理澄清请求
                await handleSSEQuery(requestBody, progressContainer);

            } catch (error) {
                console.error('澄清请求失败:', error);
                addMessage(`澄清请求失败: ${error.message}`, 'agent', 'ERROR');
            } finally {
                toggleInput(true);
            }
        }

        // 切换输入框和发送按钮的状态 - 对照test-chat.html实现
        function toggleInput(enabled) {
            userInput.disabled = !enabled;
            sendBtn.disabled = !enabled;
            if (enabled) {
                userInput.focus();
            }
        }

        // 格式化分析文本，支持简单的Markdown语法
        function formatAnalysisText(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code style="background-color: #f1f5f9; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>')
                .replace(/\n/g, '<br>');
        }

        // 调试测试函数 - 添加多个测试按钮
        function addDebugTestButton() {
            const buttonContainer = document.createElement('div');
            buttonContainer.style.position = 'fixed';
            buttonContainer.style.top = '10px';
            buttonContainer.style.right = '10px';
            buttonContainer.style.zIndex = '9999';
            buttonContainer.style.display = 'flex';
            buttonContainer.style.flexDirection = 'column';
            buttonContainer.style.gap = '5px';

            // 测试按钮配置
            const testButtons = [
                {
                    text: '连接测试',
                    color: 'bg-indigo-500',
                    action: testConnection
                },
                {
                    text: 'SSE测试',
                    color: 'bg-pink-500',
                    action: testSSEConnection
                },
                {
                    text: '基本测试',
                    color: 'bg-gray-500',
                    action: testBasicMessage
                },
                {
                    text: '测试澄清选项',
                    color: 'bg-purple-500',
                    action: testClarificationOptions
                },
                {
                    text: '测试系统消息',
                    color: 'bg-yellow-500',
                    action: testAcknowledgementResponse
                },
                {
                    text: '测试数据响应',
                    color: 'bg-blue-500',
                    action: testDataResponse
                },
                {
                    text: '测试所有类型',
                    color: 'bg-green-500',
                    action: testAllResponseTypes
                }
            ];

            testButtons.forEach(config => {
                const button = document.createElement('button');
                button.textContent = config.text;
                button.className = `px-3 py-1 ${config.color} text-white rounded text-xs hover:opacity-80`;
                button.onclick = config.action;
                buttonContainer.appendChild(button);
            });

            document.body.appendChild(buttonContainer);
        }

        // 自动测试澄清选项功能
        function testClarificationOptions() {
            console.log('🟣 开始自动测试澄清选项...');

            // 使用您实际遇到的澄清响应数据
            const testData = {
                "type": "result",
                "sessionId": "session_p7n5bgpzs",
                "responseType": "CLARIFICATION_NEEDED",
                "messageToUser": "您想要查询的是销售额最高的前两名，还是销售量最高的前两名商品？或者有其他具体的指标吗？请进一步明确您的需求。",
                "clarificationOptions": [
                    {"optionId": "1", "optionText": "查询销售额最高的前两名", "payload": {}},
                    {"optionId": "2", "optionText": "查询销售量最高的前两名商品", "payload": {}}
                ],
                "success": true,
                "responseText": "您想要查询的是销售额最高的前两名，还是销售量最高的前两名商品？或者有其他具体的指标吗？请进一步明确您的需求。"
            };

            console.log('📝 测试数据:', testData);

            // 添加用户消息
            addMessage('查询前两名', 'user');

            try {
                // 处理澄清响应
                handleAgentResponse(testData);
                console.log('✅ 澄清选项测试完成');
            } catch (error) {
                console.error('❌ 澄清选项测试失败:', error);
                console.error('错误详情:', error.stack);
            }
        }

        // 测试ACKNOWLEDGEMENT响应
        function testAcknowledgementResponse() {
            console.log('🟡 开始测试ACKNOWLEDGEMENT响应');

            const testData = {
                "sessionId": "session_rjmws3pq7",
                "responseType": "ACKNOWLEDGEMENT",
                "messageToUser": "我是一个AI数据查询助手。不过，您当前的账户似乎没有查询任何数据的权限。请联系管理员为您分配相关权限。",
                "success": true,
                "responseText": "我是一个AI数据查询助手。不过，您当前的账户似乎没有查询任何数据的权限。请联系管理员为您分配相关权限。"
            };

            console.log('📝 测试数据:', testData);
            addMessage('你好', 'user');

            try {
                handleAgentResponse(testData);
                console.log('✅ ACKNOWLEDGEMENT测试完成');
            } catch (error) {
                console.error('❌ ACKNOWLEDGEMENT测试失败:', error);
            }
        }

        // 测试DATA响应
        function testDataResponse() {
            const testData = {
                "sessionId": "session_y14cr5mdz",
                "responseType": "DATA",
                "messageToUser": "📊 **数据概览**\n本次分析的数据集包含了31个不同项目的回款情况，每个项目都有其唯一的名称，并且记录了各自的回款总额（单位：亿元）。这表明我们是在处理一个相对较小但信息密集的数据集。\n\n🔍 **核心洞察**\n- **项目多样性高**：每一条记录代表一个不同的项目，这意味着在考虑的范围内没有重复项目存在。\n- **回款金额差异显著**：最低回款金额仅为0.02亿，而最高达到了惊人的100.93亿，显示出各个项目间巨大的经济贡献差异。",
                "queryResult": {
                    "querySql": "SELECT `parent_project_name` AS 项目名称, SUM(`hk_amount`)/100000000 AS 回款总金额_亿元 FROM `t_vw_project_records` GROUP BY `parent_project_name`;",
                    "columnHeaders": ["项目名称", "回款总金额_亿元"],
                    "rows": [
                        ["镇江项目", 54.73323654],
                        ["中建·国贤府（北京）", 50.62776669],
                        ["中建·虹溪璟庭", 31.96504816],
                        ["中建·宸庐", 80.52094119],
                        ["中建·朗诗熙华雅园", 25.05913887]
                    ],
                    "totalRows": 5
                },
                "displaySuggestion": {
                    "chartType": "table",
                    "title": "项目回款统计",
                    "displayStrategy": "table_only",
                    "tableConfigJson": "{\"showTotals\":{\"column\":false,\"row\":false},\"tableType\":\"standard\"}"
                },
                "success": true,
                "summary": "好的，已为您查到5条相关记录。"
            };

            addMessage('统计项目回款额', 'user');
            handleAgentResponse(testData);
        }

        // 测试所有响应类型
        function testAllResponseTypes() {
            console.log('开始测试所有响应类型...');

            setTimeout(() => testAcknowledgementResponse(), 500);
            setTimeout(() => testClarificationOptions(), 2000);
            setTimeout(() => testDataResponse(), 4000);

            console.log('所有测试已安排');
        }

        // 简单测试 - 直接测试基本消息显示
        function testBasicMessage() {
            console.log('🧪 开始基本消息测试');

            // 测试最简单的消息
            const simpleData = {
                "sessionId": "test_session",
                "responseType": "ACKNOWLEDGEMENT",
                "messageToUser": "这是一个测试消息",
                "success": true
            };

            console.log('📝 简单测试数据:', simpleData);

            // 直接调用handleAgentResponse
            try {
                handleAgentResponse(simpleData);
                console.log('✅ 基本消息测试成功');
            } catch (error) {
                console.error('❌ 基本消息测试失败:', error);
                console.error('错误详情:', error.stack);

                // 降级测试：使用原始addMessage
                console.log('🔄 尝试降级测试');
                addMessage('降级测试消息', 'agent', 'ACKNOWLEDGEMENT');
            }
        }

        // 连接测试 - 测试服务器连接
        async function testConnection() {
            console.log('🔗 开始连接测试');

            try {
                // 测试健康检查端点
                const healthResponse = await fetch('/actuator/health', {
                    method: 'GET',
                    timeout: 5000
                });

                if (healthResponse.ok) {
                    console.log('✅ 服务器连接正常');
                    addMessage('✅ 服务器连接正常', 'agent', 'SUCCESS');
                } else {
                    console.log('⚠️ 服务器响应异常:', healthResponse.status);
                    addMessage(`⚠️ 服务器响应异常: ${healthResponse.status}`, 'agent', 'WARNING');
                }
            } catch (error) {
                console.error('❌ 连接测试失败:', error);
                addMessage(`❌ 连接失败: ${error.message}`, 'agent', 'ERROR');
            }
        }

        // SSE连接测试 - 测试SSE端点
        async function testSSEConnection() {
            console.log('📡 开始SSE连接测试');

            const testRequest = {
                userId: 'test_user',
                sessionId: 'test_session',
                queryText: '测试连接',
                additionalParams: {
                    timeZone: 'Asia/Shanghai'
                }
            };

            const progressContainer = createProgressContainer();

            try {
                await handleSSEQuery(testRequest, progressContainer);
                console.log('✅ SSE连接测试成功');
            } catch (error) {
                console.error('❌ SSE连接测试失败:', error);
                addMessage(`❌ SSE连接失败: ${error.message}`, 'agent', 'ERROR');
            }
        }
    </script>
</body>
</html>
