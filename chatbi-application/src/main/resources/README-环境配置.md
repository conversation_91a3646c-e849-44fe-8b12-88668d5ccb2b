# ChatBI Application 环境配置说明

## 配置文件结构

```
src/main/resources/
├── application.yml          # 通用配置
├── application-dev.yml      # 开发环境配置
├── application-test.yml     # 测试环境配置
├── application-prod.yml     # 生产环境配置
└── logback-spring.xml       # 日志配置
```

## 环境配置对比

| 配置项 | 开发环境(dev) | 测试环境(test) | 生产环境(prod) |
|--------|---------------|----------------|----------------|
| 端口 | 8081 | 8082 | 8080 |
| 数据库 | chatbi_metadata_dev | chatbi_metadata_test | chatbi_metadata_prod |
| JPA DDL | update | create-drop | validate |
| 显示SQL | true | true | false |
| 日志级别 | DEBUG | DEBUG | WARN |
| 日志保留 | 7天 | 3天 | 30天 |
| 文件大小 | 100MB | 50MB | 200MB |
| Milvus集合 | chatbi_embeddings_dev | chatbi_embeddings_test | chatbi_embeddings_prod |
| 相似度阈值 | 0.3 | 0.2 | 0.5 |
| Actuator端点 | 全部暴露 | 部分暴露 | 最小暴露 |

## 启动方式

### 1. IDE中启动
在IDE的运行配置中设置环境变量：
```
spring.profiles.active=dev
```

### 2. 命令行启动
```bash
# 开发环境
java -jar chatbi-application.jar --spring.profiles.active=dev

# 测试环境  
java -jar chatbi-application.jar --spring.profiles.active=test

# 生产环境
java -jar chatbi-application.jar --spring.profiles.active=prod
```

### 3. Docker启动
```bash
# 开发环境
docker run -e SPRING_PROFILES_ACTIVE=dev chatbi-application

# 生产环境
docker run -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_USERNAME=prod_user \
  -e DB_PASSWORD=prod_password \
  -e DASHSCOPE_API_KEY=your_api_key \
  chatbi-application
```

## 环境变量配置

### 生产环境必需的环境变量
```bash
# 数据库配置
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# API密钥
DASHSCOPE_API_KEY=your_dashscope_api_key

# Milvus配置
MILVUS_HOST=your_milvus_host
MILVUS_PORT=19530
MILVUS_USERNAME=your_milvus_username
MILVUS_PASSWORD=your_milvus_password
```

## 日志文件位置

### 开发环境
- 路径: `./logs/dev/`
- 文件: `chatbi-application-dev.YYYY-MM-DD.i.log`

### 测试环境
- 路径: `./logs/test/`
- 文件: `chatbi-application-test.YYYY-MM-DD.i.log`

### 生产环境
- 路径: `/var/log/chatbi/`
- 文件: `chatbi-application-prod.YYYY-MM-DD.i.log`

## 数据库配置

### 开发环境
- 自动更新表结构 (ddl-auto: update)
- 显示SQL语句
- 格式化SQL输出

### 测试环境
- 每次启动重建表 (ddl-auto: create-drop)
- 显示SQL语句
- 适合集成测试

### 生产环境
- 只验证表结构 (ddl-auto: validate)
- 不显示SQL语句
- 使用连接池优化
- 通过环境变量配置敏感信息

## 监控端点

### 开发环境
- 暴露所有Actuator端点
- 访问: http://localhost:8081/api/actuator/*

### 测试环境
- 暴露部分端点: health, info, beans, mappings, env
- 访问: http://localhost:8082/api/actuator/*

### 生产环境
- 只暴露必要端点: health, info, metrics
- 健康检查需要授权
- 访问: http://localhost:8080/api/actuator/*

## 注意事项

1. **敏感信息**: 生产环境的密码、API密钥等通过环境变量配置
2. **日志级别**: 生产环境使用WARN级别减少日志输出
3. **数据库**: 生产环境不允许自动修改表结构
4. **监控**: 生产环境限制监控端点的暴露
5. **性能**: 生产环境优化了连接池和缓存配置
