# ChatBI Application 通用配置
# 具体环境配置请参考 application-{profile}.yml 文件

spring:
  application:
    name: chatbi-application
  profiles:
    active: dev  # 默认激活开发环境
  main:
    allow-bean-definition-overriding: true

  # CORS 配置 - 通用配置
  web:
    cors:
      allowed-origins:
        - "http://localhost:*"
        - "http://127.0.0.1:*"
        - "http://***********:*"
        - "http://0.0.0.0:*"
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
        - PATCH
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600

# 通用服务器配置
server:
  servlet:
    session:
      timeout: 30m
    context-path: /  # 统一API路径前缀



# 通用日志配置 - 具体配置在各环境文件中覆盖
logging:
  pattern:
    # 通用日志输出格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 通用 Actuator 配置
management:
  endpoints:
    web:
      base-path: /actuator
  endpoint:
    health:
      enabled: true
    info:
      enabled: true

# 环境配置说明
# ---
# 使用不同环境的配置文件：
# - 开发环境: application-dev.yml
# - 测试环境: application-test.yml
# - 生产环境: application-prod.yml
#
# 启动时指定环境：
# java -jar app.jar --spring.profiles.active=dev
# java -jar app.jar --spring.profiles.active=test
# java -jar app.jar --spring.profiles.active=prod

