#!/bin/bash

# ChatBI 澄清选项测试脚本
# 测试SSE接口的澄清选项显示功能

echo "=========================================="
echo "ChatBI 澄清选项测试"
echo "=========================================="

# 服务器配置
SERVER_URL="http://localhost:8080"
SSE_ENDPOINT="/api/v1/chatbi/conversation/query-stream"

# 测试数据
USER_ID="test_user_$(date +%s)"
SESSION_ID="test_session_$(date +%s)"

echo "测试配置:"
echo "- 服务器: $SERVER_URL"
echo "- 用户ID: $USER_ID"
echo "- 会话ID: $SESSION_ID"
echo ""

# 测试澄清选项的查询
declare -a clarification_queries=(
    "统计项目回款额"
    "查询销售数据"
    "显示用户数量"
    "分析业绩情况"
)

# 函数：测试澄清查询
test_clarification_query() {
    local query="$1"
    local test_num="$2"
    
    echo "----------------------------------------"
    echo "澄清测试 $test_num: $query"
    echo "----------------------------------------"
    
    # 构建请求体
    local request_body=$(cat <<EOF
{
    "userId": "$USER_ID",
    "sessionId": "$SESSION_ID",
    "queryText": "$query",
    "additionalParams": {
        "timeZone": "Asia/Shanghai",
        "userRole": "数据分析师"
    }
}
EOF
)

    echo "发送请求..."
    echo "请求体: $request_body"
    echo ""
    
    # 发送SSE请求并解析响应
    curl -N -H "Accept: text/event-stream" \
         -H "Content-Type: application/json" \
         -X POST \
         -d "$request_body" \
         "$SERVER_URL$SSE_ENDPOINT" 2>/dev/null | while IFS= read -r line; do
        
        # 跳过空行
        if [[ -z "$line" ]]; then
            continue
        fi
        
        # 解析SSE数据
        if [[ $line == data:* ]]; then
            local json_data="${line#data: }"
            
            # 跳过空的data行
            if [[ -z "$json_data" || "$json_data" == " " ]]; then
                continue
            fi
            
            # 解析事件类型
            local event_type=$(echo "$json_data" | jq -r '.type // "unknown"' 2>/dev/null)
            local timestamp=$(date '+%H:%M:%S')
            
            case "$event_type" in
                "progress")
                    local progress=$(echo "$json_data" | jq -r '.progress // 0' 2>/dev/null)
                    local message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
                    printf "[%s] 📊 进度: %s%% - %s\n" "$timestamp" "$progress" "$message"
                    ;;
                "thinking")
                    local message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
                    printf "[%s] 💭 思考: %s\n" "$timestamp" "$message"
                    ;;
                "sql_generation")
                    local message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
                    printf "[%s] 🔧 SQL生成: %s\n" "$timestamp" "$message"
                    ;;
                "data_retrieval")
                    local message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
                    printf "[%s] 📊 数据检索: %s\n" "$timestamp" "$message"
                    ;;
                "response_generation")
                    local message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
                    printf "[%s] 📝 响应生成: %s\n" "$timestamp" "$message"
                    ;;
                "result")
                    printf "[%s] ✅ 结果: 查询完成\n" "$timestamp"
                    # 解析响应类型
                    local response_type=$(echo "$json_data" | jq -r '.responseType // "unknown"' 2>/dev/null)
                    local message_to_user=$(echo "$json_data" | jq -r '.messageToUser // ""' 2>/dev/null)
                    local clarification_options=$(echo "$json_data" | jq -r '.clarificationOptions // []' 2>/dev/null)
                    
                    printf "    响应类型: %s\n" "$response_type"
                    if [[ $message_to_user != "" && $message_to_user != "null" ]]; then
                        printf "    消息: %s\n" "$message_to_user"
                    fi
                    
                    # 检查是否有澄清选项
                    if [[ $response_type == "CLARIFICATION_NEEDED" ]]; then
                        printf "    🔍 检测到澄清请求!\n"
                        local options_count=$(echo "$json_data" | jq -r '.clarificationOptions | length' 2>/dev/null)
                        printf "    澄清选项数量: %s\n" "$options_count"
                        
                        # 显示澄清选项
                        if [[ $options_count -gt 0 ]]; then
                            echo "    澄清选项详情:"
                            echo "$json_data" | jq -r '.clarificationOptions[] | "      - " + .optionText + " (ID: " + .optionId + ")"' 2>/dev/null
                        fi
                        
                        printf "    ✅ 澄清选项测试成功!\n"
                    else
                        printf "    ℹ️  非澄清响应，响应类型: %s\n" "$response_type"
                    fi
                    ;;
                "performance")
                    local total_time=$(echo "$json_data" | jq -r '.totalTime // 0' 2>/dev/null)
                    local query_time=$(echo "$json_data" | jq -r '.queryTime // 0' 2>/dev/null)
                    printf "[%s] ⏱️  性能: 总耗时 %sms, 查询耗时 %sms\n" "$timestamp" "$total_time" "$query_time"
                    ;;
                "error")
                    local message=$(echo "$json_data" | jq -r '.message // ""' 2>/dev/null)
                    printf "[%s] ❌ 错误: %s\n" "$timestamp" "$message"
                    ;;
                *)
                    printf "[%s] ❓ 未知事件: %s\n" "$timestamp" "$event_type"
                    ;;
            esac
        fi
    done
    
    echo ""
    echo "测试 $test_num 完成"
    echo ""
}

# 检查服务是否可用
echo "检查服务状态..."
if ! curl -s "$SERVER_URL/actuator/health" > /dev/null 2>&1; then
    echo "❌ 服务不可用，请确保ChatBI服务正在运行在 $SERVER_URL"
    exit 1
fi
echo "✅ 服务状态正常"
echo ""

# 执行澄清测试用例
for i in "${!clarification_queries[@]}"; do
    test_num=$((i + 1))
    query="${clarification_queries[$i]}"
    
    test_clarification_query "$query" "$test_num"
    
    # 测试间隔
    if [[ $test_num -lt ${#clarification_queries[@]} ]]; then
        echo "等待3秒后进行下一个测试..."
        sleep 3
    fi
done

# 测试总结
echo ""
echo "=========================================="
echo "澄清选项测试总结:"
echo "- 测试了 ${#clarification_queries[@]} 个可能触发澄清的查询"
echo "- 验证了SSE流式接口的澄清选项返回"
echo "- 检查了澄清选项的数据结构"
echo ""
echo "前端测试:"
echo "- 访问: $SERVER_URL/test-chat-sse.html?test=clarification"
echo "- 或访问: $SERVER_URL/test-chat-sse.html?debug=true 点击测试按钮"
echo ""
echo "如果看到 '🔍 检测到澄清请求!' 和澄清选项详情，说明功能正常"
echo "=========================================="
