import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000, // 固定前端端口为3000
    host: true, // 允许外部访问
    // 配置代理以解决开发环境下的跨域问题
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8081', // 后端服务的地址 8081
        changeOrigin: true,
        // 增加日志，以便在Vite控制台观察代理行为
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`[vite-proxy] Fowarding request to backend: ${req.method} ${req.url}`);
          });
          proxy.on('error', (err, req, res) => {
            console.error('[vite-proxy] Error:', err);
          });
        }
      }
    }
  }
}) 