server {
    listen 3000;
    server_name localhost;

    # 根目录指向Nginx容器中存放前端构建文件的位置
    root /usr/share/nginx/html;
    index index.html index.htm;

    # 对所有请求，尝试查找文件、目录，如果都找不到，则回退到/index.html
    # 这对于单页面应用(SPA)的路由至关重要
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 可以添加其他自定义配置，例如gzip压缩、缓存策略等
    # gzip on;
    # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
} 