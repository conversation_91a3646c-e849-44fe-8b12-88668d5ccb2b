# **知识库 \- 业务术语管理功能详细设计 (最终版)**

## **1\. 目标与价值**

**核心目标**: 建立一个高质量、可扩展、易于维护的中央词典，用于管理企业内部的专业术语。管理员可以通过**单条、批量添加**等高效方式进行录入，并利用**智能搜索**快速定位。

**核心价值**:

* **提升AI理解力**: 当用户提问“查一下上个月的GMV”，AI 通过查询此术语库，知道“GMV”就是“每日销售汇总”数据集中的“销售额”字段，从而能正确生成查询。  
* **统一业务口径**: 为全公司的业务指标和术语提供一个标准的定义和解释来源。

## **2\. 后端开发计划 (chatbi-admin-backend)**

### **2.1. DTO / VO 定义**

// File: com/qding/chatbi/admin/vo/terminology/TerminologyDTO.java  
@Data  
public class TerminologyDTO {  
    private Long id;  
    private String businessTerm;  
    private String standardReferenceType;  
    private String standardReferenceName;  
    private String contextDescription;  
    private Date updatedAt;  
}

// File: com/qding/chatbi/admin/vo/terminology/CreateOrUpdateTermRequest.java  
@Data  
public class CreateOrUpdateTermRequest {  
    @NotBlank  
    private String businessTerm;  
    @NotBlank  
    private String standardReferenceType;  
    private List\<Long\> standardReferenceIdPath; // For Cascader: \[datasetId, columnId\]  
    @NotBlank  
    private String contextDescription;  
}

// File: com/qding/chatbi/admin/vo/BatchOperationReport.java  
@Data  
@AllArgsConstructor  
public class BatchOperationReport {  
    private int successCount;  
    private int failureCount;  
    private List\<String\> errorMessages;  
}

### **2.2. Controller (KnowledgeBaseAdminController)**

增加批量创建接口，并为列表接口添加搜索参数。

// File: com/qding/chatbi/admin/controller/KnowledgeBaseAdminController.java  
@RestController  
@RequestMapping("/api/admin/knowledge-base")  
public class KnowledgeBaseAdminController {

    @Autowired  
    private KnowledgeBaseAdminService knowledgeBaseAdminService;

    // \--- 业务术语管理 \---  
    @GetMapping("/terms")  
    public ResponseEntity\<PagedResponse\<TerminologyDTO\>\> listTerms(  
        @RequestParam(required \= false) String search,  
        Pageable pageable) {  
        Page\<TerminologyDTO\> page \= knowledgeBaseAdminService.searchTerms(search, pageable);  
        // ... Convert Page to PagedResponse ...  
        return ResponseEntity.ok(pagedResponse);  
    }

    @PostMapping("/terms")  
    public ResponseEntity\<TerminologyDTO\> createTerm(@Valid @RequestBody CreateOrUpdateTermRequest request) {  
        return ResponseEntity.ok(knowledgeBaseAdminService.createTerm(request));  
    }

    @PostMapping("/terms/batch")  
    public ResponseEntity\<BatchOperationReport\> createTermsInBatch(@Valid @RequestBody List\<CreateOrUpdateTermRequest\> requests) {  
        return ResponseEntity.ok(knowledgeBaseAdminService.createTermsInBatch(requests));  
    }

    @PutMapping("/terms/{id}")  
    public ResponseEntity\<TerminologyDTO\> updateTerm(@PathVariable Long id, @Valid @RequestBody CreateOrUpdateTermRequest request) {  
        return ResponseEntity.ok(knowledgeBaseAdminService.updateTerm(id, request));  
    }

    @DeleteMapping("/terms/{id}")  
    public ResponseEntity\<Void\> deleteTerm(@PathVariable Long id) {  
        knowledgeBaseAdminService.deleteTerm(id);  
        return ResponseEntity.noContent().build();  
    }  
}

### **2.3. Service (KnowledgeBaseAdminService)**

增加处理批量添加和 RAG 搜索的逻辑。

// File: com/qding/chatbi/admin/service/KnowledgeBaseAdminService.java  
@Service  
public class KnowledgeBaseAdminService {  
      
    @Autowired private KnowledgePersistenceService knowledgePersistenceService;  
    @Autowired private BusinessTerminologyRepository termRepository;  
      
    public Page\<TerminologyDTO\> searchTerms(String searchTerm, Pageable pageable) {  
        if (searchTerm \== null || searchTerm.isBlank()) {  
            return termRepository.findAll(pageable).map(this::convertToDto);  
        }  
        List\<BusinessTerminology\> results \= knowledgePersistenceService.searchSimilarTerms(searchTerm, pageable.getPageSize());  
        List\<TerminologyDTO\> dtoList \= results.stream().map(this::convertToDto).collect(Collectors.toList());  
        return new PageImpl\<\>(dtoList, pageable, dtoList.size());  
    }

    @Transactional  
    public BatchOperationReport createTermsInBatch(List\<CreateOrUpdateTermRequest\> requests) {  
        int successCount \= 0;  
        List\<String\> errorMessages \= new ArrayList\<\>();  
        for (int i \= 0; i \< requests.size(); i++) {  
            try {  
                createTerm(requests.get(i));  
                successCount++;  
            } catch (Exception e) {  
                errorMessages.add(String.format("第 %d 行创建失败: %s", i \+ 1, e.getMessage()));  
            }  
        }  
        return new BatchOperationReport(successCount, requests.size() \- successCount, errorMessages);  
    }  
      
    // ... Other single CRUD methods and DTO converters ...  
}

## **3\. 前端开发计划 (chatbi-admin-frontend)**

### **3.1. api/knowledge.js**

// File: src/api/knowledge.js  
import apiClient from './index';

export const knowledgeApi \= {  
  getTerms: (params) \=\> apiClient.get('/knowledge-base/terms', { params }),  
  createTerm: (data) \=\> apiClient.post('/knowledge-base/terms', data),  
  updateTerm: (id, data) \=\> apiClient.put(\`/knowledge-base/terms/${id}\`, data),  
  deleteTerm: (id) \=\> apiClient.delete(\`/knowledge-base/terms/${id}\`),  
  createTermsInBatch: (data) \=\> apiClient.post('/knowledge-base/terms/batch', data),  
};

### **3.2. views/knowledge/TerminologyList.vue**

此页面将集成单条、批量、搜索、编辑和删除所有功能。

* **UI 布局与交互**:  
  * **操作区**:  
    * \<a-input-search\>: 用于输入关键词进行 RAG 搜索。  
    * \<a-dropdown-button\>:  
      * 主按钮“新建术语”，点击后弹出单条记录的创建模态框。  
      * 下拉菜单中提供“批量添加”选项，点击后弹出批量操作的模态框。  
  * **搜索结果展示**:  
    * 搜索结果直接在主表格中展示，并有 \<a-alert\> 提示。  
    * “业务术语”列对搜索关键词进行高亮。  
  * **批量添加模态框**:  
    * 采用\*\*可编辑表格 (\<a-table\>)\*\*方案。  
    * 表格列包括：业务术语(输入框)、映射类型(单选组)、映射目标字段(级联选择)、描述(输入框)、操作(删除此行)。  
    * 表格下方有一个“添加一行”按钮。  
    * 模态框页脚有“导入”和“取消”按钮。“导入”按钮会将表格中的所有数据发送到批量创建的 API。

## **4\. chatbi-knowledge-service 逻辑**

KnowledgePersistenceService 中 saveOrUpdateTerm 和 deleteTerm 的实现保持不变，它们通过 upsert 和 delete 操作，天然地支持了数据一致性。

## **5\. 总结**

通过本次对标“查询范例管理”的优化，我们为“业务术语管理”功能增加了强大的**批量添加**和**智能搜索**能力。

* **前端交互**: 通过下拉按钮区分单条和批量操作，并采纳了更优的“可编辑表格”方案，为管理员提供了类似 Excel 的、直观高效的数据录入体验。  
* **后端逻辑**: 新增了专门的批量处理和 RAG 搜索 API，确保了功能的健壮性和可扩展性。

这个最终版的设计使得两个知识库管理功能在体验和实现上保持了高度一致，构建了一个功能完备、体验优秀的知识管理中心。