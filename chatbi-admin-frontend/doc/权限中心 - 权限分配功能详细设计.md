# **权限中心 \- 权限分配功能详细设计**

## **1\. 目标与价值**

**核心目标**: 提供一个直观、易于管理的界面，让后台管理员可以清晰地为不同的**角色**分配对一个或多个**数据集**的访问权限。

**核心价值**:

* **权限落地**: 将抽象的角色和数据集概念转化为具体的、可执行的权限规则，这是整个权限体系的核心。  
* **集中管理**: 在一个统一的视图中管理所有的“角色-数据集”授权关系，避免了在多个页面之间来回切换的繁琐操作。  
* **审计清晰**: 权限矩阵（Matrix）的形式使得审计工作变得非常简单，可以一目了然地看出某个角色拥有哪些数据的权限，或者某个数据集被哪些角色所使用。

## **2\. 后端开发计划 (chatbi-admin-backend)**

### **2.1. DTO / VO 定义**

我们需要为权限分配矩阵定义专门的数据结构。

// File: src/main/java/com/qding/chatbi/admin/vo/permission/PermissionAssignmentDTO.java  
package com.qding.chatbi.admin.vo.permission;

import com.qding.chatbi.admin.vo.dataset.DatasetSummaryDTO;  
import lombok.Data;  
import java.util.List;  
import java.util.Map;

@Data  
public class PermissionAssignmentDTO {  
    // 所有可用的角色列表 (用于表格的列头)  
    private List\<RoleDTO\> allRoles;  
      
    // 所有可用的数据集列表 (用于表格的行头)  
    private List\<DatasetSummaryDTO\> allDatasets;

    // 权限映射关系: Key \-\> roleId, Value \-\> 包含该角色有权访问的 datasetId 的 Set  
    private Map\<Long, Set\<Long\>\> assignments;   
}

// File: src/main/java/com/qding/chatbi/admin/vo/permission/UpdateAssignmentsRequest.java  
package com.qding.chatbi.admin.vo.permission;

import lombok.Data;  
import java.util.List;  
import javax.validation.constraints.NotNull;

@Data  
public class UpdateAssignmentsRequest {  
    @NotNull  
    private Long roleId;  
      
    // 该角色被授权的所有 datasetId 列表  
    @NotNull  
    private List\<Long\> datasetIds;  
}

### **2.2. Controller (PermissionAdminController)**

扩展 PermissionAdminController 以支持权限分配的管理。

// File: src/main/java/com/qding/chatbi/admin/controller/PermissionAdminController.java  
// ... (之前的代码)

@RestController  
@RequestMapping("/api/admin/permissions")  
public class PermissionAdminController {

    @Autowired  
    private PermissionService permissionService;

    // \--- 角色管理 (已存在) \---  
    // ...

    // \--- 权限分配 (新增) \---  
    @GetMapping("/assignments")  
    public ResponseEntity\<PermissionAssignmentDTO\> getAssignments() {  
        return ResponseEntity.ok(permissionService.getPermissionAssignments());  
    }

    @PutMapping("/assignments")  
    public ResponseEntity\<Void\> updateAssignments(@RequestBody List\<UpdateAssignmentsRequest\> requests) {  
        permissionService.updateAssignments(requests);  
        return ResponseEntity.noContent().build();  
    }  
}

### **2.3. Service (PermissionService)**

在 PermissionService 中增加处理权限分配的业务逻辑。

// File: src/main/java/com/qding/chatbi/admin/service/PermissionService.java  
// ... (之前的代码)

@Service  
public class PermissionService {  
      
    @Autowired  
    private RoleRepository roleRepository;  
    @Autowired  
    private DatasetRepository datasetRepository;  
    @Autowired  
    private DatasetAccessPermissionRepository permissionRepository;

    // ... (角色管理的CRUD方法) ...

    /\*\*  
     \* 获取构建权限分配矩阵所需的所有数据。  
     \*/  
    public PermissionAssignmentDTO getPermissionAssignments() {  
        // 1\. 获取所有角色和数据集  
        List\<Role\> allRoles \= roleRepository.findAll();  
        List\<QueryableDataset\> allDatasets \= datasetRepository.findAll();

        // 2\. 获取所有已存在的权限关系  
        List\<DatasetAccessPermission\> allPermissions \= permissionRepository.findAll();

        // 3\. 将权限关系处理成 Map\<RoleId, Set\<DatasetId\>\> 的形式  
        Map\<Long, Set\<Long\>\> assignments \= allPermissions.stream()  
            .collect(Collectors.groupingBy(  
                p \-\> p.getRole().getId(),  
                Collectors.mapping(p \-\> p.getDataset().getId(), Collectors.toSet())  
            ));  
              
        // 4\. 组装成 DTO 返回  
        PermissionAssignmentDTO dto \= new PermissionAssignmentDTO();  
        dto.setAllRoles(allRoles.stream().map(this::convertRoleToDto).collect(Collectors.toList()));  
        dto.setAllDatasets(allDatasets.stream().map(this::convertDatasetToDto).collect(Collectors.toList()));  
        dto.setAssignments(assignments);  
          
        return dto;  
    }  
      
    /\*\*  
     \* 批量更新权限分配。  
     \* 这是一个全量更新操作，对于每个角色，都用新的数据集列表替换旧的。  
     \*/  
    @Transactional  
    public void updateAssignments(List\<UpdateAssignmentsRequest\> requests) {  
        for (UpdateAssignmentsRequest request : requests) {  
            Long roleId \= request.getRoleId();  
            Role role \= roleRepository.findById(roleId)  
                .orElseThrow(() \-\> new ResourceNotFoundException("角色", roleId));

            // 1\. 删除该角色所有旧的权限  
            permissionRepository.deleteByRoleId(roleId);

            // 2\. 插入所有新的权限  
            List\<DatasetAccessPermission\> newPermissions \= request.getDatasetIds().stream()  
                .map(datasetId \-\> {  
                    QueryableDataset dataset \= datasetRepository.findById(datasetId)  
                        .orElseThrow(() \-\> new ResourceNotFoundException("数据集", datasetId));  
                    DatasetAccessPermission newPerm \= new DatasetAccessPermission();  
                    newPerm.setRole(role);  
                    newPerm.setDataset(dataset);  
                    return newPerm;  
                })  
                .collect(Collectors.toList());  
              
            permissionRepository.saveAll(newPermissions);  
        }  
    }  
      
    // ... DTO 转换辅助方法 ...  
}

## **3\. 前端开发计划 (chatbi-admin-frontend)**

### **3.1. api/permission.js (更新)**

import apiClient from './index';

export const permissionApi \= {  
  // ... (角色相关的API)  
    
  // 【新增】权限分配相关的API  
  getAssignments: () \=\> apiClient.get('/permissions/assignments'),  
  updateAssignments: (data) \=\> apiClient.put('/permissions/assignments', data),  
};

### **3.2. views/permissions/PermissionMatrix.vue (新增)**

这是权限分配的核心页面，其交互设计至关重要。

* **UI 布局**:  
  * 页面顶部是 \<a-page-header\> 和一个“**保存**”按钮。保存按钮默认是**禁用 (disabled)** 状态。  
  * 主体部分是一个 \<a-table\>，用于渲染权限矩阵。  
* **表格/矩阵设计**:  
  * **表格数据源**: 表格的 dataSource 是从 API 获取的 allDatasets 列表。  
  * **第一列 (固定)**: "数据集"，使用 fixed: 'left' 固定在左侧，展示 datasetName。  
  * **动态列 (角色)**:  
    * 遍历从 API 获取的 allRoles 列表，为每个角色动态生成一列表格列。  
    * 列的标题是 roleName。  
    * 单元格内容是一个 **\<a-checkbox\>**。  
* **数据与状态管理**:  
  * **onMounted**:  
    1. 调用 permissionApi.getAssignments() 获取所有初始化数据。  
    2. 将返回的 allRoles, allDatasets 保存到对应的 ref 中。  
    3. **核心**: 将返回的 assignments (Map\<RoleId, Set\>) 转换为一个前端友好的、响应式的二维对象或 Map，例如 permissionState\[datasetId\]\[roleId\] \= true/false。  
  * **v-model 绑定**: 每一个 \<a-checkbox\> 的 v-model 都绑定到 permissionState 中对应的值。  
  * **变更追踪**:  
    * 当任何一个复选框的状态发生改变时 (@change 事件)，将一个 isDirty 的 ref 状态设为 true。  
    * “保存”按钮的 disabled 状态与 \!isDirty 绑定。一旦有修改，保存按钮就变为可用。  
* **保存逻辑**:  
  1. 点击“保存”按钮后，前端脚本会遍历 permissionState 这个二维对象。  
  2. 将其转换回后端 API 所需的 List\<UpdateAssignmentsRequest\> 格式。  
  3. 调用 permissionApi.updateAssignments(payload)。  
  4. 请求成功后，显示成功消息，并将 isDirty 设回 false，禁用保存按钮。

## **4\. 总结**

本设计方案为“权限分配”功能提供了一个功能强大且用户体验优秀的实现蓝图。

* **前端**: 通过**权限矩阵**的形式，让复杂的授权关系变得一目了然，所有操作都在一个视图内完成，高效直观。  
* **后端**: 提供了原子化的 API，并设计了全量更新的业务逻辑，确保了权限更新操作的**事务性和一致性**。

这个模块的完成，标志着我们 ChatBI 系统的整个权限体系形成了闭环。