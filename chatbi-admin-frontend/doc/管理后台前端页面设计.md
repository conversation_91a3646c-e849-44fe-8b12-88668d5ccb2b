// =================================================================
// 文件: chatbi-admin-frontend/package.json
// =================================================================
/*
{
  "name": "chatbi-admin-frontend",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "@ant-design/icons-vue": "^6.1.0",
    "ant-design-vue": "^4.2.3",
    "axios": "^1.7.2",
    "pinia": "^2.1.7",
    "vue": "^3.4.21",
    "vue-router": "^4.3.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.4",
    "unplugin-vue-components": "^0.27.0",
    "vite": "^5.2.0"
  }
}
*/

// =================================================================
// 文件: chatbi-admin-frontend/vite.config.js
// =================================================================
/*
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  server: {
    // 配置代理以解决开发环境下的跨域问题
    proxy: {
      '/api': {
        target: 'http://localhost:8081', // 后端服务的地址
        changeOrigin: true,
      }
    }
  }
})
*/

// =================================================================
// 文件: chatbi-admin-frontend/index.html
// 描述: Vue 应用的 HTML 入口文件。
// =================================================================
/*
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ChatBI 管理后台</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
*/

// =================================================================
// 文件: chatbi-admin-frontend/src/main.js
// =================================================================
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';

import App from './App.vue'
import router from './router'
// import store from './store'   // Uncomment if you have Vuex/Pinia setup

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd);

// app.use(store)  // Uncomment if you have Vuex/Pinia setup

app.mount('#app')


// =================================================================
// 文件: chatbi-admin-frontend/src/App.vue
// =================================================================
/*
<template>
  <router-view />
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  height: 100vh;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
*/

// =================================================================
// 文件: chatbi-admin-frontend/src/router/index.js
// =================================================================
// =================================================================
// 文件: chatbi-admin-frontend/src/router/index.js
// 描述: (已更新) Vue Router 的路由配置文件。
// =================================================================
import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '../components/layout/AppLayout.vue'
import { useAuthStore } from '../store/auth';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/LoginPage.vue')
  },
  {
    path: '/',
    component: AppLayout,
    children: [
      {
        path: '', // 路径为空，作为'/'的默认子路由
        name: 'Dashboard',
        component: () => import('../views/dashboard/DashboardPage.vue'),
        meta: { title: '总览', requiresAuth: true }
      },
      // 模型管理
      {
        path: 'model/data-sources',
        name: 'DataSources',
        component: () => import('../views/model/DataSourceList.vue'),
        meta: { title: '数据源管理', requiresAuth: true }
      },
      {
        path: 'model/datasets',
        name: 'Datasets',
        component: () => import('../views/model/DatasetList.vue'),
        meta: { title: '数据集管理', requiresAuth: true }
      },
      // 知识库
      {
        path: 'knowledge/terminology',
        name: 'Terminology',
        component: () => import('../views/knowledge/TerminologyList.vue'),
        meta: { title: '业务术语', requiresAuth: true }
      },
      {
        path: 'knowledge/examples',
        name: 'QueryExamples',
        component: () => import('../views/knowledge/QueryExampleList.vue'),
        meta: { title: '查询范例', requiresAuth: true }
      },
      // 权限中心
      {
        path: 'permissions/roles',
        name: 'Roles',
        component: () => import('../views/permissions/RoleList.vue'),
        meta: { title: '角色管理', requiresAuth: true }
      },
      {
        path: 'permissions/assignments',
        name: 'Assignments',
        component: () => import('../views/permissions/PermissionMatrix.vue'),
        meta: { title: '权限分配', requiresAuth: true }
      },
      // 系统管理
      {
        path: 'system/users',
        name: 'AdminUsers',
        component: () => import('../views/system/AdminUserList.vue'),
        meta: { title: '后台用户', requiresAuth: true, roles: ['SUPER_ADMIN'] }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const isAuthenticated = authStore.isAuthenticated;
  
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  if (to.name === 'Login') {
    if (isAuthenticated) {
      next({ name: 'Dashboard' });
    } else {
      next();
    }
  } 
  else if (requiresAuth && !isAuthenticated) {
    next({ name: 'Login' });
  } 
  else if (to.meta.roles && !to.meta.roles.some(role => authStore.userRoles.includes(role))) {
    alert('您没有权限访问此页面！');
    next({ name: 'Dashboard' });
  }
  else {
    next();
  }
});

export default router;


// =================================================================
// 文件: chatbi-admin-frontend/src/store/auth.js
// =================================================================
import { defineStore } from 'pinia';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    user: JSON.parse(localStorage.getItem('user')) || null,
  }),
  getters: {
    isAuthenticated: (state) => !!state.token,
    userRoles: (state) => state.user?.roles || [],
  },
  actions: {
    login(token, user) {
      this.token = token;
      this.user = user;
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
    },
    logout() {
      this.token = null;
      this.user = null;
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    },
  },
});

// =================================================================
// 文件: chatbi-admin-frontend/src/api/index.js
// =================================================================
import axios from 'axios';
import { useAuthStore } from '../store/auth';
import { message } from 'ant-design-vue';

const apiClient = axios.create({
  baseURL: '/api/admin', // 所有后台请求的基础路径
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    if (authStore.isAuthenticated) {
      // 简单起见，这里使用 Basic Auth。实际项目中推荐使用 Bearer Token (JWT)。
      const credentials = btoa(`${authStore.user.name}:${authStore.user.password}`); // 假设登录时保存了密码
      config.headers.Authorization = `Basic ${credentials}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      message.error('认证失败或权限不足，请重新登录。');
      const authStore = useAuthStore();
      authStore.logout();
      window.location.href = '/login';
    } else {
        message.error(error.response?.data?.message || '请求发生错误');
    }
    return Promise.reject(error);
  }
);

export default apiClient;

// =================================================================
// 文件: chatbi-admin-frontend/src/components/layout/AppLayout.vue
// =================================================================
/*
<template>
  <a-layout style="min-height: 100vh">
    <SideMenu />
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0 16px; display: flex; justify-content: space-between; align-items: center;">
        <h1 class="text-lg font-semibold">{{ $route.meta.title }}</h1>
        <div class="user-info">
          <a-avatar style="background-color: #1890ff">
            <template #icon><UserOutlined /></template>
          </a-avatar>
          <span class="ml-2 mr-4">欢迎, {{ authStore.user?.name || '管理员' }}</span>
          <a-button type="link" @click="handleLogout">登出</a-button>
        </div>
      </a-layout-header>
      <a-layout-content style="margin: 24px 16px; padding: 24px; background: #fff; min-height: 280px;">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { useRouter } from 'vue-router';
import SideMenu from './SideMenu.vue';
import { useAuthStore } from '../../store/auth';
import { UserOutlined } from '@ant-design/icons-vue';

const router = useRouter();
const authStore = useAuthStore();

const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};
</script>
*/


// =================================================================
// 文件: chatbi-admin-frontend/src/components/layout/SideMenu.vue
// 描述: (已修复) 侧边栏菜单组件，修正了图标导入方式。
// =================================================================
/*
<template>
  <a-layout-sider v-model:collapsed="collapsed" collapsible>
    <div class="logo">
      <img src="/logo.svg" alt="logo" />
      <h1 v-if="!collapsed">ChatBI</h1>
    </div>
    <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline">
      <a-menu-item key="/dashboard">
        <router-link to="/">
          <PieChartOutlined />
          <span>总览</span>
        </router-link>
      </a-menu-item>
      <a-sub-menu key="model">
        <template #title>
          <span>
            <DatabaseOutlined />
            <span>模型管理</span>
          </span>
        </template>
        <a-menu-item key="/model/data-sources"><router-link to="/model/data-sources">数据源管理</router-link></a-menu-item>
        <a-menu-item key="/model/datasets"><router-link to="/model/datasets">数据集管理</router-link></a-menu-item>
      </a-sub-menu>
      <a-sub-menu key="knowledge">
        <template #title>
          <span>
            <BookOutlined />
            <span>知识库</span>
          </span>
        </template>
        <a-menu-item key="/knowledge/terminology"><router-link to="/knowledge/terminology">业务术语</router-link></a-menu-item>
        <a-menu-item key="/knowledge/examples"><router-link to="/knowledge/examples">查询范例</router-link></a-menu-item>
      </a-sub-menu>
      <a-sub-menu key="permissions">
        <template #title>
          <span>
            <PropertySafetyOutlined />
            <span>权限中心</span>
          </span>
        </template>
        <a-menu-item key="/permissions/roles"><router-link to="/permissions/roles">角色管理</router-link></a-menu-item>
        <a-menu-item key="/permissions/assignments"><router-link to="/permissions/assignments">权限分配</router-link></a-menu-item>
      </a-sub-menu>
      <a-sub-menu key="system" v-if="isSuperAdmin">
         <template #title>
          <span>
            <SettingOutlined />
            <span>系统管理</span>
          </span>
        </template>
        <a-menu-item key="/system/users"><router-link to="/system/users">后台用户</router-link></a-menu-item>
      </a-sub-menu>
    </a-menu>
  </a-layout-sider>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '../../store/auth';
import {
  PieChartOutlined,
  DatabaseOutlined,
  BookOutlined,
  PropertySafetyOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';

const collapsed = ref(false);
const selectedKeys = ref(['']);
const route = useRoute();
const authStore = useAuthStore();

const isSuperAdmin = computed(() => authStore.userRoles.includes('SUPER_ADMIN'));

watch(
  () => route.path,
  (path) => {
    selectedKeys.value = [path === '/' ? '/dashboard' : path];
  },
  { immediate: true }
);
</script>

<style scoped>
.logo {
  height: 32px;
  margin: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}
.logo img {
  height: 100%;
}
.logo h1 {
  color: white;
  font-size: 20px;
  margin-left: 8px;
  font-weight: 600;
  white-space: nowrap;
}
</style>
*/


// =================================================================
// 文件: chatbi-admin-frontend/src/views/login/LoginPage.vue
// =================================================================
/*
<template>
  <div class="login-container">
    <a-card title="ChatBI 管理后台登录" :bordered="false" class="login-card">
      <a-form :model="formState" @finish="handleLogin">
        <a-form-item
          name="username"
          :rules="[{ required: true, message: '请输入用户名!' }]"
        >
          <a-input v-model:value="formState.username" placeholder="用户名">
            <template #prefix><UserOutlined /></template>
          </a-input>
        </a-form-item>
        <a-form-item
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password v-model:value="formState.password" placeholder="密码">
             <template #prefix><LockOutlined /></template>
          </a-input-password>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" block :loading="loading">
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../store/auth';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';

const formState = reactive({
  username: '',
  password: '',
});
const loading = ref(false);
const router = useRouter();
const authStore = useAuthStore();

const handleLogin = async () => {
  loading.value = true;
  try {
    if (formState.username === 'superadmin' && formState.password === 'password123') {
        const token = btoa(`${formState.username}:${formState.password}`);
        const user = { name: 'superadmin', roles: ['SUPER_ADMIN', 'ADMIN'], password: formState.password };
        authStore.login(token, user);
        router.push('/');
        message.success('登录成功！');
    } else if (formState.username === 'admin' && formState.password === 'password123') {
        const token = btoa(`${formState.username}:${formState.password}`);
        const user = { name: 'admin', roles: ['ADMIN'], password: formState.password };
        authStore.login(token, user);
        router.push('/');
        message.success('登录成功！');
    } else {
        message.error('用户名或密码错误！');
    }
  } catch (error) {
    console.error('Login failed:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}
.login-card {
  width: 400px;
}
</style>
*/

// =================================================================
// 文件: chatbi-admin-frontend/src/views/dashboard/DashboardPage.vue
// =================================================================
/*
<template>
  <div>
    <a-page-header
        title="欢迎使用 ChatBI 管理后台"
        sub-title="在这里您可以管理系统的所有核心配置"
    />
    <a-card>
        <p>请从左侧菜单选择一项开始操作。</p>
    </a-card>
  </div>
</template>
*/

// =================================================================
// 其他 Views (如 DataSourceList.vue, AdminUserList.vue 等) 
// 将遵循类似模式：
// 1. 使用 a-page-header 定义页面标题。
// 2. 使用 a-card 包裹主要内容。
// 3. 顶部放置 a-button 用于"新建"，以及 a-input 等用于搜索。
// 4. 使用 a-table 展示数据列表。
// 5. 使用 a-modal 和 a-form 实现新建/编辑的弹窗。
// 6. 使用 a-popconfirm 实现删除前的确认。
// =================================================================
