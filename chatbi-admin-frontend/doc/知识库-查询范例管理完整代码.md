// =================================================================
// Backend Code
// =================================================================

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/entity/QueryExample.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.Date;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "query_examples")
@Data
public class QueryExample {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Lob
    @Column(nullable = false)
    private String userQuestion;

    @Column(nullable = false)
    private Long targetDatasetId;

    @Lob
    @Column(nullable = false)
    private String targetQueryRepresentation;

    private String difficultyLevel;

    @Lob
    private String notes;

    @UpdateTimestamp
    private Date updatedAt;
}

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/QueryExampleRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.QueryExample;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface QueryExampleRepository extends JpaRepository<QueryExample, Long> {}


// -----------------------------------------------------------------
// File: chatbi-knowledge-service/src/main/java/com/qding/chatbi/knowledge/service/KnowledgePersistenceService.java
// -----------------------------------------------------------------
package com.qding.chatbi.knowledge.service;

import com.qding.chatbi.metadata.entity.BusinessTerminology;
import com.qding.chatbi.metadata.entity.QueryExample;
import com.qding.chatbi.metadata.repository.BusinessTerminologyRepository;
import com.qding.chatbi.metadata.repository.QueryExampleRepository;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.model.embedding.EmbeddingModel;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.dml.UpsertParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KnowledgePersistenceService {

    private static final String TERM_COLLECTION_NAME = "business_terms";
    private static final String EXAMPLE_COLLECTION_NAME = "query_examples";

    @Autowired private BusinessTerminologyRepository termRepository;
    @Autowired private QueryExampleRepository exampleRepository;
    @Autowired private MilvusServiceClient milvusClient;
    @Autowired private EmbeddingModel embeddingModel;

    // ... (业务术语相关的方法 saveOrUpdateTerm, deleteTerm, searchSimilarTerms) ...

    @Transactional
    public void saveOrUpdateExample(QueryExample example) {
        try {
            QueryExample savedExample = exampleRepository.save(example);
            Long primaryKey = savedExample.getId();
            Embedding embedding = embeddingModel.embed(savedExample.getUserQuestion()).content();

            UpsertParam upsertParam = UpsertParam.newBuilder()
                    .withCollectionName(EXAMPLE_COLLECTION_NAME)
                    .withPrimaryKeys(List.of(primaryKey))
                    .withVectors(List.of(embedding.vectorAsList()))
                    .build();
            milvusClient.upsert(upsertParam);
        } catch (Exception e) {
            throw new RuntimeException("保存或更新查询范例失败: " + e.getMessage(), e);
        }
    }
    
    @Transactional
    public void deleteExample(Long exampleId) {
        try {
            milvusClient.delete(DeleteParam.newBuilder()
                .withCollectionName(EXAMPLE_COLLECTION_NAME)
                .withPrimaryKeys(List.of(exampleId)).build());
            exampleRepository.deleteById(exampleId);
        } catch (Exception e) {
            throw new RuntimeException("删除查询范例失败: " + e.getMessage(), e);
        }
    }

    public List<QueryExample> searchSimilarExamples(String queryText, int topK) {
        Embedding queryEmbedding = embeddingModel.embed(queryText).content();
        SearchParam searchParam = SearchParam.newBuilder()
                .withCollectionName(EXAMPLE_COLLECTION_NAME)
                .withVectors(List.of(queryEmbedding.vectorAsList()))
                .withTopK(topK)
                .build();
        
        List<Long> resultIds = milvusClient.search(searchParam)
                                    .getResults().get(0).getIds().stream()
                                    .map(id -> (Long) id).collect(Collectors.toList());
        
        return resultIds.isEmpty() ? List.of() : exampleRepository.findAllById(resultIds);
    }
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/example/QueryExampleDTO.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.example;
// ... (Code from design plan) ...

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/example/CreateOrUpdateExampleRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.example;
// ... (Code from design plan) ...

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/KnowledgeBaseAdminService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.BatchOperationReport;
import com.qding.chatbi.admin.vo.example.*;
import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import com.qding.chatbi.metadata.entity.QueryExample;
import com.qding.chatbi.metadata.repository.DatasetRepository;
import com.qding.chatbi.metadata.repository.QueryExampleRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KnowledgeBaseAdminService {
    
    @Autowired private KnowledgePersistenceService knowledgePersistenceService;
    @Autowired private QueryExampleRepository exampleRepository;
    @Autowired private DatasetRepository datasetRepository; // To get dataset name

    public Page<QueryExampleDTO> searchOrListExamples(String searchTerm, Pageable pageable) {
        if (searchTerm == null || searchTerm.isBlank()) {
            return exampleRepository.findAll(pageable).map(this::convertExampleToDto);
        }
        List<QueryExample> results = knowledgePersistenceService.searchSimilarExamples(searchTerm, pageable.getPageSize());
        List<QueryExampleDTO> dtoList = results.stream().map(this::convertExampleToDto).collect(Collectors.toList());
        return new PageImpl<>(dtoList, pageable, dtoList.size());
    }

    @Transactional
    public QueryExampleDTO createExample(CreateOrUpdateExampleRequest request) {
        QueryExample example = new QueryExample();
        BeanUtils.copyProperties(request, example);
        knowledgePersistenceService.saveOrUpdateExample(example);
        return convertExampleToDto(example);
    }
    
    @Transactional
    public BatchOperationReport createExamplesInBatch(List<CreateOrUpdateExampleRequest> requests) {
        // ... (Logic from design plan) ...
    }

    // ... (update, delete, and DTO conversion methods) ...
    private QueryExampleDTO convertExampleToDto(QueryExample entity) {
        QueryExampleDTO dto = new QueryExampleDTO();
        BeanUtils.copyProperties(entity, dto);
        datasetRepository.findById(entity.getTargetDatasetId()).ifPresent(d -> dto.setTargetDatasetName(d.getDatasetName()));
        return dto;
    }
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/KnowledgeBaseAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;
// ... (Controller implementation from design plan) ...


// =================================================================
// Frontend: chatbi-admin-frontend
// =================================================================

// -----------------------------------------------------------------
// File: src/api/knowledge.js
// -----------------------------------------------------------------
/*
import apiClient from './index';

export const knowledgeApi = {
  // ... (term APIs)
  getExamples: (params) => apiClient.get('/knowledge-base/examples', { params }),
  createExample: (data) => apiClient.post('/knowledge-base/examples', data),
  updateExample: (id, data) => apiClient.put(`/knowledge-base/examples/${id}`, data),
  deleteExample: (id) => apiClient.delete(`/knowledge-base/examples/${id}`),
  createExamplesInBatch: (data) => apiClient.post('/knowledge-base/examples/batch', data),
};
*/

// -----------------------------------------------------------------
// File: src/views/knowledge/QueryExampleList.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="查询范例管理" sub-title="管理高质量的“问题-答案”范例以提升AI能力" />
    <a-card>
      <div class="table-operations">
        <a-input-search v-model:value="searchTerm" placeholder="按问题语义搜索范例..." style="width: 300px" @search="onSearch" />
        <a-dropdown-button type="primary" @click="handleAddNew">
          新建范例
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item key="batch">批量添加</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown-button>
      </div>

      <a-alert v-if="searchTerm" :message="`为您找到 ${pagination.total} 条关于“${searchTerm}”的相似范例`" type="info" show-icon class="my-4" />
      
      <a-table :columns="columns" :data-source="exampleList" :loading="loading" :pagination="pagination" row-key="id" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userQuestion'">
                <span v-html="highlightText(record.userQuestion, searchTerm)"></span>
            </template>
            <template v-if="column.key === 'targetDatasetName'">
                <a-tag color="processing">{{ record.targetDatasetName }}</a-tag>
            </template>
            <template v-if="column.key === 'difficultyLevel'">
                <a-tag :color="getDifficultyColor(record.difficultyLevel)">{{ record.difficultyLevel }}</a-tag>
            </template>
            <template v-if="column.key === 'action'">
                 <a-space>
                    <a @click="handleEdit(record)">编辑</a>
                    <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
                        <a style="color: red">删除</a>
                    </a-popconfirm>
                </a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 单条新建/编辑模态框 -->
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑范例' : '新建范例'" @ok="handleOk" :confirm-loading="modalLoading" width="800px">
        <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
            <!-- Form fields as designed -->
        </a-form>
    </a-modal>

    <!-- 批量添加模态框 -->
    <a-modal v-model:open="isBatchModalVisible" title="批量添加范例" width="90vw" @ok="handleBatchImport" :confirm-loading="batchLoading">
        <a-table :data-source="batchListData" :columns="batchColumns" :pagination="false" row-key="id">
            <template #bodyCell="{ column, record }">
                <template v-if="['userQuestion', 'targetQueryRepresentation'].includes(column.dataIndex)">
                    <a-textarea v-model:value="record[column.dataIndex]" :auto-size="{ minRows: 1, maxRows: 3 }"/>
                </template>
                <template v-if="column.dataIndex === 'targetDatasetId'">
                    <a-select v-model:value="record.targetDatasetId" style="width: 150px" placeholder="选择数据集"></a-select>
                </template>
                <template v-if="column.dataIndex === 'difficultyLevel'">
                     <a-select v-model:value="record.difficultyLevel" style="width: 100px"><a-select-option value="简单">简单</a-select-option><a-select-option value="中等">中等</a-select-option><a-select-option value="困难">困难</a-select-option></a-select>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <a-button type="link" danger @click="removeBatchRow(record.id)">删除</a-button>
                </template>
            </template>
        </a-table>
        <a-button @click="addBatchRow" type="dashed" class="mt-4 w-full"> + 添加一行 </a-button>
    </a-modal>
  </div>
</template>

<script setup>
// ... (Full script with all logic for search, single/batch CRUD operations)
</script>
