// =================================================================
// Backend Code
// =================================================================

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/entity/AdminUser.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import java.util.Date;
import java.util.Set;

@Entity
@Table(name = "admin_users")
@Data
public class AdminUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String username;

    @Column(nullable = false)
    private String password;

    @Column(nullable = false)
    private String name;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "admin_user_roles", joinColumns = @JoinColumn(name = "user_id"))
    @Column(name = "role")
    @Enumerated(EnumType.STRING)
    private Set<AdminRole> roles;

    private boolean enabled = true;

    @CreationTimestamp
    private Date createdAt;

    @UpdateTimestamp
    private Date updatedAt;
    
    public enum AdminRole {
        ROLE_ADMIN,
        ROLE_SUPER_ADMIN
    }
}

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/AdminUserRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.AdminUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface AdminUserRepository extends JpaRepository<AdminUser, Long> {
    Optional<AdminUser> findByUsername(String username);
    boolean existsByUsername(String username);
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/user/AdminUserDTO.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.user;

import com.qding.chatbi.metadata.entity.AdminUser;
import lombok.Data;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class AdminUserDTO {
    private Long id;
    private String username;
    private String name;
    private Set<String> roles;
    private boolean enabled;
    private Date createdAt;
    
    public static AdminUserDTO fromEntity(AdminUser entity) {
        AdminUserDTO dto = new AdminUserDTO();
        dto.setId(entity.getId());
        dto.setUsername(entity.getUsername());
        dto.setName(entity.getName());
        dto.setRoles(entity.getRoles().stream().map(Enum::name).collect(Collectors.toSet()));
        dto.setEnabled(entity.isEnabled());
        dto.setCreatedAt(entity.getCreatedAt());
        return dto;
    }
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/user/CreateAdminUserRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.user;

import com.qding.chatbi.metadata.entity.AdminUser;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Set;

@Data
public class CreateAdminUserRequest {
    @NotBlank(message = "登录账号不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, message = "密码至少为6位")
    private String password;

    @NotBlank(message = "姓名不能为空")
    private String name;

    @NotEmpty(message = "必须至少分配一个角色")
    private Set<AdminUser.AdminRole> roles;
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/AdminUserService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.user.AdminUserDTO;
import com.qding.chatbi.admin.vo.user.CreateAdminUserRequest;
import com.qding.chatbi.common.exception.InvalidInputException;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import com.qding.chatbi.metadata.entity.AdminUser;
import com.qding.chatbi.metadata.repository.AdminUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AdminUserService {

    @Autowired private AdminUserRepository adminUserRepository;
    @Autowired private PasswordEncoder passwordEncoder;

    public Page<AdminUserDTO> getAllAdminUsers(Pageable pageable) {
        return adminUserRepository.findAll(pageable).map(AdminUserDTO::fromEntity);
    }

    @Transactional
    public AdminUserDTO createAdminUser(CreateAdminUserRequest request) {
        if (adminUserRepository.findByUsername(request.getUsername()).isPresent()) {
            throw new InvalidInputException("登录账号 '" + request.getUsername() + "' 已存在。");
        }
        AdminUser user = new AdminUser();
        user.setUsername(request.getUsername());
        user.setName(request.getName());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRoles(request.getRoles());
        user.setEnabled(true);
        return AdminUserDTO.fromEntity(adminUserRepository.save(user));
    }

    @Transactional
    public void updateUserStatus(Long id, boolean enabled) {
        AdminUser user = adminUserRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("管理员账号", id));
        user.setEnabled(enabled);
        adminUserRepository.save(user);
    }

    @Transactional
    public void deleteUser(Long id) {
        // TODO: 添加更复杂的业务校验，如禁止删除最后一个超级管理员或自己
        if (!adminUserRepository.existsById(id)) {
            throw new ResourceNotFoundException("管理员账号", id);
        }
        adminUserRepository.deleteById(id);
    }
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/UserAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.AdminUserService;
import com.qding.chatbi.admin.vo.PagedResponse;
import com.qding.chatbi.admin.vo.user.AdminUserDTO;
import com.qding.chatbi.admin.vo.user.CreateAdminUserRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/users")
@PreAuthorize("hasRole('SUPER_ADMIN')")
public class UserAdminController {

    @Autowired
    private AdminUserService adminUserService;

    @GetMapping("/")
    public ResponseEntity<PagedResponse<AdminUserDTO>> listUsers(Pageable pageable) {
        Page<AdminUserDTO> page = adminUserService.getAllAdminUsers(pageable);
        return ResponseEntity.ok(new PagedResponse<>(page.getContent(), page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(), page.isLast()));
    }

    @PostMapping("/")
    public ResponseEntity<AdminUserDTO> createUser(@Valid @RequestBody CreateAdminUserRequest request) {
        AdminUserDTO createdUser = adminUserService.createAdminUser(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<Void> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Boolean> status) {
        adminUserService.updateUserStatus(id, status.get("enabled"));
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        adminUserService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }
}


// =================================================================
// Frontend: chatbi-admin-frontend
// =================================================================

// -----------------------------------------------------------------
// File: src/api/user.js
// -----------------------------------------------------------------
/*
import apiClient from './index';

export const userApi = {
  getUsers: (params) => apiClient.get('/users', { params }),
  createUser: (data) => apiClient.post('/users', data),
  updateUserStatus: (id, enabled) => apiClient.put(`/users/${id}/status`, { enabled }),
  deleteUser: (id) => apiClient.delete(`/users/${id}`),
};
*/


// -----------------------------------------------------------------
// File: src/views/system/AdminUserList.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="管理员设置" sub-title="创建和管理后台系统操作员账号" />
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAddNew">新建管理员</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="userList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'roles'">
                <a-space>
                    <a-tag v-for="role in record.roles" :key="role" :color="role.includes('SUPER') ? 'gold' : 'blue'">
                        {{ role.replace('ROLE_', '') }}
                    </a-tag>
                </a-space>
            </template>
            <template v-if="column.key === 'enabled'">
                <a-switch :checked="record.enabled" @change="(checked) => onStatusChange(record, checked)" />
            </template>
            <template v-if="column.key === 'createdAt'">
                {{ new Date(record.createdAt).toLocaleString() }}
            </template>
            <template v-if="column.key === 'action'">
                 <a-space>
                    <a>编辑</a>
                    <a-popconfirm title="确定删除该管理员吗?" @confirm="handleDelete(record.id)">
                        <a style="color: red">删除</a>
                    </a-popconfirm>
                </a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑管理员' : '新建管理员'" @ok="handleOk" :confirm-loading="modalLoading">
        <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
            <a-form-item label="姓名" name="name" :rules="[{ required: true }]"><a-input v-model:value="formState.name" /></a-form-item>
            <a-form-item label="登录账号" name="username" :rules="[{ required: true }]"><a-input v-model:value="formState.username" /></a-form-item>
            <a-form-item v-if="!isEditing" label="初始密码" name="password" :rules="[{ required: true, min: 6 }]"><a-input-password v-model:value="formState.password" /></a-form-item>
            <a-form-item label="角色" name="roles" :rules="[{ required: true, type: 'array' }]">
                <a-checkbox-group v-model:value="formState.roles">
                    <a-checkbox value="ROLE_ADMIN">管理员</a-checkbox>
                    <a-checkbox value="ROLE_SUPER_ADMIN">超级管理员</a-checkbox>
                </a-checkbox-group>
            </a-form-item>
        </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { userApi } from '../../api/user.js';
import { message } from 'ant-design-vue';

const columns = [
  { title: '姓名', dataIndex: 'name' },
  { title: '登录账号', dataIndex: 'username' },
  { title: '角色', key: 'roles' },
  { title: '状态', key: 'enabled' },
  { title: '创建时间', key: 'createdAt' },
  { title: '操作', key: 'action' },
];

const userList = ref([]);
const loading = ref(false);
const pagination = reactive({ current: 1, pageSize: 10, total: 0 });

const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({
    id: null,
    username: '',
    name: '',
    password: '',
    roles: ['ROLE_ADMIN'],
});

const fetchData = async () => {
    loading.value = true;
    try {
        const params = { page: pagination.current - 1, size: pagination.pageSize };
        const res = await userApi.getUsers(params);
        userList.value = res.data.content;
        pagination.total = res.data.totalElements;
    } finally {
        loading.value = false;
    }
};

onMounted(fetchData);

const handleTableChange = (p) => {
  pagination.current = p.current;
  pagination.pageSize = p.pageSize;
  fetchData();
};

const handleAddNew = () => {
    isEditing.value = false;
    Object.assign(formState, { id: null, username: '', name: '', password: '', roles: ['ROLE_ADMIN'] });
    isModalVisible.value = true;
};

const onStatusChange = async (user, checked) => {
    try {
        await userApi.updateUserStatus(user.id, checked);
        message.success("状态更新成功");
        user.enabled = checked;
    } catch (error) {
        message.error("状态更新失败");
    }
};

const handleDelete = async (id) => {
    try {
        await userApi.deleteUser(id);
        message.success("删除成功");
        fetchData();
    } catch (error) {
        message.error("删除失败");
    }
};

const handleOk = async () => {
    try {
        await formRef.value.validate();
        modalLoading.value = true;
        if (isEditing.value) {
            // await userApi.updateUser(formState.id, formState); // API not defined for brevity
        } else {
            await userApi.createUser(formState);
        }
        message.success(isEditing.value ? "更新成功" : "创建成功");
        isModalVisible.value = false;
        fetchData();
    } catch (error) {
        console.error("表单提交失败:", error);
    } finally {
        modalLoading.value = false;
    }
};
</script>

<style scoped>
.table-operations {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}
</style>
*/
