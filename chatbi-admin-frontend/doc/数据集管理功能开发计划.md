# **数据集管理功能开发计划**

## **1\. 目标**

本计划旨在详细设计并实现“数据集管理”模块的所有核心功能，包括新建、编辑、删除数据集，以及最重要的**数据集配置**页面（涵盖物理字段和计算指标），同时确保交互流程清晰、用户体验友好。

## **2\. 后端开发计划 (chatbi-admin-backend)**

后端 API 设计基本保持不变，现有的接口已经可以很好地支持新的前端页面结构。PUT /api/admin/datasets/{id} 用于更新数据集基本信息，而字段相关的 API 用于处理下半部分页卡中的操作。

### **2.1. 新增/更新 DTOs & VOs**

我们需要定义用于创建和更新操作的请求体对象。

// 【新增】用于在创建流程中，预览并配置字段的请求体  
// File: src/main/java/com/qding/chatbi/admin/vo/dataset/ColumnInitialConfig.java  
package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.SemanticType;  
import lombok.Data;  
import javax.validation.constraints.NotBlank;  
import javax.validation.constraints.NotNull;

@Data  
public class ColumnInitialConfig {  
    @NotBlank  
    private String technicalName; // 原始物理字段名  
    private String dataType;      // 原始数据类型  
    private boolean enabled \= true; // 默认启用  
      
    @NotBlank  
    private String columnName; // 业务名称 (可由前端预填充为技术名)  
      
    @NotNull  
    private SemanticType semanticType; // 语义类型  
}

// 【变更】CreateDatasetRequest 现在包含初始化的字段配置  
// File: src/main/java/com/qding/chatbi/admin/vo/dataset/CreateDatasetRequest.java  
package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.BaseObjectType;  
import lombok.Data;  
import javax.validation.Valid;  
import javax.validation.constraints.NotBlank;  
import javax.validation.constraints.NotNull;  
import java.util.List;

@Data  
public class CreateDatasetRequest {  
    // ... (基本信息)  
    @NotBlank  
    private String datasetName;  
    private String description;  
    @NotNull  
    private Long dataSourceId;  
    @NotNull  
    private BaseObjectType baseObjectType;  
    @NotBlank  
    private String sourceDefinition;  
      
    // 【新增】包含用户在第三步选择并配置好的字段列表  
    @Valid  
    private List\<ColumnInitialConfig\> columns;  
}

// 【新增】用于预览数据源字段的请求体  
// File: src/main/java/com/qding/chatbi/admin/vo/dataset/PreviewSourceRequest.java  
package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.BaseObjectType;  
import lombok.Data;  
import javax.validation.constraints.NotBlank;  
import javax.validation.constraints.NotNull;

@Data  
public class PreviewSourceRequest {  
    @NotNull  
    private Long dataSourceId;  
    @NotNull  
    private BaseObjectType baseObjectType;  
    @NotBlank  
    private String sourceDefinition;  
}

### **2.2. Controller (DatasetAdminController) API 扩展**

我们需要在 DatasetAdminController 中添加以下端点：

| HTTP 方法 | 路径 | 描述 | 请求体 | 成功响应 |
| :---- | :---- | :---- | :---- | :---- |
| POST | / | 新建一个数据集（包含字段配置） | CreateDatasetRequest | DatasetDetailDTO |
| PUT | /{id} | 更新数据集的基本信息 | UpdateDatasetRequest | DatasetDetailDTO |
| DELETE | /{id} | 删除一个数据集 | \- | void |
| POST | /{id}/columns/sync | 从数据源同步/刷新字段列表 | \- | List\<ColumnConfigDTO\> |
| POST | /preview-columns | **【新增】** 预览数据源的字段信息 | PreviewSourceRequest | List\<ColumnInitialConfig\> |
| PUT | /columns/{columnId} | 更新单个字段的配置 | UpdateColumnConfigRequest | ColumnConfigDTO |
| POST | /{id}/columns/computed | 为数据集添加一个计算指标 | CreateComputedColumnRequest | ColumnConfigDTO |
| POST | /{id}/columns/validate | 校验一个SQL表达式的有效性 | ValidateExpressionRequest | { "valid": true/false, "message": "..." } |

### **2.3. Service (ModelManagementService) 业务逻辑**

* **【新增】previewColumns(PreviewSourceRequest request)**:  
  1. 根据请求中的数据源 ID 和来源定义（表名、DDL等）。  
  2. 连接到数据库或解析 DDL，获取所有物理字段的名称和数据类型。  
  3. 将这些物理字段转换为 ColumnInitialConfig 对象列表返回给前端。columnName 可以默认填充为 technicalName。  
* **【变更】createDataset(CreateDatasetRequest request)**:  
  1. 创建一个 QueryableDataset 实体并保存基本信息。  
  2. 遍历 request.getColumns() 列表。  
  3. 对于每一个 columnConfig，如果其 enabled 为 true，则创建一个 DatasetColumn 实体，填充所有信息（业务名、语义类型等），并与第一步创建的数据集关联。  
  4. 批量保存所有启用的 DatasetColumn 实体。  
  5. 返回新创建的数据集的 DatasetDetailDTO。

## **3\. 前端开发计划 (chatbi-admin-frontend)**

### **3.1. DatasetList.vue 页面交互变更**

* **操作合并**: 原有的“配置”和“编辑”按钮将**合并为一个“编辑”按钮**。  
* **交互流程**:  
  1. 点击某行数据的“编辑”按钮。  
  2. 使用 router.push() **直接跳转**到该数据集的统一编辑页面，URL为 /model/datasets/:id/edit。

### **3.2. 全新的编辑页面 (DatasetEditPage.vue)**

我们将创建一个全新的页面组件 DatasetEditPage.vue，用于承载统一的编辑体验。

* **路由**: 在 router/index.js 中新增一条路由：  
  {  
    path: 'model/datasets/:id/edit', // 使用 :id 作为动态参数  
    name: 'DatasetEdit',  
    component: () \=\> import('../views/model/DatasetEditPage.vue'),  
    meta: { title: '编辑数据集', requiresAuth: true }  
  }

* **页面布局**:  
  * **顶部**: 使用 \<a-page-header\> 显示数据集名称，并提供一个返回按钮。  
  * **上半部分 (数据集信息)**:  
    * 一个 \<a-card\> 组件，标题为“基本信息”。  
    * 内部是一个 \<a-form\>，包含“数据集名称”和“描述”等输入框，用于编辑数据集本身的信息。  
    * 表单旁边有一个“保存”按钮，用于提交对上半部分信息的修改。  
  * **下半部分 (字段配置)**:  
    * 一个 \<a-card\> 组件，标题为“字段与指标配置”。  
    * 内部是一个 \<a-tabs\> 组件，包含三个页卡。  
* **下半部分页卡设计**:  
  1. **页卡一: 全部字段**  
     * **目的**: 提供一个统一的视图，查看所有对 AI 可见的字段，无论其来源是物理表还是计算得来。  
     * **内容**: 一个 \<a-table\>，数据源是 datasetDetails.columns。  
     * **操作**:  
       * **启用开关**: 列表第一列是一个 \<a-switch\>，用于控制该字段是否对AI暴露。  
       * **编辑**: 每行提供“编辑”按钮，点击后弹出模态框进行详细配置。  
  2. **页卡二: 实体字段**  
     * **目的**: 专注于管理从数据源同步来的物理字段。  
     * **内容**: 一个 \<a-table\>，数据源是经过计算筛选后的物理字段列表。  
     * **操作**:  
       * **从数据源同步**: 表格右上角提供一个“同步字段”按钮，点击后调用 syncColumns API。  
       * 列表中的操作与“全部字段”页卡类似。  
  3. **页卡三: 计算指标**  
     * **目的**: 专门用于创建和管理基于表达式的衍生指标。  
     * **内容**: 一个 \<a-table\>，数据源是经过计算筛选后的计算指标列表。  
     * **操作**:  
       * **添加计算指标**: 表格右上角提供此按钮，点击后弹出专用的创建模态框。  
       * **创建模态框**: 包含“业务名称”、“SQL表达式”输入框，并提供\*\*“校验表达式”\*\*按钮，实时调用后端接口验证表达式的有效性。  
       * 列表中的每一行提供“编辑”和“删除”操作。

### **3.3. 【核心】新建数据集流程交互实现 (方案B)**

此流程将通过一个多步骤的模态框来实现。

1. **触发**: 用户在 DatasetList.vue 页面点击“新建数据集”按钮，打开一个包含 \<a-steps\> 的模态框。  
2. **步骤一 & 二 (基本信息与来源定义)**:  
   * 用户填写数据集名称、选择数据源和来源类型（物理表/DDL）。  
   * 此阶段的交互与之前设计保持一致。  
3. **进入第三步 (字段配置)**:  
   * 用户在第二步完成输入并点击“下一步”后，前端调用 modelApi.previewColumns 接口，将数据源和来源定义发送到后端。  
   * 在等待后端响应时，显示加载状态。  
   * **界面展示**: 模态框内容切换为一个**可编辑表格** (\<a-table\>)。  
   * **数据**: 表格的数据源是 previewColumns 接口返回的字段列表。  
   * **可编辑表格列设计**:  
     * **启用**: (width: 80px): 使用 \<a-checkbox\>，默认勾选。管理员可以取消勾选以排除不需要的字段。  
     * **物理名称**: (width: 200px): **只读**文本，展示从数据源获取的原始列名。  
     * **业务名称**: (width: 200px): 使用 \<a-input\>，允许管理员修改，默认填充为物理名称。  
     * **语义类型**: (width: 150px): 使用 \<a-select\>，选项为“维度”和“指标”。前端可根据原始数据类型（如 INT, DECIMAL）智能推荐默认为“指标”。  
     * **数据类型**: (width: 120px): **只读**文本，展示从数据源获取的原始数据类型。  
4. **完成创建**:  
   * 用户在第三步完成配置后，点击模态框的“完成”按钮。  
   * 前端收集所有在可编辑表格中勾选了“启用”复选框的行的数据。  
   * 将所有信息（步骤1、2、3）组合成一个完整的 CreateDatasetRequest 对象，调用 modelApi.createDataset。  
   * 成功后，关闭模态框，刷新列表，并可选择性地弹出提示，询问是否立即跳转到新数据集的编辑页面。

## **4\. 总结**

通过采纳您的建议，我们将数据集的**编辑和配置体验**进行了彻底的重构和统一。新的 DatasetEditPage.vue 页面通过**上下分区和页卡设计**，将数据集的宏观信息和微观字段配置清晰地组织在一起，极大地提升了管理员的操作效率和流程的连贯性。这是一个非常出色的优化方案。