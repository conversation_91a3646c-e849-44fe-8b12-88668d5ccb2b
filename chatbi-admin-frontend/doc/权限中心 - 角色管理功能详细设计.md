# **权限中心 \- 角色管理功能详细设计**

## **1\. 目标与价值**

**核心目标**: 提供一个直观的界面，让后台管理员可以方便地创建、查看、编辑和删除与业务岗位或部门相对应的**数据查询角色**。同时，提供**模糊搜索**能力，以便在角色数量多时快速定位。

**核心价值**:

* **权限抽象**: 将对数据集的访问权限从具体的用户身上解耦，转而赋予给“角色”，使得权限管理更加清晰和可维护。  
* **批量授权**: 一个角色可以被赋予给多个用户，当需要调整一类用户的权限时，只需修改该角色所拥有的数据集权限即可，无需逐个为用户进行调整。  
* **业务语义**: 角色名称（如“华北区销售经理”）本身就带有清晰的业务含义，便于理解和审计。

## **2\. 后端开发计划 (chatbi-admin-backend)**

### **2.1. DTO / VO 定义**

(保持不变)

// File: src/main/java/com/qding/chatbi/admin/vo/permission/RoleDTO.java  
package com.qding.chatbi.admin.vo.permission;

import lombok.Data;  
import java.util.Date;

@Data  
public class RoleDTO {  
    private Long id;  
    private String roleName;  
    private String description;  
    private Date createdAt;  
}

// File: src/main/java/com/qding/chatbi/admin/vo/permission/CreateOrUpdateRoleRequest.java  
package com.qding.chatbi.admin.vo.permission;

import lombok.Data;  
import javax.validation.constraints.NotBlank;

@Data  
public class CreateOrUpdateRoleRequest {  
    @NotBlank(message \= "角色名称不能为空")  
    private String roleName;  
      
    private String description;  
}

### **2.2. Controller (PermissionAdminController)**

**【变更】** listRoles 接口将增加一个可选的 search 请求参数。

// File: src/main/java/com/qding/chatbi/admin/controller/PermissionAdminController.java  
package com.qding.chatbi.admin.controller;

// ... (imports) ...  
import org.springframework.web.bind.annotation.RequestParam;

@RestController  
@RequestMapping("/api/admin/permissions")  
public class PermissionAdminController {

    @Autowired  
    private PermissionService permissionService;

    // \--- 角色管理 \---  
    @GetMapping("/roles")  
    public ResponseEntity\<PagedResponse\<RoleDTO\>\> listRoles(  
        @RequestParam(required \= false) String search, // 【新增】接收搜索关键词  
        Pageable pageable) {  
          
        Page\<RoleDTO\> page \= permissionService.searchOrListRoles(search, pageable);  
        // ... 将 Page 转换为 PagedResponse ...  
        return ResponseEntity.ok(pagedResponse);  
    }

    // ... 其他端点保持不变 ...  
}

### **2.3. Service (PermissionService)**

**【变更】** 增加处理模糊搜索的逻辑。

// File: src/main/java/com/qding/chatbi/admin/service/PermissionService.java  
package com.qding.chatbi.admin.service;

// ... (imports) ...

@Service  
public class PermissionService {  
      
    @Autowired  
    private RoleRepository roleRepository; // 注入 metadata 模块的 Repository

    /\*\*  
     \* 【核心变更】根据搜索词或分页信息查询角色。  
     \* @param searchTerm 可选的搜索关键词。  
     \* @param pageable 分页信息。  
     \* @return 分页的角色DTO。  
     \*/  
    public Page\<RoleDTO\> searchOrListRoles(String searchTerm, Pageable pageable) {  
        Page\<Role\> page;  
        if (searchTerm \== null || searchTerm.isBlank()) {  
            // 如果搜索词为空，则返回所有角色  
            page \= roleRepository.findAll(pageable);  
        } else {  
            // 如果有搜索词，则执行模糊搜索  
            page \= roleRepository.findByRoleNameContainingIgnoreCase(searchTerm, pageable);  
        }  
        return page.map(this::convertToDto);  
    }  
      
    // ... 其他单条增删改和DTO转换方法保持不变 ...  
}

### **2.4. Repository (RoleRepository in metadata-service)**

**【新增】** 在 RoleRepository 接口中增加支持模糊查询的方法。

// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/RoleRepository.java  
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.Role;  
import org.springframework.data.domain.Page;  
import org.springframework.data.domain.Pageable;  
import org.springframework.data.jpa.repository.JpaRepository;

public interface RoleRepository extends JpaRepository\<Role, Long\> {  
      
    /\*\*  
     \* 【新增】根据角色名称进行不区分大小写的模糊查询，并返回分页结果。  
     \* Spring Data JPA 会自动根据方法名生成 "WHERE roleName LIKE '%searchTerm%'" 的SQL查询。  
     \* @param roleName 搜索关键词  
     \* @param pageable 分页对象  
     \* @return 分页的角色实体  
     \*/  
    Page\<Role\> findByRoleNameContainingIgnoreCase(String roleName, Pageable pageable);  
}

## **3\. 前端开发计划 (chatbi-admin-frontend)**

### **3.1. api/permission.js (更新)**

// File: src/api/permission.js  
import apiClient from './index';

export const permissionApi \= {  
  // 【变更】getRoles现在可以接受包含搜索词的参数  
  getRoles: (params) \=\> apiClient.get('/permissions/roles', { params }),  
  createRole: (data) \=\> apiClient.post('/permissions/roles', data),  
  updateRole: (id, data) \=\> apiClient.put(\`/permissions/roles/${id}\`, data),  
  deleteRole: (id) \=\> apiClient.delete(\`/permissions/roles/${id}\`),  
};

### **3.2. views/permissions/RoleList.vue (更新)**

* **UI 布局**:  
  * 在“新建角色”按钮旁边，**【新增】** 一个 \<a-input-search\> 组件，用于输入角色名称进行搜索。  
* **组件状态与逻辑**:  
  \<\!-- File: src/views/permissions/RoleList.vue \--\>  
  \<template\>  
    \<div\>  
      \<a-page-header title="角色管理" sub-title="创建和管理数据查询角色" /\>  
      \<a-card\>  
        \<div class="table-operations"\>  
          \<a-input-search  
            v-model:value="searchTerm"  
            placeholder="按角色名称搜索..."  
            style="width: 280px"  
            @search="onSearch"  
            allow-clear  
          /\>  
          \<a-button type="primary" @click="handleAddNew"\>新建角色\</a-button\>  
        \</div\>  
        \<a-table  
          :columns="columns"  
          :data-source="roleList"  
          :loading="loading"  
          :pagination="pagination"  
          row-key="id"  
          @change="handleTableChange"  
        \>  
          \<\!-- ... 表格列渲染 ... \--\>  
        \</a-table\>  
      \</a-card\>  
      \<\!-- ... 新建/编辑模态框 ... \--\>  
    \</div\>  
  \</template\>

  \<script setup\>  
  import { ref, reactive, onMounted, watch } from 'vue';  
  import { permissionApi } from '../../api/permission.js';

  const searchTerm \= ref(''); // 【新增】绑定搜索框  
  const roleList \= ref(\[\]);  
  const loading \= ref(false);  
  const pagination \= reactive({ /\* ... \*/ });

  const fetchData \= async () \=\> {  
    loading.value \= true;  
    try {  
      const params \= {  
        page: pagination.current \- 1,  
        size: pagination.pageSize,  
        search: searchTerm.value, // 【新增】将搜索词加入请求  
      };  
      const res \= await permissionApi.getRoles(params);  
      roleList.value \= res.data.content;  
      pagination.total \= res.data.totalElements;  
    } finally {  
      loading.value \= false;  
    }  
  };

  const onSearch \= () \=\> {  
    pagination.current \= 1; // 每次搜索都回到第一页  
    fetchData();  
  };

  // 监听搜索框清空事件  
  watch(searchTerm, (val) \=\> {  
      if (\!val) {  
          onSearch();  
      }  
  });

  onMounted(fetchData);

  const handleTableChange \= (p) \=\> {  
    pagination.current \= p.current;  
    pagination.pageSize \= p.pageSize;  
    fetchData();  
  };

  // ... 其他增删改的交互逻辑 ...  
  \</script\>

## **4\. 总结**

通过本次增强，我们为“角色管理”功能增加了一个非常实用的**模糊搜索**能力。

* **后端**: 通过扩展 Repository, Service, 和 Controller，实现了对带有搜索条件的列表请求的处理。  
* **前端**: 通过增加一个搜索框，并更新数据请求逻辑，为管理员提供了无缝的、在常规列表和搜索结果之间切换的体验。

这个设计使得角色管理功能在角色数量增多后，依然能保持高效和易用性。