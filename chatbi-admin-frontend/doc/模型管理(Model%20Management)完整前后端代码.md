// =================================================================
// Backend: chatbi-admin-backend
// =================================================================

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/vo/PagedResponse.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * 通用的分页响应VO。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PagedResponse<T> {
    private List<T> content;
    private int pageNumber;
    private int pageSize;
    private long totalElements;
    private int totalPages;
    private boolean isLast;
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/vo/CreateDataSourceRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo;

import com.qding.chatbi.common.enums.DatabaseType;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

@Data
public class CreateDataSourceRequest {
    @NotBlank(message = "数据源名称不能为空")
    private String sourceName;
    @NotNull(message = "数据库类型不能为空")
    private DatabaseType databaseType;
    @NotBlank(message = "主机地址不能为空")
    private String host;
    @NotNull(message = "端口号不能为空")
    @Min(value = 1, message = "端口号必须大于0")
    private Integer port;
    @NotBlank(message = "用户名不能为空")
    private String username;
    private String password;
    @NotBlank(message = "数据库名称不能为空")
    private String databaseName;
    private String description;
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/service/ModelManagementService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.CreateDataSourceRequest;
import com.qding.chatbi.admin.vo.DataSourceDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import java.util.Collections;
import org.springframework.data.domain.PageImpl;

/**
 * 封装模型管理相关的所有业务逻辑。
 */
@Service
public class ModelManagementService {

    // 实际应注入 Repository
    // @Autowired private DataSourceRepository dataSourceRepository;

    public Page<DataSourceDTO> getAllDataSources(Pageable pageable) {
        // TODO: 实现从数据库查询并转换
        // return dataSourceRepository.findAll(pageable).map(this::convertToDto);
        // 模拟返回
        DataSourceDTO dto = new DataSourceDTO();
        dto.setId(1L);
        dto.setSourceName("生产环境MySQL集群 (模拟)");
        dto.setDatabaseType(com.qding.chatbi.common.enums.DatabaseType.MYSQL);
        dto.setHost("rm-bp1xxxxxxxx.mysql.rds.aliyuncs.com");
        dto.setPort(3306);
        dto.setStatus("CONNECTED");
        return new PageImpl<>(Collections.singletonList(dto), pageable, 1);
    }

    public DataSourceDTO createDataSource(CreateDataSourceRequest request) {
        // TODO: 实现创建逻辑
        System.out.println("创建数据源: " + request);
        // 模拟返回
        DataSourceDTO dto = new DataSourceDTO();
        dto.setId(System.currentTimeMillis());
        dto.setSourceName(request.getSourceName());
        dto.setDatabaseType(request.getDatabaseType());
        dto.setHost(request.getHost());
        dto.setPort(request.getPort());
        dto.setStatus("CONNECTED");
        return dto;
    }
    
    public boolean testConnection(CreateDataSourceRequest request) {
        // TODO: 实现真实的数据库连接测试
        System.out.println("测试连接: " + request);
        return true;
    }
    
    // ... 其他数据集等方法的实现 ...
}


// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/controller/DataSourceAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.ModelManagementService;
import com.qding.chatbi.admin.vo.CreateDataSourceRequest;
import com.qding.chatbi.admin.vo.DataSourceDTO;
import com.qding.chatbi.admin.vo.PagedResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.Collections;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/data-sources")
public class DataSourceAdminController {

    @Autowired
    private ModelManagementService modelService;

    @GetMapping("/")
    public ResponseEntity<PagedResponse<DataSourceDTO>> listDataSources(
        @PageableDefault(size = 20, sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        
        Page<DataSourceDTO> page = modelService.getAllDataSources(pageable);
        
        PagedResponse<DataSourceDTO> response = new PagedResponse<>(
            page.getContent(),
            page.getNumber(),
            page.getSize(),
            page.getTotalElements(),
            page.getTotalPages(),
            page.isLast()
        );
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<DataSourceDTO> createDataSource(@Valid @RequestBody CreateDataSourceRequest request) {
        return ResponseEntity.ok(modelService.createDataSource(request));
    }

    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@Valid @RequestBody CreateDataSourceRequest request) {
        boolean success = modelService.testConnection(request);
        String message = success ? "连接成功" : "连接失败";
        return ResponseEntity.ok(Map.of("success", success, "message", message));
    }
    
    // 其他更新、删除的端点...
}


// =================================================================
// Frontend: chatbi-admin-frontend
// =================================================================

// -----------------------------------------------------------------
// File: src/api/model.js
// -----------------------------------------------------------------
import apiClient from './index';

export const modelApi = {
  // 数据源API
  getDataSources: (params) => apiClient.get('/data-sources/', { params }),
  createDataSource: (data) => apiClient.post('/data-sources/', data),
  testConnection: (data) => apiClient.post('/data-sources/test-connection', data),
  updateDataSource: (id, data) => apiClient.put(`/data-sources/${id}`, data),
  deleteDataSource: (id) => apiClient.delete(`/data-sources/${id}`),

  // 数据集API (占位)
  getDatasets: (params) => {
    console.log("Fetching datasets with params:", params);
    // 模拟分页返回
    return Promise.resolve({ 
        data: {
            content: [
                { id: 1, datasetName: '每日产品销售汇总 (模拟)', sourceName: '生产环境MySQL集群', baseObjectType: 'VIEW', status: 'ACTIVE', updatedAt: new Date() },
                { id: 2, datasetName: '用户行为日志 (模拟)', sourceName: '数据仓库Hive', baseObjectType: 'TABLE', status: 'ACTIVE', updatedAt: new Date() },
            ],
            totalElements: 2
        }
    });
  },
  getDatasetDetails: (id) => {
    console.log("Fetching dataset details for id:", id);
    // 模拟返回
     return Promise.resolve({
        data: {
            id: 1, 
            datasetName: '每日产品销售汇总 (模拟)', 
            sourceName: '生产环境MySQL集群', 
            baseObjectType: 'VIEW', 
            status: 'ACTIVE', 
            updatedAt: new Date(),
            description: '这是对每日产品销售汇总数据的详细业务描述。',
            columns: [
                { id: 101, columnName: '销售额', technicalNameOrExpression: 'sale_amount', semanticType: 'METRIC', dataType: 'NUMBER', synonyms: ['GMV', '成交额'], isFilterable: true, isGroupable: false, description: '商品的最终成交金额' },
                { id: 102, columnName: '城市', technicalNameOrExpression: 'city', semanticType: 'DIMENSION', dataType: 'STRING', synonyms: ['地区'], isFilterable: true, isGroupable: true, description: '订单所属的城市' },
            ]
        }
     });
  }
};


// -----------------------------------------------------------------
// File: src/views/model/DataSourceList.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="数据源管理" sub-title="配置并管理系统可以连接的所有外部数据库" />
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAddNew">新建数据源</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSourceList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'databaseType'">
            <a-tag :color="getDbTypeColor(record.databaseType)">{{ record.databaseType }}</a-tag>
          </template>
          <template v-if="column.key === 'connectionInfo'">
            {{ record.host }}:{{ record.port }}
          </template>
           <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'CONNECTED' ? 'green' : 'red'">{{ record.status }}</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
                <a style="color: red">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:open="isModalVisible"
      :title="isEditing ? '编辑数据源' : '新建数据源'"
      @ok="handleOk"
      :confirm-loading="modalLoading"
    >
      <a-form ref="formRef" :model="formState" layout="vertical">
        <a-form-item name="sourceName" label="数据源名称" :rules="[{ required: true }]">
          <a-input v-model:value="formState.sourceName" />
        </a-form-item>
        <a-form-item name="databaseType" label="数据库类型" :rules="[{ required: true }]">
          <a-select v-model:value="formState.databaseType">
            <a-select-option value="MYSQL">MySQL</a-select-option>
            <a-select-option value="POSTGRESQL">PostgreSQL</a-select-option>
            <a-select-option value="HIVE">Hive</a-select-option>
          </a-select>
        </a-form-item>
        <a-row :gutter="16">
          <a-col :span="16">
            <a-form-item name="host" label="主机地址" :rules="[{ required: true }]">
              <a-input v-model:value="formState.host" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
             <a-form-item name="port" label="端口" :rules="[{ required: true }]">
              <a-input-number v-model:value="formState.port" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item name="databaseName" label="数据库名称" :rules="[{ required: true }]">
            <a-input v-model:value="formState.databaseName" />
        </a-form-item>
        <a-form-item name="username" label="用户名" :rules="[{ required: true }]">
            <a-input v-model:value="formState.username" />
        </a-form-item>
        <a-form-item name="password" label="密码">
            <a-input-password v-model:value="formState.password" placeholder="不修改则请留空" />
        </a-form-item>
      </a-form>
       <template #footer>
        <a-button key="test" @click="handleTestConnection" :loading="testLoading">测试连接</a-button>
        <a-button key="back" @click="isModalVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="modalLoading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { modelApi } from '../../api/model.js';
import { message } from 'ant-design-vue';

const columns = [
  { title: '数据源名称', dataIndex: 'sourceName', key: 'sourceName' },
  { title: '数据库类型', dataIndex: 'databaseType', key: 'databaseType' },
  { title: '连接信息', key: 'connectionInfo' },
  { title: '状态', key: 'status' },
  { title: '操作', key: 'action' },
];

const dataSourceList = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

const isModalVisible = ref(false);
const modalLoading = ref(false);
const testLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({
  id: null,
  sourceName: '',
  databaseType: null,
  host: '',
  port: null,
  databaseName: '',
  username: '',
  password: '',
});

const fetchData = async (pageInfo = { current: 1, pageSize: 20 }) => {
  loading.value = true;
  try {
    const params = { page: pageInfo.current - 1, size: pageInfo.pageSize };
    const response = await modelApi.getDataSources(params);
    dataSourceList.value = response.data.content;
    pagination.total = response.data.totalElements;
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
  } catch (error) {
    console.error("Failed to fetch data sources:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData(pagination);
});

const handleTableChange = (pageInfo) => {
  fetchData(pageInfo);
};

const handleAddNew = () => {
  isEditing.value = false;
  Object.assign(formState, { id: null, sourceName: '', databaseType: null, host: '', port: null, databaseName: '', username: '', password: '' });
  isModalVisible.value = true;
};

const handleEdit = (record) => {
  isEditing.value = true;
  Object.assign(formState, record);
  formState.password = ''; // 编辑时不显示旧密码
  isModalVisible.value = true;
};

const handleOk = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    if (isEditing.value) {
      await modelApi.updateDataSource(formState.id, formState);
      message.success('更新成功');
    } else {
      await modelApi.createDataSource(formState);
      message.success('创建成功');
    }
    isModalVisible.value = false;
    fetchData(pagination);
  } catch (error) {
    console.error("Form submission failed:", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleTestConnection = async () => {
    try {
        await formRef.value.validate();
        testLoading.value = true;
        const res = await modelApi.testConnection(formState);
        if(res.data.success) {
            message.success(res.data.message);
        } else {
            message.error(res.data.message);
        }
    } catch(error) {
        // validation failed
    } finally {
        testLoading.value = false;
    }
}

const getDbTypeColor = (type) => {
  const colors = { MYSQL: 'blue', POSTGRESQL: 'green', HIVE: 'orange' };
  return colors[type] || 'default';
};
</script>

<style scoped>
.table-operations {
  margin-bottom: 16px;
}
</style>
*/


// -----------------------------------------------------------------
// File: src/views/model/DatasetList.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="数据集管理" sub-title="定义可供AI查询的逻辑数据集" />
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAddNew">新建数据集</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="datasetList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'baseObjectType'">
                <a-tag color="geekblue">{{ record.baseObjectType }}</a-tag>
            </template>
            <template v-if="column.key === 'status'">
                <a-tag :color="record.status === 'ACTIVE' ? 'success' : 'warning'">{{ record.status }}</a-tag>
            </template>
            <template v-if="column.key === 'updatedAt'">
                {{ new Date(record.updatedAt).toLocaleString() }}
            </template>
            <template v-if="column.key === 'action'">
                <a-space>
                    <router-link :to="`/model/datasets/${record.id}/config`">配置</router-link>
                    <a @click="handleEdit(record)">编辑</a>
                    <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
                        <a style="color: red">删除</a>
                    </a-popconfirm>
                </a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 新建/编辑数据集的模态框 -->
    <a-modal v-model:open="isModalVisible" title="新建/编辑数据集" @ok="handleOk">
        <!-- 这里可以使用 a-steps 来引导用户创建 -->
        <p>数据集创建/编辑表单...</p>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { modelApi } from '../../api/model.js';
import { useRouter } from 'vue-router';

const router = useRouter();

const columns = [
  { title: '数据集名称', dataIndex: 'datasetName', key: 'datasetName' },
  { title: '所属数据源', dataIndex: 'sourceName', key: 'sourceName' },
  { title: '类型', key: 'baseObjectType' },
  { title: '状态', key: 'status' },
  { title: '最后更新时间', dataIndex: 'updatedAt', key: 'updatedAt' },
  { title: '操作', key: 'action' },
];

const datasetList = ref([]);
const loading = ref(false);
const isModalVisible = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

const fetchData = async (pageInfo = { current: 1, pageSize: 20 }) => {
  loading.value = true;
  try {
    const params = { page: pageInfo.current - 1, size: pageInfo.pageSize };
    const response = await modelApi.getDatasets(params);
    datasetList.value = response.data.content;
    pagination.total = response.data.totalElements;
  } catch (error) {
    console.error("Failed to fetch datasets:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => fetchData(pagination));

const handleTableChange = (pageInfo) => {
  fetchData(pageInfo);
};

const handleAddNew = () => { isModalVisible.value = true; };
const handleEdit = (record) => { isModalVisible.value = true; };
const handleOk = () => { isModalVisible.value = false; };
</script>

*/

// -----------------------------------------------------------------
// File: src/views/model/DatasetConfig.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header :title="datasetDetails?.datasetName || '加载中...'" :sub-title="datasetDetails?.description" @back="() => router.back()">
        <template #tags>
            <a-tag color="blue">{{ datasetDetails?.baseObjectType }}</a-tag>
        </template>
        <template #extra>
            <a-button key="sync" type="primary" @click="handleSyncColumns">从数据源同步</a-button>
        </template>
    </a-page-header>
    <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="fields" tab="字段配置">
            <a-card>
                 <a-table :columns="columns" :data-source="datasetDetails?.columns" row-key="id">
                     <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'semanticType'">
                            <a-tag :color="record.semanticType === 'METRIC' ? 'orange' : 'blue'">{{ record.semanticType }}</a-tag>
                        </template>
                        <template v-if="column.key === 'synonyms'">
                           <span v-for="tag in record.synonyms" :key="tag"><a-tag>{{tag}}</a-tag></span>
                        </template>
                        <template v-if="column.key === 'properties'">
                            <a-space>
                                <a-tooltip title="可用于筛选">
                                    <FilterOutlined v-if="record.isFilterable" />
                                </a-tooltip>
                                <a-tooltip title="可用于分组">
                                    <GroupOutlined v-if="record.isGroupable" />
                                </a-tooltip>
                            </a-space>
                        </template>
                        <template v-if="column.key === 'description'">
                            <a-tooltip :title="record.description">
                                <InfoCircleOutlined />
                            </a-tooltip>
                        </template>
                        <template v-if="column.key === 'action'">
                            <a @click="handleEditColumn(record)">编辑</a>
                        </template>
                    </template>
                 </a-table>
            </a-card>
        </a-tab-pane>
        <a-tab-pane key="computed" tab="计算指标">
            <a-card>
                <p>此处管理基于已有字段创建的计算指标。</p>
            </a-card>
        </a-tab-pane>
    </a-tabs>

    <a-modal v-model:open="isColumnModalVisible" title="编辑字段配置" @ok="handleColumnOk">
      <p>字段配置表单...</p>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { modelApi } from '../../api/model.js';
import { FilterOutlined, GroupOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();
const datasetDetails = ref(null);
const activeTab = ref('fields');
const isColumnModalVisible = ref(false);

const columns = [
    { title: '业务名称', dataIndex: 'columnName', key: 'columnName' },
    { title: '技术定义', dataIndex: 'technicalNameOrExpression', key: 'technicalNameOrExpression' },
    { title: '语义类型', key: 'semanticType' },
    { title: '数据类型', dataIndex: 'dataType', key: 'dataType' },
    { title: '同义词', key: 'synonyms' },
    { title: '关键属性', key: 'properties' },
    { title: '描述', key: 'description' },
    { title: '操作', key: 'action' },
];

onMounted(async () => {
    const datasetId = route.params.id;
    if (datasetId) {
        const response = await modelApi.getDatasetDetails(datasetId);
        datasetDetails.value = response.data;
    }
});

const handleSyncColumns = () => { console.log('Syncing columns...'); };
const handleEditColumn = (record) => { isColumnModalVisible.value = true; };
const handleColumnOk = () => { isColumnModalVisible.value = false; };
</script>
