// =================================================================
// Backend: chatbi-admin-backend
// =================================================================

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/vo/dataset/CreateDatasetRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.BaseObjectType;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CreateDatasetRequest {
    @NotBlank
    private String datasetName;
    private String description;

    @NotNull
    private Long dataSourceId;

    @NotNull
    private BaseObjectType baseObjectType; // TABLE, VIEW, or QUERY

    @NotBlank
    private String sourceDefinition; // Table/View name or the full SQL script
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/vo/dataset/UpdateDatasetRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.dataset;

import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class UpdateDatasetRequest {
    @NotBlank
    private String datasetName;
    private String description;
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/vo/dataset/UpdateColumnConfigRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.SemanticType;
import lombok.Data;
import java.util.List;

@Data
public class UpdateColumnConfigRequest {
    private String columnName;
    private String description;
    private SemanticType semanticType;
    private List<String> synonyms;
    private Boolean isFilterable;
    private Boolean isGroupable;
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/entity/QueryableDataset.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.entity;

import com.qding.chatbi.common.enums.BaseObjectType;
import jakarta.persistence.*;
import lombok.Data;
import java.util.List;

@Entity
@Table(name = "queryable_datasets")
@Data
public class QueryableDataset {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String datasetName;
    private String description;
    private Long dataSourceId;
    @Enumerated(EnumType.STRING)
    private BaseObjectType baseObjectType;
    @Lob
    private String sourceDefinition;

    @OneToMany(mappedBy = "dataset", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<DatasetColumn> columns;
    
    // ... other fields and timestamps
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/entity/DatasetColumn.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.entity;

import com.qding.chatbi.common.enums.SemanticType;
import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "dataset_columns")
@Data
public class DatasetColumn {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String columnName;
    private String description;
    private String technicalNameOrExpression;
    @Enumerated(EnumType.STRING)
    private SemanticType semanticType;
    private String dataType;
    private String synonyms; // Stored as JSON string
    private boolean isFilterable;
    private boolean isGroupable;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id")
    private QueryableDataset dataset;

    // ... other fields and timestamps
}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/repository/DatasetRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.repository;

import com.qding.chatbi.admin.entity.QueryableDataset;
import org.springframework.data.jpa.repository.JpaRepository;

public interface DatasetRepository extends JpaRepository<QueryableDataset, Long> {}

// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/repository/DatasetColumnRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.repository;

import com.qding.chatbi.admin.entity.DatasetColumn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

public interface DatasetColumnRepository extends JpaRepository<DatasetColumn, Long> {
    @Transactional
    void deleteByDatasetId(Long datasetId);
}


// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/service/ModelManagementService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

// ... (imports)
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.qding.chatbi.admin.vo.dataset.*;
import com.qding.chatbi.admin.repository.*;
import com.qding.chatbi.admin.entity.*;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class ModelManagementService {
    
    // ... (DataSource related code from previous turns)

    @Autowired private DatasetRepository datasetRepository;
    @Autowired private DatasetColumnRepository datasetColumnRepository;

    @Transactional
    public DatasetDetailDTO createDataset(CreateDatasetRequest request) {
        QueryableDataset dataset = new QueryableDataset();
        BeanUtils.copyProperties(request, dataset);
        QueryableDataset savedDataset = datasetRepository.save(dataset);
        
        // Auto-sync columns after creation
        syncColumnsFromSource(savedDataset.getId());

        return getDatasetDetails(savedDataset.getId());
    }
    
    @Transactional
    public void deleteDataset(Long id) {
        if (!datasetRepository.existsById(id)) {
            throw new ResourceNotFoundException("数据集", id);
        }
        // First delete all associated columns to avoid constraint violation
        datasetColumnRepository.deleteByDatasetId(id);
        // Then delete the dataset itself
        datasetRepository.deleteById(id);
    }

    @Transactional
    public List<ColumnConfigDTO> syncColumnsFromSource(Long datasetId) {
        QueryableDataset dataset = datasetRepository.findById(datasetId)
            .orElseThrow(() -> new ResourceNotFoundException("数据集", datasetId));
        
        // TODO: Implement real JDBC metadata fetching logic here
        // This is a simulation
        List<DatasetColumn> newColumns = new ArrayList<>();
        
        DatasetColumn col1 = new DatasetColumn();
        col1.setTechnicalNameOrExpression("user_id");
        col1.setColumnName("用户ID");
        col1.setDataType("STRING");
        col1.setDataset(dataset);
        newColumns.add(col1);

        DatasetColumn col2 = new DatasetColumn();
        col2.setTechnicalNameOrExpression("order_amount");
        col2.setColumnName("订单金额");
        col2.setDataType("NUMBER");
        col2.setDataset(dataset);
        newColumns.add(col2);

        // In real implementation, you would merge new/existing columns intelligently
        datasetColumnRepository.deleteByDatasetId(datasetId); // simple clear and add
        datasetColumnRepository.saveAll(newColumns);
        
        return newColumns.stream().map(this::convertColumnEntityToDto).collect(Collectors.toList());
    }

    // ... other helper methods and DTO converters ...
    private ColumnConfigDTO convertColumnEntityToDto(DatasetColumn entity) {
        // ... conversion logic ...
        return new ColumnConfigDTO();
    }
}


// -----------------------------------------------------------------
// File: src/main/java/com/qding/chatbi/admin/controller/DatasetAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.ModelManagementService;
import com.qding.chatbi.admin.vo.dataset.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/admin/datasets")
public class DatasetAdminController {

    @Autowired
    private ModelManagementService modelService;

    // ... (GET list and GET details endpoints from previous turns)

    @PostMapping("/")
    public ResponseEntity<DatasetDetailDTO> createDataset(@Valid @RequestBody CreateDatasetRequest request) {
        DatasetDetailDTO createdDataset = modelService.createDataset(request);
        return ResponseEntity.ok(createdDataset);
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateDataset(@PathVariable Long id, @Valid @RequestBody UpdateDatasetRequest request) {
        // ... call service to update ...
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDataset(@PathVariable Long id) {
        modelService.deleteDataset(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/columns/sync")
    public ResponseEntity<List<ColumnConfigDTO>> syncColumns(@PathVariable Long id) {
        List<ColumnConfigDTO> columns = modelService.syncColumnsFromSource(id);
        return ResponseEntity.ok(columns);
    }

    @PutMapping("/columns/{columnId}")
    public ResponseEntity<?> updateColumn(@PathVariable Long columnId, @Valid @RequestBody UpdateColumnConfigRequest request) {
        // ... call service to update column ...
        return ResponseEntity.ok().build();
    }
}


// =================================================================
// Frontend: chatbi-admin-frontend
// =================================================================

// -----------------------------------------------------------------
// File: src/api/model.js
// -----------------------------------------------------------------
// ... (existing code)
/*
export const modelApi = {
  // ... (dataSource APIs)
  getDatasets: (params) => apiClient.get('/datasets/', { params }),
  getDatasetDetails: (id) => apiClient.get(`/datasets/${id}`),
  createDataset: (data) => apiClient.post('/datasets/', data),
  updateDataset: (id, data) => apiClient.put(`/datasets/${id}`, data),
  deleteDataset: (id) => apiClient.delete(`/datasets/${id}`),
  syncColumns: (id) => apiClient.post(`/datasets/${id}/columns/sync`),
  updateColumn: (columnId, data) => apiClient.put(`/datasets/columns/${columnId}`, data),
};
*/

// -----------------------------------------------------------------
// File: src/views/model/DatasetList.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="数据集管理" sub-title="定义可供AI查询的逻辑数据集" />
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAddNew">新建数据集</a-button>
      </div>
      <a-table :columns="columns" :data-source="datasetList" :loading="loading" :pagination="pagination" row-key="id" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
            <!-- ... (table cell rendering) ... -->
            <template v-if="column.key === 'action'">
                <a-space>
                    <router-link :to="`/model/datasets/${record.id}/config`">配置</router-link>
                    <a @click="handleEdit(record)">编辑</a>
                    <a-popconfirm title="确定删除吗? 这将一并删除所有字段配置。" @confirm="handleDelete(record.id)">
                        <a style="color: red">删除</a>
                    </a-popconfirm>
                </a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑数据集' : '新建数据集'" @ok="handleOk" :confirm-loading="modalLoading" width="600px">
        <a-form ref="formRef" :model="formState" layout="vertical" v-if="!isEditing">
             <!-- Create Form with Steps -->
             <a-steps :current="currentStep">
                <a-step title="基本信息" />
                <a-step title="选择来源" />
             </a-steps>
             <div class="steps-content mt-4">
                <div v-show="currentStep === 0">
                    <a-form-item label="数据集名称" name="datasetName" :rules="[{ required: true }]"><a-input v-model:value="formState.datasetName" /></a-form-item>
                    <a-form-item label="描述" name="description"><a-textarea v-model:value="formState.description" /></a-form-item>
                </div>
                <div v-show="currentStep === 1">
                    <a-form-item label="数据源" name="dataSourceId" :rules="[{ required: true }]"><a-select v-model:value="formState.dataSourceId" placeholder="请选择数据源"></a-select></a-form-item>
                    <a-form-item label="来源类型" name="baseObjectType" :rules="[{ required: true }]"><a-radio-group v-model:value="formState.baseObjectType"><a-radio value="TABLE">物理表/视图</a-radio><a-radio value="QUERY">自定义SQL</a-radio></a-radio-group></a-form-item>
                    <a-form-item v-if="formState.baseObjectType === 'TABLE'" label="表/视图名称" name="sourceDefinition" :rules="[{ required: true }]"><a-select v-model:value="formState.sourceDefinition" placeholder="请选择表或视图"></a-select></a-form-item>
                    <a-form-item v-if="formState.baseObjectType === 'QUERY'" label="SQL脚本" name="sourceDefinition" :rules="[{ required: true }]"><a-textarea v-model:value="formState.sourceDefinition" :rows="4" /></a-form-item>
                </div>
             </div>
             <div class="steps-action">
                <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
                <a-button v-if="currentStep < 1" type="primary" @click="nextStep">下一步</a-button>
            </div>
        </a-form>
        <a-form ref="formRef" :model="formState" layout="vertical" v-else>
            <!-- Edit Form (simple) -->
            <a-form-item label="数据集名称" name="datasetName" :rules="[{ required: true }]"><a-input v-model:value="formState.datasetName" /></a-form-item>
            <a-form-item label="描述" name="description"><a-textarea v-model:value="formState.description" /></a-form-item>
        </a-form>
    </a-modal>
  </div>
</template>
<script setup>
// ... (script setup logic from previous turn)
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { modelApi } from '../../api/model';

const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const currentStep = ref(0);
const formRef = ref();
const formState = reactive({}); // Initialize empty

const handleAddNew = () => { /* reset formState, set isEditing false, show modal */ };
const handleEdit = (record) => { /* set formState, set isEditing true, show modal */ };
const handleDelete = async (id) => {
    try {
        await modelApi.deleteDataset(id);
        message.success('删除成功');
        // fetchData();
    } catch (error) {
        console.error('Delete failed:', error);
    }
};
const handleOk = async () => { /* form validation, call create/update API */ };
const nextStep = () => { currentStep.value++; };
const prevStep = () => { currentStep.value--; };

</script>
*/


// -----------------------------------------------------------------
// File: src/views/model/DatasetConfig.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header :title="datasetDetails?.datasetName" @back="() => router.back()">
        <template #extra>
            <a-button key="sync" type="primary" @click="handleSyncColumns" :loading="syncing">从数据源同步</a-button>
        </template>
    </a-page-header>
    <a-card>
        <a-table :columns="columns" :data-source="datasetDetails?.columns" row-key="id">
            <template #bodyCell="{ column, record }">
                <!-- ... (column rendering from previous turn) ... -->
                <template v-if="column.key === 'action'">
                    <a @click="handleEditColumn(record)">编辑</a>
                </template>
            </template>
        </a-table>
    </a-card>

    <a-modal v-model:open="isColumnModalVisible" title="编辑字段配置" @ok="handleColumnOk">
      <a-form ref="columnFormRef" :model="columnFormState" layout="vertical">
        <a-form-item label="业务名称" name="columnName" :rules="[{ required: true }]"><a-input v-model:value="columnFormState.columnName" /></a-form-item>
        <a-form-item label="描述" name="description"><a-textarea v-model:value="columnFormState.description" /></a-form-item>
        <a-form-item label="语义类型" name="semanticType"><a-select v-model:value="columnFormState.semanticType"><a-select-option value="METRIC">指标</a-select-option><a-select-option value="DIMENSION">维度</a-select-option></a-select></a-form-item>
        <a-form-item label="同义词" name="synonyms"><a-select v-model:value="columnFormState.synonyms" mode="tags" placeholder="输入后按回车确认" /></a-form-item>
        <a-form-item label="关键属性">
            <a-checkbox v-model:checked="columnFormState.isFilterable">可筛选</a-checkbox>
            <a-checkbox v-model:checked="columnFormState.isGroupable">可分组</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
// ... (script setup logic from previous turn)
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { modelApi } from '../../api/model';

const route = useRoute();
const datasetId = route.params.id;
const datasetDetails = ref(null);
const syncing = ref(false);

const isColumnModalVisible = ref(false);
const columnFormRef = ref();
const columnFormState = reactive({}); // Initialize empty

const fetchDetails = async () => {
    const res = await modelApi.getDatasetDetails(datasetId);
    datasetDetails.value = res.data;
};

onMounted(fetchDetails);

const handleSyncColumns = async () => {
    syncing.value = true;
    try {
        await modelApi.syncColumns(datasetId);
        message.success('字段同步成功');
        await fetchDetails(); // Refresh details
    } catch(error) {
        console.error('Sync failed:', error);
    } finally {
        syncing.value = false;
    }
};

const handleEditColumn = (column) => {
    Object.assign(columnFormState, column);
    isColumnModalVisible.value = true;
};

const handleColumnOk = async () => {
    await columnFormRef.value.validate();
    await modelApi.updateColumn(columnFormState.id, columnFormState);
    message.success('字段配置更新成功');
    isColumnModalVisible.value = false;
    await fetchDetails(); // Refresh details
};
</script>
*/
