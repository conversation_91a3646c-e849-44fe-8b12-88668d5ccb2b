# **知识库 \- 查询范例管理功能详细设计 (最终版)**

## **1\. 目标与价值**

**核心目标**: 建立一个高质量、可扩展的“问题-答案”范例库。管理员可以通过**单条、批量添加**等高效方式，录入典型的用户自然语言问题，并为其提供标准的查询表示。同时，提供**智能搜索**能力，方便管理员快速定位和维护范例。

**核心价值**:

* **提升AI处理复杂查询的能力**: 当 AI 遇到一个新问题时，它可以从这个范例库中检索最相似的问题及其“标准答案”，从而模仿范例的逻辑来构建更准确、更高效的查询。  
* **固化最佳实践**: 对于一些常见的、重要的、或者复杂的查询场景，管理员可以预先定义好最佳的查询方式。  
* **降低AI幻觉**: 通过提供具体的、可参考的范例，可以有效减少 AI 在生成复杂 SQL 时可能出现的“幻觉”或错误。

## **2\. 后端开发计划 (chatbi-admin-backend)**

### **2.1. DTO / VO 定义**

// File: src/main/java/com/qding/chatbi/admin/vo/example/QueryExampleDTO.java  
package com.qding.chatbi.admin.vo.example;  
// ... (保持不变)

// File: src/main/java/com/qding/chatbi/admin/vo/example/CreateOrUpdateExampleRequest.java  
package com.qding.chatbi.admin.vo.example;

import lombok.Data;  
import javax.validation.constraints.NotBlank;  
import javax.validation.constraints.NotNull;

// 【变更】合并Create和Update的请求体  
@Data  
public class CreateOrUpdateExampleRequest {  
    @NotBlank  
    private String userQuestion;  
    @NotNull  
    private Long targetDatasetId;  
    @NotBlank  
    private String targetQueryRepresentation;  
    private String difficultyLevel;  
    private String notes;  
}

### **2.2. Controller (KnowledgeBaseAdminController)**

增加批量创建接口，并为列表接口添加搜索参数。

// File: src/main/java/com/qding/chatbi/admin/controller/KnowledgeBaseAdminController.java  
// ... (之前的代码)

@RestController  
@RequestMapping("/api/admin/knowledge-base")  
public class KnowledgeBaseAdminController {  
    // ... (术语管理部分) ...

    @GetMapping("/examples")  
    public ResponseEntity\<PagedResponse\<QueryExampleDTO\>\> listExamples(  
        @RequestParam(required \= false) String search,  
        Pageable pageable) {  
        Page\<QueryExampleDTO\> page \= knowledgeBaseAdminService.searchOrListExamples(search, pageable);  
        // ... 转换为 PagedResponse ...  
        return ResponseEntity.ok(pagedResponse);  
    }

    @PostMapping("/examples")  
    public ResponseEntity\<QueryExampleDTO\> createExample(@Valid @RequestBody CreateOrUpdateExampleRequest request) {  
        return ResponseEntity.ok(knowledgeBaseAdminService.createExample(request));  
    }  
      
    // 【新增】批量创建查询范例  
    @PostMapping("/examples/batch")  
    public ResponseEntity\<BatchOperationReport\> createExamplesInBatch(@Valid @RequestBody List\<CreateOrUpdateExampleRequest\> requests) {  
        BatchOperationReport report \= knowledgeBaseAdminService.createExamplesInBatch(requests);  
        return ResponseEntity.ok(report);  
    }  
      
    // ... (PUT, DELETE 单条记录的端点) ...  
}

### **2.3. Service (KnowledgeBaseAdminService)**

增加处理批量添加和搜索的逻辑。

// File: src/main/java/com/qding/chatbi/admin/service/KnowledgeBaseAdminService.java  
// ... (之前的代码)

@Service  
public class KnowledgeBaseAdminService {  
    // ... (术语相关的服务和仓库) ...  
    @Autowired private QueryExampleRepository exampleRepository;

    public Page\<QueryExampleDTO\> searchOrListExamples(String searchTerm, Pageable pageable) {  
        if (searchTerm \== null || searchTerm.isBlank()) {  
            return exampleRepository.findAll(pageable).map(this::convertExampleToDto);  
        }  
        List\<QueryExample\> results \= knowledgePersistenceService.searchSimilarExamples(searchTerm, pageable.getPageSize());  
        List\<QueryExampleDTO\> dtoList \= results.stream().map(this::convertExampleToDto).collect(Collectors.toList());  
        return new PageImpl\<\>(dtoList, pageable, dtoList.size());  
    }

    @Transactional  
    public BatchOperationReport createExamplesInBatch(List\<CreateOrUpdateExampleRequest\> requests) {  
        int successCount \= 0;  
        List\<String\> errorMessages \= new ArrayList\<\>();  
        for (int i \= 0; i \< requests.size(); i++) {  
            try {  
                createExample(requests.get(i)); // 复用单条创建逻辑  
                successCount++;  
            } catch (Exception e) {  
                errorMessages.add(String.format("第 %d 行范例创建失败: %s", i \+ 1, e.getMessage()));  
            }  
        }  
        return new BatchOperationReport(successCount, requests.size() \- successCount, errorMessages);  
    }

    // ... (单条创建、更新、删除的方法) ...  
}

## **3\. 前端开发计划 (chatbi-admin-frontend)**

### **3.1. api/knowledge.js (更新)**

// File: src/api/knowledge.js  
// ... (之前的API)  
export const knowledgeApi \= {  
  // ... (术语相关的API)  
  getExamples: (params) \=\> apiClient.get('/knowledge-base/examples', { params }),  
  createExample: (data) \=\> apiClient.post('/knowledge-base/examples', data),  
  updateExample: (id, data) \=\> apiClient.put(\`/knowledge-base/examples/${id}\`, data),  
  deleteExample: (id) \=\> apiClient.delete(\`/knowledge-base/examples/${id}\`),  
  createExamplesInBatch: (data) \=\> apiClient.post('/knowledge-base/examples/batch', data),  
};

### **3.2. views/knowledge/QueryExampleList.vue**

此页面将集成单条、批量、搜索、编辑和删除所有功能。

* **UI 布局与交互**:  
  * **操作区**:  
    * \<a-input-search\>: 用于输入用户问题进行 RAG 搜索。  
    * \<a-dropdown-button\>:  
      * 主按钮“新建范例”，点击后弹出单条记录的创建模态框。  
      * 下拉菜单中提供“批量添加”选项，点击后弹出批量操作的模态框。  
  * **搜索结果展示**:  
    * 搜索结果直接在主表格中展示。  
    * 表格上方出现一个 \<a-alert\> 提示，如“为您找到 X 条关于‘搜索词’的相似范例”。  
    * “用户问题”列对搜索关键词进行高亮。  
  * **批量添加模态框**:  
    * 采用\*\*可编辑表格 (\<a-table\>)\*\*方案。  
    * 表格列包括：用户问题(输入框)、关联数据集(下拉选择)、查询表示(代码输入框)、难度(下拉选择)、操作(删除此行)。  
    * 表格下方有一个“添加一行”按钮。  
    * 模态框页脚有“导入”和“取消”按钮。“导入”按钮会将表格中的所有数据发送到批量创建的 API。  
* **核心代码逻辑 (\<script setup\>)**:  
  \<script setup\>  
  import { ref, reactive, onMounted, watch } from 'vue';  
  // ...

  const searchTerm \= ref('');  
  const exampleList \= ref(\[\]);  
  const isBatchModalVisible \= ref(false);  
  const batchListData \= ref(\[{ id: 1, userQuestion: '', targetDatasetId: null, ... }\]); // 可编辑表格的数据源

  // 搜索逻辑  
  const onSearch \= () \=\> {  
    pagination.current \= 1;  
    fetchData();  
  };

  // 批量操作  
  const handleMenuClick \= ({ key }) \=\> {  
    if (key \=== 'batch') {  
      batchListData.value \= \[{ id: 1 }\]; // 初始化一行  
      isBatchModalVisible.value \= true;  
    }  
  };

  const addBatchRow \= () \=\> {  
    batchListData.value.push({ id: Date.now(), ... });  
  };

  const handleBatchImport \= async () \=\> {  
    // 1\. 前端校验 batchListData  
    // 2\. 调用 knowledgeApi.createExamplesInBatch(batchListData.value)  
    // 3\. 显示操作结果报告  
    // 4\. 关闭模态框并刷新列表  
  };  
  \</script\>

## **4\. 总结**

通过本次设计，我们为“查询范例管理”功能规划了一套完整、高效且用户友好的操作流程。

* **功能完备**: 同时支持单条、批量、搜索、编辑、删除等所有管理场景。  
* **体验优秀**: RAG 语义搜索让查找更智能；可编辑表格让批量录入如同使用 Excel 般便捷。  
* **架构一致**: 与“业务术语管理”功能保持了高度一致的设计哲学和技术实现，降低了开发和维护成本。

这个功能模块的完成，将为我们 AI Agent 的“训练”提供强大的后勤保障。