// =================================================================
// Backend Code
// =================================================================

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/entity/BusinessTerminology.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.entity;

import jakarta.persistence.\*;
import lombok.Data;
import java.util.Date;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "business_terminology")
@Data
public class BusinessTerminology {
@Id
@GeneratedValue(strategy = GenerationType.IDENTITY)
private Long id;

    @Column(nullable = false, unique = true)
    private String businessTerm;

    @Column(nullable = false)
    private String standardReferenceType;

    private String standardReferenceId; // Storing as String to be flexible

    private String standardReferenceName;

    @Lob
    @Column(nullable = false)
    private String contextDescription;

    @UpdateTimestamp
    private Date updatedAt;

}

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/BusinessTerminologyRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.BusinessTerminology;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BusinessTerminologyRepository extends JpaRepository<BusinessTerminology, Long> {}

// -----------------------------------------------------------------
// File: chatbi-knowledge-service/src/main/java/com/qding/chatbi/knowledge/service/KnowledgePersistenceService.java
// -----------------------------------------------------------------
package com.qding.chatbi.knowledge.service;

import com.qding.chatbi.metadata.entity.BusinessTerminology;
import com.qding.chatbi.metadata.repository.BusinessTerminologyRepository;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.model.embedding.EmbeddingModel;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.SearchResults;
import io.milvus.param.R;
import io.milvus.param.dml.DeleteParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.param.dml.UpsertParam;
import io.milvus.response.SearchResultsWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KnowledgePersistenceService {

    private static final String TERM_COLLECTION_NAME = "business_terms";

    @Autowired private BusinessTerminologyRepository termRepository;
    @Autowired private MilvusServiceClient milvusClient;
    @Autowired private EmbeddingModel embeddingModel;

    @Transactional
    public BusinessTerminology saveOrUpdateTerm(BusinessTerminology term) {
        try {
            BusinessTerminology savedTerm = termRepository.save(term);
            Long primaryKey = savedTerm.getId();
            // 使用术语本身和它的描述来生成向量，以获得更丰富的语义
            String textToEmbed = savedTerm.getBusinessTerm() + "\n" + savedTerm.getContextDescription();
            Embedding embedding = embeddingModel.embed(textToEmbed).content();

            List<List<?>> vectors = List.of(embedding.vectorAsList());
            UpsertParam.Field primaryKeyField = new UpsertParam.Field("id", List.of(primaryKey));
            UpsertParam.Field vectorField = new UpsertParam.Field("vector", vectors);

            UpsertParam upsertParam = UpsertParam.newBuilder()
                    .withCollectionName(TERM_COLLECTION_NAME)
                    .withFields(List.of(primaryKeyField, vectorField))
                    .build();
            milvusClient.upsert(upsertParam);
            return savedTerm;
        } catch (Exception e) {
            throw new RuntimeException("保存或更新业务术语失败: " + e.getMessage(), e);
        }
    }

    @Transactional
    public void deleteTerm(Long termId) {
        if (!termRepository.existsById(termId)) {
            return; // 如果不存在，直接返回
        }
        try {
            String deleteExpr = "id in [" + termId + "]";
            DeleteParam deleteParam = DeleteParam.newBuilder()
                    .withCollectionName(TERM_COLLECTION_NAME)
                    .withExpr(deleteExpr)
                    .build();
            milvusClient.delete(deleteParam);
            termRepository.deleteById(termId);
        } catch (Exception e) {
            throw new RuntimeException("删除业务术语失败: " + e.getMessage(), e);
        }
    }

    public List<BusinessTerminology> searchSimilarTerms(String queryText, int topK) {
        Embedding queryEmbedding = embeddingModel.embed(queryText).content();

        List<String> outFields = List.of("id");
        SearchParam searchParam = SearchParam.newBuilder()
                .withCollectionName(TERM_COLLECTION_NAME)
                .withVectors(List.of(queryEmbedding.vectorAsList()))
                .withTopK(topK)
                .withVectorFieldName("vector")
                .withOutFields(outFields)
                .build();

        R<SearchResults> response = milvusClient.search(searchParam);
        if (response.getStatus() != 0 || response.getData() == null) {
            throw new RuntimeException("Failed to search in Milvus: " + response.getMessage());
        }

        SearchResultsWrapper wrapper = new SearchResultsWrapper(response.getData().getResults());
        List<SearchResultsWrapper.IDScore> idScores = wrapper.getIDScore(0);

        if (idScores.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> resultIds = idScores.stream().map(SearchResultsWrapper.IDScore::getLongID).collect(Collectors.toList());
        return termRepository.findAllById(resultIds);
    }

}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/terminology/TerminologyDTO.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.terminology;
import lombok.Data;
import java.util.Date;

@Data
public class TerminologyDTO {
private Long id;
private String businessTerm;
private String standardReferenceType;
private String standardReferenceName;
private String contextDescription;
private Date updatedAt;
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/terminology/CreateOrUpdateTermRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.terminology;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CreateOrUpdateTermRequest {
@NotBlank
private String businessTerm;
@NotBlank
private String standardReferenceType;
private List<Long> standardReferenceIdPath;
@NotBlank
private String contextDescription;
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/BatchOperationReport.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.util.List;

@Data
@AllArgsConstructor
public class BatchOperationReport {
private int successCount;
private int failureCount;
private List<String> errorMessages;
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/KnowledgeBaseAdminService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.BatchOperationReport;
import com.qding.chatbi.admin.vo.terminology.\*;
import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import com.qding.chatbi.metadata.entity.BusinessTerminology;
import com.qding.chatbi.metadata.entity.DatasetColumn;
import com.qding.chatbi.metadata.repository.BusinessTerminologyRepository;
import com.qding.chatbi.metadata.repository.DatasetColumnRepository;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KnowledgeBaseAdminService {

    @Autowired private KnowledgePersistenceService knowledgePersistenceService;
    @Autowired private BusinessTerminologyRepository termRepository;
    @Autowired private DatasetColumnRepository columnRepository;

    public Page<TerminologyDTO> searchTerms(String searchTerm, Pageable pageable) {
        if (searchTerm == null || searchTerm.isBlank()) {
            return termRepository.findAll(pageable).map(this::convertToDto);
        }
        List<BusinessTerminology> results = knowledgePersistenceService.searchSimilarTerms(searchTerm, pageable.getPageSize());
        List<TerminologyDTO> dtoList = results.stream().map(this::convertToDto).collect(Collectors.toList());
        return new PageImpl<>(dtoList, pageable, dtoList.size());
    }

    @Transactional
    public TerminologyDTO createTerm(CreateOrUpdateTermRequest request) {
        BusinessTerminology term = buildTermEntityFromRequest(null, request);
        BusinessTerminology savedTerm = knowledgePersistenceService.saveOrUpdateTerm(term);
        return convertToDto(savedTerm);
    }

    @Transactional
    public TerminologyDTO updateTerm(Long id, CreateOrUpdateTermRequest request) {
        BusinessTerminology term = buildTermEntityFromRequest(id, request);
        BusinessTerminology updatedTerm = knowledgePersistenceService.saveOrUpdateTerm(term);
        return convertToDto(updatedTerm);
    }

    @Transactional
    public void deleteTerm(Long id) {
        knowledgePersistenceService.deleteTerm(id);
    }

    @Transactional
    public BatchOperationReport createTermsInBatch(List<CreateOrUpdateTermRequest> requests) {
        int successCount = 0;
        List<String> errorMessages = new ArrayList<>();
        for (int i = 0; i < requests.size(); i++) {
            CreateOrUpdateTermRequest request = requests.get(i);
            try {
                createTerm(request);
                successCount++;
            } catch (Exception e) {
                errorMessages.add(String.format("第 %d 行 '%s' 创建失败: %s", i + 1, request.getBusinessTerm(), e.getMessage()));
            }
        }
        return new BatchOperationReport(successCount, requests.size() - successCount, errorMessages);
    }

    private BusinessTerminology buildTermEntityFromRequest(Long id, CreateOrUpdateTermRequest request) {
        BusinessTerminology term = id == null ? new BusinessTerminology() : termRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("业务术语", id));

        BeanUtils.copyProperties(request, term);

        if ("DatasetColumn".equals(request.getStandardReferenceType())) {
            List<Long> path = request.getStandardReferenceIdPath();
            if (path == null || path.size() != 2) {
                throw new IllegalArgumentException("映射到字段时，必须提供[数据集ID, 字段ID]");
            }
            Long columnId = path.get(1);
            DatasetColumn column = columnRepository.findById(columnId)
                .orElseThrow(() -> new ResourceNotFoundException("数据集字段", columnId));

            term.setStandardReferenceId(String.valueOf(column.getId()));
            term.setStandardReferenceName(column.getColumnName());
        } else {
            term.setStandardReferenceId(null);
            term.setStandardReferenceName(null);
        }
        return term;
    }

    private TerminologyDTO convertToDto(BusinessTerminology entity) {
        TerminologyDTO dto = new TerminologyDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/KnowledgeBaseAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.KnowledgeBaseAdminService;
import com.qding.chatbi.admin.vo.BatchOperationReport;
import com.qding.chatbi.admin.vo.PagedResponse;
import com.qding.chatbi.admin.vo.terminology._;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation._;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/admin/knowledge-base")
public class KnowledgeBaseAdminController {

    @Autowired
    private KnowledgeBaseAdminService knowledgeBaseAdminService;

    @GetMapping("/terms")
    public ResponseEntity<PagedResponse<TerminologyDTO>> listTerms(
        @RequestParam(required = false) String search,
        Pageable pageable) {
        Page<TerminologyDTO> page = knowledgeBaseAdminService.searchTerms(search, pageable);
        return ResponseEntity.ok(new PagedResponse<>(page.getContent(), page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(), page.isLast()));
    }

    @PostMapping("/terms")
    public ResponseEntity<TerminologyDTO> createTerm(@Valid @RequestBody CreateOrUpdateTermRequest request) {
        return ResponseEntity.ok(knowledgeBaseAdminService.createTerm(request));
    }

    @PostMapping("/terms/batch")
    public ResponseEntity<BatchOperationReport> createTermsInBatch(@Valid @RequestBody List<CreateOrUpdateTermRequest> requests) {
        return ResponseEntity.ok(knowledgeBaseAdminService.createTermsInBatch(requests));
    }

    @PutMapping("/terms/{id}")
    public ResponseEntity<TerminologyDTO> updateTerm(@PathVariable Long id, @Valid @RequestBody CreateOrUpdateTermRequest request) {
        return ResponseEntity.ok(knowledgeBaseAdminService.updateTerm(id, request));
    }

    @DeleteMapping("/terms/{id}")
    public ResponseEntity<Void> deleteTerm(@PathVariable Long id) {
        knowledgeBaseAdminService.deleteTerm(id);
        return ResponseEntity.noContent().build();
    }

}

// =================================================================
// Frontend: chatbi-admin-frontend
// =================================================================

// -----------------------------------------------------------------
// File: src/api/knowledge.js
// -----------------------------------------------------------------
/\*
import apiClient from './index';

export const knowledgeApi = {
getTerms: (params) => apiClient.get('/knowledge-base/terms', { params }),
createTerm: (data) => apiClient.post('/knowledge-base/terms', data),
updateTerm: (id, data) => apiClient.put(`/knowledge-base/terms/${id}`, data),
deleteTerm: (id) => apiClient.delete(`/knowledge-base/terms/${id}`),
createTermsInBatch: (data) => apiClient.post('/knowledge-base/terms/batch', data),
};
\*/

// -----------------------------------------------------------------
// File: src/views/knowledge/TerminologyList.vue
// -----------------------------------------------------------------
/\*
<template>

  <div>
    <a-page-header title="业务术语管理" sub-title="管理AI可理解的业务术语和别名" />
    <a-card>
      <div class="table-operations">
        <a-input-search
          v-model:value="searchTerm"
          placeholder="按语义搜索术语..."
          style="width: 280px"
          @search="onSearch"
          allow-clear
        />
        <a-dropdown-button type="primary" @click="handleAddNew">
          新建术语
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item key="batch">批量添加</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown-button>
      </div>
      <a-alert v-if="searchTerm" :message="`为您找到 ${pagination.total} 条关于"${searchTerm}"的相似范例`" type="info" show-icon class="my-4" />
      <a-table :columns="columns" :data-source="termList" :loading="loading" :pagination="pagination" row-key="id" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'standardReferenceName'"><a-tag v-if="record.standardReferenceName">{{ record.standardReferenceName }}</a-tag></template>
            <template v-if="column.key === 'action'">
                 <a-space><a @click="handleEdit(record)">编辑</a><a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)"><a style="color: red">删除</a></a-popconfirm></a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑术语' : '新建术语'" @ok="handleOk" :confirm-loading="modalLoading" width="600px">
        <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
            <a-form-item label="业务术语" name="businessTerm" :rules="[{ required: true }]"><a-input v-model:value="formState.businessTerm" /></a-form-item>
            <a-form-item label="映射类型" name="standardReferenceType"><a-radio-group v-model:value="formState.standardReferenceType"><a-radio value="DatasetColumn">数据集字段</a-radio><a-radio value="Custom">自定义术语</a-radio></a-radio-group></a-form-item>
            <a-form-item v-if="formState.standardReferenceType === 'DatasetColumn'" label="映射目标字段" name="standardReferenceIdPath" :rules="[{ required: true, message: '请选择一个具体字段'}]"><a-cascader v-model:value="formState.standardReferenceIdPath" :options="datasetColumnOptions" placeholder="选择数据集/字段" /></a-form-item>
            <a-form-item label="描述/定义" name="contextDescription" :rules="[{ required: true }]"><a-textarea v-model:value="formState.contextDescription" :rows="4" /></a-form-item>
        </a-form>
    </a-modal>
    <a-modal v-model:open="isBatchModalVisible" title="批量添加术语" width="80vw" @ok="handleBatchImport" :confirm-loading="batchLoading">
        <a-table :data-source="batchListData" :columns="batchColumns" :pagination="false" row-key="id">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'businessTerm'"><a-input v-model:value="record.businessTerm" /></template>
                <template v-if="column.dataIndex === 'standardReferenceType'"><a-radio-group v-model:value="record.standardReferenceType"><a-radio value="DatasetColumn">字段</a-radio><a-radio value="Custom">概念</a-radio></a-radio-group></template>
                <template v-if="column.dataIndex === 'standardReferenceIdPath'"><a-cascader v-model:value="record.standardReferenceIdPath" :options="datasetColumnOptions" style="width: 200px" /></template>
                <template v-if="column.dataIndex === 'contextDescription'"><a-textarea v-model:value="record.contextDescription" :auto-size="{ minRows: 1, maxRows: 3 }" /></template>
                <template v-if="column.dataIndex === 'action'"><a-button type="link" danger @click="removeBatchRow(record.id)">删除</a-button></template>
            </template>
        </a-table>
        <a-button @click="addBatchRow" type="dashed" class="mt-4 w-full"> + 添加一行 </a-button>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { knowledgeApi } from '../../api/knowledge';
import { message } from 'ant-design-vue';

// Main Table
const columns = [ { title: '业务术语', dataIndex: 'businessTerm' }, { title: '映射目标', key: 'standardReferenceName' }, /* ... other columns */ { title: '操作', key: 'action' } ];
const termList = ref([]);
const loading = ref(false);
const searchTerm = ref('');
const pagination = reactive({ current: 1, pageSize: 10, total: 0 });

// Single Add/Edit Modal
const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({});
const datasetColumnOptions = ref([]); // Data for cascader

// Batch Add Modal
const isBatchModalVisible = ref(false);
const batchLoading = ref(false);
const batchListData = ref([]);
const batchColumns = [
    { title: '业务术语', dataIndex: 'businessTerm' },
    { title: '映射类型', dataIndex: 'standardReferenceType' },
    { title: '映射目标字段', dataIndex: 'standardReferenceIdPath' },
    { title: '描述', dataIndex: 'contextDescription' },
    { title: '操作', dataIndex: 'action' }
];

const fetchData = async () => { /* ... API call logic ... */ };
onMounted(fetchData);

const onSearch = () => { pagination.current = 1; fetchData(); };
watch(searchTerm, (val) => { if (!val) onSearch(); });
const handleTableChange = (p) => { pagination.current = p.current; fetchData(); };

// ... all CRUD and modal handling functions ...

</script>
