// =================================================================
// Backend Code
// =================================================================

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/entity/DatasetAccessPermission.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import java.util.Date;

@Entity
@Table(name = "dataset_access_permissions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"role_id", "dataset_id"})
})
@Data
public class DatasetAccessPermission {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id", nullable = false)
    private QueryableDataset dataset;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private Date createdAt;
}


// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/DatasetAccessPermissionRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.DatasetAccessPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface DatasetAccessPermissionRepository extends JpaRepository<DatasetAccessPermission, Long> {
    
    /**
     * 根据角色ID删除所有权限记录。
     * @param roleId 角色ID
     */
    @Transactional
    void deleteByRoleId(Long roleId);
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/permission/PermissionAssignmentDTO.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.permission;

import com.qding.chatbi.admin.vo.dataset.DatasetSummaryDTO;
import lombok.Data;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class PermissionAssignmentDTO {
    private List<RoleDTO> allRoles;
    private List<DatasetSummaryDTO> allDatasets;
    private Map<Long, Set<Long>> assignments; 
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/permission/UpdateAssignmentsRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.permission;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpdateAssignmentsRequest {
    @NotNull
    private Long roleId;
    
    @NotNull
    private List<Long> datasetIds;
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/PermissionService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.permission.*;
import com.qding.chatbi.admin.vo.dataset.DatasetSummaryDTO;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import com.qding.chatbi.metadata.entity.DatasetAccessPermission;
import com.qding.chatbi.metadata.entity.QueryableDataset;
import com.qding.chatbi.metadata.entity.Role;
import com.qding.chatbi.metadata.repository.DatasetAccessPermissionRepository;
import com.qding.chatbi.metadata.repository.DatasetRepository;
import com.qding.chatbi.metadata.repository.RoleRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class PermissionService {
    
    @Autowired private RoleRepository roleRepository;
    @Autowired private DatasetRepository datasetRepository;
    @Autowired private DatasetAccessPermissionRepository permissionRepository;

    // ... (Role CRUD methods from previous turn) ...

    public PermissionAssignmentDTO getPermissionAssignments() {
        List<Role> allRoles = roleRepository.findAll();
        List<QueryableDataset> allDatasets = datasetRepository.findAll();
        List<DatasetAccessPermission> allPermissions = permissionRepository.findAll();

        Map<Long, Set<Long>> assignments = allPermissions.stream()
            .collect(Collectors.groupingBy(
                p -> p.getRole().getId(),
                Collectors.mapping(p -> p.getDataset().getId(), Collectors.toSet())
            ));
            
        PermissionAssignmentDTO dto = new PermissionAssignmentDTO();
        dto.setAllRoles(allRoles.stream().map(this::convertRoleToDto).collect(Collectors.toList()));
        dto.setAllDatasets(allDatasets.stream().map(this::convertDatasetToDto).collect(Collectors.toList()));
        dto.setAssignments(assignments);
        
        return dto;
    }
    
    @Transactional
    public void updateAssignments(List<UpdateAssignmentsRequest> requests) {
        for (UpdateAssignmentsRequest request : requests) {
            Long roleId = request.getRoleId();
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("角色", roleId));

            permissionRepository.deleteByRoleId(roleId);

            List<DatasetAccessPermission> newPermissions = request.getDatasetIds().stream()
                .map(datasetId -> {
                    QueryableDataset dataset = datasetRepository.findById(datasetId)
                        .orElseThrow(() -> new ResourceNotFoundException("数据集", datasetId));
                    DatasetAccessPermission newPerm = new DatasetAccessPermission();
                    newPerm.setRole(role);
                    newPerm.setDataset(dataset);
                    return newPerm;
                })
                .collect(Collectors.toList());
            
            if (!newPermissions.isEmpty()) {
                permissionRepository.saveAll(newPermissions);
            }
        }
    }
    
    private RoleDTO convertRoleToDto(Role entity) { /* ... */ return new RoleDTO(); }
    private DatasetSummaryDTO convertDatasetToDto(QueryableDataset entity) { /* ... */ return new DatasetSummaryDTO(); }
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/PermissionAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.PermissionService;
import com.qding.chatbi.admin.vo.permission.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/admin/permissions")
public class PermissionAdminController {

    @Autowired
    private PermissionService permissionService;

    // ... (Role CRUD endpoints) ...

    @GetMapping("/assignments")
    public ResponseEntity<PermissionAssignmentDTO> getAssignments() {
        return ResponseEntity.ok(permissionService.getPermissionAssignments());
    }

    @PutMapping("/assignments")
    public ResponseEntity<Void> updateAssignments(@RequestBody List<UpdateAssignmentsRequest> requests) {
        permissionService.updateAssignments(requests);
        return ResponseEntity.noContent().build();
    }
}


// =================================================================
// Frontend Code
// =================================================================

// -----------------------------------------------------------------
// File: src/api/permission.js
// -----------------------------------------------------------------
/*
import apiClient from './index';

export const permissionApi = {
  // ... (Role APIs) ...
  getAssignments: () => apiClient.get('/permissions/assignments'),
  updateAssignments: (data) => apiClient.put('/permissions/assignments', data),
};
*/

// -----------------------------------------------------------------
// File: src/views/permissions/PermissionMatrix.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="权限分配" sub-title="为不同角色分配数据集的访问权限">
      <template #extra>
        <a-button type="primary" :loading="saving" :disabled="!isDirty" @click="handleSave">
          保存更改
        </a-button>
      </template>
    </a-page-header>
    <a-card>
      <a-spin :spinning="loading">
        <a-table
          :columns="columns"
          :data-source="allDatasets"
          :pagination="false"
          row-key="id"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key !== 'datasetName'">
              <div class="text-center">
                 <a-checkbox
                    v-model:checked="permissionState[record.id][column.key]"
                    @change="onPermissionChange"
                 />
              </div>
            </template>
          </template>
        </a-table>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { permissionApi } from '../../api/permission.js';
import { message } from 'ant-design-vue';

const loading = ref(true);
const saving = ref(false);
const isDirty = ref(false);

const allRoles = ref([]);
const allDatasets = ref([]);
const permissionState = reactive({});

// 动态生成表格列
const columns = computed(() => {
  const roleColumns = allRoles.value.map(role => ({
    title: role.roleName,
    key: role.id,
    width: 150,
    align: 'center'
  }));
  return [
    { title: '数据集', dataIndex: 'datasetName', key: 'datasetName', fixed: 'left', width: 250 },
    ...roleColumns
  ];
});

// 加载初始数据
onMounted(async () => {
  try {
    const res = await permissionApi.getAssignments();
    const data = res.data;
    allRoles.value = data.allRoles;
    allDatasets.value = data.allDatasets;
    
    // 初始化权限矩阵状态
    data.allDatasets.forEach(dataset => {
      permissionState[dataset.id] = {};
      data.allRoles.forEach(role => {
        const hasPermission = data.assignments[role.id]?.includes(dataset.id) || false;
        permissionState[dataset.id][role.id] = hasPermission;
      });
    });

  } catch (error) {
    message.error("加载权限数据失败！");
  } finally {
    loading.value = false;
  }
});

// 标记有变动
const onPermissionChange = () => {
  isDirty.value = true;
};

// 保存更改
const handleSave = async () => {
  saving.value = true;
  try {
    // 将前端的 permissionState 转换成后端需要的格式
    const payload = allRoles.value.map(role => {
      const datasetIds = allDatasets.value
        .filter(dataset => permissionState[dataset.id]?.[role.id])
        .map(dataset => dataset.id);
      return {
        roleId: role.id,
        datasetIds: datasetIds
      };
    });

    await permissionApi.updateAssignments(payload);
    message.success("权限保存成功！");
    isDirty.value = false;
  } catch (error) {
    message.error("保存失败，请重试！");
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.text-center {
    text-align: center;
}
</style>
*/
