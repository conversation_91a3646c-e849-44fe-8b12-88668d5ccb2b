# **系统管理 \- 管理员帐号管理功能详细设计**

## **1\. 目标与价值**

**核心目标**: 提供一个安全的、仅限**超级管理员**访问的界面，用于创建、管理和维护所有可以登录 ChatBI 后台系统的操作员账号。

**核心价值**:

* **后台安全基石**: 这是整个后台系统的访问控制中枢，确保只有授权的人员才能进行系统配置。  
* **职责分离**: 通过定义两种后台角色（“超级管理员”和“管理员”），实现了权限的初步分离。  
  * **超级管理员 (SUPER\_ADMIN)**: 拥有后台所有功能模块的访问权限，包括创建和管理其他管理员账号。系统中应至少有一个且不可被删除的超级管理员账号。  
  * **管理员 (ADMIN)**: 拥有除“管理员帐号管理”之外的所有功能权限，负责日常的数据建模、知识库维护和权限分配。

## **2\. 后端开发计划 (chatbi-admin-backend)**

### **2.1. 实体与仓库 (Entity & Repository)**

我们需要一个专门的表来存储后台管理员的账号信息。

// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/entity/AdminUser.java  
package com.qding.chatbi.metadata.entity;

import jakarta.persistence.\*;  
import lombok.Data;  
import java.util.Set;

@Entity  
@Table(name \= "admin\_users")  
@Data  
public class AdminUser {  
    @Id  
    @GeneratedValue(strategy \= GenerationType.IDENTITY)  
    private Long id;

    @Column(nullable \= false, unique \= true)  
    private String username; // 登录名，可以是手机号或自定义字符串

    @Column(nullable \= false)  
    private String password; // 必须存储哈希后的密码

    @Column(nullable \= false)  
    private String name; // 真实姓名

    @ElementCollection(fetch \= FetchType.EAGER) // 将角色列表存储在独立的表中  
    @CollectionTable(name \= "admin\_user\_roles", joinColumns \= @JoinColumn(name \= "user\_id"))  
    @Column(name \= "role")  
    private Set\<String\> roles; // e.g., {"ROLE\_ADMIN", "ROLE\_SUPER\_ADMIN"}

    private boolean enabled \= true;  
      
    // ... createdAt, updatedAt fields ...  
}

// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/AdminUserRepository.java  
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.AdminUser;  
import org.springframework.data.jpa.repository.JpaRepository;  
import java.util.Optional;

public interface AdminUserRepository extends JpaRepository\<AdminUser, Long\> {  
    // Spring Security UserDetailsService 会用到  
    Optional\<AdminUser\> findByUsername(String username);  
}

### **2.2. DTO / VO 定义**

// File: com/qding/chatbi/admin/vo/user/AdminUserDTO.java  
@Data  
public class AdminUserDTO {  
    private Long id;  
    private String username;  
    private String name;  
    private Set\<String\> roles;  
    private boolean enabled;  
    private Date createdAt;  
}

// File: com/qding/chatbi/admin/vo/user/CreateAdminUserRequest.java  
@Data  
public class CreateAdminUserRequest {  
    @NotBlank  
    private String username;  
    @NotBlank @Size(min \= 6\)  
    private String password;  
    @NotBlank  
    private String name;  
    @NotEmpty  
    private Set\<String\> roles; // e.g., {"ROLE\_ADMIN"}  
}

### **2.3. Controller (UserAdminController)**

所有端点都将通过 Spring Security 的 @PreAuthorize 注解进行保护，确保只有超级管理员可以访问。

// File: com/qding/chatbi/admin/controller/UserAdminController.java  
@RestController  
@RequestMapping("/api/admin/users")  
@PreAuthorize("hasRole('SUPER\_ADMIN')")  
public class UserAdminController {

    @Autowired  
    private AdminUserService adminUserService;

    @GetMapping("/")  
    public ResponseEntity\<PagedResponse\<AdminUserDTO\>\> listUsers(Pageable pageable) {  
        // ...  
    }

    @PostMapping("/")  
    public ResponseEntity\<AdminUserDTO\> createUser(@Valid @RequestBody CreateAdminUserRequest request) {  
        // ...  
    }

    @PutMapping("/{id}/status")  
    public ResponseEntity\<Void\> updateUserStatus(@PathVariable Long id, @RequestBody Map\<String, Boolean\> status) {  
        // ...  
    }

    @DeleteMapping("/{id}")  
    public ResponseEntity\<Void\> deleteUser(@PathVariable Long id) {  
        // ...  
    }  
}

### **2.4. Service (AdminUserService & UserDetailsServiceImpl)**

* **AdminUserService**: 负责管理员账号的 CRUD 业务逻辑。  
  * createAdminUser() 方法内部必须使用 PasswordEncoder 对明文密码进行哈希加密后再存入数据库。  
  * deleteUser() 方法需要有逻辑判断，禁止删除系统中最后一个超级管理员或当前登录的超级管理员自己。  
* **UserDetailsServiceImpl**: 创建一个新类实现 Spring Security 的 UserDetailsService 接口，负责在登录时根据用户名从 AdminUserRepository 加载用户信息，这是整合数据库认证的关键。

## **4\. 前端开发计划 (chatbi-admin-frontend)**

### **4.1. api/user.js (新增)**

import apiClient from './index';

export const userApi \= {  
  getUsers: (params) \=\> apiClient.get('/users', { params }),  
  createUser: (data) \=\> apiClient.post('/users', data),  
  updateUserStatus: (id, enabled) \=\> apiClient.put(\`/users/${id}/status\`, { enabled }),  
  deleteUser: (id) \=\> apiClient.delete(\`/users/${id}\`),  
};

### **4.2. views/system/AdminUserList.vue (新增)**

这是管理员账号管理的核心页面。

* **访问控制**:  
  * **菜单**: SideMenu.vue 中，“系统管理” \-\> “管理员设置”这个菜单项将使用 v-if="authStore.userRoles.includes('ROLE\_SUPER\_ADMIN')" 来控制，确保只有超级管理员能看到入口。  
  * **路由**: router/index.js 中，此页面的路由配置 meta 中会标记 roles: \['SUPER\_ADMIN'\]，由路由守卫进行二次防护。  
* **UI 布局**:  
  * 页面顶部是 \<a-page-header\> 和“新建管理员”按钮。  
  * 主体部分是一个 \<a-table\>，用于分页展示所有后台用户。  
* 表格列设计:  
  | 列标题 | 对应DTO字段 | 展示方式 |  
  | :--- | :--- | :--- |  
  | 姓名 | name | 文本 |  
  | 登录账号 | username | 文本 |  
  | 角色 | roles | 使用多个 \<a-tag\> 展示，如 SUPER\_ADMIN, ADMIN |  
  | 状态 | enabled | 一个 \<a-switch\> 开关，直接绑定状态并触发更新 |  
  | 创建时间 | createdAt | 格式化后的日期时间 |  
  | 操作 | \- | “编辑”和“删除”链接 |  
* **新建/编辑模态框 (\<a-modal\>)**:  
  * **新建**: 表单包含“姓名”、“登录账号”、“初始密码”和“角色”（多选框）。  
  * **编辑**: 表单只允许修改“姓名”和“角色”，密码通常不提供直接修改，而是通过“重置密码”功能。  
* **删除操作**:  
  * 点击“删除”链接时，使用 \<a-popconfirm\> 弹出气泡确认框。

## **5\. 总结**

本设计方案为“管理员帐号管理”功能提供了一个安全、完整的前后端实现路径。通过在**菜单、路由、API** 三个层面进行严格的权限控制，确保了此核心功能的安全性。同时，通过直观的列表和表单交互，为超级管理员提供了高效、便捷的用户管理体验。