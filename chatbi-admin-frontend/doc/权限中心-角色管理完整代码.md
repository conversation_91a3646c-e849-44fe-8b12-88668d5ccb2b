// =================================================================
// Backend Code
// =================================================================

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/entity/Role.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import java.util.Date;

@Entity
@Table(name = "roles")
@Data
public class Role {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String roleName;

    @Lob
    private String description;

    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private Date updatedAt;
}

// -----------------------------------------------------------------
// File: chatbi-metadata-service/src/main/java/com/qding/chatbi/metadata/repository/RoleRepository.java
// -----------------------------------------------------------------
package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * 根据角色名称进行不区分大小写的模糊查询，并返回分页结果。
     */
    Page<Role> findByRoleNameContainingIgnoreCase(String roleName, Pageable pageable);
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/permission/RoleDTO.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.permission;

import lombok.Data;
import java.util.Date;

@Data
public class RoleDTO {
    private Long id;
    private String roleName;
    private String description;
    private Date createdAt;
}

// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/permission/CreateOrUpdateRoleRequest.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.vo.permission;

import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class CreateOrUpdateRoleRequest {
    @NotBlank(message = "角色名称不能为空")
    private String roleName;
    
    private String description;
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/service/PermissionService.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.permission.*;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import com.qding.chatbi.metadata.entity.Role;
import com.qding.chatbi.metadata.repository.RoleRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PermissionService {
    
    @Autowired
    private RoleRepository roleRepository;

    public Page<RoleDTO> searchOrListRoles(String searchTerm, Pageable pageable) {
        Page<Role> page;
        if (searchTerm == null || searchTerm.isBlank()) {
            page = roleRepository.findAll(pageable);
        } else {
            page = roleRepository.findByRoleNameContainingIgnoreCase(searchTerm, pageable);
        }
        return page.map(this::convertToDto);
    }

    @Transactional
    public RoleDTO createRole(CreateOrUpdateRoleRequest request) {
        Role role = new Role();
        BeanUtils.copyProperties(request, role);
        return convertToDto(roleRepository.save(role));
    }
    
    @Transactional
    public RoleDTO updateRole(Long id, CreateOrUpdateRoleRequest request) {
        Role role = roleRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("角色", id));
        BeanUtils.copyProperties(request, role);
        return convertToDto(roleRepository.save(role));
    }
    
    @Transactional
    public void deleteRole(Long id) {
        // In a real-world scenario, you should check for associations before deleting.
        if (!roleRepository.existsById(id)) {
            throw new ResourceNotFoundException("角色", id);
        }
        roleRepository.deleteById(id);
    }
    
    private RoleDTO convertToDto(Role entity) {
        RoleDTO dto = new RoleDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}


// -----------------------------------------------------------------
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/PermissionAdminController.java
// -----------------------------------------------------------------
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.PermissionService;
import com.qding.chatbi.admin.vo.PagedResponse;
import com.qding.chatbi.admin.vo.permission.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/admin/permissions")
public class PermissionAdminController {

    @Autowired
    private PermissionService permissionService;

    @GetMapping("/roles")
    public ResponseEntity<PagedResponse<RoleDTO>> listRoles(
        @RequestParam(required = false) String search,
        Pageable pageable) {
        
        Page<RoleDTO> page = permissionService.searchOrListRoles(search, pageable);
        PagedResponse<RoleDTO> response = new PagedResponse<>(page.getContent(), page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(), page.isLast());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/roles")
    public ResponseEntity<RoleDTO> createRole(@Valid @RequestBody CreateOrUpdateRoleRequest request) {
        return ResponseEntity.ok(permissionService.createRole(request));
    }

    @PutMapping("/roles/{id}")
    public ResponseEntity<RoleDTO> updateRole(@PathVariable Long id, @Valid @RequestBody CreateOrUpdateRoleRequest request) {
        return ResponseEntity.ok(permissionService.updateRole(id, request));
    }

    @DeleteMapping("/roles/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        permissionService.deleteRole(id);
        return ResponseEntity.noContent().build();
    }
}


// =================================================================
// Frontend: chatbi-admin-frontend
// =================================================================

// -----------------------------------------------------------------
// File: src/api/permission.js
// -----------------------------------------------------------------
/*
import apiClient from './index';

export const permissionApi = {
  getRoles: (params) => apiClient.get('/permissions/roles', { params }),
  createRole: (data) => apiClient.post('/permissions/roles', data),
  updateRole: (id, data) => apiClient.put(`/permissions/roles/${id}`, data),
  deleteRole: (id) => apiClient.delete(`/permissions/roles/${id}`),
};
*/

// -----------------------------------------------------------------
// File: src/views/permissions/RoleList.vue
// -----------------------------------------------------------------
/*
<template>
  <div>
    <a-page-header title="角色管理" sub-title="创建和管理数据查询角色" />
    <a-card>
      <div class="table-operations">
        <a-input-search
          v-model:value="searchTerm"
          placeholder="按角色名称搜索..."
          style="width: 280px"
          @search="onSearch"
          allow-clear
        />
        <a-button type="primary" @click="handleAddNew">新建角色</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="roleList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'createdAt'">
                {{ new Date(record.createdAt).toLocaleString() }}
            </template>
            <template v-if="column.key === 'action'">
                 <a-space>
                    <a @click="handleEdit(record)">编辑</a>
                    <a-popconfirm
                        title="确定删除这个角色吗?"
                        ok-text="确定"
                        cancel-text="取消"
                        @confirm="handleDelete(record.id)"
                    >
                        <a style="color: red">删除</a>
                    </a-popconfirm>
                </a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑角色' : '新建角色'" @ok="handleOk" :confirm-loading="modalLoading">
        <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
            <a-form-item label="角色名称" name="roleName" :rules="[{ required: true, message: '角色名称不能为空' }]">
                <a-input v-model:value="formState.roleName" placeholder="例如: 华北区销售经理" />
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-textarea v-model:value="formState.description" :rows="4" placeholder="请输入角色的职责描述..." />
            </a-form-item>
        </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { permissionApi } from '../../api/permission.js';
import { message } from 'ant-design-vue';

const columns = [
  { title: '角色名称', dataIndex: 'roleName', key: 'roleName' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
  { title: '操作', key: 'action' },
];

const roleList = ref([]);
const loading = ref(false);
const searchTerm = ref('');
const pagination = reactive({ current: 1, pageSize: 10, total: 0 });

const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({
    id: null,
    roleName: '',
    description: ''
});

const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current - 1,
      size: pagination.pageSize,
      search: searchTerm.value,
    };
    const res = await permissionApi.getRoles(params);
    roleList.value = res.data.content;
    pagination.total = res.data.totalElements;
  } catch (error) {
    console.error("获取角色列表失败:", error);
    message.error("加载数据失败！");
  } finally {
    loading.value = false;
  }
};

onMounted(fetchData);

const onSearch = () => {
  pagination.current = 1;
  fetchData();
};

watch(searchTerm, (val) => {
    if (!val) {
        onSearch();
    }
});

const handleTableChange = (p) => {
  pagination.current = p.current;
  pagination.pageSize = p.pageSize;
  fetchData();
};

const handleAddNew = () => {
    isEditing.value = false;
    Object.assign(formState, { id: null, roleName: '', description: '' });
    isModalVisible.value = true;
};

const handleEdit = (record) => {
    isEditing.value = true;
    Object.assign(formState, record);
    isModalVisible.value = true;
};

const handleDelete = async (id) => {
    try {
        await permissionApi.deleteRole(id);
        message.success('删除成功');
        fetchData();
    } catch (error) {
        console.error("删除失败:", error);
    }
};

const handleOk = async () => {
    try {
        await formRef.value.validate();
        modalLoading.value = true;
        if (isEditing.value) {
            await permissionApi.updateRole(formState.id, formState);
            message.success('更新成功');
        } else {
            await permissionApi.createRole(formState);
            message.success('创建成功');
        }
        isModalVisible.value = false;
        fetchData();
    } catch (error) {
        console.error('表单提交失败:', error);
    } finally {
        modalLoading.value = false;
    }
};
</script>

<style scoped>
.table-operations {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
</style>
*/
