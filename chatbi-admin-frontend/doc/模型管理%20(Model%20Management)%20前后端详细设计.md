# **模型管理 (Model Management) 前后端详细设计**

## **1\. 概述**

“模型管理”是 ChatBI 后台的核心模块，它赋予了管理员将原始业务数据“翻译”成 AI 可理解的结构化知识的能力。本模块主要包含两大功能：

1. **数据源管理**: 配置并管理系统可以连接的所有外部数据库。  
2. **数据集管理**: 在已连接的数据源基础上，定义供 AI 查询的逻辑“数据集”，并对数据集的字段进行语义化增强。

本设计将详细阐述实现这两个功能所需的前后端代码。

## **2\. 后端设计 (chatbi-admin-backend)**

### **2.1. DTO / VO 定义 (在 chatbi-common 或 admin 模块的 vo 包中)**

这些类是前后端交互的数据契约。

// \--- 通用分页响应 DTO (新增) \---  
@Data  
public class PagedResponse\<T\> {  
    private List\<T\> content;      // 当前页的数据列表  
    private int pageNumber;     // 当前页码 (从 0 开始)  
    private int pageSize;       // 每页数量  
    private long totalElements; // 总记录数  
    private int totalPages;     // 总页数  
    private boolean isLast;       // 是否为最后一页  
}

// \--- 数据源相关的 DTOs \---

// CreateDataSourceRequest.java  
@Data  
public class CreateDataSourceRequest {  
    @NotBlank  
    private String sourceName;  
    @NotNull  
    private DatabaseType databaseType; // e.g., MYSQL, POSTGRESQL  
    @NotBlank  
    private String host;  
    @NotNull  
    private Integer port;  
    @NotBlank  
    private String username;  
    private String password; // 密码应在传输和存储时加密  
    @NotBlank  
    private String databaseName;  
    private String description;  
}

// DataSourceDTO.java (用于列表和详情返回)  
@Data  
public class DataSourceDTO {  
    private Long id;  
    private String sourceName;  
    private DatabaseType databaseType;  
    private String host;  
    private Integer port;  
    private String databaseName;  
    private String description;  
    private String status; // e.g., "CONNECTED", "FAILED"  
    private Date createdAt;  
}

// \--- 数据集相关的 DTOs \---

// DatasetSummaryDTO.java (用于列表页)  
@Data  
public class DatasetSummaryDTO {  
    private Long id;  
    private String datasetName;  
    private String sourceName; // 所属数据源名称  
    private BaseObjectType baseObjectType; // TABLE or QUERY  
    private String status;  
    private Date updatedAt;  
}

// DatasetDetailDTO.java (用于配置页)  
@Data  
public class DatasetDetailDTO extends DatasetSummaryDTO {  
    private String description;  
    private Long dataSourceId;  
    private String technicalDefinition; // 对于QUERY类型，存储SQL  
    private List\<ColumnConfigDTO\> columns;  
}

// ColumnConfigDTO.java  
@Data  
public class ColumnConfigDTO {  
    private Long id;  
    private String columnName;  
    private String description;  
    private String technicalNameOrExpression;  
    private SemanticType semanticType; // METRIC or DIMENSION  
    private String dataType; // e.g., "STRING", "NUMBER"  
    private List\<String\> synonyms;  
    private boolean isFilterable;  
    private boolean isGroupable;  
}

### **2.2. Service 层设计**

在 chatbi-admin-backend 模块中，创建一个 ModelManagementService 来统一处理模型管理的业务逻辑。

// package com.qding.chatbi.admin.service;

@Service  
public class ModelManagementService {

    // 假设注入了下游服务的Repository  
    // private final DataSourceRepository dataSourceRepo;  
    // private final DatasetRepository datasetRepo;

    // \--- 数据源管理逻辑 (已更新) \---  
    // 使用 Spring Data 的 Pageable 对象处理分页和排序  
    public Page\<DataSourceDTO\> getAllDataSources(Pageable pageable) { /\* ... \*/ }  
    public DataSourceDTO createDataSource(CreateDataSourceRequest request) { /\* ... \*/ }  
    public boolean testConnection(CreateDataSourceRequest request) { /\* ... \*/ }  
    // ... 其他CRUD方法

    // \--- 数据集管理逻辑 (已更新) \---  
    public Page\<DatasetSummaryDTO\> getAllDatasets(Pageable pageable) { /\* ... \*/ }  
    public DatasetDetailDTO getDatasetDetails(Long id) { /\* ... \*/ }  
    public DatasetDetailDTO createDataset(CreateDatasetRequest request) { /\* ... \*/ }  
    public List\<ColumnConfigDTO\> syncColumnsFromSource(Long datasetId) { /\* ... \*/ }  
    public ColumnConfigDTO updateColumnConfig(Long columnId, UpdateColumnRequest request) { /\* ... \*/ }  
    // ... 其他CRUD方法  
}

### **2.3. Controller 层设计**

实现设计文档中定义的 DataSourceAdminController 和 DatasetAdminController。

// package com.qding.chatbi.admin.controller;

@RestController  
@RequestMapping("/api/admin/data-sources")  
public class DataSourceAdminController {

    @Autowired  
    private ModelManagementService modelService;

    // 【变更】方法签名更新，接收Pageable参数，返回PagedResponse  
    @GetMapping("/")  
    public ResponseEntity\<PagedResponse\<DataSourceDTO\>\> listDataSources(  
        @PageableDefault(size \= 20, sort \= "createdAt", direction \= Sort.Direction.DESC) Pageable pageable) {  
          
        Page\<DataSourceDTO\> page \= modelService.getAllDataSources(pageable);  
        // 将 Spring Data 的 Page 对象转换为自定义的 PagedResponse 对象  
        PagedResponse\<DataSourceDTO\> response \= new PagedResponse\<\>(  
            page.getContent(),  
            page.getNumber(),  
            page.getSize(),  
            page.getTotalElements(),  
            page.getTotalPages(),  
            page.isLast()  
        );  
        return ResponseEntity.ok(response);  
    }

    @PostMapping("/")  
    public ResponseEntity\<DataSourceDTO\> createDataSource(@Valid @RequestBody CreateDataSourceRequest request) {  
        return ResponseEntity.ok(modelService.createDataSource(request));  
    }  
      
    // ... 其他端点  
}

@RestController  
@RequestMapping("/api/admin/datasets")  
public class DatasetAdminController {

    @Autowired  
    private ModelManagementService modelService;

    // 【变更】方法签名更新，接收Pageable参数，返回PagedResponse  
    @GetMapping("/")  
    public ResponseEntity\<PagedResponse\<DatasetSummaryDTO\>\> listDatasets(  
        @PageableDefault(size \= 20, sort \= "updatedAt", direction \= Sort.Direction.DESC) Pageable pageable) {  
          
        Page\<DatasetSummaryDTO\> page \= modelService.getAllDatasets(pageable);  
        PagedResponse\<DatasetSummaryDTO\> response \= new PagedResponse\<\>(  
            page.getContent(), page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(), page.isLast()  
        );  
        return ResponseEntity.ok(response);  
    }

    @GetMapping("/{id}")  
    public ResponseEntity\<DatasetDetailDTO\> getDataset(@PathVariable Long id) {  
        return ResponseEntity.ok(modelService.getDatasetDetails(id));  
    }

    // ... 其他端点  
}

## **3\. 前端设计 (chatbi-admin-frontend)**

### **3.1. API 服务层 (src/api/model.js)**

封装所有与模型管理相关的后端 API 调用。

import apiClient from './index';

export const modelApi \= {  
  // 【变更】数据源API，现在接收分页参数  
  getDataSources: (params) \=\> apiClient.get('/data-sources/', { params }), // params: { page: 0, size: 20 }  
  createDataSource: (data) \=\> apiClient.post('/data-sources/', data),  
  testConnection: (data) \=\> apiClient.post('/data-sources/test-connection', data),  
  updateDataSource: (id, data) \=\> apiClient.put(\`/data-sources/${id}\`, data),  
  deleteDataSource: (id) \=\> apiClient.delete(\`/data-sources/${id}\`),

  // 【变更】数据集API，现在接收分页参数  
  getDatasets: (params) \=\> apiClient.get('/datasets/', { params }),  
  getDatasetDetails: (id) \=\> apiClient.get(\`/datasets/${id}\`),  
  // ... 其他数据集相关API  
};

### **3.2. Vue 组件设计**

#### **DataSourceList.vue \- 数据源管理页面**

* 列表字段展示建议 (已更新)  
  | 字段 (Table Column) | 对应 DTO 字段 | 为什么建议展示 | 备注 |  
  | :--- | :--- | :--- | :--- |  
  | 数据源名称 | sourceName | 核心标识。这是管理员识别数据源最直接的方式。 | 应作为主列，通常放在最左侧。 |  
  | 数据库类型 | databaseType | 重要分类。管理员需要知道这是 MySQL、Hive 还是其他类型的数据库。 | 建议使用 Ant Design Vue 的 \<a-tag\> 组件进行美化，不同类型显示不同颜色。 |  
  | 连接信息 | host, port | 定位关键。用于快速确认连接的是哪个服务器。 | 可以将 host 和 port 合并为一列显示，如 your\_host:3306。 |  
  | 状态 | status | 健康指标。管理员最关心的是这个数据源是否能正常连接。 | 同样建议使用 \<a-tag\>，并用不同颜色（如绿色表示“已连接”，红色表示“失败”）来突出显示。 |  
  | 创建时间 | createdAt | 追溯信息。了解数据源的创建历史。 | |  
  | 操作 | \- | 核心功能入口。 | 固定包含“编辑”、“测试连接”和“删除”功能。 |

#### **DatasetList.vue & DatasetConfig.vue \- 数据集管理**

* **DatasetList.vue**  
  * 列表字段展示建议 (已更新)  
    | 字段 (Table Column) | 对应 DTO 字段 | 为什么建议展示 | 备注 |  
    | :--- | :--- | :--- | :--- |  
    | 数据集名称 | datasetName | 核心标识。这是最主要的识别字段。 | 应作为主列，通常放在最左侧。 |  
    | 所属数据源 | sourceName | 来源追溯。明确该数据集的数据来自哪个数据库。 | |  
    | 类型 | baseObjectType | 技术分类。让管理员知道这是基于物理表、视图还是自定义SQL。 | 建议使用 \<a-tag\> 组件，如 TABLE, VIEW, QUERY。 |  
    | 状态 | status | 可用性指标。显示数据集是否处于激活、停用等状态。 | 建议使用 \<a-tag\> 组件并用颜色区分。 |  
    | 最后更新时间 | updatedAt | 时效性判断。了解该数据集的配置最近是否发生过变更。 | 比创建时间更能反映配置的活跃度。 |  
    | 操作 | \- | 核心功能入口。 | 固定包含“配置”（进入字段管理）、“编辑”和“删除”。 |  
* **DatasetConfig.vue**  
  * **Template (字段配置 Tab)**:  
    * 字段列表展示建议 (已更新)  
      | 字段 (Table Column) | 对应 DTO 字段 | 为什么建议展示 | 备注 |  
      | :--- | :--- | :--- | :--- |  
      | 业务名称 | columnName | 核心标识。管理员主要通过它来理解字段含义。 | 应作为主列。 |  
      | 技术定义 | technicalNameOrExpression | 技术映射。必须清晰展示字段的物理来源或计算逻辑。 | 可以使用等宽字体以优化 SQL 表达式的可读性。 |  
      | 语义类型 | semanticType | AI决策关键。决定了 AI 如何使用该字段（聚合或分组）。 | 使用 \<a-tag\> 并用颜色区分，如蓝色代表 维度，橙色代表 指标。 |  
      | 数据类型 | dataType | 基础信息。告知管理员字段的基础类型。 | |  
      | 同义词 | synonyms | 丰富AI知识。直观展示该字段有哪些别名。 | 使用多个 \<a-tag\> 来展示。若同义词过多，可用 Tooltip 展示。 |  
      | 关键属性 | isFilterable, isGroupable | 行为控制。直观展示该字段是否可用于筛选和分组。 | 不用文字 true/false，而是用图标（如 FilterOutlined, GroupOutlined）来表示，更直观。 |  
      | 描述 | description | 业务口径。提供详细的业务解释。 | 因描述可能很长，默认只显示摘要或一个信息图标(InfoCircleOutlined)，鼠标悬浮时用 \<a-tooltip\> 或 \<a-popover\> 显示完整内容。 |  
      | 操作 | \- | 核心功能入口。 | 固定包含“编辑”按钮，点击后弹出模态框进行详细配置。 |

## **4\. 创建与修改流程设计 (新增)**

为了提供流畅的用户体验，所有数据项的创建和编辑操作都将通过**模态框（Modal Dialog）** 在当前页面完成，避免不必要的页面跳转。

### **4.1. 通用交互模式**

1. **触发**: 用户在列表页点击“新建”按钮，或点击某行数据后的“编辑”按钮。  
2. **展示**: 一个 \<a-modal\> 弹窗出现，标题为“新建\[数据项\]”或“编辑\[数据项\]”。  
3. **表单**: 模态框内部是一个 \<a-form\> 组件，包含了该数据项所有可配置的字段。  
   * **新建模式**: 表单为空。  
   * **编辑模式**: 表单预先填充了该行数据。  
4. **操作**:  
   * **确定**: 用户点击“确定”按钮，前端首先进行表单校验。校验通过后，调用对应的 API (create 或 update)。请求成功后，关闭模态框，并**刷新列表页的数据**以展示最新结果。  
   * **取消**: 用户点击“取消”按钮或模态框右上角的关闭图标，直接关闭模态框，不进行任何操作。

### **4.2. 各数据项的表单设计**

#### **数据源 (DataSource) 表单**

* **表单字段**:  
  * 数据源名称 (sourceName): \<a-input\>，必填。  
  * 数据库类型 (databaseType): \<a-select\>，选项来自 DatabaseType 枚举，必填。  
  * 主机地址 (host): \<a-input\>，必填。  
  * 端口 (port): \<a-input-number\>，必填。  
  * 数据库名称 (databaseName): \<a-input\>，必填。  
  * 用户名 (username): \<a-input\>，必填。  
  * 密码 (password): \<a-input-password\>，非必填（允许留空或后续配置）。  
  * 描述 (description): \<a-textarea\>。  
* **特殊操作**: 表单底部提供一个“**测试连接**”按钮，点击后将当前表单中的连接信息发送到后端的 /test-connection 接口，并用 message.success 或 message.error 提示用户连接结果。

#### **数据集 (Dataset) 创建表单 (多步骤)**

由于数据集的创建涉及多个步骤，建议在模态框中使用 \<a-steps\> 组件来引导用户。

* **步骤一：基本信息**  
  * 数据集名称 (datasetName): \<a-input\>，必填。  
  * 描述 (description): \<a-textarea\>。  
* **步骤二：选择数据源**  
  * 从一个 \<a-select\> 下拉框中选择一个已创建的数据源（列表从 /api/admin/data-sources 获取）。  
* **步骤三：定义来源**  
  * 提供一个 \<a-radio-group\> 让用户选择“**基于物理表/视图**”或“**自定义SQL**”。  
  * 如果选择“物理表/视图”，则显示另一个 \<a-select\>，其选项是根据上一步选择的数据源动态从后端拉取的所有表和视图列表。  
  * 如果选择“自定义SQL”，则显示一个支持 SQL 语法高亮的文本域 (\<a-textarea\>)，让用户输入查询语句。  
* **完成**: 用户完成所有步骤后，点击“确定”按钮，将整合后的数据发送到后端。

#### **字段 (Column) 配置表单**

这个表单在 DatasetConfig.vue 页面中，当用户点击字段列表中的“编辑”按钮时弹出。

* **表单字段**:  
  * 业务名称 (columnName): \<a-input\>，必填。  
  * 描述 (description): \<a-textarea\>，详细解释字段的业务口径。  
  * 语义类型 (semanticType): \<a-select\>，选项为“维度”和“指标”，必填。  
  * 同义词 (synonyms): \<a-select mode="tags"\>，允许管理员输入多个别名。  
  * 关键属性:  
    * 可用于筛选 (isFilterable): \<a-switch\>。  
    * 可用于分组 (isGroupable): \<a-switch\>。

## **5\. 总结**

通过上述设计，我们不仅定义了需要管理的数据项，还详细规划了如何通过一个统一且高效的“模态框表单”交互模式来完成它们的创建和编辑。多步骤向导的设计也使得复杂如“数据集创建”的操作变得清晰易懂。这个设计为项目的顺利开发奠定了坚实的基础。