<template>
  <a-layout-sider v-model:collapsed="collapsed" collapsible>
    <div class="logo">
      <img src="/logo.svg" alt="logo" />
      <h1 v-if="!collapsed">ChatBI</h1>
    </div>
    <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline">
      <a-menu-item key="/dashboard">
        <router-link to="/">
          <PieChartOutlined />
          <span>总览</span>
        </router-link>
      </a-menu-item>
      <a-sub-menu key="model">
        <template #title>
          <span>
            <DatabaseOutlined />
            <span>模型管理</span>
          </span>
        </template>
        <a-menu-item key="/model/data-sources"><router-link to="/model/data-sources">数据源管理</router-link></a-menu-item>
        <a-menu-item key="/model/datasets"><router-link to="/model/datasets">数据集管理</router-link></a-menu-item>
      </a-sub-menu>
      <a-sub-menu key="knowledge">
        <template #title>
          <span>
            <BookOutlined />
            <span>知识库</span>
          </span>
        </template>
        <a-menu-item key="/knowledge/terminology"><router-link to="/knowledge/terminology">业务术语</router-link></a-menu-item>
        <a-menu-item key="/knowledge/examples"><router-link to="/knowledge/examples">查询范例</router-link></a-menu-item>
      </a-sub-menu>
      <a-sub-menu key="permissions">
        <template #title>
          <span>
            <PropertySafetyOutlined />
            <span>权限中心</span>
          </span>
        </template>
        <a-menu-item key="/permissions/roles"><router-link to="/permissions/roles">角色管理</router-link></a-menu-item>
        <a-menu-item key="/permissions/assignments"><router-link to="/permissions/assignments">权限分配</router-link></a-menu-item>
      </a-sub-menu>
      <a-sub-menu key="system" v-if="isSuperAdmin">
        <template #title>
          <span>
            <SettingOutlined />
            <span>系统管理</span>
          </span>
        </template>
        <a-menu-item key="/system/admin-users"><router-link to="/system/admin-users">管理员设置</router-link></a-menu-item>
      </a-sub-menu>
    </a-menu>
  </a-layout-sider>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '../../store/auth';
import {
  PieChartOutlined,
  DatabaseOutlined,
  BookOutlined,
  PropertySafetyOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';

const collapsed = ref(false);
const selectedKeys = ref(['']);
const route = useRoute();
const authStore = useAuthStore();

const isSuperAdmin = computed(() => authStore.userRoles.includes('SUPER_ADMIN'));

watch(
  () => route.path,
  (path) => {
    selectedKeys.value = [path === '/' ? '/dashboard' : path];
  },
  { immediate: true }
);
</script>

<style scoped>
.logo {
  height: 32px;
  margin: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}
.logo img {
  height: 100%;
}
.logo h1 {
  color: white;
  font-size: 20px;
  margin-left: 8px;
  font-weight: 600;
  white-space: nowrap;
}
</style> 