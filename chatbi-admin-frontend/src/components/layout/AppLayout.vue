<template>
  <a-layout style="min-height: 100vh">
    <SideMenu />
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0 16px; display: flex; justify-content: space-between; align-items: center;">
        <h1 class="text-lg font-semibold">{{ $route.meta.title }}</h1>
        <div class="user-info">
          <a-avatar style="background-color: #1890ff">
            <template #icon><UserOutlined /></template>
          </a-avatar>
          <span class="ml-2 mr-4">欢迎, {{ authStore.user?.name || '管理员' }}</span>
          <a-button type="link" @click="handleLogout">登出</a-button>
        </div>
      </a-layout-header>
      <a-layout-content style="margin: 24px 16px; padding: 24px; background: #fff; min-height: 280px;">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { useRouter } from 'vue-router';
import SideMenu from './SideMenu.vue';
import { useAuthStore } from '../../store/auth';
import { UserOutlined } from '@ant-design/icons-vue';

const router = useRouter();
const authStore = useAuthStore();

const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};
</script> 