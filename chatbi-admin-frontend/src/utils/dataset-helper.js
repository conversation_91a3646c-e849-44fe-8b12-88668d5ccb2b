export const getTypeName = (type) => {
    const names = {
        TABLE: '数据表',
        VIEW: '视图',
        QUERY: '自定义SQL'
    };
    return names[type] || type;
};

export const getTypeColor = (type) => {
    const colors = {
        TABLE: 'blue',
        VIEW: 'green',
        QUERY: 'orange'
    };
    return colors[type] || 'default';
};

export const getSemanticTypeName = (type) => {
    const names = {
        'METRIC': '指标',
        'DIMENSION': '普通维度',
        'TIME_DIMENSION': '时间维度',
        'GEO_DIMENSION': '地理维度'
    };
    return names[type] || '未知';
};

export const getSemanticTypeColor = (type) => {
    const colors = {
        'METRIC': 'orange',
        'DIMENSION': 'blue',
        'TIME_DIMENSION': 'green',
        'GEO_DIMENSION': 'purple'
    };
    return colors[type] || 'gray';
}; 