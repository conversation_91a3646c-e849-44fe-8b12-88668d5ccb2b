import apiClient from './index';

export const modelApi = {
  // 数据源API
  getDataSources: (params) => apiClient.get('/api/admin/data-sources', { params }),
  createDataSource: (data) => apiClient.post('/api/admin/data-sources', data),
  testConnection: (data) => apiClient.post('/api/admin/data-sources/test-connection', data),
  updateDataSource: (id, data) => apiClient.put(`/api/admin/data-sources/${id}`, data),
  deleteDataSource: (id) => apiClient.delete(`/api/admin/data-sources/${id}`),

  // 数据集API
  getDatasets: (params) => apiClient.get('/api/admin/datasets', { params }),
  getDatasetDetails: (id) => apiClient.get(`/api/admin/datasets/${id}`, {
    headers: {
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    }
  }),
  createDataset: (data) => apiClient.post('/api/admin/datasets', data),
  updateDataset: (id, data) => apiClient.put(`/api/admin/datasets/${id}`, data),
  deleteDataset: (id) => apiClient.delete(`/api/admin/datasets/${id}`),
  syncColumns: (id) => apiClient.post(`/api/admin/datasets/${id}/columns/sync`),
  updateColumn: (columnId, data) => apiClient.put(`/api/admin/datasets/columns/${columnId}`, data),
  
  // 【新增】检查数据集名称是否存在
  checkDatasetName: (name, excludeId = null) => {
    const params = { name };
    if (excludeId) {
      params.excludeId = excludeId;
    }
    return apiClient.get('/api/admin/datasets/check-name', { params });
  },
  
  // 【新增】计算指标相关API
  createComputedColumn: (id, data) => apiClient.post(`/api/admin/datasets/${id}/columns/computed`, data),
  validateExpression: (id, data) => apiClient.post(`/api/admin/datasets/${id}/columns/validate`, data),
  
  // 【新增】预览数据源字段信息
  previewColumns: (data) => apiClient.post('/api/admin/datasets/preview-columns', data),

  // 【新增】更新数据集的物理字段
  updatePhysicalColumns: (id, data) => apiClient.put(`/api/admin/datasets/${id}/physical-columns`, data),
  deleteColumn: (id) => apiClient.delete(`/api/admin/datasets/columns/${id}`),

  // 获取数据库表列表
  getTables: (dataSourceId) => apiClient.get(`/api/admin/data-sources/${dataSourceId}/tables`),
}; 