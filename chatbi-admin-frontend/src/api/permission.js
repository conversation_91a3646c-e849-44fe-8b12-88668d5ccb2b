import apiClient from './index';

export const permissionApi = {
  // 角色管理API
  getRoles: (params) => apiClient.get('/api/admin/permissions/roles', { params }),
  createRole: (data) => apiClient.post('/api/admin/permissions/roles', data),
  updateRole: (id, data) => apiClient.put(`/api/admin/permissions/roles/${id}`, data),
  deleteRole: (id) => apiClient.delete(`/api/admin/permissions/roles/${id}`),
  
  // --- 新的、以数据集为中心的权限分配API ---
  getDatasetPermissions: () => apiClient.get('/api/admin/permissions/datasets-permissions'),
  updateDatasetRoles: (datasetId, data) => apiClient.put(`/api/admin/permissions/datasets-permissions/${datasetId}/roles`, data),

  // --- 旧的权限分配API (保留以兼容) ---
  getAssignments: () => apiClient.get('/api/admin/permissions/assignments'),
  updateAssignments: (data) => apiClient.put('/api/admin/permissions/assignments', data),
}; 