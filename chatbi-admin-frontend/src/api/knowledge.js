import apiClient from './index';

export const knowledgeApi = {
  // 【变更】getTerms现在可以接受一个包含search和分页参数的对象
  getTerms: (params) => apiClient.get('/api/admin/knowledge-base/terms', { params }),
  createTerm: (data) => apiClient.post('/api/admin/knowledge-base/terms', data),
  updateTerm: (id, data) => apiClient.put(`/api/admin/knowledge-base/terms/${id}`, data),
  deleteTerm: (id) => apiClient.delete(`/api/admin/knowledge-base/terms/${id}`),
  createTermsInBatch: (data) => apiClient.post('/api/admin/knowledge-base/terms/batch', data),

  // Query Example APIs
  getExamples: (params) => apiClient.get('/api/admin/knowledge-base/examples', { params }),
  createExample: (data) => apiClient.post('/api/admin/knowledge-base/examples', data),
  updateExample: (id, data) => apiClient.put(`/api/admin/knowledge-base/examples/${id}`, data),
  deleteExample: (id) => apiClient.delete(`/api/admin/knowledge-base/examples/${id}`),
  createExamplesInBatch: (data) => apiClient.post('/api/admin/knowledge-base/examples/batch', data),
}; 