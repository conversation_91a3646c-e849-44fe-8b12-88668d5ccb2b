import axios from 'axios';
import { useAuthStore } from '../store/auth';
import { message } from 'ant-design-vue';
import router from '../router'; // 假设 router 实例从 ../router/index.js 导出

const apiClient = axios.create({
  baseURL: 'http://127.0.0.1:8081', // 8081修正为后端服务的正确地址
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// 请求拦截器 - 添加JWT Token
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response, // 返回完整的response对象
  (error) => {
    const authStore = useAuthStore(); // 在外部获取，避免在if/else中重复

    if (error.response?.status === 401) {
      message.error(error.response?.data?.message || '会话已过期或需要认证，请重新登录。');
      authStore.logout(); // 清理认证状态
      router.push('/login'); // 跳转到登录页
    } else if (error.response?.status === 403) {
      message.error(error.response?.data?.message || '您没有权限执行此操作。');
      // 对于403错误，不执行登出或跳转
    } else {
      // 其他所有错误
      message.error(error.response?.data?.message || '请求发生错误');
    }
    return Promise.reject(error);
  }
);

export default apiClient;