import apiClient from './index';

export const userApi = {
  getUsers: (params) => apiClient.get('/api/admin/users/', { params }),
  createUser: (data) => apiClient.post('/api/admin/users/', data),
  updateUser: (id, data) => apiClient.put(`/api/admin/users/${id}`, data),
  updateUserStatus: (id, enabled) => apiClient.patch(`/api/admin/users/${id}/status`, { status: enabled ? 'active' : 'inactive' }),
  deleteUser: (id) => apiClient.delete(`/api/admin/users/${id}`),
}; 