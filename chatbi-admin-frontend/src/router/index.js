// =================================================================
// 文件: chatbi-admin-frontend/src/router/index.js
// 描述: (已更新) Vue Router 的路由配置文件。
// =================================================================
import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '../components/layout/AppLayout.vue'
import { useAuthStore } from '../store/auth';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/LoginPage.vue')
  },
  {
    path: '/',
    component: AppLayout,
    children: [
      {
        path: '', // 路径为空，作为'/'的默认子路由
        name: 'Dashboard',
        component: () => import('../views/dashboard/DashboardPage.vue'),
        meta: { title: '总览', requiresAuth: true }
      },
      // 模型管理
      {
        path: 'model/data-sources',
        name: 'DataSources',
        component: () => import('../views/model/DataSourceList.vue'),
        meta: { title: '数据源管理', requiresAuth: true }
      },
      {
        path: 'model/datasets',
        name: 'Datasets',
        component: () => import('../views/model/DatasetList.vue'),
        meta: { title: '数据集管理', requiresAuth: true }
      },
      {
        path: 'model/datasets/:id/config',
        name: 'DatasetConfig',
        component: () => import('../views/model/DatasetConfig.vue'),
        meta: { title: '数据集配置', requiresAuth: true }
      },
      {
        path: 'model/datasets/:id/edit',
        name: 'DatasetEdit',
        component: () => import('../views/model/DatasetEditPage.vue'),
        meta: { title: '编辑数据集', requiresAuth: true }
      },
      // 知识库
      {
        path: 'knowledge/terminology',
        name: 'Terminology',
        component: () => import('../views/knowledge/TerminologyList.vue'),
        meta: { title: '业务术语', requiresAuth: true }
      },
      {
        path: 'knowledge/examples',
        name: 'QueryExamples',
        component: () => import('../views/knowledge/QueryExampleList.vue'),
        meta: { title: '查询范例', requiresAuth: true }
      },
      // 权限中心
      {
        path: 'permissions/roles',
        name: 'Roles',
        component: () => import('@/views/permissions/RoleList.vue'),
        meta: { title: '角色管理', requiresAuth: true, roles: ['SUPER_ADMIN', 'ADMIN'] }
      },
      {
        path: 'permissions/assignments',
        name: 'Assignments',
        component: () => import('@/views/permissions/PermissionMatrix.vue'),
        meta: { title: '权限分配', requiresAuth: true }
      },
      // 系统管理
      {
        path: 'system/admin-users',
        name: 'SystemAdminUsers',
        component: () => import('@/views/system/AdminUserList.vue'),
        meta: { title: '管理员设置', requiresAuth: true, roles: ['SUPER_ADMIN'] }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const isAuthenticated = authStore.isAuthenticated;
  
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  if (to.name === 'Login') {
    if (isAuthenticated) {
      next({ name: 'Dashboard' });
    } else {
      next();
    }
  } 
  else if (requiresAuth && !isAuthenticated) {
    next({ name: 'Login' });
  } 
  else if (to.meta.roles && !to.meta.roles.some(role => authStore.userRoles.includes(role))) {
    alert('您没有权限访问此页面！');
    next({ name: 'Dashboard' });
  }
  else {
    next();
  }
});

export default router;
