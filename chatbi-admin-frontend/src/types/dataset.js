/**
 * 数据集相关类型定义
 */

// 基础对象类型枚举
export const BaseObjectType = {
  TABLE: 'TABLE',
  VIEW: 'VIEW', 
  QUERY: 'QUERY'
};

// 语义类型枚举
export const SemanticType = {
  METRIC: 'METRIC',     // 指标
  DIMENSION: 'DIMENSION' // 维度
};

// 数据集状态枚举
export const DatasetStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

/**
 * 创建数据集请求
 */
export class CreateDatasetRequest {
  constructor() {
    this.datasetName = '';
    this.description = '';
    this.dataSourceId = null;
    this.baseObjectType = BaseObjectType.TABLE;
    this.sourceDefinition = '';
  }
}

/**
 * 更新数据集请求
 */
export class UpdateDatasetRequest {
  constructor() {
    this.datasetName = '';
    this.description = '';
  }
}

/**
 * 字段配置更新请求
 */
export class UpdateColumnConfigRequest {
  constructor() {
    this.columnName = '';
    this.description = '';
    this.semanticType = null;
    this.synonyms = [];
    this.isFilterable = false;
    this.isGroupable = false;
  }
}

/**
 * 数据集详情DTO
 */
export class DatasetDetailDTO {
  constructor() {
    this.id = null;
    this.datasetName = '';
    this.description = '';
    this.dataSourceId = null;
    this.sourceName = '';
    this.baseObjectType = BaseObjectType.TABLE;
    this.sourceDefinition = '';
    this.status = DatasetStatus.ACTIVE;
    this.columns = [];
    this.createdAt = null;
    this.updatedAt = null;
  }
}

/**
 * 字段配置DTO
 */
export class ColumnConfigDTO {
  constructor() {
    this.id = null;
    this.columnName = '';
    this.description = '';
    this.technicalNameOrExpression = '';
    this.semanticType = null;
    this.dataType = '';
    this.synonyms = [];
    this.status = 'active'; // 字段状态: active/inactive
    this.isFilterable = false;
    this.isGroupable = false;
  }
}

/**
 * 分页响应
 */
export class PageResponse {
  constructor() {
    this.content = [];
    this.totalElements = 0;
    this.totalPages = 0;
    this.size = 10;
    this.number = 0;
  }
}

// 辅助函数
export const getTypeDisplayName = (type) => {
  const names = {
    [BaseObjectType.TABLE]: '数据表',
    [BaseObjectType.VIEW]: '视图',
    [BaseObjectType.QUERY]: '自定义SQL'
  };
  return names[type] || type;
};

export const getTypeColor = (type) => {
  const colors = {
    [BaseObjectType.TABLE]: 'blue',
    [BaseObjectType.VIEW]: 'green',
    [BaseObjectType.QUERY]: 'orange'
  };
  return colors[type] || 'default';
};

export const getSemanticTypeDisplayName = (type) => {
  const names = {
    [SemanticType.METRIC]: '指标',
    [SemanticType.DIMENSION]: '维度'
  };
  return names[type] || type;
};

export const getSemanticTypeColor = (type) => {
  const colors = {
    [SemanticType.METRIC]: 'orange',
    [SemanticType.DIMENSION]: 'blue'
  };
  return colors[type] || 'default';
}; 