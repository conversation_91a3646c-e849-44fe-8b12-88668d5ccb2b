import { defineStore } from 'pinia';
import apiClient from '../api'; // apiClient for making API calls
import router from '../router';   // Vue router for navigation
import { message } from 'ant-design-vue'; // Ant Design Vue for messages

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    user: JSON.parse(localStorage.getItem('user')) || null,
  }),
  getters: {
    isAuthenticated: (state) => !!state.token,
    userRoles: (state) => state.user?.roles || [],
  },
  actions: {
    login(token, user) {
      this.token = token;
      this.user = user;
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
    },
    logout() {
      this.token = null;
      this.user = null;
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    },
    async performLogout() {
      try {
        await apiClient.post('/api/admin/logout'); // Call the backend logout endpoint
        this.logout(); // Clear local user state and token
        router.push('/login');
        message.success('已成功登出');
      } catch (error) {
        console.error('Logout API call failed:', error);
        // Even if API logout fails, ensure local state is cleared and user is redirected
        this.logout();
        router.push('/login');
        message.error('登出操作未能成功完成，但您已在本地安全登出。');
      }
    },
  },
});