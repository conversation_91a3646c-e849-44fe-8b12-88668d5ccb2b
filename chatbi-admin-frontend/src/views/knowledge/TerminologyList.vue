<template>
  <div>
    <a-page-header
      :title="$route.meta.title"
      sub-title="管理业务术语，提升语言模型理解能力"
    />
    <a-card>
      <div class="table-operations">
        <a-input-search
          v-model:value="searchTerm"
          placeholder="按语义搜索术语..."
          style="width: 280px"
          @search="onSearch"
          allow-clear
        />
        <a-space>
          <a-button type="primary" @click="handleAddNew">新建术语</a-button>
          <a-button @click="handleBatchAdd">批量添加</a-button>
        </a-space>
      </div>
      <a-alert v-if="activeSearchTerm" :message="`为您找到 ${pagination.total} 条关于'${activeSearchTerm}'的相似术语`" type="info" show-icon class="my-4" />
      <a-table
        :columns="columns"
        :data-source="termList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
        class="mt-4"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'businessTerm'">
            <span v-html="highlightSearchTerm(record.businessTerm)"></span>
          </template>
          <template v-if="column.key === 'standardReferenceType'">
            <a-tag v-if="record.standardReferenceType === 'DatasetColumn'" color="blue">数据集字段</a-tag>
            <a-tag v-else-if="record.standardReferenceType === 'Custom'" color="green">自定义业务概念</a-tag>
            <span v-else class="text-gray-400">-</span>
          </template>
          <template v-if="column.dataIndex === 'contextDescription'">
            <span v-html="highlightSearchTerm(record.contextDescription)"></span>
          </template>
          <template v-if="column.key === 'standardReferenceName'">
            <a-tag v-if="record.standardReferenceName" color="blue">{{ record.standardReferenceName }}</a-tag>
            <span v-else class="text-gray-400">-</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm
                title="确定删除这个术语吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record.id)"
              >
                <a style="color: red">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 单条添加/编辑模态框 -->
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑术语' : '新建术语'" @ok="handleOk" :confirm-loading="modalLoading" width="600px">
      <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
        <a-form-item label="业务术语" name="businessTerm" :rules="[{ required: true, message: '请输入业务术语' }]">
          <a-input v-model:value="formState.businessTerm" placeholder="例如：GMV、日活" />
        </a-form-item>
        <a-form-item label="映射类型" name="standardReferenceType" :rules="[{ required: true, message: '请选择映射类型' }]">
          <a-radio-group v-model:value="formState.standardReferenceType">
            <a-radio value="DatasetColumn">数据集字段</a-radio>
            <a-radio value="Custom">自定义业务概念</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="formState.standardReferenceType === 'DatasetColumn'" label="映射目标字段" name="standardReferenceIdPath" :rules="[{ required: true, message: '请选择一个具体字段'}]">
          <a-cascader v-model:value="formState.standardReferenceIdPath" :options="datasetColumnOptions" placeholder="选择数据集/字段" />
        </a-form-item>
        <a-form-item label="描述/定义" name="contextDescription" :rules="[{ required: true, message: '请输入描述' }]">
          <a-textarea v-model:value="formState.contextDescription" :rows="4" placeholder="详细描述该术语的含义和使用场景" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 批量添加模态框 -->
    <a-modal v-model:open="isBatchModalVisible" title="批量添加术语" width="80vw" @ok="handleBatchImport" :confirm-loading="batchLoading">
      <a-table :data-source="batchListData" :columns="batchColumns" :pagination="false" row-key="id">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'businessTerm'">
            <a-input v-model:value="record.businessTerm" placeholder="业务术语" />
          </template>
          <template v-if="column.dataIndex === 'standardReferenceType'">
            <a-radio-group v-model:value="record.standardReferenceType" @change="handleReferenceTypeChange(record)">
              <a-radio value="DatasetColumn">数据集字段</a-radio>
              <a-radio value="Custom">自定义业务概念</a-radio>
            </a-radio-group>
          </template>
          <template v-if="column.dataIndex === 'standardReferenceIdPath'">
            <a-cascader 
              v-if="record.standardReferenceType === 'DatasetColumn'"
              v-model:value="record.standardReferenceIdPath" 
              :options="datasetColumnOptions" 
              style="width: 200px" 
              placeholder="选择字段" 
            />
            <span v-else class="text-gray-400">-</span>
          </template>
          <template v-if="column.dataIndex === 'contextDescription'">
            <a-textarea v-model:value="record.contextDescription" :auto-size="{ minRows: 1, maxRows: 3 }" placeholder="描述" />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="removeBatchRow(record.id)">删除</a-button>
          </template>
        </template>
      </a-table>
      <a-button @click="addBatchRow" type="dashed" class="mt-4 w-full"> + 添加一行 </a-button>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { knowledgeApi } from '@/api/knowledge.js';
import { modelApi } from '@/api/model.js';
import { message } from 'ant-design-vue';

const termList = ref([]);
const loading = ref(false);
const searchTerm = ref(''); // 搜索框输入的内容
const activeSearchTerm = ref(''); // 实际执行搜索的关键词，用于高亮
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
});

const columns = [
  {
    title: '术语',
    dataIndex: 'businessTerm',
    key: 'businessTerm',
    width: 200,
  },
  {
    title: '映射类型',
    key: 'standardReferenceType',
    width: 120,
  },
  {
    title: '映射目标',
    key: 'standardReferenceName',
    width: 200,
  },
  {
    title: '描述',
    dataIndex: 'contextDescription',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
  },
];

const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current - 1,
      size: pagination.pageSize,
      search: searchTerm.value || undefined,
    };
    
    const response = await knowledgeApi.getTerms(params);
    termList.value = response.data.content;
    pagination.total = response.data.totalElements;
    
    // 更新实际搜索的关键词，用于高亮显示
    activeSearchTerm.value = searchTerm.value || '';
  } catch (error) {
    message.error('获取术语列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const onSearch = () => { 
  pagination.current = 1; 
  fetchData(); 
};

watch(searchTerm, (val) => { 
  if (!val) {
    // 清空搜索词时，清除高亮并重新加载数据
    activeSearchTerm.value = '';
    onSearch();
  }
});

const handleTableChange = (pag) => { 
  pagination.current = pag.current; 
  pagination.pageSize = pag.pageSize;
  fetchData(); 
};

// 数据集字段选项 - 动态从API加载
const datasetColumnOptions = ref([]);
const loadingDatasetOptions = ref(false);

// 加载数据集和字段选项
const fetchDatasetOptions = async () => {
  loadingDatasetOptions.value = true;
  try {
    const response = await modelApi.getDatasets({ page: 0, size: 100 });
    const datasets = response.data.content || [];
    
    if (datasets.length === 0) {
      message.warning('暂无可用的数据集，请先创建数据集');
      return;
    }
    
    // 并行获取所有数据集的详细信息
    const datasetOptionsPromises = datasets.map(async (dataset) => {
      try {
        const detailResponse = await modelApi.getDatasetDetails(dataset.id);
        const columns = detailResponse.data.columns || [];
        return {
          value: dataset.id,
          label: dataset.datasetName,
          children: columns.map(column => ({
            value: column.id,
            label: column.columnName || column.technicalNameOrExpression
          }))
        };
      } catch (error) {
        console.warn(`数据集 "${dataset.datasetName}" 详情获取失败，将跳过该数据集`);
        return null; // 返回null以便过滤
      }
    });
    
    const datasetOptions = (await Promise.all(datasetOptionsPromises))
      .filter(option => option !== null); // 过滤掉失败的数据集
    
    datasetColumnOptions.value = datasetOptions;
    
    if (datasetOptions.length === 0) {
      message.warning('所有数据集都无法加载，请检查数据集配置');
    }
  } catch (error) {
    console.error('获取数据集选项失败:', error);
    message.error('获取数据集选项失败，请稍后重试');
  } finally {
    loadingDatasetOptions.value = false;
  }
};

onMounted(() => {
  fetchData();
  fetchDatasetOptions();
});

const handleAddNew = () => {
  resetForm();
  isEditing.value = false;
  isModalVisible.value = true;
};

/**
 * 根据字段ID查找对应的级联选择器路径
 * @param {string|number} columnId - 字段ID
 * @returns {Array} [数据集ID, 字段ID] 或空数组
 */
const findDatasetPathByColumnId = (columnId) => {
  if (!columnId || !datasetColumnOptions.value.length) return [];
  
  const targetColumnId = parseInt(columnId);
  for (const dataset of datasetColumnOptions.value) {
    for (const column of dataset.children) {
      if (column.value === targetColumnId) {
        return [dataset.value, column.value];
      }
    }
  }
  return [];
};

/**
 * 根据字段名称查找对应的级联选择器路径（备用方案）
 * @param {string} columnName - 字段名称
 * @returns {Array} [数据集ID, 字段ID] 或空数组
 */
const findDatasetPathByColumnName = (columnName) => {
  if (!columnName || !datasetColumnOptions.value.length) return [];
  
  for (const dataset of datasetColumnOptions.value) {
    for (const column of dataset.children) {
      if (column.label === columnName) {
        return [dataset.value, column.value];
      }
    }
  }
  return [];
};

const handleEdit = async (record) => {
  resetForm();
  isEditing.value = true;
  editingId.value = record.id;
  
  // 复制基本字段
  Object.assign(formState, {
    businessTerm: record.businessTerm,
    standardReferenceType: record.standardReferenceType,
    contextDescription: record.contextDescription
  });
  
  // 等待数据集选项加载完成后再处理级联选择器
  if (loadingDatasetOptions.value) {
    const loadingMessage = message.loading('正在加载数据集选项...', 0);
    
    // 使用Promise方式等待加载完成，更优雅
    await new Promise((resolve) => {
      const checkLoading = () => {
        if (!loadingDatasetOptions.value) {
          loadingMessage();
          resolve();
        } else {
          setTimeout(checkLoading, 100);
        }
      };
      checkLoading();
    });
  }
  
  processEdit(record);
  isModalVisible.value = true;
};

// 处理编辑逻辑的辅助函数
const processEdit = (record) => {
  // 处理数据集字段映射的级联选择器路径
  if (record.standardReferenceType === 'DatasetColumn') {
    let path = [];
    
    // 优先使用standardReferenceId进行精确匹配
    if (record.standardReferenceId) {
      path = findDatasetPathByColumnId(record.standardReferenceId);
    }
    
    // 如果ID匹配失败，尝试使用字段名称进行模糊匹配
    if (path.length === 0 && record.standardReferenceName) {
      path = findDatasetPathByColumnName(record.standardReferenceName);
      
      // 如果字段名称也匹配不到，提示用户重新选择
      if (path.length === 0) {
        message.warning(`映射字段"${record.standardReferenceName}"在当前数据集中不存在，请重新选择映射目标`);
      }
    }
    
    formState.standardReferenceIdPath = path;
  } else {
    formState.standardReferenceIdPath = [];
  }
};

const handleDelete = async (id) => {
  try {
    await knowledgeApi.deleteTerm(id);
    message.success('删除成功');
    fetchData();
  } catch (error) {
    message.error('删除失败');
    console.error(error);
  }
};



// Single Add/Edit Modal
const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const editingId = ref(null);
const formRef = ref();
const formState = reactive({
  businessTerm: '',
  standardReferenceType: 'DatasetColumn',
  standardReferenceIdPath: [],
  contextDescription: ''
});

// Batch Add Modal
const isBatchModalVisible = ref(false);
const batchLoading = ref(false);
const batchListData = ref([]);
const batchIdCounter = ref(1);
const batchColumns = [
    { title: '业务术语', dataIndex: 'businessTerm', width: 150 },
    { title: '映射类型', dataIndex: 'standardReferenceType', width: 150 },
    { title: '映射目标', dataIndex: 'standardReferenceIdPath', width: 200 },
    { title: '描述', dataIndex: 'contextDescription', width: 250 },
    { title: '操作', dataIndex: 'action', width: 80 }
];

// 重置表单
const resetForm = () => {
  Object.assign(formState, {
    businessTerm: '',
    standardReferenceType: 'DatasetColumn',
    standardReferenceIdPath: [],
    contextDescription: ''
  });
  editingId.value = null;
  formRef.value?.resetFields();
};

// 确认添加/编辑
const handleOk = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    
    if (isEditing.value) {
      await knowledgeApi.updateTerm(editingId.value, formState);
      message.success('术语更新成功');
    } else {
      await knowledgeApi.createTerm(formState);
      message.success('术语创建成功');
    }
    
    isModalVisible.value = false;
    fetchData();
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入');
    } else {
      message.error(isEditing.value ? '更新失败' : '创建失败');
      console.error(error);
    }
  } finally {
    modalLoading.value = false;
  }
};

// 批量添加处理
const handleBatchAdd = () => {
  initBatchData();
  isBatchModalVisible.value = true;
};

// 批量操作相关
const initBatchData = () => {
  batchListData.value = [createEmptyBatchRow()];
  batchIdCounter.value = 1;
};

const createEmptyBatchRow = () => ({
  id: batchIdCounter.value++,
  businessTerm: '',
  standardReferenceType: 'DatasetColumn',
  standardReferenceIdPath: [],
  contextDescription: ''
});

const addBatchRow = () => {
  batchListData.value.push(createEmptyBatchRow());
};

const removeBatchRow = (id) => {
  batchListData.value = batchListData.value.filter(item => item.id !== id);
};

// 处理映射类型变化
const handleReferenceTypeChange = (record) => {
  // 当切换到自定义类型时，清空字段路径
  if (record.standardReferenceType === 'Custom') {
    record.standardReferenceIdPath = [];
  }
};

const handleBatchImport = async () => {
  // 验证数据完整性
  const validRows = batchListData.value.filter(row => {
    const hasBasicInfo = row.businessTerm?.trim() && row.contextDescription?.trim();
    if (!hasBasicInfo) return false;
    
    // 如果是字段映射类型，必须选择字段
    if (row.standardReferenceType === 'DatasetColumn') {
      return row.standardReferenceIdPath && row.standardReferenceIdPath.length === 2;
    }
    
    // 自定义类型不需要字段路径
    return true;
  });
  
  if (validRows.length === 0) {
    message.warning('请至少填写一行完整的术语数据。字段类型需要选择映射目标，自定义类型只需填写术语名称和描述。');
    return;
  }
  
  // 检查是否有重复的术语名称
  const termNames = validRows.map(row => row.businessTerm.trim());
  const duplicates = termNames.filter((name, index) => termNames.indexOf(name) !== index);
  if (duplicates.length > 0) {
    message.error(`发现重复的术语名称：${duplicates.join(', ')}`);
    return;
  }
  
  batchLoading.value = true;
  try {
    const response = await knowledgeApi.createTermsInBatch(validRows);
    const { successCount, failureCount, errorMessages } = response.data;
    
    if (failureCount === 0) {
      message.success(`批量导入成功！共创建 ${successCount} 条术语`);
      isBatchModalVisible.value = false;
      fetchData();
    } else {
      // 显示详细的错误信息
      const errorDetails = errorMessages.length > 0 ? 
        `\n错误详情：\n${errorMessages.slice(0, 3).join('\n')}${errorMessages.length > 3 ? '\n...' : ''}` : '';
      
      message.warning({
        content: `导入完成：成功 ${successCount} 条，失败 ${failureCount} 条${errorDetails}`,
        duration: 6
      });
      
      // 部分成功时也刷新数据
      if (successCount > 0) {
        fetchData();
      }
    }
  } catch (error) {
    message.error('批量导入失败，请检查网络连接或联系管理员');
    console.error('批量导入错误:', error);
  } finally {
    batchLoading.value = false;
  }
};

const highlightSearchTerm = (text) => {
  // 只有在实际执行了搜索且有搜索结果时才进行高亮
  if (!activeSearchTerm.value || !text) return text;
  return text.replace(new RegExp(activeSearchTerm.value, 'gi'), `<mark>$&</mark>`);
};
</script>

<style scoped>
.table-operations {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.ml-2 {
    margin-left: 8px;
}

.mt-4 {
    margin-top: 16px;
}

.my-4 {
  margin: 16px 0;
}

.w-full {
  width: 100%;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 2px solid #e8e8e8;
  color: #262626;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f9ff;
}

:deep(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
  border: none;
  padding: 2px 8px;
}

/* 搜索框样式 */
:deep(.ant-input-search) {
  border-radius: 8px;
}

:deep(.ant-input-search .ant-input) {
  border-radius: 8px 0 0 8px;
}

:deep(.ant-input-search .ant-input-search-button) {
  border-radius: 0 8px 8px 0;
  background: #1890ff;
  border: 1px solid #1890ff;
}

:deep(.ant-input-search .ant-input-search-button:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 按钮组样式 */
:deep(.ant-space) {
  gap: 12px !important;
}

:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.ant-btn-primary) {
  background: #1890ff;
  border: 1px solid #1890ff;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

:deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

:deep(.ant-btn-default) {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  color: #666;
}

:deep(.ant-btn-default:hover) {
  background: #f5f5f5;
  border-color: #40a9ff;
  color: #1890ff;
}

/* 分页样式 */
:deep(.ant-pagination) {
  margin-top: 20px;
  text-align: center;
}

:deep(.ant-pagination-item) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

:deep(.ant-pagination-item-active) {
  background: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-pagination-item-active a) {
  color: white;
}

/* 模态框样式 */
:deep(.ant-modal) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-modal-header) {
  background: #1890ff;
  color: white;
  border-bottom: none;
  padding: 20px 24px;
}

:deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.ant-modal-close) {
  color: white;
  opacity: 0.8;
  top: 20px;
  right: 20px;
}

:deep(.ant-modal-close:hover) {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
  padding: 16px 24px;
  background: #ffffff;
}

/* 表单控件样式 */
:deep(.ant-form-item-label) {
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

:deep(.ant-select), :deep(.ant-input), :deep(.ant-input-number), :deep(.ant-textarea) {
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid #d9d9d9;
}

:deep(.ant-select:hover), :deep(.ant-input:hover), :deep(.ant-input-number:hover), :deep(.ant-textarea:hover) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 搜索高亮 */
:deep(mark) {
  background-color: #fff3cd;
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-operations {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }
}
</style> 