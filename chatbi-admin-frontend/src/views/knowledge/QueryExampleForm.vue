<template>
  <div class="query-example-form">
    <a-form ref="formRef" :model="formState" :rules="formRules" layout="vertical" class="mt-4">
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <a-row :gutter="20">
          <a-col :span="24">
            <a-form-item label="用户问题" name="userQuestion">
              <a-textarea 
                v-model:value="formState.userQuestion" 
                placeholder="请输入用户可能会问的自然语言问题" 
                :rows="2"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="关联数据集" name="targetDatasetId">
              <a-select 
                v-model:value="formState.targetDatasetId" 
                placeholder="请选择数据集" 
                :loading="datasetLoading" 
                show-search 
                :options="datasetOptions"
                @change="onDatasetChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="意图类型" name="intent">
              <a-select 
                v-model:value="formState.intent" 
                placeholder="选择意图类型" 
                @change="onIntentChange"
              >
                <a-select-option value="DATA_QUERY">数据查询</a-select-option>
                <a-select-option value="CLARIFICATION_NEEDED">需要澄清</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" v-if="formState.intent === 'DATA_QUERY'">
            <a-form-item label="查询类型" name="queryType">
              <a-select 
                v-model:value="formState.queryType" 
                placeholder="选择查询类型"
              >
                <a-select-option value="AGGREGATION_QUERY">聚合查询</a-select-option>
                <a-select-option value="RANKING_QUERY">排名查询</a-select-option>
                <a-select-option value="TREND_QUERY">趋势查询</a-select-option>
                <a-select-option value="DETAIL_QUERY">明细查询</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6" v-if="formState.intent === 'DATA_QUERY'">
            <a-form-item label="匹配置信度" name="datasetMatchConfidence">
              <a-select 
                v-model:value="formState.datasetMatchConfidence" 
                placeholder="选择置信度"
              >
                <a-select-option value="HIGH">高</a-select-option>
                <a-select-option value="MEDIUM">中</a-select-option>
                <a-select-option value="LOW">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 数据查询配置 -->
      <div v-if="formState.intent === 'DATA_QUERY'" class="form-section">
        <h3 class="section-title">查询配置</h3>
        <a-row :gutter="16">
          <a-col :span="10">
            <a-form-item label="指标字段">
              <a-select
                v-model:value="formState.metrics"
                mode="multiple"
                placeholder="选择指标字段"
                :loading="fieldsLoading"
              >
                <a-select-option 
                  v-for="option in metricOptions" 
                  :key="option.value" 
                  :value="option.value"
                  :class="option.fieldType === 'CALCULATED' ? 'calculated-field' : 'physical-field'"
                >
                  <span :class="option.fieldType === 'CALCULATED' ? 'calculated-field-text' : 'physical-field-text'">
                    {{ option.label }}
                    <span class="field-type-badge">{{ option.fieldType === 'CALCULATED' ? '计算' : '物理' }}</span>
                  </span>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="分组维度">
              <a-select
                v-model:value="formState.dimensionsToGroup"
                mode="multiple"
                placeholder="选择分组维度"
                :loading="fieldsLoading"
              >
                <a-select-option 
                  v-for="option in dimensionOptions" 
                  :key="option.value" 
                  :value="option.value"
                  :class="getDimensionClass(option.semanticType)"
                >
                  <span :class="getDimensionTextClass(option.semanticType)">
                    {{ option.label }}
                    <span class="field-type-badge">{{ getDimensionLabel(option.semanticType) }}</span>
                  </span>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="返回记录数">
              <a-input-number 
                v-model:value="formState.limit" 
                :min="1" 
                :max="1000" 
                placeholder="10" 
                style="width: 100%" 
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="section-header">
            <span class="section-subtitle">筛选条件</span>
            <a-button type="primary" @click="addFilter">+ 添加筛选条件</a-button>
          </div>
          <div v-for="(filter, index) in formState.filters" :key="index" class="filter-item">
            <a-row :gutter="12" align="middle">
              <a-col :span="9">
                <a-select 
                  v-model:value="filter.fieldName" 
                  placeholder="选择字段" 
                  :options="allFieldOptions"
                  style="width: 300px"
                />
              </a-col>
              <a-col :span="6">
                <a-select 
                  v-model:value="filter.operator" 
                  placeholder="操作符" 
                  @change="(val) => onOperatorChange(index, val)"
                  style="width: 160px"
                >
                  <a-select-option value="=">=</a-select-option>
                  <a-select-option value="!=">!=</a-select-option>
                  <a-select-option value=">">></a-select-option>
                  <a-select-option value="<"><</a-select-option>
                  <a-select-option value=">=">=</a-select-option>
                  <a-select-option value="<="><=</a-select-option>
                  <a-select-option value="IN">IN</a-select-option>
                  <a-select-option value="NOT_IN">NOT IN</a-select-option>
                  <a-select-option value="BETWEEN">BETWEEN</a-select-option>
                  <a-select-option value="LIKE">LIKE</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-select
                  v-if="['IN', 'NOT_IN'].includes(filter.operator)"
                  v-model:value="filter.value"
                  mode="tags"
                  placeholder="输入多个值"
                />
                                  <a-input-group v-else-if="filter.operator === 'BETWEEN'" compact>
                    <a-input 
                      v-model:value="filter.value[0]" 
                      placeholder="起始值" 
                      style="width: 50%" 
                    />
                    <a-input 
                      v-model:value="filter.value[1]" 
                      placeholder="结束值" 
                      style="width: 50%" 
                    />
                  </a-input-group>
                <a-input 
                  v-else 
                  v-model:value="filter.value" 
                  placeholder="输入值"
                />
              </a-col>
              <a-col :span="3">
                <a-button size="small" danger @click="removeFilter(index)">删除</a-button>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 排序设置 -->
        <div class="sort-section">
          <div class="section-header">
            <span class="section-subtitle">排序设置</span>
            <a-button type="primary" @click="addSort">+ 添加排序字段</a-button>
          </div>
          <div v-for="(sort, index) in formState.sortBy" :key="index" class="sort-item">
            <a-row :gutter="12" align="middle">
              <a-col :span="9">
                <a-select 
                  v-model:value="sort.fieldName" 
                  placeholder="选择字段" 
                  :options="allFieldOptions"
                  style="width: 300px"
                />
              </a-col>
              <a-col :span="6">
                <a-select 
                  v-model:value="sort.order" 
                  placeholder="排序方向"
                  style="width: 160px"
                >
                  <a-select-option value="ASC">升序</a-select-option>
                  <a-select-option value="DESC">降序</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <!-- 空白列用于对齐 -->
              </a-col>
              <a-col :span="3">
                <a-button size="small" danger @click="removeSort(index)">删除</a-button>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <!-- 澄清配置 -->
      <div v-if="formState.intent === 'CLARIFICATION_NEEDED'" class="form-section">
        <h3 class="section-title">澄清配置</h3>
        <a-form-item label="回复内容">
          <a-textarea 
            v-model:value="formState.response" 
            placeholder="请输入澄清回复内容" 
            :rows="2"
          />
        </a-form-item>
        <div class="clarification-options">
          <div class="section-header">
            <span class="section-subtitle">澄清选项</span>
            <a-button type="primary" @click="addClarificationOption">+ 添加选项</a-button>
          </div>
          <div v-for="(option, index) in formState.clarificationOptions" :key="index" class="clarification-item">
            <a-row :gutter="12" align="middle">
              <a-col :span="18">
                <a-input 
                  v-model:value="option.optionText" 
                  placeholder="输入澄清选项文本"
                />
              </a-col>
              <a-col :span="6">
                <a-button size="small" danger @click="removeClarificationOption(index)">删除</a-button>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <!-- 上下文信息 - 隐藏，自动与用户问题保持一致 -->
      <!-- <div class="form-section">
        <h3 class="section-title">上下文信息</h3>
        <a-row :gutter="20">
          <a-col :span="12">
            <a-form-item label="原始查询">
              <a-input 
                v-model:value="formState.originalQuery" 
                placeholder="通常与用户问题相同"
                size="large"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合并上下文">
              <a-input 
                v-model:value="formState.mergedContext" 
                placeholder="通常与原始查询相同"
                size="large"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div> -->

      <!-- 其他信息 -->
      <div class="form-section">
        <h3 class="section-title">其他信息</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="难度等级" name="difficultyLevel">
              <a-select 
                v-model:value="formState.difficultyLevel" 
                placeholder="选择难度"
              >
                <a-select-option value="Easy">简单</a-select-option>
                <a-select-option value="Medium">中等</a-select-option>
                <a-select-option value="Hard">困难</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="备注" name="notes">
              <a-textarea 
                v-model:value="formState.notes" 
                placeholder="请输入对该范例的额外说明" 
                :rows="1"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- JSON 预览 -->
      <div class="form-section">
        <div class="section-header">
          <span class="section-title">JSON 预览</span>
          <a-button type="default" @click="toggleJsonPreview">{{ showJsonPreview ? '隐藏' : '显示' }}</a-button>
        </div>
        <div v-if="showJsonPreview" class="json-preview">
          <a-textarea
            :value="generatedJsonString"
            :rows="15"
            readonly
            style="font-family: 'Courier New', monospace; font-size: 12px;"
          />
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import apiClient from '@/api';

const emit = defineEmits(['formDataChange']);

// --- State ---
const formRef = ref();
const showJsonPreview = ref(true);
const datasetLoading = ref(false);
const datasetOptions = ref([]);
const fieldsLoading = ref(false);
const currentDatasetFields = ref([]);

// Form state
const formState = reactive({
  id: null,
  userQuestion: '',
  targetDatasetId: null,
  intent: 'DATA_QUERY',
  queryType: 'AGGREGATION_QUERY',
  datasetMatchConfidence: 'HIGH',
  metrics: [],
  dimensionsToGroup: [],
  filters: [],
  sortBy: [],
  limit: 10,
  response: '',
  clarificationOptions: [],
  originalQuery: '',
  mergedContext: '',
  difficultyLevel: 'Medium',
  notes: '',
});

// Form rules
const formRules = {
  userQuestion: [{ required: true, message: '请输入用户问题' }],
  targetDatasetId: [{ required: true, message: '请选择关联数据集' }],
  intent: [{ required: true, message: '请选择意图类型' }],
};

// 计算属性
const metricOptions = computed(() => {
  return currentDatasetFields.value
    .filter(field => field.semanticType === 'METRIC')
    .map(field => ({ 
      value: field.columnName, 
      label: field.columnName,
      // 如果字段有fieldType属性则使用，否则默认为物理字段
      fieldType: field.fieldType || 'PHYSICAL'
    }));
});

const dimensionOptions = computed(() => {
  return currentDatasetFields.value
    .filter(field => ['DIMENSION', 'TIME_DIMENSION', 'GEO_DIMENSION'].includes(field.semanticType))
    .map(field => ({ 
      value: field.columnName, 
      label: field.columnName,
      semanticType: field.semanticType
    }));
});

const allFieldOptions = computed(() => {
  return currentDatasetFields.value.map(field => ({ 
    value: field.columnName, 
    label: `${field.columnName} (${field.semanticType})` 
  }));
});

const generatedJsonString = computed(() => {
  const jsonData = generateJSON();
  return JSON.stringify(jsonData, null, 2);
});

// 监听表单变化
watch(formState, () => {
  const data = {
    ...formState,
    targetQueryRepresentation: JSON.stringify(generateJSON())
  };
  emit('formDataChange', data);
}, { deep: true });

// 自动同步用户问题到上下文信息字段
watch(() => formState.userQuestion, (newVal) => {
  // 始终与用户问题保持一致
  formState.originalQuery = newVal || '';
  formState.mergedContext = newVal || '';
});

// --- Methods ---
function generateJSON() {
  if (formState.intent === 'DATA_QUERY') {
    return {
      intent: formState.intent,
      entities: {
        metrics: formState.metrics,
        dimensions_to_group: formState.dimensionsToGroup,
        filters: formState.filters.filter(f => f.fieldName && f.operator && f.value),
        sort_by: formState.sortBy.filter(s => s.fieldName && s.order),
        limit: formState.limit || 10
      },
      queryType: formState.queryType,
      targetDatasetId: formState.targetDatasetId,
      datasetMatchConfidence: formState.datasetMatchConfidence,
      internal_context: {
        originalQuery: formState.originalQuery || formState.userQuestion,
        mergedContext: formState.mergedContext || formState.userQuestion
      }
    };
  } else if (formState.intent === 'CLARIFICATION_NEEDED') {
    return {
      intent: formState.intent,
      response: formState.response,
      clarificationOptions: formState.clarificationOptions.filter(opt => opt.optionText),
      internal_context: {
        originalQuery: formState.originalQuery || formState.userQuestion,
        mergedContext: formState.mergedContext || formState.userQuestion
      }
    };
  }
}

async function fetchDatasets() {
  datasetLoading.value = true;
  try {
    const response = await apiClient.get('/api/admin/knowledge-base/datasets');
    const datasets = response.data;
    datasetOptions.value = datasets.map(d => ({ value: d.id, label: d.datasetName }));
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    datasetOptions.value = [];
  } finally {
    datasetLoading.value = false;
  }
}

async function fetchDatasetFields(datasetId) {
  if (!datasetId) {
    currentDatasetFields.value = [];
    return;
  }
  
  fieldsLoading.value = true;
  try {
    const response = await apiClient.get(`/api/admin/datasets/${datasetId}`);
    currentDatasetFields.value = response.data.columns || [];
  } catch (error) {
    console.error('获取数据集字段失败:', error);
    currentDatasetFields.value = [];
  } finally {
    fieldsLoading.value = false;
  }
}

function onDatasetChange(datasetId) {
  fetchDatasetFields(datasetId);
  // 清空字段相关的选择
  formState.metrics = [];
  formState.dimensionsToGroup = [];
  formState.filters = [];
  formState.sortBy = [];
}

function onIntentChange(intent) {
  if (intent === 'CLARIFICATION_NEEDED') {
    formState.queryType = '';
    formState.datasetMatchConfidence = '';
    formState.metrics = [];
    formState.dimensionsToGroup = [];
    formState.filters = [];
    formState.sortBy = [];
    formState.limit = 10;
  } else if (intent === 'DATA_QUERY') {
    formState.response = '';
    formState.clarificationOptions = [];
    formState.queryType = 'AGGREGATION_QUERY';
    formState.datasetMatchConfidence = 'HIGH';
  }
}

// Filter Management
function addFilter() {
  formState.filters.push({
    fieldName: '',
    operator: '=',
    value: ''
  });
}

function removeFilter(index) {
  formState.filters.splice(index, 1);
}

function onOperatorChange(index, operator) {
  const filter = formState.filters[index];
  if (['IN', 'NOT_IN'].includes(operator)) {
    filter.value = [];
  } else if (operator === 'BETWEEN') {
    filter.value = ['', ''];
  } else {
    filter.value = '';
  }
}

// Sort Management
function addSort() {
  formState.sortBy.push({
    fieldName: '',
    order: 'DESC'
  });
}

function removeSort(index) {
  formState.sortBy.splice(index, 1);
}

// Clarification Options
function addClarificationOption() {
  formState.clarificationOptions.push({
    optionText: ''
  });
}

function removeClarificationOption(index) {
  formState.clarificationOptions.splice(index, 1);
}

function toggleJsonPreview() {
  showJsonPreview.value = !showJsonPreview.value;
}

// 维度类型处理方法
function getDimensionClass(semanticType) {
  switch(semanticType) {
    case 'TIME_DIMENSION':
      return 'time-dimension';
    case 'GEO_DIMENSION':
      return 'geo-dimension';
    case 'DIMENSION':
    default:
      return 'normal-dimension';
  }
}

function getDimensionTextClass(semanticType) {
  switch(semanticType) {
    case 'TIME_DIMENSION':
      return 'time-dimension-text';
    case 'GEO_DIMENSION':
      return 'geo-dimension-text';
    case 'DIMENSION':
    default:
      return 'normal-dimension-text';
  }
}

function getDimensionLabel(semanticType) {
  switch(semanticType) {
    case 'TIME_DIMENSION':
      return '时间';
    case 'GEO_DIMENSION':
      return '地理';
    case 'DIMENSION':
    default:
      return '普通';
  }
}

// Public Methods
function validate() {
  return formRef.value.validate();
}

function loadData(record) {
  try {
    const jsonData = typeof record.targetQueryRepresentation === 'string' 
      ? JSON.parse(record.targetQueryRepresentation)
      : record.targetQueryRepresentation;

    Object.assign(formState, {
      id: record.id,
      userQuestion: record.userQuestion,
      targetDatasetId: record.targetDatasetId,
      difficultyLevel: record.difficultyLevel,
      notes: record.notes,
      intent: 'DATA_QUERY',
      queryType: 'AGGREGATION_QUERY',
      datasetMatchConfidence: 'HIGH',
      metrics: [],
      dimensionsToGroup: [],
      filters: [],
      sortBy: [],
      limit: 10,
      response: '',
      clarificationOptions: [],
      originalQuery: '',
      mergedContext: '',
    });

    if (jsonData) {
      formState.intent = jsonData.intent || 'DATA_QUERY';
      
      if (jsonData.intent === 'DATA_QUERY') {
        formState.queryType = jsonData.queryType || 'AGGREGATION_QUERY';
        formState.datasetMatchConfidence = jsonData.datasetMatchConfidence || 'HIGH';
        
        if (jsonData.entities) {
          formState.metrics = jsonData.entities.metrics || [];
          formState.dimensionsToGroup = jsonData.entities.dimensions_to_group || [];
          formState.filters = jsonData.entities.filters || [];
          formState.sortBy = jsonData.entities.sort_by || [];
          formState.limit = jsonData.entities.limit || 10;
        }
      } else if (jsonData.intent === 'CLARIFICATION_NEEDED') {
        formState.response = jsonData.response || '';
        formState.clarificationOptions = jsonData.clarificationOptions || [];
      }

      if (jsonData.internal_context) {
        formState.originalQuery = jsonData.internal_context.originalQuery || '';
        formState.mergedContext = jsonData.internal_context.mergedContext || '';
      }
    }

    if (formState.targetDatasetId) {
      fetchDatasetFields(formState.targetDatasetId);
    }
  } catch (error) {
    console.error('解析记录数据失败:', error);
  }
}

function reset() {
  Object.assign(formState, {
    id: null,
    userQuestion: '',
    targetDatasetId: null,
    intent: 'DATA_QUERY',
    queryType: 'AGGREGATION_QUERY',
    datasetMatchConfidence: 'HIGH',
    metrics: [],
    dimensionsToGroup: [],
    filters: [],
    sortBy: [],
    limit: 10,
    response: '',
    clarificationOptions: [],
    originalQuery: '',
    mergedContext: '',
    difficultyLevel: 'Medium',
    notes: '',
  });
}

defineExpose({
  validate,
  loadData,
  reset,
  formState
});

onMounted(() => {
  fetchDatasets();
});
</script>

<style scoped>
.query-example-form {
  padding: 0;
}

.form-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.form-section:hover {
  border-color: #d9d9d9;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  position: relative;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 4px;
}

.filter-section, .sort-section, .clarification-options {
  margin-top: 16px;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.filter-item, .sort-item, .clarification-item {
  margin-bottom: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.filter-item:hover, .sort-item:hover, .clarification-item:hover {
  background: #f0f9ff;
  border-color: #d4edda;
}

.filter-item:last-child, .sort-item:last-child, .clarification-item:last-child {
  margin-bottom: 0;
}

.json-preview {
  margin-top: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;
}

/* 与其他页面保持一致的标准样式 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-select), :deep(.ant-input), :deep(.ant-input-number), :deep(.ant-textarea) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.ant-select:hover), :deep(.ant-input:hover), :deep(.ant-input-number:hover), :deep(.ant-textarea:hover) {
  border-color: #40a9ff;
}



:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 400;
}

:deep(.ant-btn-primary) {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

:deep(.ant-btn-primary:hover) {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  color: #fff !important;
}

:deep(.ant-btn-danger) {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: white;
}

:deep(.ant-btn-danger:hover) {
  background: #ff7875;
  border-color: #ff7875;
}

/* 确保所有表单内的主要按钮使用统一蓝色 */
.section-header :deep(.ant-btn-primary),
.filter-section :deep(.ant-btn-primary),
.sort-section :deep(.ant-btn-primary),
.clarification-options :deep(.ant-btn-primary) {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

.section-header :deep(.ant-btn-primary:hover),
.filter-section :deep(.ant-btn-primary:hover),
.sort-section :deep(.ant-btn-primary:hover),
.clarification-options :deep(.ant-btn-primary:hover) {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  color: #fff !important;
}

:deep(.ant-input-group) {
  border-radius: 6px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .filter-item, .sort-item, .clarification-item {
    padding: 6px;
  }
}

/* 字段类型颜色区分 */
:deep(.calculated-field) {
  background-color: #f6ffed !important;
  border-left: 3px solid #52c41a;
}

:deep(.physical-field) {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff;
}

:deep(.calculated-field:hover) {
  background-color: #d9f7be !important;
}

:deep(.physical-field:hover) {
  background-color: #bae7ff !important;
}

.calculated-field-text {
  color: #389e0d;
  font-weight: 500;
}

.physical-field-text {
  color: #096dd9;
  font-weight: 500;
}

.field-type-badge {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 6px;
  background-color: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.6);
}

/* 选中状态的颜色 */
:deep(.ant-select-item-option-selected.calculated-field) {
  background-color: #d9f7be !important;
  font-weight: 600;
}

:deep(.ant-select-item-option-selected.physical-field) {
  background-color: #bae7ff !important;
  font-weight: 600;
}

/* 维度类型颜色区分 */
:deep(.normal-dimension) {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff;
}

:deep(.time-dimension) {
  background-color: #fff7e6 !important;
  border-left: 3px solid #fa8c16;
}

:deep(.geo-dimension) {
  background-color: #f9f0ff !important;
  border-left: 3px solid #722ed1;
}

:deep(.normal-dimension:hover) {
  background-color: #bae7ff !important;
}

:deep(.time-dimension:hover) {
  background-color: #ffd591 !important;
}

:deep(.geo-dimension:hover) {
  background-color: #efdbff !important;
}

.normal-dimension-text {
  color: #096dd9;
  font-weight: 500;
}

.time-dimension-text {
  color: #d46b08;
  font-weight: 500;
}

.geo-dimension-text {
  color: #531dab;
  font-weight: 500;
}

/* 维度选中状态的颜色 */
:deep(.ant-select-item-option-selected.normal-dimension) {
  background-color: #bae7ff !important;
  font-weight: 600;
}

:deep(.ant-select-item-option-selected.time-dimension) {
  background-color: #ffd591 !important;
  font-weight: 600;
}

:deep(.ant-select-item-option-selected.geo-dimension) {
  background-color: #efdbff !important;
  font-weight: 600;
}

/* 保持简洁的标准样式 */
</style> 