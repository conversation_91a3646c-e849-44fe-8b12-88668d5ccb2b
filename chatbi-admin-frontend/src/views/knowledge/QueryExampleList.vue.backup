<template>
  <div>
    <a-page-header
      title="查询范例管理"
      sub-title='管理高质量的"问题-答案"范例以提升AI能力'
    />
    <a-card>
      <div class="table-operations">
        <a-input-search
          v-model:value="searchTerm"
          placeholder="按问题语义搜索范例..."
          style="width: 300px"
          @search="onSearch"
          allow-clear
        />
        <a-space>
          <a-button type="primary" @click="handleAddNew">
            新建范例
          </a-button>
          <a-button @click="handleBatchAdd">
            批量添加
          </a-button>
        </a-space>
      </div>

      <a-alert v-if="searchPerformed" :message="`为您找到 ${pagination.total} 条关于'${searchTerm}'的相似范例`" type="info" show-icon class="my-4" />

      <a-table
        :columns="columns"
        :data-source="exampleList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userQuestion'">
            <span v-html="highlightText(record.userQuestion, searchPerformed ? searchTerm : '')"></span>
          </template>
          <template v-if="column.key === 'targetDatasetName'">
            <a-tag color="blue">{{ record.targetDatasetName }}</a-tag>
          </template>
          <template v-if="column.key === 'difficultyLevel'">
            <a-tag :color="getDifficultyColor(record.difficultyLevel)">{{ record.difficultyLevel || '未指定' }}</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm title="确定删除这个范例吗?" @confirm="handleDelete(record.id)">
                <a style="color: red">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 单条新建/编辑模态框 -->
    <a-modal
      v-model:open="isModalVisible"
      :title="isEditing ? '编辑范例' : '新建范例'"
      @ok="handleOk"
      :confirm-loading="modalLoading"
      width="1200px"
      :destroy-on-close="true"
      class="example-modal"
    >
      <a-form ref="formRef" :model="formState" :rules="formRules" layout="vertical" class="mt-4">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="用户问题" name="userQuestion">
                <a-textarea v-model:value="formState.userQuestion" placeholder="请输入用户可能会问的自然语言问题" :rows="3" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="关联数据集" name="targetDatasetId">
                <a-select 
                  v-model:value="formState.targetDatasetId" 
                  placeholder="请选择此问题应查询的数据集" 
                  :loading="datasetLoading" 
                  show-search 
                  :options="datasetOptions"
                  @change="onDatasetChange"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="意图类型" name="intent">
                <a-select v-model:value="formState.intent" placeholder="选择意图类型" @change="onIntentChange">
                  <a-select-option value="DATA_QUERY">数据查询</a-select-option>
                  <a-select-option value="CLARIFICATION_NEEDED">需要澄清</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="formState.intent === 'DATA_QUERY'">
              <a-form-item label="查询类型" name="queryType">
                <a-select v-model:value="formState.queryType" placeholder="选择查询类型">
                  <a-select-option value="AGGREGATION_QUERY">聚合查询</a-select-option>
                  <a-select-option value="RANKING_QUERY">排名查询</a-select-option>
                  <a-select-option value="TREND_QUERY">趋势查询</a-select-option>
                  <a-select-option value="DETAIL_QUERY">明细查询</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="formState.intent === 'DATA_QUERY'">
              <a-form-item label="匹配置信度" name="datasetMatchConfidence">
                <a-select v-model:value="formState.datasetMatchConfidence" placeholder="选择置信度">
                  <a-select-option value="HIGH">高</a-select-option>
                  <a-select-option value="MEDIUM">中</a-select-option>
                  <a-select-option value="LOW">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 数据查询配置 -->
        <div v-if="formState.intent === 'DATA_QUERY'" class="form-section">
          <h3 class="section-title">查询配置</h3>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="指标字段">
                <a-select
                  v-model:value="formState.metrics"
                  mode="multiple"
                  placeholder="选择指标字段"
                  :options="metricOptions"
                  :loading="fieldsLoading"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="分组维度">
                <a-select
                  v-model:value="formState.dimensionsToGroup"
                  mode="multiple"
                  placeholder="选择分组维度"
                  :options="dimensionOptions"
                  :loading="fieldsLoading"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="返回记录数">
                <a-input-number v-model:value="formState.limit" :min="1" :max="1000" placeholder="10" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 筛选条件 -->
          <div class="filter-section">
            <div class="section-header">
              <span class="section-title">筛选条件</span>
              <a-button size="small" @click="addFilter">+ 添加筛选条件</a-button>
            </div>
            <div v-for="(filter, index) in formState.filters" :key="index" class="filter-item">
              <a-row :gutter="8" align="middle">
                <a-col :span="6">
                  <a-select v-model:value="filter.fieldName" placeholder="选择字段" :options="allFieldOptions" />
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="filter.operator" placeholder="操作符" @change="(val) => onOperatorChange(index, val)">
                    <a-select-option value="=">=</a-select-option>
                    <a-select-option value="!=">!=</a-select-option>
                    <a-select-option value=">">></a-select-option>
                    <a-select-option value="<"><</a-select-option>
                    <a-select-option value=">=">=</a-select-option>
                    <a-select-option value="<="><=</a-select-option>
                    <a-select-option value="IN">IN</a-select-option>
                    <a-select-option value="NOT_IN">NOT IN</a-select-option>
                    <a-select-option value="BETWEEN">BETWEEN</a-select-option>
                    <a-select-option value="LIKE">LIKE</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="10">
                  <!-- 根据操作符显示不同的输入框 -->
                  <a-select
                    v-if="['IN', 'NOT_IN'].includes(filter.operator)"
                    v-model:value="filter.value"
                    mode="tags"
                    placeholder="输入多个值"
                    style="width: 100%"
                  />
                  <a-input-group v-else-if="filter.operator === 'BETWEEN'" compact>
                    <a-input v-model:value="filter.value[0]" placeholder="起始值" style="width: 50%" />
                    <a-input v-model:value="filter.value[1]" placeholder="结束值" style="width: 50%" />
                  </a-input-group>
                  <a-input v-else v-model:value="filter.value" placeholder="输入值" />
                </a-col>
                <a-col :span="2">
                  <a-button size="small" danger @click="removeFilter(index)">删除</a-button>
                </a-col>
              </a-row>
            </div>
          </div>

          <!-- 排序设置 -->
          <div class="sort-section">
            <div class="section-header">
              <span class="section-title">排序设置</span>
              <a-button size="small" @click="addSort">+ 添加排序字段</a-button>
            </div>
            <div v-for="(sort, index) in formState.sortBy" :key="index" class="sort-item">
              <a-row :gutter="8" align="middle">
                <a-col :span="12">
                  <a-select v-model:value="sort.fieldName" placeholder="选择字段" :options="allFieldOptions" />
                </a-col>
                <a-col :span="8">
                  <a-select v-model:value="sort.order" placeholder="排序方向">
                    <a-select-option value="ASC">升序</a-select-option>
                    <a-select-option value="DESC">降序</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-button size="small" danger @click="removeSort(index)">删除</a-button>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>

        <!-- 澄清配置 -->
        <div v-if="formState.intent === 'CLARIFICATION_NEEDED'" class="form-section">
          <h3 class="section-title">澄清配置</h3>
          <a-form-item label="回复内容">
            <a-textarea v-model:value="formState.response" placeholder="请输入澄清回复内容" :rows="3" />
          </a-form-item>
          <div class="clarification-options">
            <div class="section-header">
              <span class="section-title">澄清选项</span>
              <a-button size="small" @click="addClarificationOption">+ 添加选项</a-button>
            </div>
            <div v-for="(option, index) in formState.clarificationOptions" :key="index" class="clarification-item">
              <a-row :gutter="8" align="middle">
                <a-col :span="20">
                  <a-input v-model:value="option.optionText" placeholder="输入澄清选项文本" />
                </a-col>
                <a-col :span="4">
                  <a-button size="small" danger @click="removeClarificationOption(index)">删除</a-button>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>

        <!-- 上下文信息 -->
        <div class="form-section">
          <h3 class="section-title">上下文信息</h3>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="原始查询">
                <a-input v-model:value="formState.originalQuery" placeholder="原始查询，通常与用户问题相同" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合并上下文">
                <a-input v-model:value="formState.mergedContext" placeholder="合并上下文，通常与原始查询相同" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <h3 class="section-title">其他信息</h3>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="难度等级" name="difficultyLevel">
                <a-select v-model:value="formState.difficultyLevel" placeholder="请选择范例的难度">
                  <a-select-option value="Easy">简单</a-select-option>
                  <a-select-option value="Medium">中等</a-select-option>
                  <a-select-option value="Hard">困难</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="备注" name="notes">
                <a-textarea v-model:value="formState.notes" placeholder="请输入对该范例的额外说明" :rows="2" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- JSON 预览 -->
        <div class="form-section">
          <div class="section-header">
            <span class="section-title">JSON 预览</span>
            <a-button size="small" @click="toggleJsonPreview">{{ showJsonPreview ? '隐藏' : '显示' }}</a-button>
          </div>
          <div v-if="showJsonPreview" class="json-preview">
            <a-textarea
              :value="generatedJsonString"
              :rows="15"
              readonly
              style="font-family: 'Courier New', monospace; font-size: 12px;"
            />
          </div>
        </div>
      </a-form>
    </a-modal>

    <!-- 批量添加模态框 -->
    <a-modal v-model:open="isBatchModalVisible" title="批量添加范例" width="90vw" @ok="handleBatchImport" :confirm-loading="batchLoading" :destroy-on-close="true">
       <a-alert message="请确保每一行都填写了用户问题、关联数据集和查询表示。导入时将跳过不完整的行。" type="warning" show-icon class="mb-4" />
      <a-table :data-source="batchListData" :columns="batchColumns" :pagination="false" row-key="id">
        <template #bodyCell="{ column, record }">
          <template v-if="['userQuestion', 'targetQueryRepresentation', 'notes'].includes(column.dataIndex)">
            <a-textarea v-model:value="record[column.dataIndex]" :auto-size="{ minRows: 1, maxRows: 3 }" />
          </template>
          <template v-if="column.dataIndex === 'targetDatasetId'">
            <a-select v-model:value="record.targetDatasetId" style="width: 180px" placeholder="选择数据集" show-search :options="datasetOptions" />
          </template>
          <template v-if="column.dataIndex === 'difficultyLevel'">
            <a-select v-model:value="record.difficultyLevel" style="width: 100px" placeholder="选择难度">
              <a-select-option value="Easy">简单</a-select-option>
              <a-select-option value="Medium">中等</a-select-option>
              <a-select-option value="Hard">困难</a-select-option>
            </a-select>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="removeBatchRow(record.id)">删除</a-button>
          </template>
        </template>
      </a-table>
      <a-button @click="addBatchRow" type="dashed" class="mt-4 w-full"> + 添加一行 </a-button>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { knowledgeApi } from '@/api/knowledge';
import apiClient from '@/api';
import { message, Modal } from 'ant-design-vue';

// --- State ---
const searchTerm = ref('');
const searchPerformed = ref(false);
const exampleList = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

const columns = [
  { title: '用户问题', dataIndex: 'userQuestion', key: 'userQuestion', width: '30%' },
  { title: '关联数据集', dataIndex: 'targetDatasetName', key: 'targetDatasetName', width: '15%' },
  { title: '查询表示', dataIndex: 'targetQueryRepresentation', key: 'targetQueryRepresentation', ellipsis: true },
  { title: '难度', dataIndex: 'difficultyLevel', key: 'difficultyLevel', width: '10%' },
  { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt', width: '15%', customRender: ({ text }) => new Date(text).toLocaleString() },
  { title: '操作', key: 'action', width: '10%' },
];

// --- Datasets and Fields ---
const datasetLoading = ref(false);
const datasetOptions = ref([]);
const fieldsLoading = ref(false);
const currentDatasetFields = ref([]);

// 计算属性：从当前数据集字段中筛选不同类型的字段
const metricOptions = computed(() => {
  return currentDatasetFields.value
    .filter(field => field.semanticType === 'METRIC')
    .map(field => ({ value: field.columnName, label: field.columnName }));
});

const dimensionOptions = computed(() => {
  return currentDatasetFields.value
    .filter(field => field.semanticType === 'DIMENSION')
    .map(field => ({ value: field.columnName, label: field.columnName }));
});

const allFieldOptions = computed(() => {
  return currentDatasetFields.value.map(field => ({ 
    value: field.columnName, 
    label: `${field.columnName} (${field.semanticType})` 
  }));
});

async function fetchDatasets() {
  datasetLoading.value = true;
  try {
    const response = await apiClient.get('/api/admin/knowledge-base/datasets');
    const datasets = response.data;
    datasetOptions.value = datasets.map(d => ({ value: d.id, label: d.datasetName }));
  } catch (error) {
    console.warn('获取数据集列表失败，使用模拟数据:', error);
    const mockDatasets = [
      { id: 1, datasetName: '每日销售汇总' },
      { id: 2, datasetName: '公司月度财务报表' },
      { id: 3, datasetName: '用户地域分布' },
    ];
    datasetOptions.value = mockDatasets.map(d => ({ value: d.id, label: d.datasetName }));
  } finally {
    datasetLoading.value = false;
  }
}

async function fetchDatasetFields(datasetId) {
  if (!datasetId) {
    currentDatasetFields.value = [];
    return;
  }
  
  fieldsLoading.value = true;
  try {
    const response = await apiClient.get(`/api/admin/datasets/${datasetId}`);
    currentDatasetFields.value = response.data.columns || [];
  } catch (error) {
    console.warn('获取数据集字段失败，使用模拟数据:', error);
    // 使用模拟数据
    currentDatasetFields.value = [
      { columnName: '销售额', semanticType: 'METRIC' },
      { columnName: '订单数', semanticType: 'METRIC' },
      { columnName: '城市', semanticType: 'DIMENSION' },
      { columnName: '日期', semanticType: 'DIMENSION' },
      { columnName: '产品', semanticType: 'DIMENSION' },
    ];
  } finally {
    fieldsLoading.value = false;
  }
}

// --- Fetching Data ---
async function fetchData() {
  loading.value = true;
  try {
    const params = {
      page: pagination.current - 1,
      size: pagination.pageSize,
      search: searchTerm.value,
    };
    const response = await knowledgeApi.getExamples(params);
    const { content, totalElements } = response.data;
    exampleList.value = content;
    pagination.total = totalElements;
  } catch (error) {
    message.error('加载范例列表失败');
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  pagination.current = 1;
  searchPerformed.value = !!searchTerm.value;
  fetchData();
}

function handleTableChange(pager) {
  pagination.current = pager.current;
  pagination.pageSize = pager.pageSize;
  fetchData();
}

// 监听搜索框变化
watch(searchTerm, (newValue) => {
  if (!newValue) {
    searchPerformed.value = false;
    pagination.current = 1;
    fetchData();
  }
});

onMounted(() => {
  fetchData();
  fetchDatasets();
});

// --- Form State ---
const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const showJsonPreview = ref(false);

const formState = reactive({
  id: null,
  userQuestion: '',
  targetDatasetId: null,
  intent: 'DATA_QUERY',
  queryType: 'AGGREGATION_QUERY',
  datasetMatchConfidence: 'HIGH',
  metrics: [],
  dimensionsToGroup: [],
  filters: [],
  sortBy: [],
  limit: 10,
  response: '',
  clarificationOptions: [],
  originalQuery: '',
  mergedContext: '',
  difficultyLevel: 'Medium',
  notes: '',
});

const formRules = {
  userQuestion: [{ required: true, message: '请输入用户问题' }],
  targetDatasetId: [{ required: true, message: '请选择关联数据集' }],
  intent: [{ required: true, message: '请选择意图类型' }],
};

// 生成JSON的计算属性
const generatedJsonString = computed(() => {
  const jsonData = generateJSON();
  return JSON.stringify(jsonData, null, 2);
});

function generateJSON() {
  if (formState.intent === 'DATA_QUERY') {
    return {
      intent: formState.intent,
      entities: {
        metrics: formState.metrics,
        dimensions_to_group: formState.dimensionsToGroup,
        filters: formState.filters.filter(f => f.fieldName && f.operator && f.value),
        sort_by: formState.sortBy.filter(s => s.fieldName && s.order),
        limit: formState.limit || 10
      },
      queryType: formState.queryType,
      targetDatasetId: formState.targetDatasetId,
      datasetMatchConfidence: formState.datasetMatchConfidence,
      internal_context: {
        originalQuery: formState.originalQuery || formState.userQuestion,
        mergedContext: formState.mergedContext || formState.userQuestion
      }
    };
  } else if (formState.intent === 'CLARIFICATION_NEEDED') {
    return {
      intent: formState.intent,
      response: formState.response,
      clarificationOptions: formState.clarificationOptions.filter(opt => opt.optionText),
      internal_context: {
        originalQuery: formState.originalQuery || formState.userQuestion,
        mergedContext: formState.mergedContext || formState.userQuestion
      }
    };
  }
}

// --- Form Events ---
function onDatasetChange(datasetId) {
  fetchDatasetFields(datasetId);
  // 清空字段相关的选择
  formState.metrics = [];
  formState.dimensionsToGroup = [];
  formState.filters = [];
  formState.sortBy = [];
}

function onIntentChange(intent) {
  if (intent === 'CLARIFICATION_NEEDED') {
    // 重置数据查询相关字段
    formState.queryType = '';
    formState.datasetMatchConfidence = '';
    formState.metrics = [];
    formState.dimensionsToGroup = [];
    formState.filters = [];
    formState.sortBy = [];
    formState.limit = 10;
  } else if (intent === 'DATA_QUERY') {
    // 重置澄清相关字段
    formState.response = '';
    formState.clarificationOptions = [];
    // 设置默认值
    formState.queryType = 'AGGREGATION_QUERY';
    formState.datasetMatchConfidence = 'HIGH';
  }
}

// --- Filter Management ---
function addFilter() {
  formState.filters.push({
    fieldName: '',
    operator: '=',
    value: ''
  });
}

function removeFilter(index) {
  formState.filters.splice(index, 1);
}

function onOperatorChange(index, operator) {
  const filter = formState.filters[index];
  if (['IN', 'NOT_IN'].includes(operator)) {
    filter.value = [];
  } else if (operator === 'BETWEEN') {
    filter.value = ['', ''];
  } else {
    filter.value = '';
  }
}

// --- Sort Management ---
function addSort() {
  formState.sortBy.push({
    fieldName: '',
    order: 'DESC'
  });
}

function removeSort(index) {
  formState.sortBy.splice(index, 1);
}

// --- Clarification Options ---
function addClarificationOption() {
  formState.clarificationOptions.push({
    optionText: ''
  });
}

function removeClarificationOption(index) {
  formState.clarificationOptions.splice(index, 1);
}

// --- Modal Management ---
function handleAddNew() {
  isEditing.value = false;
  resetFormState();
  isModalVisible.value = true;
}

function handleEdit(record) {
  isEditing.value = true;
  resetFormState();
  loadRecordToForm(record);
  isModalVisible.value = true;
}

function resetFormState() {
  Object.assign(formState, {
    id: null,
    userQuestion: '',
    targetDatasetId: null,
    intent: 'DATA_QUERY',
    queryType: 'AGGREGATION_QUERY',
    datasetMatchConfidence: 'HIGH',
    metrics: [],
    dimensionsToGroup: [],
    filters: [],
    sortBy: [],
    limit: 10,
    response: '',
    clarificationOptions: [],
    originalQuery: '',
    mergedContext: '',
    difficultyLevel: 'Medium',
    notes: '',
  });
}

function loadRecordToForm(record) {
  try {
    const jsonData = typeof record.targetQueryRepresentation === 'string' 
      ? JSON.parse(record.targetQueryRepresentation)
      : record.targetQueryRepresentation;

    formState.id = record.id;
    formState.userQuestion = record.userQuestion;
    formState.targetDatasetId = record.targetDatasetId;
    formState.difficultyLevel = record.difficultyLevel;
    formState.notes = record.notes;

    if (jsonData) {
      formState.intent = jsonData.intent || 'DATA_QUERY';
      
      if (jsonData.intent === 'DATA_QUERY') {
        formState.queryType = jsonData.queryType || 'AGGREGATION_QUERY';
        formState.datasetMatchConfidence = jsonData.datasetMatchConfidence || 'HIGH';
        
        if (jsonData.entities) {
          formState.metrics = jsonData.entities.metrics || [];
          formState.dimensionsToGroup = jsonData.entities.dimensions_to_group || [];
          formState.filters = jsonData.entities.filters || [];
          formState.sortBy = jsonData.entities.sort_by || [];
          formState.limit = jsonData.entities.limit || 10;
        }
      } else if (jsonData.intent === 'CLARIFICATION_NEEDED') {
        formState.response = jsonData.response || '';
        formState.clarificationOptions = jsonData.clarificationOptions || [];
      }

      if (jsonData.internal_context) {
        formState.originalQuery = jsonData.internal_context.originalQuery || '';
        formState.mergedContext = jsonData.internal_context.mergedContext || '';
      }
    }

    // 加载数据集字段
    if (formState.targetDatasetId) {
      fetchDatasetFields(formState.targetDatasetId);
    }
  } catch (error) {
    console.error('解析记录数据失败:', error);
    message.error('解析记录数据失败');
  }
}

async function handleOk() {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    
    const requestData = {
      userQuestion: formState.userQuestion,
      targetDatasetId: formState.targetDatasetId,
      targetQueryRepresentation: JSON.stringify(generateJSON()),
      difficultyLevel: formState.difficultyLevel,
      notes: formState.notes,
    };

    if (isEditing.value) {
      await knowledgeApi.updateExample(formState.id, requestData);
      message.success('更新成功');
    } else {
      await knowledgeApi.createExample(requestData);
      message.success('创建成功');
    }
    isModalVisible.value = false;
    fetchData();
  } catch (error) {
    message.error(isEditing.value ? '更新失败' : '创建失败');
  } finally {
    modalLoading.value = false;
  }
}

function toggleJsonPreview() {
  showJsonPreview.value = !showJsonPreview.value;
}

// --- Delete ---
async function handleDelete(id) {
  try {
    await knowledgeApi.deleteExample(id);
    message.success('删除成功');
    fetchData();
  } catch (error) {
    message.error('删除失败');
  }
}

// --- Batch Create Modal ---
const isBatchModalVisible = ref(false);
const batchLoading = ref(false);
const batchListData = ref([]);
const batchColumns = [
  { title: '用户问题', dataIndex: 'userQuestion', width: '30%' },
  { title: '关联数据集', dataIndex: 'targetDatasetId', width: '20%' },
  { title: '查询表示', dataIndex: 'targetQueryRepresentation', width: '30%' },
  { title: '难度', dataIndex: 'difficultyLevel', width: '10%' },
  { title: '备注', dataIndex: 'notes', width: '10%' },
  { title: '操作', dataIndex: 'action', width: '5%' },
];

function handleBatchAdd() {
  batchListData.value = [{ id: Date.now() }];
  isBatchModalVisible.value = true;
}

function addBatchRow() {
  batchListData.value.push({ id: Date.now() });
}

function removeBatchRow(id) {
  batchListData.value = batchListData.value.filter(item => item.id !== id);
}

async function handleBatchImport() {
  const validRows = batchListData.value.filter(
    row => row.userQuestion && row.targetDatasetId && row.targetQueryRepresentation
  );

  if (validRows.length === 0) {
    message.warn('没有可导入的有效数据行');
    return;
  }

  batchLoading.value = true;
  try {
    const report = await knowledgeApi.createExamplesInBatch(validRows);
    Modal.success({
      title: '批量导入完成',
      content: `成功 ${report.successCount} 条，失败 ${report.failureCount} 条。`,
    });
    isBatchModalVisible.value = false;
    fetchData();
  } catch (error) {
    message.error('批量导入请求失败');
  } finally {
    batchLoading.value = false;
  }
}

// --- Helpers ---
function getDifficultyColor(level) {
  switch (level) {
    case 'Easy': return 'green';
    case 'Medium': return 'orange';
    case 'Hard': return 'red';
    default: return 'default';
  }
}

function highlightText(text, term) {
  if (!term || !text) return text;
  const regex = new RegExp(`(${term})`, 'gi');
  return text.replace(regex, '<span style="background-color: yellow;">$1</span>');
}
</script>

<style scoped>
.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.example-modal .form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  display: block;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-item, .sort-item, .clarification-item {
  margin-bottom: 8px;
}

.json-preview {
  margin-top: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.filter-section, .sort-section, .clarification-options {
  margin-top: 16px;
}
</style> 