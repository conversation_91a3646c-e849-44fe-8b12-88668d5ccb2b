<template>
  <div>
    <a-page-header
      title="查询范例管理"
      sub-title='管理高质量的"问题-答案"范例以提升AI能力'
    />
    <a-card>
      <div class="table-operations">
        <a-input-search
          v-model:value="searchTerm"
          placeholder="按问题语义搜索范例..."
          style="width: 300px"
          @search="onSearch"
          allow-clear
        />
        <a-space>
          <a-button type="primary" @click="handleAddNew">
            新建范例
          </a-button>
        </a-space>
      </div>

      <a-alert v-if="searchPerformed" :message="`为您找到 ${pagination.total} 条关于'${searchTerm}'的相似范例`" type="info" show-icon class="my-4" />

      <a-table
        :columns="columns"
        :data-source="exampleList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userQuestion'">
            <span v-html="highlightText(record.userQuestion, searchPerformed ? searchTerm : '')"></span>
          </template>
          <template v-if="column.key === 'targetDatasetName'">
            <a-tag color="blue">{{ record.targetDatasetName }}</a-tag>
          </template>
          <template v-if="column.key === 'difficultyLevel'">
            <a-tag :color="getDifficultyColor(record.difficultyLevel)">{{ record.difficultyLevel || '未指定' }}</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm title="确定删除这个范例吗?" @confirm="handleDelete(record.id)">
                <a style="color: red">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 单条新建/编辑模态框 -->
    <a-modal
      v-model:open="isModalVisible"
      :title="isEditing ? '编辑范例' : '新建范例'"
      @ok="handleOk"
      :confirm-loading="modalLoading"
      width="1200px"
      :destroy-on-close="true"
      class="example-modal"
    >
      <QueryExampleForm 
        ref="exampleFormRef"
        @form-data-change="onFormDataChange"
      />
    </a-modal>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import { knowledgeApi } from '@/api/knowledge';
import apiClient from '@/api';
import { message } from 'ant-design-vue';
import QueryExampleForm from './QueryExampleForm.vue';

// --- State ---
const searchTerm = ref('');
const searchPerformed = ref(false);
const exampleList = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

const columns = [
  { title: '用户问题', dataIndex: 'userQuestion', key: 'userQuestion', width: '30%' },
  { title: '关联数据集', dataIndex: 'targetDatasetName', key: 'targetDatasetName', width: '15%' },
  { title: '查询表示', dataIndex: 'targetQueryRepresentation', key: 'targetQueryRepresentation', ellipsis: true },
  { title: '难度', dataIndex: 'difficultyLevel', key: 'difficultyLevel', width: '10%' },
  { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt', width: '15%', customRender: ({ text }) => new Date(text).toLocaleString() },
  { title: '操作', key: 'action', width: '10%' },
];

// --- Form Management ---
const datasetLoading = ref(false);
const datasetOptions = ref([]);

// --- Fetching Data ---
async function fetchData() {
  loading.value = true;
  try {
    const params = {
      page: pagination.current - 1,
      size: pagination.pageSize,
      search: searchTerm.value,
    };
    const response = await knowledgeApi.getExamples(params);
    const { content, totalElements } = response.data;
    exampleList.value = content;
    pagination.total = totalElements;
  } catch (error) {
    message.error('加载范例列表失败');
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  pagination.current = 1;
  searchPerformed.value = !!searchTerm.value;
  fetchData();
}

function handleTableChange(pager) {
  pagination.current = pager.current;
  pagination.pageSize = pager.pageSize;
  fetchData();
}

// 监听搜索框变化
watch(searchTerm, (newValue) => {
  if (!newValue) {
    searchPerformed.value = false;
    pagination.current = 1;
    fetchData();
  }
});

onMounted(() => {
  fetchData();
});

// --- Single Create / Edit Modal ---
const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const exampleFormRef = ref();
const currentFormData = ref({});

function handleAddNew() {
  isEditing.value = false;
  isModalVisible.value = true;
  // 等待下一个tick再重置表单，确保组件已挂载
  nextTick(() => {
    if (exampleFormRef.value) {
      exampleFormRef.value.reset();
    }
  });
}

function handleEdit(record) {
  isEditing.value = true;
  isModalVisible.value = true;
  // 等待下一个tick再加载数据，确保组件已挂载
  nextTick(() => {
    if (exampleFormRef.value) {
      exampleFormRef.value.loadData(record);
    }
  });
}

function onFormDataChange(formData) {
  currentFormData.value = formData;
}

async function handleOk() {
  try {
    await exampleFormRef.value.validate();
    modalLoading.value = true;
    
    const requestData = {
      userQuestion: currentFormData.value.userQuestion,
      targetDatasetId: currentFormData.value.targetDatasetId,
      targetQueryRepresentation: currentFormData.value.targetQueryRepresentation,
      difficultyLevel: currentFormData.value.difficultyLevel,
      notes: currentFormData.value.notes,
    };

    if (isEditing.value) {
      await knowledgeApi.updateExample(currentFormData.value.id, requestData);
      message.success('更新成功');
    } else {
      await knowledgeApi.createExample(requestData);
      message.success('创建成功');
    }
    isModalVisible.value = false;
    fetchData();
  } catch (error) {
    message.error(isEditing.value ? '更新失败' : '创建失败');
  } finally {
    modalLoading.value = false;
  }
}

// --- Delete ---
async function handleDelete(id) {
  try {
    await knowledgeApi.deleteExample(id);
    message.success('删除成功');
    fetchData();
  } catch (error) {
    message.error('删除失败');
  }
}



// --- Helpers ---
function getDifficultyColor(level) {
  switch (level) {
    case 'Easy': return 'green';
    case 'Medium': return 'orange';
    case 'Hard': return 'red';
    default: return 'default';
  }
}

function highlightText(text, term) {
  if (!term || !text) return text;
  const regex = new RegExp(`(${term})`, 'gi');
  return text.replace(regex, '<span style="background-color: yellow;">$1</span>');
}
</script>

<style scoped>
.table-operations {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.example-modal {
  max-height: 90vh;
}

.example-modal :deep(.ant-modal) {
  border-radius: 12px;
  overflow: hidden;
}

.example-modal :deep(.ant-modal-header) {
  background: #1890ff;
  color: white;
  border-bottom: none;
  padding: 20px 24px;
}

.example-modal :deep(.ant-modal-title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.example-modal :deep(.ant-modal-close) {
  color: white;
  opacity: 0.8;
  top: 20px;
  right: 20px;
}

.example-modal :deep(.ant-modal-close:hover) {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.example-modal :deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
  background: #f8f9fa;
}

.example-modal :deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
  padding: 16px 24px;
  background: #ffffff;
}

.example-modal :deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
  min-width: 80px;
  height: 36px;
}

.example-modal :deep(.ant-btn-primary) {
  background: #1890ff;
  border: 1px solid #1890ff;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

.example-modal :deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.example-modal :deep(.ant-btn-default) {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  color: #666;
}

.example-modal :deep(.ant-btn-default:hover) {
  background: #f5f5f5;
  border-color: #40a9ff;
  color: #1890ff;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 2px solid #e8e8e8;
  color: #262626;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f9ff;
}

:deep(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
  border: none;
  padding: 2px 8px;
}

/* 搜索框样式 */
:deep(.ant-input-search) {
  border-radius: 8px;
}

:deep(.ant-input-search .ant-input) {
  border-radius: 8px 0 0 8px;
}

:deep(.ant-input-search .ant-input-search-button) {
  border-radius: 0 8px 8px 0;
  background: #1890ff;
  border: 1px solid #1890ff;
}

:deep(.ant-input-search .ant-input-search-button:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 按钮组样式 */
:deep(.ant-space) {
  gap: 12px !important;
}

:deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.ant-btn-primary) {
  background: #1890ff;
  border: 1px solid #1890ff;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

:deep(.ant-btn-primary:hover) {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

:deep(.ant-btn-default) {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  color: #666;
}

:deep(.ant-btn-default:hover) {
  background: #f5f5f5;
  border-color: #40a9ff;
  color: #1890ff;
}

/* 分页样式 */
:deep(.ant-pagination) {
  margin-top: 20px;
  text-align: center;
}

:deep(.ant-pagination-item) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

:deep(.ant-pagination-item-active) {
  background: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-pagination-item-active a) {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-operations {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .example-modal :deep(.ant-modal) {
    margin: 10px;
    max-width: calc(100vw - 20px);
  }
}
</style> 