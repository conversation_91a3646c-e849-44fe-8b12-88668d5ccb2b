<template>
  <div>
    <a-page-header
        title="数据集管理"
        sub-title="定义可供AI查询的逻辑数据集"
    >

    </a-page-header>
    <a-card>
      <div class="table-operations" style="margin-bottom: 16px;">
        <a-button type="primary" @click="handleAddNew">
          <PlusOutlined />
          新建数据集
        </a-button>
      </div>
      
      <a-table 
        :columns="columns" 
        :data-source="datasetList" 
        :loading="loading" 
        :pagination="pagination" 
        row-key="id" 
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'baseObjectType'">
            <a-tag :color="getTypeColor(record.baseObjectType)">
              {{ getTypeName(record.baseObjectType) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'updatedAt'">
            {{ formatDate(record.updatedAt) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <router-link :to="`/model/datasets/${record.id}/edit`">
                <a-button type="link" size="small">编辑</a-button>
              </router-link>
              <a-popconfirm 
                title="确定删除吗？这将一并删除所有字段配置。" 
                @confirm="handleDelete(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 新建/编辑数据集模态框 -->
    <a-modal 
      v-model:open="isModalVisible" 
      :title="isEditing ? '编辑数据集' : '新建数据集'" 
      @ok="handleOk" 
      :confirm-loading="modalLoading" 
      width="700px"
      :mask-closable="false"
      :footer="null"
    >
      <!-- 创建表单 -->
      <a-form ref="formRef" :model="formState" layout="vertical" v-if="!isEditing">
        <a-steps :current="currentStep" style="margin-bottom: 24px;">
          <a-step title="基本信息" />
          <a-step title="选择来源" />
          <a-step title="字段配置" />
        </a-steps>
        
        <div class="steps-content">
          <!-- 第一步：基本信息 -->
          <div v-show="currentStep === 0">
            <a-form-item 
              label="数据集名称" 
              name="datasetName" 
              :rules="[{ required: true, message: '请输入数据集名称' }]"
            >
              <a-input 
                v-model:value="formState.datasetName" 
                placeholder="请输入数据集名称"
              />
            </a-form-item>
            <a-form-item label="描述" name="description">
              <a-textarea 
                v-model:value="formState.description" 
                placeholder="请输入数据集描述"
                :rows="3"
              />
            </a-form-item>
          </div>
          
          <!-- 第二步：选择来源 -->
          <div v-show="currentStep === 1">
                         <a-form-item 
               label="数据源" 
               name="dataSourceId" 
               :rules="[{ required: true, message: '请选择数据源' }]"
             >
               <a-select 
                 v-model:value="formState.dataSourceId" 
                 placeholder="请选择数据源"
                 @change="handleDataSourceChange"
                 :loading="dataSourceLoading"
                 :not-found-content="dataSourceLoading ? '加载中...' : '暂无数据源，请先创建数据源'"
               >
                 <a-select-option 
                   v-for="source in dataSources" 
                   :key="source.id" 
                   :value="source.id"
                 >
                   {{ getDataSourceDisplayName(source) }}
                 </a-select-option>
               </a-select>
             </a-form-item>
            
            <a-form-item 
              label="来源类型" 
              name="baseObjectType" 
              :rules="[{ required: true, message: '请选择来源类型' }]"
            >
              <a-radio-group v-model:value="formState.baseObjectType" @change="handleTypeChange">
                <a-radio value="TABLE">物理表/视图</a-radio>
                <a-radio value="QUERY">自定义SQL</a-radio>
              </a-radio-group>
            </a-form-item>
            
                         <a-form-item 
               v-if="formState.baseObjectType === 'TABLE'" 
               label="表/视图名称" 
               name="sourceDefinition" 
               :rules="[{ required: true, message: '请选择表或视图' }]"
             >
                             <a-select 
                 v-model:value="formState.sourceDefinition" 
                 :placeholder="availableTables.length > 0 ? '请选择表或视图' : '正在加载表列表...'"
                 :loading="tablesLoading"
                 show-search
                 :filter-option="filterTableOption"
                 :not-found-content="tablesLoading ? '加载中...' : '暂无可用表，请检查数据源连接'"
               >
                 <a-select-option 
                   v-for="table in availableTables" 
                   :key="table.name" 
                   :value="table.name"
                 >
                   {{ table.name }} 
                   <span style="color: #999;">({{ table.type }})</span>
                   <span v-if="table.comment" style="color: #666; margin-left: 8px;">- {{ table.comment }}</span>
                 </a-select-option>
               </a-select>
            </a-form-item>
            
            <a-form-item 
              v-if="formState.baseObjectType === 'QUERY'" 
              label="SQL脚本" 
              name="sourceDefinition" 
              :rules="[{ required: true, message: '请输入SQL脚本' }]"
            >
              <a-textarea 
                v-model:value="formState.sourceDefinition" 
                placeholder="请输入完整的SQL查询语句"
                :rows="8"
                style="font-family: 'Courier New', monospace;"
              />
            </a-form-item>
          </div>
          
          <!-- 第三步：字段配置 -->
          <div v-show="currentStep === 2">
            <div v-if="loadingColumns" style="text-align: center; padding: 40px;">
              <a-spin size="large" />
              <div style="margin-top: 16px;">正在加载字段信息...</div>
            </div>
            <div v-else-if="previewColumns.length > 0">
              <div style="margin-bottom: 16px;">
                <div style="color: #666; margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center;">
                  <span>请配置需要对AI暴露的字段，系统已根据数据类型智能推荐语义类型。</span>
                  <a-tag color="blue">
                    共 {{ previewColumns.length }} 个字段，已启用 {{ previewColumns.filter(col => col.enabled).length }} 个
                  </a-tag>
                </div>
                <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px; padding: 8px; font-size: 12px; color: #52c41a; margin-bottom: 8px;">
                  <strong>提示：</strong>您可以取消勾选不需要的字段，修改业务名称和语义类型。建议将数值型字段设为"指标"，文本型字段设为"维度"。
                </div>
              </div>
                             <a-table 
                 :columns="columnPreviewColumns"
                 :data-source="previewColumns"
                 :pagination="false"
                 row-key="technicalName"
                 size="small"
                 :scroll="{ x: 620, y: 400 }"
                 style="border: 1px solid #f0f0f0;"
               >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'enabled'">
                    <a-checkbox v-model:checked="record.enabled" />
                  </template>
                  <template v-if="column.key === 'columnName'">
                    <a-input 
                      v-model:value="record.columnName" 
                      placeholder="请输入业务名称"
                      size="small"
                    />
                  </template>
                  <template v-if="column.key === 'semanticType'">
                    <a-select 
                      v-model:value="record.semanticType" 
                      size="small"
                      style="width: 100%;"
                    >
                      <a-select-option value="METRIC">
                        <a-tag color="orange" size="small">指标</a-tag>
                      </a-select-option>
                      <a-select-option value="DIMENSION">
                        <a-tag color="blue" size="small">普通维度</a-tag>
                      </a-select-option>
                      <a-select-option value="TIME_DIMENSION">
                        <a-tag color="green" size="small">时间维度</a-tag>
                      </a-select-option>
                      <a-select-option value="GEO_DIMENSION">
                        <a-tag color="purple" size="small">地理维度</a-tag>
                      </a-select-option>
                    </a-select>
                  </template>
                </template>
              </a-table>
            </div>
            <div v-else style="text-align: center; padding: 40px; color: #999;">
              暂无字段信息
            </div>
          </div>
        </div>
        
        <div class="steps-action">
          <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
          <a-button 
            v-if="currentStep < 2" 
            type="primary" 
            @click="nextStep"
            :disabled="!canGoNext"
            :loading="currentStep === 1 && loadingColumns"
          >
            {{ currentStep === 1 ? '加载字段' : '下一步' }}
          </a-button>
          <a-button 
            v-if="currentStep === 2" 
            type="primary" 
            @click="handleOk"
            :loading="modalLoading"
            :disabled="!canCreateDataset"
          >
            创建数据集
          </a-button>
          <a-button @click="handleModalClose">取消</a-button>
        </div>
      </a-form>
      
             <!-- 编辑表单 -->
       <a-form ref="editFormRef" :model="formState" layout="vertical" v-else>
         <a-form-item 
           label="数据集名称" 
           name="datasetName" 
           :rules="[{ required: true, message: '请输入数据集名称' }]"
         >
           <a-input v-model:value="formState.datasetName" />
         </a-form-item>
         <a-form-item label="描述" name="description">
           <a-textarea v-model:value="formState.description" :rows="3" />
         </a-form-item>
         
         <div class="edit-form-action">
           <a-button @click="handleModalClose">取消</a-button>
           <a-button 
             type="primary" 
             @click="handleOk"
             :loading="modalLoading"
           >
             保存
           </a-button>
         </div>
       </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { modelApi } from '../../api/model';
import { getTypeName, getTypeColor } from '../../utils/dataset-helper';
import { formatDate } from '../../utils/date-formatter';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const datasetList = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
});

// 模态框相关
const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const currentStep = ref(0);
const formRef = ref();
const editFormRef = ref();
const formState = reactive({
  datasetName: '',
  description: '',
  dataSourceId: null,
  baseObjectType: 'TABLE',
  sourceDefinition: ''
});

// 数据源相关
const dataSources = ref([]);
const dataSourceLoading = ref(false);
const availableTables = ref([]);
const tablesLoading = ref(false);
const editingRecord = ref(null);

// 字段配置相关
const previewColumns = ref([]);
const loadingColumns = ref(false);



// 表格列定义
const columns = [
  {
    title: '数据集名称',
    dataIndex: 'datasetName',
    key: 'datasetName',
    width: 200,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '数据源',
    dataIndex: 'sourceName',
    key: 'sourceName',
    width: 150,
  },
  {
    title: '来源类型',
    key: 'baseObjectType',
    width: 100,
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 160,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
  },
];

// 字段预览表格列定义
const columnPreviewColumns = [
  {
    title: '启用',
    key: 'enabled',
    width: 70,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '物理名称',
    dataIndex: 'technicalName',
    key: 'technicalName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '业务名称',
    key: 'columnName',
    width: 160,
  },
  {
    title: '语义类型',
    key: 'semanticType',
    width: 120,
    align: 'center',
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    key: 'dataType',
    width: 120,
    align: 'center',
  },
];

// 计算属性
const canGoNext = computed(() => {
  if (currentStep.value === 0) {
    return formState.datasetName.trim() !== '';
  }
  if (currentStep.value === 1) {
    return formState.dataSourceId !== null && 
           formState.baseObjectType !== '' && 
           formState.sourceDefinition.trim() !== '';
  }
  return false;
});

const canCreateDataset = computed(() => {
  const hasEnabledColumns = previewColumns.value.some(col => col.enabled);
  return formState.datasetName.trim() !== '' && 
         formState.dataSourceId !== null && 
         formState.baseObjectType !== '' && 
         formState.sourceDefinition.trim() !== '' &&
         hasEnabledColumns;
});

// 生命周期
onMounted(() => {
  fetchData();
  fetchDataSources();
});

// 方法
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current - 1,
      size: pagination.pageSize,
    };
    const response = await modelApi.getDatasets(params);
    datasetList.value = response.data.content || [];
    pagination.total = response.data.totalElements || 0;
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    message.error('获取数据集列表失败');
  } finally {
    loading.value = false;
  }
};

const fetchDataSources = async () => {
  dataSourceLoading.value = true;
  try {
    const response = await modelApi.getDataSources();
    if (response.data) {
      if (response.data.content && Array.isArray(response.data.content)) {
        dataSources.value = response.data.content;
      } else if (Array.isArray(response.data)) {
        dataSources.value = response.data;
      } else {
        dataSources.value = [];
      }
    } else {
      dataSources.value = [];
    }
  } catch (error) {
    console.error('获取数据源列表失败:', error);
    message.error('获取数据源列表失败');
    dataSources.value = [];
  } finally {
    dataSourceLoading.value = false;
  }
};

const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

const handleAddNew = () => {
  resetForm();
  isEditing.value = false;
  currentStep.value = 0;
  isModalVisible.value = true;
};

const handleEdit = (record) => {
  resetForm();
  editingRecord.value = record;
  formState.datasetName = record.datasetName;
  formState.description = record.description;
  isEditing.value = true;
  isModalVisible.value = true;
};

const handleDelete = async (id) => {
  try {
    await modelApi.deleteDataset(id);
    message.success('删除成功');
    fetchData();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

const handleOk = async () => {
  try {
    const form = isEditing.value ? editFormRef.value : formRef.value;
    if (form) {
        await form.validate();
    }

    modalLoading.value = true;
    
    if (isEditing.value) {
      // 编辑模式的逻辑
      const updateData = {
        datasetName: formState.datasetName,
        description: formState.description,
      };
      await modelApi.updateDataset(editingRecord.value.id, updateData);
      message.success('数据集更新成功');
      isModalVisible.value = false;
      fetchData(); // 刷新列表
    } else {
      // 创建模式的逻辑
      const enabledColumns = previewColumns.value.filter(col => col.enabled).map(col => ({
        ...col,
        status: col.enabled ? "active" : "inactive"
      }));
      const createData = {
        datasetName: formState.datasetName,
        description: formState.description,
        dataSourceId: formState.dataSourceId,
        baseObjectType: formState.baseObjectType,
        sourceDefinition: formState.sourceDefinition,
        columns: enabledColumns
      };
      
      const response = await modelApi.createDataset(createData);
      
      // 使用延时消息和回调进行跳转
      message.success('数据集创建成功，即将跳转到编辑页面...', 2, () => {
        router.push(`/model/datasets/${response.data.id}/edit`);
        isModalVisible.value = false;
        resetForm(); // 在跳转后重置表单
      });
    }
  } catch (error) {
    console.error('操作失败:', error);
    // 阻止因表单校验失败而关闭模态框
    if (error && error.errorFields) {
        // antd form validation aails, do nothing here.
    } else {
        message.error(error.response?.data?.message || '操作失败，请重试');
    }
  } finally {
    // 仅在非创建成功的情况下设置 loading 为 false
    // 因为创建成功后页面会跳转，不需要再改变 loading 状态
    if (isEditing.value) {
        modalLoading.value = false;
    }
  }
};

const nextStep = async () => {
  try {
    if (currentStep.value === 0) {
      // 第一步：验证基本信息并检查名称是否重复
      await formRef.value.validateFields(['datasetName']);
      
      // 检查数据集名称是否已存在
      if (formState.datasetName.trim()) {
        try {
          const response = await modelApi.checkDatasetName(formState.datasetName.trim());
          if (response.data.exists) {
            message.error(response.data.message || '数据集名称已存在，请使用其他名称');
            return; // 阻止进入下一步
          }
        } catch (error) {
          console.error('检查数据集名称失败:', error);
          message.error('检查数据集名称失败，请稍后重试');
          return; // 阻止进入下一步
        }
      }
      
      currentStep.value++;
    } else if (currentStep.value === 1) {
      await formRef.value.validateFields(['dataSourceId', 'baseObjectType', 'sourceDefinition']);
      // 加载字段信息
      await loadPreviewColumns();
      currentStep.value++;
    }
  } catch (error) {
    // 验证失败，不执行下一步
    console.error('验证失败:', error);
  }
};

const prevStep = () => {
  currentStep.value--;
};

const handleDataSourceChange = async (dataSourceId) => {
  if (dataSourceId && formState.baseObjectType === 'TABLE') {
    await fetchTables(dataSourceId);
  }
  formState.sourceDefinition = '';
};

const handleTypeChange = async () => {
  formState.sourceDefinition = '';
  if (formState.baseObjectType === 'TABLE' && formState.dataSourceId) {
    await fetchTables(formState.dataSourceId);
  }
};

const fetchTables = async (dataSourceId) => {
  tablesLoading.value = true;
  try {
    const response = await modelApi.getTables(dataSourceId);
    availableTables.value = response.data || [];
  } catch (error) {
    console.error('获取表列表失败:', error);
    message.error('获取表列表失败');
    availableTables.value = [];
  } finally {
    tablesLoading.value = false;
  }
};

const filterTableOption = (input, option) => {
  return option.children[0].children.toLowerCase().includes(input.toLowerCase());
};

const loadPreviewColumns = async () => {
  if (!formState.dataSourceId || !formState.sourceDefinition.trim()) {
    message.error('请先选择数据源和来源定义');
    return;
  }

  loadingColumns.value = true;
  try {
    const response = await modelApi.previewColumns({
      dataSourceId: formState.dataSourceId,
      baseObjectType: formState.baseObjectType,
      sourceDefinition: formState.sourceDefinition
    });
    
    // 转换status字段为enabled字段供前端使用
    previewColumns.value = (response.data || []).map(column => ({
      ...column,
      enabled: column.status === "active"
    }));
  } catch (error) {
    console.error('加载字段预览失败:', error);
    message.error('加载字段预览失败: ' + (error.response?.data?.message || error.message));
    previewColumns.value = [];
  } finally {
    loadingColumns.value = false;
  }
};

const resetForm = () => {
  Object.assign(formState, {
    datasetName: '',
    description: '',
    dataSourceId: null,
    baseObjectType: 'TABLE',
    sourceDefinition: ''
  });
  availableTables.value = [];
  previewColumns.value = [];
  editingRecord.value = null;
};

const handleModalClose = () => {
  isModalVisible.value = false;
  resetForm();
  currentStep.value = 0;
};

const getDataSourceDisplayName = (source) => {
  return `${source.sourceName} (${source.databaseType})`;
};
</script>

<style scoped>
.steps-content {
  margin-top: 16px;
  margin-bottom: 16px;
}

.steps-action,
.edit-form-action {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style> 