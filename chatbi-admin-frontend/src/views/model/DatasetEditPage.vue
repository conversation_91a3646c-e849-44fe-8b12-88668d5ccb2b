<template>
  <div>
    <!-- 顶部导航 -->
    <a-page-header 
      :title="datasetDetails?.datasetName || '加载中...'" 
      :sub-title="datasetDetails?.description" 
      @back="() => router.back()"
    >
      <template #tags>
        <a-tag v-if="datasetDetails?.baseObjectType" :color="getTypeColor(datasetDetails.baseObjectType)">
          {{ getTypeName(datasetDetails.baseObjectType) }}
        </a-tag>
      </template>
    </a-page-header>

    <!-- 上半部分：数据集基本信息 -->
    <a-card title="基本信息" style="margin-bottom: 24px;">
      <a-form 
        ref="basicFormRef" 
        :model="basicFormState" 
        layout="vertical"
        style="max-width: 600px;"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item 
              label="数据集名称" 
              name="datasetName" 
              :rules="[{ required: true, message: '请输入数据集名称' }]"
            >
              <a-input 
                v-model:value="basicFormState.datasetName" 
                placeholder="请输入数据集名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="数据源">
              <a-input 
                :value="datasetDetails?.sourceName" 
                disabled
                style="color: #666;"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="basicFormState.description" 
            placeholder="请输入数据集描述"
            :rows="3"
          />
        </a-form-item>
        <a-form-item>
          <a-button 
            type="primary" 
            @click="handleSaveBasicInfo"
            :loading="savingBasicInfo"
          >
            保存基本信息
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 下半部分：字段与指标配置 -->
    <a-card title="字段与指标配置">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 页卡一：全部字段 -->
        <a-tab-pane key="all" tab="全部字段">
          <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
            <div>
              <a-tag color="blue">共 {{ allColumns.length }} 个字段</a-tag>
              <span style="margin-left: 8px; color: #666;">
                启用字段：{{ enabledColumns.length }} 个
              </span>
            </div>
          </div>
          <a-table 
            :columns="allColumnsConfig" 
            :data-source="allColumns" 
            row-key="id" 
            :pagination="false" 
            size="middle"
            :scroll="{ x: 1500 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'enabled'">
                <a-switch 
                  v-model:checked="record.enabled" 
                  @change="(checked) => handleToggleColumn(record, checked)"
                />
              </template>
              <template v-if="column.key === 'semanticType'">
                <a-tag :color="getSemanticTypeColor(record.semanticType)" v-if="record.semanticType">
                  {{ getSemanticTypeName(record.semanticType) }}
                </a-tag>
                <span v-else style="color: #999;">未设置</span>
              </template>
              <template v-if="column.key === 'isComputed'">
                <a-tag v-if="record.isComputed" color="green" size="small">计算字段</a-tag>
                <a-tag v-else color="blue" size="small">物理字段</a-tag>
              </template>
              <template v-if="column.key === 'synonyms'">
                <a-tag v-for="syn in record.synonyms" :key="syn" color="purple">{{ syn }}</a-tag>
                <span v-if="!record.synonyms || record.synonyms.length === 0" style="color: #999;">无</span>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="handleEditColumn(record)">编辑</a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 页卡二：实体字段 -->
        <a-tab-pane key="physical" tab="实体字段">
          <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
            <div>
              <a-tag color="blue">共 {{ physicalColumns.length }} 个字段</a-tag>
            </div>
            <a-button 
              type="primary" 
              @click="handleSyncColumns" 
              :loading="syncing"
            >
              <SyncOutlined />
              从数据源同步
            </a-button>
          </div>
          <a-table 
            :columns="physicalColumnsConfig" 
            :data-source="physicalColumns" 
            row-key="id" 
            :pagination="false" 
            size="middle"
            :scroll="{ x: 1500 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'enabled'">
                <a-switch 
                  v-model:checked="record.enabled" 
                  @change="(checked) => handleToggleColumn(record, checked)"
                />
              </template>
              <template v-if="column.key === 'semanticType'">
                <a-tag :color="getSemanticTypeColor(record.semanticType)" v-if="record.semanticType">
                  {{ getSemanticTypeName(record.semanticType) }}
                </a-tag>
                <span v-else style="color: #999;">未设置</span>
              </template>
              <template v-if="column.key === 'synonyms'">
                <a-tag v-for="syn in record.synonyms" :key="syn" color="purple">{{ syn }}</a-tag>
                <span v-if="!record.synonyms || record.synonyms.length === 0" style="color: #999;">无</span>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="handleEditColumn(record)">编辑</a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 页卡三：计算指标 -->
        <a-tab-pane key="computed" tab="计算指标">
          <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
            <div>
              <a-tag color="green">共 {{ computedColumns.length }} 个指标</a-tag>
            </div>
            <a-button 
              type="primary" 
              @click="handleAddComputedColumn"
            >
              <PlusOutlined />
              添加计算指标
            </a-button>
          </div>
          <a-table 
            :columns="computedColumnsConfig" 
            :data-source="computedColumns" 
            row-key="id" 
            :pagination="false" 
            size="middle"
            :scroll="{ x: 1500 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'enabled'">
                <a-switch 
                  v-model:checked="record.enabled" 
                  @change="(checked) => handleToggleColumn(record, checked)"
                />
              </template>
              <template v-if="column.key === 'dataType'">
                <a-tag color="cyan">{{ record.dataType }}</a-tag>
              </template>
              <template v-if="column.key === 'synonyms'">
                <a-tag v-for="syn in record.synonyms" :key="syn" color="purple">{{ syn }}</a-tag>
                <span v-if="!record.synonyms || record.synonyms.length === 0" style="color: #999;">无</span>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleEditColumn(record)">编辑</a-button>
                  <a-popconfirm 
                    title="确定删除这个计算指标吗？" 
                    @confirm="handleDeleteComputedColumn(record)"
                  >
                    <a-button type="link" size="small" danger>删除</a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 字段编辑模态框 -->
    <a-modal 
      v-model:open="isColumnModalVisible" 
      :title="isEditingComputedColumn ? '编辑计算指标' : '编辑字段配置'" 
      @ok="handleColumnOk" 
      :confirm-loading="columnModalLoading" 
      width="600px"
    >
      <a-form 
        ref="columnFormRef" 
        :model="columnFormState" 
        layout="vertical"
      >
        <a-form-item 
          :label="isEditingComputedColumn ? '表达式' : '字段名称'" 
          name="technicalNameOrExpression"
        >
          <a-input 
            v-model:value="columnFormState.technicalNameOrExpression" 
            disabled
            style="color: #666;"
          />
        </a-form-item>
        
        <a-form-item 
          :label="isEditingComputedColumn ? '指标名称' : '业务名称'" 
          name="columnName" 
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input 
            v-model:value="columnFormState.columnName" 
            placeholder="请输入业务名称或指标名称"
          />
        </a-form-item>
        
        <a-form-item 
          label="字段类型" 
          name="isComputed"
          :rules="[
            { required: true, message: '请选择字段类型' },
            { validator: validateFieldType }
          ]"
        >
          <a-select 
            v-model:value="formIsComputed" 
            placeholder="请选择字段类型"
            @change="() => columnFormRef?.validate(['isComputed'])"
          >
            <a-select-option value="false">
              <a-tag color="blue" size="small">物理字段</a-tag>
              <span style="margin-left: 8px;">来自数据库表的实际字段</span>
            </a-select-option>
            <a-select-option value="true">
              <a-tag color="green" size="small">计算字段</a-tag>
              <span style="margin-left: 8px;">通过SQL表达式计算得出</span>
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item 
          label="数据类型" 
          name="dataType"
          :rules="[{ required: true, message: '请选择数据类型' }]"
        >
          <a-select 
            v-model:value="columnFormState.dataType" 
            placeholder="请选择数据类型"
          >
            <a-select-option value="NUMBER">数值 (NUMBER)</a-select-option>
            <a-select-option value="STRING">文本 (STRING)</a-select-option>
            <a-select-option value="DATETIME">日期时间 (DATETIME)</a-select-option>
            <a-select-option value="DATE">日期 (DATE)</a-select-option>
            <a-select-option value="TIMESTAMP">时间戳 (TIMESTAMP)</a-select-option>
            <a-select-option value="BOOLEAN">布尔 (BOOLEAN)</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="columnFormState.description" 
            placeholder="请输入字段的详细描述，帮助AI理解字段含义"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item 
          label="语义类型" 
          name="semanticType"
          :rules="[{ required: true, message: '请选择语义类型' }]"
        >
          <a-select 
            v-model:value="columnFormState.semanticType" 
            placeholder="请选择语义类型"
          >
            <a-select-option value="METRIC">
              <a-tag color="orange">指标</a-tag>
              <span style="margin-left: 8px;">可聚合的数值，如"销售额"</span>
            </a-select-option>
            <a-select-option value="DIMENSION">
              <a-tag color="blue">普通维度</a-tag>
              <span style="margin-left: 8px;">分类或文本，如"商品品类"</span>
            </a-select-option>
            <a-select-option value="TIME_DIMENSION">
              <a-tag color="green">时间维度</a-tag>
              <span style="margin-left: 8px;">专门用于标识日期的维度，如"下单日期"</span>
            </a-select-option>
            <a-select-option value="GEO_DIMENSION">
              <a-tag color="purple">地理维度</a-tag>
              <span style="margin-left: 8px;">专门用于标识地理位置的维度，如"城市"</span>
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="同义词" name="synonyms">
          <a-select 
            v-model:value="columnFormState.synonyms" 
            mode="tags" 
            placeholder="输入同义词后按回车确认，帮助AI理解用户的多种表达方式"
            :token-separators="[',', '，']"
          />
          <div style="margin-top: 4px; color: #666; font-size: 12px;">
            例如：销售额的同义词可以是"GMV"、"成交额"、"营业额"等
          </div>
        </a-form-item>
        
        <a-form-item label="关键属性">
          <a-space direction="vertical">
            <a-checkbox v-model:checked="columnFormState.filterable">
              <FilterOutlined style="margin-right: 4px;" />
              可筛选 - 该字段可以用作查询条件
            </a-checkbox>
            <a-checkbox v-model:checked="columnFormState.groupable">
              <GroupOutlined style="margin-right: 4px;" />
              可分组 - 该字段可以用作分组依据
            </a-checkbox>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 计算指标创建模态框 -->
    <a-modal 
      v-model:open="isComputedColumnModalVisible" 
      title="添加计算指标" 
      @ok="handleComputedColumnOk" 
      :confirm-loading="computedColumnModalLoading" 
      width="700px"
    >
      <a-form 
        ref="computedColumnFormRef" 
        :model="computedColumnFormState" 
        layout="vertical"
      >
        <a-form-item 
          label="指标名称" 
          name="columnName" 
          :rules="[{ required: true, message: '请输入指标名称' }]"
        >
          <a-input 
            v-model:value="computedColumnFormState.columnName" 
            placeholder="例如：客单价、转化率等"
          />
        </a-form-item>
        
        <a-form-item 
          label="SQL表达式" 
          name="expression" 
          :rules="[{ required: true, message: '请输入SQL表达式' }]"
        >
          <a-textarea 
            v-model:value="computedColumnFormState.expression" 
            placeholder="例如：sale_amount / order_count"
            :rows="4"
            style="font-family: 'Courier New', monospace;"
            @change="onExpressionChange"
          />
          <div style="margin-top: 8px;">
            <a-button 
              type="dashed" 
              @click="handleValidateExpression"
              :loading="validatingExpression"
              size="small"
            >
              校验表达式
            </a-button>
            <span 
              v-if="validationResult.message" 
              :style="{ marginLeft: '8px', color: validationResult.color }"
            >
              {{ validationResult.message }}
            </span>
          </div>
        </a-form-item>
        
        <a-form-item 
          label="数据类型" 
          name="dataType" 
          :rules="[{ required: true, message: '请选择返回数据类型' }]"
        >
          <a-select 
            v-model:value="computedColumnFormState.dataType" 
            placeholder="请选择表达式预期的返回数据类型"
          >
            <a-select-option value="NUMBER">数值 (NUMBER)</a-select-option>
            <a-select-option value="STRING">文本 (STRING)</a-select-option>
            <a-select-option value="DATETIME">日期时间 (DATETIME)</a-select-option>
            <a-select-option value="BOOLEAN">布尔 (BOOLEAN)</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="computedColumnFormState.description" 
            placeholder="请输入指标的详细描述，帮助AI理解其业务含义" 
            :rows="3" 
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 同步字段模态框 -->
    <a-modal 
      v-model:open="isSyncModalVisible" 
      title="从数据源同步字段" 
      @ok="handleSyncColumnsOk" 
      :confirm-loading="syncing" 
      width="800px"
      ok-text="确认"
      cancel-text="取消"
    >
      <a-alert message="请选择需要保留在数据集中的物理字段。未被勾选的已有字段将会被移除。" type="info" show-icon style="margin-bottom: 16px;" />
      
      <a-table 
        :columns="syncTableColumns" 
        :data-source="sourceColumns"
        :row-selection="{ selectedRowKeys: syncModalSelectedKeys, onChange: onSyncSelectChange }"
        row-key="columnName"
        :pagination="false" 
        size="middle"
        :scroll="{ y: 400 }"
      >
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
  SyncOutlined, 
  PlusOutlined, 
  FilterOutlined, 
  GroupOutlined 
} from '@ant-design/icons-vue';
import { modelApi } from '../../api/model';
import { getTypeName, getTypeColor, getSemanticTypeName, getSemanticTypeColor } from '../../utils/dataset-helper';

const route = useRoute();
const router = useRouter();
const datasetId = route.params.id;

// 数据集详情
const datasetDetails = ref(null);
const loading = ref(false);

// 基本信息表单
const basicFormRef = ref();
const basicFormState = reactive({
  datasetName: '',
  description: ''
});
const savingBasicInfo = ref(false);

// 页卡状态
const activeTab = ref('all');

// 字段数据
const allColumns = ref([]);
const physicalColumns = computed(() => allColumns.value.filter(col => !col.isComputed));
const computedColumns = computed(() => allColumns.value.filter(col => col.isComputed));
const enabledColumns = computed(() => allColumns.value.filter(col => col.enabled));

// 同步字段
const syncing = ref(false);

// 字段编辑模态框
const isColumnModalVisible = ref(false);
const columnModalLoading = ref(false);
const columnFormRef = ref();
const isEditingComputedColumn = ref(false);
const columnFormState = reactive({
  id: null,
  columnName: '',
  description: '',
  technicalNameOrExpression: '',
  dataType: 'STRING',
  semanticType: null,
  synonyms: [],
  filterable: false,
  groupable: false,
  isComputed: false
});

const formIsComputed = computed({
  get: () => String(columnFormState.isComputed),
  set: val => {
    columnFormState.isComputed = val === 'true';
  }
});

// 计算指标模态框
const isComputedColumnModalVisible = ref(false);
const computedColumnModalLoading = ref(false);
const computedColumnFormRef = ref();
const validatingExpression = ref(false);
const isExpressionValid = ref(false);
const validationResult = reactive({
  message: '',
  color: ''
});
const computedColumnFormState = reactive({
  datasetId: datasetId,
  columnName: '',
  expression: '',
  dataType: 'NUMBER',
  description: '',
  semanticType: 'METRIC'
});

// 同步字段模态框
const isSyncModalVisible = ref(false);
const syncModalSelectedKeys = ref([]);
const sourceColumns = ref([]);

const syncTableColumns = [
    { title: '字段名', dataIndex: 'columnName', key: 'columnName' },
    { title: '数据类型', dataIndex: 'dataType', key: 'dataType' },
    { title: '注释', dataIndex: 'comment', key: 'comment', ellipsis: true },
];

// 通用基础列定义
const baseColumns = [
  { title: '启用', dataIndex: 'enabled', key: 'enabled', width: 80, fixed: 'left' },
  { title: '指标名称', dataIndex: 'columnName', key: 'columnName', width: 150, ellipsis: true },
  { title: '技术定义', dataIndex: 'technicalNameOrExpression', key: 'technicalNameOrExpression', width: 150, ellipsis: true },
  { title: '同义词', dataIndex: 'synonyms', key: 'synonyms', width: 150, ellipsis: true },
  { title: '详细描述及计算口径', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '数据类型', dataIndex: 'dataType', key: 'dataType', width: 120 },
  { title: '语义类型', dataIndex: 'semanticType', key: 'semanticType', width: 120 },
];

// 表格列定义
const allColumnsConfig = [
  ...baseColumns,
  { title: '字段类型', dataIndex: 'isComputed', key: 'isComputed', width: 120 },
  { title: '操作', key: 'action', width: 100, fixed: 'right' },
];

const physicalColumnsConfig = [
  ...baseColumns,
  { title: '操作', key: 'action', width: 100, fixed: 'right' },
];

const computedColumnsConfig = [
  { title: '启用', dataIndex: 'enabled', key: 'enabled', width: 80, fixed: 'left' },
  { title: '指标名称', dataIndex: 'columnName', key: 'columnName', width: 150, ellipsis: true },
  { title: '表达式', dataIndex: 'technicalNameOrExpression', key: 'technicalNameOrExpression', width: 150, ellipsis: true },
  { title: '指标别名', dataIndex: 'synonyms', key: 'synonyms', width: 150, ellipsis: true },
  { title: '详细描述及计算口径', dataIndex: 'description', key: 'description', ellipsis: true },
  { title: '数据类型', dataIndex: 'dataType', key: 'dataType', width: 120 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' },
];

// 生命周期
onMounted(() => {
  fetchDatasetDetails();
});

// 方法
const fetchDatasetDetails = async () => {
  loading.value = true;
  try {
    const response = await modelApi.getDatasetDetails(datasetId);
    datasetDetails.value = response.data;
    // 转换status字段为enabled字段供前端使用
    allColumns.value = (response.data.columns || []).map(column => ({
      ...column,
      enabled: column.status === "active"
    }));
    
    // 初始化基本信息表单
    basicFormState.datasetName = response.data.datasetName || '';
    basicFormState.description = response.data.description || '';
  } catch (error) {
    console.error('获取数据集详情失败:', error);
    message.error('获取数据集详情失败');
  } finally {
    loading.value = false;
  }
};

const handleSaveBasicInfo = async () => {
  try {
    await basicFormRef.value.validate();
    savingBasicInfo.value = true;
    
    await modelApi.updateDataset(datasetId, {
      datasetName: basicFormState.datasetName,
      description: basicFormState.description
    });
    
    message.success('基本信息保存成功');
    // 更新页面标题
    datasetDetails.value.datasetName = basicFormState.datasetName;
    datasetDetails.value.description = basicFormState.description;
  } catch (error) {
    console.error('保存基本信息失败:', error);
    if (!error.errorFields) {
      message.error('保存基本信息失败');
    }
  } finally {
    savingBasicInfo.value = false;
  }
};

const handleToggleColumn = async (record, enabled) => {
  try {
    await modelApi.updateColumn(record.id, {
      ...record,
      status: enabled ? "active" : "inactive"
    });
    message.success(`字段${enabled ? '启用' : '禁用'}成功`);
    // 更新本地状态
    record.enabled = enabled;
  } catch (error) {
    console.error('更新字段状态失败:', error);
    message.error('更新字段状态失败');
    // 回滚状态
    record.enabled = !enabled;
  }
};

const handleSyncColumns = async () => {
    syncing.value = true;
    try {
        if (!datasetDetails.value || !datasetDetails.value.baseObjectName) {
            message.error('无法确定数据集对应的数据表，请检查数据集配置。');
            return;
        }

        const tableName = datasetDetails.value.baseObjectName;
        
        // 清空之前的数据
        sourceColumns.value = [];
        syncModalSelectedKeys.value = [];

        const res = await modelApi.previewColumns({
            dataSourceId: datasetDetails.value.sourceId,
            sourceDefinition: tableName,
            baseObjectType: 'TABLE'
        });
        
        if (res.data && res.data.length > 0) {
            sourceColumns.value = res.data.map(c => ({ ...c, key: c.columnName }));
            
            const currentPhysicalColumns = allColumns.value.filter(c => !c.isComputed);
            syncModalSelectedKeys.value = currentPhysicalColumns.map(c => c.technicalNameOrExpression);
            
            isSyncModalVisible.value = true;
        } else {
            message.error(`在数据表 "${tableName}" 中未找到任何字段，或该表不存在。`);
        }
    } catch (error) {
        console.error("加载表字段失败:", error);
        message.error('加载表字段失败: ' + (error.response?.data?.message || error.message));
    } finally {
        syncing.value = false;
    }
};

const handleEditColumn = (record) => {
  isEditingComputedColumn.value = record.isComputed;
  
  // 重置表单状态
  Object.assign(columnFormState, {
    id: record.id,
    columnName: record.columnName || '',
    description: record.description || '',
    technicalNameOrExpression: record.technicalNameOrExpression || '',
    dataType: record.dataType || 'STRING', // 添加数据类型字段
    semanticType: record.semanticType || null,
    synonyms: Array.isArray(record.synonyms) ? [...record.synonyms] : [],
    filterable: record.filterable !== undefined ? record.filterable : false,
    groupable: record.groupable !== undefined ? record.groupable : false,
    isComputed: record.isComputed
  });
  
  isColumnModalVisible.value = true;
};

const handleColumnOk = async () => {
  try {
    await columnFormRef.value.validate();
    columnModalLoading.value = true;
    await modelApi.updateColumn(columnFormState.id, {
      columnName: columnFormState.columnName,
      description: columnFormState.description,
      dataType: columnFormState.dataType,
      semanticType: columnFormState.semanticType,
      synonyms: columnFormState.synonyms,
      filterable: columnFormState.filterable,
      groupable: columnFormState.groupable,
      isComputed: columnFormState.isComputed
    });
    message.success('配置更新成功');
    isColumnModalVisible.value = false;
    await fetchDatasetDetails();
  } catch (error) {
    console.error('更新字段配置失败:', error);
    if (error.response && error.response.data && error.response.data.message) {
      message.error(error.response.data.message);
    } else {
      message.error('配置更新失败，请重试');
    }
  } finally {
    columnModalLoading.value = false;
  }
};

const handleAddComputedColumn = () => {
  Object.assign(computedColumnFormState, {
    columnName: '',
    expression: '',
    dataType: 'NUMBER',
    description: '',
    semanticType: 'METRIC'
  });
  isExpressionValid.value = false;
  validationResult.message = '';
  isComputedColumnModalVisible.value = true;
};

const onExpressionChange = () => {
  isExpressionValid.value = false;
  validationResult.message = '';
};

const handleValidateExpression = async () => {
  if (!computedColumnFormState.expression) {
    message.warn('请输入SQL表达式后再进行校验');
    return;
  }
  
  validatingExpression.value = true;
  try {
    const response = await modelApi.validateExpression(datasetId, {
      datasetId: datasetId,
      expression: computedColumnFormState.expression
    });
    
    if (response.data.valid) {
      isExpressionValid.value = true;
      validationResult.message = response.data.message;
      validationResult.color = 'green';
      message.success('表达式校验通过！');
    } else {
      isExpressionValid.value = false;
      validationResult.message = response.data.message;
      validationResult.color = 'red';
      message.error('表达式校验失败');
    }
  } catch (error) {
    isExpressionValid.value = false;
    validationResult.message = error.response?.data?.message || '校验时发生未知错误';
    validationResult.color = 'red';
    message.error('表达式校验失败');
  } finally {
    validatingExpression.value = false;
  }
};

const handleComputedColumnOk = async () => {
  try {
    await computedColumnFormRef.value.validate();
    
    if (!isExpressionValid.value) {
      message.error('请先校验并确保表达式有效');
      return;
    }
    
    computedColumnModalLoading.value = true;
    
    await modelApi.createComputedColumn(datasetId, computedColumnFormState);
    message.success('添加计算指标成功');
    isComputedColumnModalVisible.value = false;
    await fetchDatasetDetails();
  } catch (error) {
    console.error('添加计算指标失败:', error);
    if (!error.errorFields) {
      message.error('添加计算指标失败: ' + (error.response?.data?.message || '未知错误'));
    }
  } finally {
    computedColumnModalLoading.value = false;
  }
};

const handleDeleteComputedColumn = async (record) => {
  try {
    await modelApi.deleteColumn(record.id);
    message.success('删除计算指标成功');
    await fetchDatasetDetails();
  } catch (error) {
    console.error('删除计算指标失败:', error);
    message.error('删除计算指标失败');
  }
};

const onSyncSelectChange = (selectedKeys) => {
    syncModalSelectedKeys.value = selectedKeys;
};

const handleSyncColumnsOk = async () => {
    try {
        const columnsToUpdate = syncModalSelectedKeys.value;
        // 调用新的API，传入datasetId和需要保留的字段名列表
        await modelApi.updatePhysicalColumns(datasetId, { columnNames: columnsToUpdate });
        message.success('字段同步成功');
        isSyncModalVisible.value = false;
        await fetchDatasetDetails(); // 同步成功后刷新页面数据
    } catch (error) {
        message.error('字段同步失败');
        console.error("字段同步失败:", error);
    }
};

// 校验字段类型与技术定义的一致性
const validateFieldType = (rule, value) => {
  const technicalDef = columnFormState.technicalNameOrExpression;
  if (!technicalDef) {
    return Promise.resolve();
  }
  
  // 简单判断是否为表达式：包含运算符、函数调用、空格等
  const isExpression = /[\+\-\*\/\(\)\s]|[a-zA-Z_][a-zA-Z0-9_]*\s*\(/.test(technicalDef);
  
  if (isExpression && !value) {
    return Promise.reject(new Error('技术定义包含表达式，字段类型必须选择"计算字段"'));
  }
  
  if (!isExpression && value) {
    return Promise.reject(new Error('技术定义是简单字段名，字段类型应该选择"物理字段"'));
  }
  
  return Promise.resolve();
};
</script>

<style scoped>
.ant-table-tbody > tr > td {
  padding: 12px 8px;
}

.ant-tag {
  margin-right: 4px;
}
</style> 