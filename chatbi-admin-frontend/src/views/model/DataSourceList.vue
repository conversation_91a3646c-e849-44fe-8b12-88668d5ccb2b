<template>
  <div>
    <a-page-header
        title="数据源管理"
        sub-title="配置并管理系统可以连接的所有外部数据库"
    />
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAddNew">新建数据源</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSourceList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'databaseType'">
            <a-tag :color="getDbTypeColor(record.databaseType)">{{ record.databaseType }}</a-tag>
          </template>
          <template v-if="column.key === 'connectionInfo'">
            {{ record.host }}:{{ record.port }}
          </template>
           <template v-if="column.key === 'status'">
            <a-tag :color="getStatus(record.status).color">{{ getStatus(record.status).text }}</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
                <a style="color: red">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:open="isModalVisible"
      :title="isEditing ? '编辑数据源' : '新建数据源'"
      @ok="handleOk"
      :confirm-loading="modalLoading"
      :destroyOnClose="true"
    >
      <a-form ref="formRef" :model="formState" layout="vertical">
        <a-form-item name="sourceName" label="数据源名称" :rules="[{ required: true }]">
          <a-input v-model:value="formState.sourceName" />
        </a-form-item>
        <a-form-item name="databaseType" label="数据库类型" :rules="[{ required: true }]">
          <a-select v-model:value="formState.databaseType">
            <a-select-option value="MYSQL">MySQL</a-select-option>
            <a-select-option value="POSTGRESQL">PostgreSQL</a-select-option>
            <a-select-option value="HIVE">Hive</a-select-option>
          </a-select>
        </a-form-item>
        <a-row :gutter="16">
          <a-col :span="16">
            <a-form-item name="host" label="主机地址" :rules="[{ required: true }]">
              <a-input v-model:value="formState.host" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
             <a-form-item name="port" label="端口" :rules="[{ required: true }]">
              <a-input-number v-model:value="formState.port" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item name="databaseName" label="数据库名称" :rules="[{ required: true }]">
            <a-input v-model:value="formState.databaseName" />
        </a-form-item>
        <a-form-item name="username" label="用户名" :rules="[{ required: true }]">
            <a-input v-model:value="formState.username" autocomplete="off" />
        </a-form-item>
        <a-form-item name="password" label="密码">
            <a-input-password v-model:value="formState.password" placeholder="不修改则请留空" autocomplete="new-password" />
        </a-form-item>
        <a-form-item name="status" label="状态" :rules="[{ required: true, message: '请选择状态!' }]">
          <a-select v-model:value="formState.status" placeholder="请选择状态">
            <a-select-option value="active">已激活</a-select-option>
            <a-select-option value="inactive">未激活</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
       <template #footer>
        <a-button key="test" @click="handleTestConnection" :loading="testLoading">测试连接</a-button>
        <a-button key="back" @click="isModalVisible = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="modalLoading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import apiClient from '../../api'; // 修改这里，直接使用 apiClient
import { message } from 'ant-design-vue';

const columns = [
  { title: '数据源名称', dataIndex: 'sourceName', key: 'sourceName' },
  { title: '数据库类型', dataIndex: 'databaseType', key: 'databaseType' },
  { title: '数据库名称', dataIndex: 'databaseName', key: 'databaseName' },
  { title: '连接信息', key: 'connectionInfo' },
  { title: '状态', key: 'status' },
  { title: '操作', key: 'action' },
];

const dataSourceList = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

const isModalVisible = ref(false);
const modalLoading = ref(false);
const testLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({
  id: null,
  sourceName: '',
  databaseType: null,
  host: '',
  port: null,
  databaseName: '',
  username: '',
  password: '',
  status: null
});

const fetchData = async (pageInfo = { current: 1, pageSize: 20 }) => {
  loading.value = true;
  try {
    const params = { page: pageInfo.current - 1, size: pageInfo.pageSize };
    const response = await apiClient.get('/api/admin/data-sources', { params });
    dataSourceList.value = response.data.content;
    pagination.total = response.data.totalElements;
    pagination.current = pageInfo.current;
  } catch (error) {
    console.error("Failed to fetch data sources:", error);
    message.error("加载数据源失败");
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchData(pagination);
});

const handleTableChange = (pageInfo) => {
  fetchData(pageInfo);
};

const handleAddNew = () => {
  isEditing.value = false;
  Object.assign(formState, { id: null, sourceName: '', databaseType: null, host: '', port: null, databaseName: '', username: '', password: '', status: null });
  isModalVisible.value = true;
};

const handleEdit = (record) => {
  isEditing.value = true;
  Object.assign(formState, record);
  formState.password = ''; // 编辑时不显示旧密码
  isModalVisible.value = true;
};

const handleDelete = async (id) => {
    try {
        await apiClient.delete(`/api/admin/data-sources/${id}`);
        message.success('删除成功');
        fetchData(pagination);
    } catch(error) {
        message.error('删除失败');
    }
};

const handleOk = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    
    const payload = { ...formState };
    
    if (!payload.password) {
        delete payload.password;
    }

    if (isEditing.value) {
      await apiClient.put(`/api/admin/data-sources/${payload.id}`, payload);
      message.success('更新成功');
    } else {
      await apiClient.post('/api/admin/data-sources', payload);
      message.success('创建成功');
    }
    isModalVisible.value = false;
    fetchData(pagination);
  } catch (error) {
    console.error("Form submission/validation failed:", error);
  } finally {
    modalLoading.value = false;
  }
};

const handleTestConnection = async () => {
    try {
        await formRef.value.validate();
        testLoading.value = true;
        const res = await apiClient.post('/api/admin/data-sources/test-connection', formState);
        if(res.data.success) {
            message.success(res.data.message);
        } else {
            message.error(res.data.message);
        }
    } catch(error) {
        // validation failed
    } finally {
        testLoading.value = false;
    }
}

const statusMap = {
    'active': { text: '已激活', color: 'green' },
    'inactive': { text: '未激活', color: 'red' },
    'maintenance': { text: '维护中', color: 'orange' }
};

const getStatus = (status) => {
    return statusMap[status] || { text: status, color: 'default' };
};

const getDbTypeColor = (type) => {
  const colors = { MYSQL: 'blue', POSTGRESQL: 'green', HIVE: 'orange' };
  return colors[type] || 'default';
};
</script>

<style scoped>
.table-operations {
  margin-bottom: 16px;
}
</style> 