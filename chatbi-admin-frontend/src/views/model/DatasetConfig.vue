<template>
  <div>
    <a-page-header :title="datasetDetails?.datasetName || '加载中...'" :sub-title="datasetDetails?.description" @back="() => router.back()">
        <template #tags>
            <a-tag :color="getTypeColor(datasetDetails?.objectType)">
                {{ getTypeName(datasetDetails?.objectType) }}
            </a-tag>
        </template>
        <template #extra>
            <a-space>
                <a-button key="sync" type="primary" @click="handleSyncColumns" :loading="syncing">
                    <SyncOutlined />
                    从数据源同步
                </a-button>
            </a-space>
        </template>
    </a-page-header>
    <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="fields" tab="物理字段">
            <a-card>
                <template #title>
                    <span>物理字段</span>
                    <a-tag style="margin-left: 8px;" color="blue">共 {{ physicalColumns.length }} 个字段</a-tag>
                </template>
                <a-table :columns="physicalColumnsConfig" :data-source="physicalColumns" row-key="id" :pagination="false" size="middle">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'semanticType'">
                            <a-tag :color="getSemanticTypeColor(record.semanticType)" v-if="record.semanticType">
                                {{ getSemanticTypeName(record.semanticType) }}
                            </a-tag>
                            <span v-else style="color: #999;">未设置</span>
                        </template>
                        <template v-if="column.key === 'synonyms'">
                            <a-space wrap v-if="record.synonyms && record.synonyms.length > 0">
                                <a-tag v-for="tag in record.synonyms" :key="tag" color="geekblue" size="small">{{ tag }}</a-tag>
                            </a-space>
                            <span v-else style="color: #999;">无</span>
                        </template>
                        <template v-if="column.key === 'properties'">
                          <a-space>
                            <a-tooltip title="可用于筛选">
                              <a-tag v-if="record.isFilterable" color="green" size="small"><FilterOutlined /> 筛选</a-tag>
                            </a-tooltip>
                            <a-tooltip title="可用于分组">
                              <a-tag v-if="record.isGroupable" color="purple" size="small"><GroupOutlined /> 分组</a-tag>
                            </a-tooltip>
                          </a-space>
                        </template>
                        <template v-if="column.key === 'description'">
                            <a-tooltip :title="record.description" v-if="record.description">
                                <InfoCircleOutlined style="color: #1890ff;" />
                            </a-tooltip>
                            <span v-else style="color: #999;">无描述</span>
                        </template>
                        <template v-if="column.key === 'action'">
                            <a-button type="link" size="small" @click="handleEditColumn(record)">编辑</a-button>
                        </template>
                    </template>
                </a-table>
            </a-card>
        </a-tab-pane>
        <a-tab-pane key="computed" tab="计算指标">
            <a-card>
                <template #title>
                    <span>计算指标</span>
                    <a-tag style="margin-left: 8px;" color="green">共 {{ computedColumns.length }} 个指标</a-tag>
                </template>
                <template #extra>
                    <a-button type="primary" @click="handleAddComputedColumn"><PlusOutlined /> 添加计算指标</a-button>
                </template>
                <a-table :columns="computedColumnsConfig" :data-source="computedColumns" row-key="id" :pagination="false" size="middle">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'dataType'">
                      <a-tag color="cyan">{{ record.dataType }}</a-tag>
                    </template>
                    <template v-if="column.key === 'description'">
                      <a-tooltip :title="record.description" v-if="record.description">
                        <InfoCircleOutlined style="color: #1890ff;" />
                      </a-tooltip>
                      <span v-else style="color: #999;">无描述</span>
                    </template>
                    <template v-if="column.key === 'action'">
                      <!-- <a>编辑</a> <a-divider type="vertical" /> <a>删除</a> -->
                      <span style="color: #999;">暂无操作</span>
                    </template>
                  </template>
                </a-table>
            </a-card>
        </a-tab-pane>
    </a-tabs>

    <a-modal v-model:open="isColumnModalVisible" title="编辑字段配置" @ok="handleColumnOk" :confirm-loading="columnModalLoading" width="600px">
        <a-form 
            ref="columnFormRef" 
            :model="columnFormState" 
            layout="vertical"
            :rules="columnFormRules"
        >
            <a-form-item label="技术定义" name="technicalNameOrExpression">
                <a-input 
                    v-model:value="columnFormState.technicalNameOrExpression" 
                    disabled
                    style="color: #666;"
                />
            </a-form-item>
            
            <a-form-item 
                label="业务名称" 
                name="columnName" 
                :rules="[{ required: true, message: '请输入业务名称' }]"
            >
                <a-input 
                    v-model:value="columnFormState.columnName" 
                    placeholder="请输入字段的业务名称"
                />
            </a-form-item>
            
            <a-form-item label="描述" name="description">
                <a-textarea 
                    v-model:value="columnFormState.description" 
                    placeholder="请输入字段的详细描述，帮助AI理解字段含义"
                    :rows="3"
                />
            </a-form-item>
            
            <a-form-item 
                label="语义类型" 
                name="semanticType"
                :rules="[{ required: true, message: '请选择语义类型' }]"
            >
                <a-select 
                    v-model:value="columnFormState.semanticType" 
                    placeholder="请选择语义类型"
                >
                    <a-select-option value="METRIC">
                        <a-tag color="orange">指标</a-tag>
                        <span style="margin-left: 8px;">可聚合的数值，如"销售额"</span>
                    </a-select-option>
                    <a-select-option value="DIMENSION">
                        <a-tag color="blue">普通维度</a-tag>
                        <span style="margin-left: 8px;">分类或文本，如"商品品类"</span>
                    </a-select-option>
                    <a-select-option value="TIME_DIMENSION">
                        <a-tag color="green">时间维度</a-tag>
                        <span style="margin-left: 8px;">专门用于标识日期的维度，如"下单日期"</span>
                    </a-select-option>
                    <a-select-option value="GEO_DIMENSION">
                        <a-tag color="purple">地理维度</a-tag>
                        <span style="margin-left: 8px;">专门用于标识地理位置的维度，如"城市"</span>
                    </a-select-option>
                </a-select>
            </a-form-item>
            
            <a-form-item label="同义词" name="synonyms">
                <a-select 
                    v-model:value="columnFormState.synonyms" 
                    mode="tags" 
                    placeholder="输入同义词后按回车确认，帮助AI理解用户的多种表达方式"
                    :token-separators="[',', '，']"
                />
                <div style="margin-top: 4px; color: #666; font-size: 12px;">
                    例如：销售额的同义词可以是"GMV"、"成交额"、"营业额"等
                </div>
            </a-form-item>
            
            <a-form-item label="关键属性">
                <a-space direction="vertical">
                    <a-checkbox v-model:checked="columnFormState.isFilterable">
                        <FilterOutlined style="margin-right: 4px;" />
                        可筛选 - 该字段可以用作查询条件
                    </a-checkbox>
                    <a-checkbox v-model:checked="columnFormState.isGroupable">
                        <GroupOutlined style="margin-right: 4px;" />
                        可分组 - 该字段可以用作分组依据
                    </a-checkbox>
                </a-space>
            </a-form-item>
        </a-form>
    </a-modal>
    
    <!-- 新增：计算指标模态框 -->
    <a-modal v-model:open="isComputedColumnModalVisible" title="添加计算指标" @ok="handleComputedColumnOk" :confirm-loading="computedColumnModalLoading" width="700px" ok-text="确定" cancel-text="取消" :ok-button-props="{ disabled: !isExpressionValid }">
        <a-form ref="computedColumnFormRef" :model="computedColumnFormState" layout="vertical" :rules="computedColumnFormRules">
            <a-form-item label="业务名称" name="columnName" :rules="[{ required: true, message: '请输入业务名称' }]">
                <a-input v-model:value="computedColumnFormState.columnName" placeholder="例如：客单价" />
            </a-form-item>
            <a-form-item label="SQL 表达式" name="expression" :rules="[{ required: true, message: '请输入SQL表达式' }]">
                <a-textarea v-model:value="computedColumnFormState.expression" @change="onExpressionChange" placeholder="例如：sale_amount / order_count" :rows="4" />
                <template #extra>
                    <a-button @click="handleValidateExpression" :loading="validatingExpression">校验表达式</a-button>
                    <span v-if="validationResult.message" :style="{ color: validationResult.color, marginLeft: '12px' }">{{ validationResult.message }}</span>
                </template>
            </a-form-item>
            <a-form-item label="数据类型" name="dataType" :rules="[{ required: true, message: '请选择返回数据类型' }]">
                <a-select v-model:value="computedColumnFormState.dataType" placeholder="请选择表达式预期的返回数据类型">
                    <a-select-option value="NUMBER">数值 (NUMBER)</a-select-option>
                    <a-select-option value="STRING">文本 (STRING)</a-select-option>
                    <a-select-option value="DATETIME">日期时间 (DATETIME)</a-select-option>
                    <a-select-option value="BOOLEAN">布尔 (BOOLEAN)</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-textarea v-model:value="computedColumnFormState.description" placeholder="请输入指标的详细描述，帮助AI理解其业务含义" :rows="3" />
            </a-form-item>
        </a-form>
    </a-modal>
    
    <!-- 新增：从数据源同步字段模态框 -->
    <a-modal 
        v-model:open="isSyncModalVisible" 
        title="从数据源同步字段" 
        @ok="handleSyncOk" 
        :confirm-loading="syncModalLoading" 
        width="800px"
        ok-text="确认"
        cancel-text="取消"
    >
        <a-alert message="请选择需要保留在数据集中的物理字段。未被勾选的已有字段将会被移除。" type="info" show-icon style="margin-bottom: 16px;" />
        <a-table 
            :columns="syncTableColumns" 
            :data-source="sourceColumns"
            :row-selection="{ selectedRowKeys: syncModalSelectedKeys, onChange: onSelectChange }"
            row-key="columnName"
            :pagination="false" 
            size="middle"
            :scroll="{ y: 400 }"
        >
        </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
    FilterOutlined, 
    GroupOutlined, 
    InfoCircleOutlined,
    SyncOutlined,
    PlusOutlined,
} from '@ant-design/icons-vue';
import { modelApi } from '../../api/model.js';
import { getTypeName, getTypeColor, getSemanticTypeColor, getSemanticTypeName } from '../../utils/dataset-helper';

const route = useRoute();
const router = useRouter();
const datasetId = route.params.id;
const datasetDetails = ref(null);
const activeTab = ref('fields');
const syncing = ref(false);

const physicalColumns = ref([]);
const computedColumns = ref([]);

// --- 新增：用于同步字段模态框的状态 ---
const isSyncModalVisible = ref(false);
const syncModalLoading = ref(false);
const sourceColumns = ref([]);
const syncModalSelectedKeys = ref([]);
// --- 结束：新增状态 ---

const fetchDatasetDetails = async () => {
  try {
    const res = await modelApi.getDatasetDetails(datasetId);
    datasetDetails.value = res.data;
    physicalColumns.value = res.data.columns.filter(c => !c.isComputed);
    computedColumns.value = res.data.columns.filter(c => c.isComputed);
  } catch (error) {
    message.error('加载数据集详情失败');
    console.error("加载数据集详情失败:", error);
  }
};

onMounted(() => {
  fetchDatasetDetails();
});

// --- 修改：从数据源同步功能 ---
const handleSyncColumns = async () => {
  message.info("新的同步逻辑被调用了！"); // 明显的测试消息
  console.log("开始同步，检查数据集详情:", datasetDetails.value);
  console.log("datasetDetails.value.extraInfo:", datasetDetails.value?.extraInfo);
  console.log("datasetDetails.value.dataSourceId:", datasetDetails.value?.dataSourceId);
  
  syncing.value = true;
  try {
    // 假设 tableName 存在于 extraInfo 中
    if (!datasetDetails.value || !datasetDetails.value.extraInfo || !datasetDetails.value.extraInfo.tableName) {
        message.error('无法获取数据集的表信息，无法同步。');
        console.error("无法同步：缺少 tableName", datasetDetails.value);
        syncing.value = false;
        return;
    }
    console.log("表名:", datasetDetails.value.extraInfo.tableName);
    // 1. 调用 previewColumns 获取数据源的全量字段
    const res = await modelApi.previewColumns({
      dataSourceId: datasetDetails.value.dataSourceId,
      tableName: datasetDetails.value.extraInfo.tableName
    });
    sourceColumns.value = res.data.map(c => ({ ...c, key: c.columnName })); // 增加key，确保表格渲染
    
    // 2. 设置当前已有的物理字段为默认选中项
    syncModalSelectedKeys.value = physicalColumns.value.map(c => c.technicalNameOrExpression);
    console.log("准备打开弹窗，源字段数量:", sourceColumns.value.length, "已选字段:", syncModalSelectedKeys.value);
    
    // 3. 打开模态框
    isSyncModalVisible.value = true;
    console.log("弹窗状态已设置为 true:", isSyncModalVisible.value);
  } catch (error) {
    message.error('加载数据源字段失败');
    console.error("加载数据源字段失败:", error);
  } finally {
    syncing.value = false;
  }
};

const onSelectChange = (selectedKeys) => {
    syncModalSelectedKeys.value = selectedKeys;
};

const syncTableColumns = [
    { title: '字段名', dataIndex: 'columnName', key: 'columnName' },
    { title: '数据类型', dataIndex: 'dataType', key: 'dataType' },
    { title: '注释', dataIndex: 'comment', key: 'comment', ellipsis: true },
];

const handleSyncOk = async () => {
    syncModalLoading.value = true;
    try {
        const columnsToUpdate = syncModalSelectedKeys.value;
        // 调用新的API，传入datasetId和需要保留的字段名列表
        await modelApi.updatePhysicalColumns(datasetId, { columnNames: columnsToUpdate });
        message.success('字段同步成功');
        isSyncModalVisible.value = false;
        await fetchDatasetDetails(); // 同步成功后刷新页面数据
    } catch (error) {
        message.error('字段同步失败');
        console.error("字段同步失败:", error);
    } finally {
        syncModalLoading.value = false;
    }
};
// --- 结束：修改同步功能 ---


// --- 物理字段表格配置 ---
const physicalColumnsConfig = [
    { title: '业务名称', dataIndex: 'columnName', key: 'columnName', width: 200, fixed: 'left' },
    { title: '技术定义', dataIndex: 'technicalNameOrExpression', key: 'technicalNameOrExpression', width: 200 },
    { title: '语义类型', dataIndex: 'semanticType', key: 'semanticType', width: 120 },
    { title: '同义词', dataIndex: 'synonyms', key: 'synonyms', width: 250 },
    { title: '关键属性', key: 'properties', width: 150 },
    { title: '描述', dataIndex: 'description', key: 'description', width: 80, align: 'center' },
    { title: '操作', key: 'action', width: 100, fixed: 'right' }
];

// --- 字段编辑相关 ---
const isColumnModalVisible = ref(false);
const columnModalLoading = ref(false);
const columnFormRef = ref();
const columnFormState = reactive({
    id: null,
    columnName: '',
    description: '',
    technicalNameOrExpression: '',
    semanticType: null,
    synonyms: [],
    isFilterable: false,
    isGroupable: false,
});
const columnFormRules = {
    columnName: [{ required: true, message: '请输入业务名称' }],
    semanticType: [{ required: true, message: '请选择语义类型' }],
};

const handleEditColumn = (record) => {
    Object.assign(columnFormState, record);
    isColumnModalVisible.value = true;
};

const handleColumnOk = async () => {
    try {
        await columnFormRef.value.validate();
        columnModalLoading.value = true;
        // 只提取需要更新的字段
        const { id, columnName, description, semanticType, synonyms, isFilterable, isGroupable } = columnFormState;
        await modelApi.updateColumn(id, { columnName, description, semanticType, synonyms, isFilterable, isGroupable });
        message.success('字段配置更新成功');
        isColumnModalVisible.value = false;
        await fetchDatasetDetails();
    } catch (error) {
        console.error('字段配置更新失败:', error);
        message.error('字段配置更新失败');
    } finally {
        columnModalLoading.value = false;
    }
};
// --- 结束：字段编辑 ---

// --- 计算指标相关 ---
const computedColumnsConfig = [
    { title: '指标名称', dataIndex: 'columnName', key: 'columnName', width: 200, fixed: 'left' },
    { title: 'SQL 表达式', dataIndex: 'technicalNameOrExpression', key: 'expression', width: 300 },
    { title: '数据类型', dataIndex: 'dataType', key: 'dataType', width: 150 },
    { title: '描述', dataIndex: 'description', key: 'description', width: 80, align: 'center' },
    { title: '操作', key: 'action', width: 120, fixed: 'right' }
];

const isComputedColumnModalVisible = ref(false);
const computedColumnModalLoading = ref(false);
const computedColumnFormRef = ref();
const computedColumnFormState = reactive({
   datasetId: datasetId,
    columnName: '',
    expression: '',
    dataType: null,
    description: ''
});
const computedColumnFormRules = {
    columnName: [{ required: true, message: '请输入业务名称' }],
    expression: [{ required: true, message: '请输入SQL表达式' }],
    dataType: [{ required: true, message: '请选择返回数据类型' }],
};
const validatingExpression = ref(false);
const isExpressionValid = ref(false);
const validationResult = reactive({ color: '', message: '' });

const handleAddComputedColumn = () => {
    // Reset form state
    Object.assign(computedColumnFormState, { columnName: '', expression: '', dataType: null, description: '' });
    isComputedColumnModalVisible.value = true;
};

const onExpressionChange = () => {
    isExpressionValid.value = false;
    validationResult.message = '';
};

const handleComputedColumnOk = async () => {
    try {
        await computedColumnFormRef.value.validate();
        if (!isExpressionValid.value) {
            message.warn('请先校验并确保SQL表达式正确无误');
            return;
        }
        computedColumnModalLoading.value = true;
        await modelApi.createComputedColumn(datasetId, computedColumnFormState);
        message.success('计算指标添加成功');
        isComputedColumnModalVisible.value = false;
        await fetchDatasetDetails();
    } catch (error) {
        console.error("添加计算指标失败:", error);
        message.error('添加计算指标失败');
    } finally {
        computedColumnModalLoading.value = false;
    }
};

const handleValidateExpression = async () => {
    if (!computedColumnFormState.expression) {
        message.warn('请输入SQL表达式');
        return;
    }
    validatingExpression.value = true;
    try {
        const res = await modelApi.validateExpression(datasetId, { expression: computedColumnFormState.expression });
        isExpressionValid.value = res.data.valid;
        if (res.data.valid) {
            validationResult.value = { color: 'green', message: '校验通过' };
        } else {
            validationResult.value = { color: 'red', message: res.data.errorMessage };
        }
    } catch (error) {
        validationResult.value = { color: 'red', message: '校验失败，请检查表达式或服务状态' };
    } finally {
        validatingExpression.value = false;
    }
}
</script>

<style scoped>
.ant-page-header {
    background-color: #fff;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.ant-tag {
    font-size: 12px;
}

.ant-table-cell-with-append {
  padding-right: 38px;
}
</style> 