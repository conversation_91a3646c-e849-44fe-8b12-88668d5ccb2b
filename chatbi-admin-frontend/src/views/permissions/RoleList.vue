<template>
  <div>
    <a-page-header
      title="角色管理"
      sub-title="创建和管理数据查询角色"
    />
    <a-card>
      <div class="table-operations">
        <a-input-search
          v-model:value="searchTerm"
          placeholder="按角色名称搜索..."
          style="width: 280px"
          @search="onSearch"
          allow-clear
        />
        <a-button type="primary" @click="handleAddNew" v-if="isSuperAdmin">新建角色</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="roleList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'createdAt'">
            {{ new Date(record.createdAt).toLocaleString() }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm
                title="确定删除这个角色吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record.id)"
              >
                <a style="color: red">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:open="isModalVisible"
      :title="isEditing ? '编辑角色' : '新建角色'"
      @ok="handleOk"
      :confirm-loading="modalLoading"
    >
      <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
        <a-form-item label="角色名称" name="roleName" :rules="[{ required: true, message: '角色名称不能为空' }]">
          <a-input v-model:value="formState.roleName" placeholder="例如: 华北区销售经理" />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formState.description" :rows="4" placeholder="请输入角色的职责描述..." />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { permissionApi } from '../../api/permission.js';
import { message } from 'ant-design-vue';
import { useAuthStore } from '../../store/auth';

const authStore = useAuthStore();
const isSuperAdmin = computed(() => authStore.userRoles.includes('SUPER_ADMIN'));

const columns = [
  { title: '角色名称', dataIndex: 'roleName', key: 'roleName' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
  { title: '操作', key: 'action' },
];

const roleList = ref([]);
const loading = ref(false);
const searchTerm = ref('');
const pagination = reactive({ current: 1, pageSize: 10, total: 0 });

const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({
  id: null,
  roleName: '',
  description: ''
});

const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current - 1,
      size: pagination.pageSize,
      search: searchTerm.value,
    };
    const res = await permissionApi.getRoles(params);
    roleList.value = res.data.content;
    pagination.total = res.data.totalElements;
  } catch (error) {
    console.error("获取角色列表失败:", error);
    message.error("加载数据失败！");
  } finally {
    loading.value = false;
  }
};

onMounted(fetchData);

const onSearch = () => {
  pagination.current = 1;
  fetchData();
};

watch(searchTerm, (val) => {
  if (!val) {
    onSearch();
  }
});

const handleTableChange = (p) => {
  pagination.current = p.current;
  pagination.pageSize = p.pageSize;
  fetchData();
};

const handleAddNew = () => {
  isEditing.value = false;
  Object.assign(formState, { id: null, roleName: '', description: '' });
  isModalVisible.value = true;
};

const handleEdit = (record) => {
  isEditing.value = true;
  Object.assign(formState, record);
  isModalVisible.value = true;
};

const handleDelete = async (id) => {
  try {
    await permissionApi.deleteRole(id);
    message.success('删除成功');
    fetchData();
  } catch (error) {
    console.error("删除失败:", error);
    message.error('删除失败');
  }
};

const handleOk = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;
    if (isEditing.value) {
      await permissionApi.updateRole(formState.id, formState);
      message.success('更新成功');
    } else {
      await permissionApi.createRole(formState);
      message.success('创建成功');
    }
    isModalVisible.value = false;
    fetchData();
  } catch (error) {
    console.error('表单提交失败:', error);
    message.error('操作失败');
  } finally {
    modalLoading.value = false;
  }
};
</script>

<style scoped>
.table-operations {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
.mt-4 {
  margin-top: 16px;
}
</style> 