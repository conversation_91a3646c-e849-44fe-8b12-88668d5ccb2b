<template>
  <div>
    <a-page-header title="权限分配" sub-title="为不同数据集分配可访问的角色" />
    <a-card>
      <!-- 数据集权限列表 -->
      <a-table
        :columns="columns"
        :data-source="datasetPermissions"
        :loading="loading"
        row-key="datasetId"
        :pagination="{ pageSize: 15 }"
      >
        <template #bodyCell="{ column, record }">
          <!-- 数据集名称列 (带Tooltip) -->
          <template v-if="column.key === 'datasetName'">
            <a-tooltip :title="record.description">
              <span>{{ record.datasetName }}</span>
            </a-tooltip>
          </template>

          <!-- 角色标签列 -->
          <template v-else-if="column.key === 'roles'">
            <a-tooltip
              v-for="role in record.accessibleRoles.slice(0, 5)"
              :key="role.id"
              :title="role.description"
            >
              <a-tag color="blue">{{ role.roleName }}</a-tag>
            </a-tooltip>
            <a-popover
              v-if="record.accessibleRoles.length > 5"
              title="所有可访问角色"
              trigger="hover"
            >
              <template #content>
                <div style="max-width: 250px">
                  <a-tag
                    v-for="role in record.accessibleRoles"
                    :key="role.id"
                    color="blue"
                    style="margin-bottom: 4px"
                  >
                    {{ role.roleName }}
                  </a-tag>
                </div>
              </template>
              <a-tag>+{{ record.accessibleRoles.length - 5 }}...</a-tag>
            </a-popover>
          </template>
          
          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a @click="handleEdit(record)">编辑权限</a>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 编辑权限弹窗 -->
    <a-modal
      v-if="isModalVisible"
      v-model:open="isModalVisible"
      :title="`编辑 '${editingRecord?.datasetName}' 的权限`"
      :confirm-loading="modalSaving"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <a-form class="mt-4" layout="vertical">
        <a-form-item label="选择可访问的角色">
          <a-select
            v-model:value="selectedRoleIds"
            mode="multiple"
            placeholder="请搜索并选择角色"
            show-search
            :options="allRoles"
            :field-names="{ label: 'roleName', value: 'id' }"
            :filter-option="filterOption"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { permissionApi } from '../../api/permission.js';
import { message } from 'ant-design-vue';

// --- 表格定义 (已优化) ---
const columns = [
  { title: '数据集名称', dataIndex: 'datasetName', key: 'datasetName', width: '30%' },
  { title: '可访问的角色', key: 'roles', width: '55%' },
  { title: '操作', key: 'action', width: '15%', align: 'center' },
];

// --- 响应式状态 ---
const loading = ref(true);
const datasetPermissions = ref([]);
const allRoles = ref([]);

const isModalVisible = ref(false);
const modalSaving = ref(false);
const editingRecord = ref(null);
const selectedRoleIds = ref([]);

// --- 数据获取 ---
const fetchData = async () => {
  loading.value = true;
  try {
    const [permissionsRes, rolesRes] = await Promise.all([
      permissionApi.getDatasetPermissions(),
      permissionApi.getRoles({ page: 0, size: 1000 }), // 获取所有角色用于下拉框
    ]);
    datasetPermissions.value = permissionsRes.data;
    allRoles.value = rolesRes.data.content;
  } catch (error) {
    console.error("获取权限数据失败:", error);
    message.error("数据加载失败，请刷新重试！");
  } finally {
    loading.value = false;
  }
};

onMounted(fetchData);

// --- 事件处理 ---
const handleEdit = (record) => {
  editingRecord.value = record;
  selectedRoleIds.value = record.accessibleRoles.map(role => role.id);
  isModalVisible.value = true;
};

const handleCancel = () => {
  isModalVisible.value = false;
  editingRecord.value = null;
  selectedRoleIds.value = [];
};

const handleSave = async () => {
  if (!editingRecord.value) return;
  modalSaving.value = true;
  try {
    await permissionApi.updateDatasetRoles(editingRecord.value.datasetId, {
      roleIds: selectedRoleIds.value,
    });
    message.success("权限更新成功！");
    isModalVisible.value = false;
    await fetchData(); // 重新加载数据以刷新列表
  } catch (error) {
    console.error("保存权限失败:", error);
    message.error("操作失败，请重试。");
  } finally {
    modalSaving.value = false;
  }
};

// --- 辅助函数 ---
const filterOption = (input, option) => {
  return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<style scoped>
.mt-4 {
  margin-top: 16px;
}
</style> 