<template>
  <div class="login-container">
    <a-card title="ChatBI 管理后台登录" :bordered="false" class="login-card">
      <a-form :model="formState" @finish="handleLogin">
        <a-form-item
          name="username"
          :rules="[{ required: true, message: '请输入用户名!' }]"
        >
          <a-input v-model:value="formState.username" placeholder="用户名">
            <template #prefix><UserOutlined /></template>
          </a-input>
        </a-form-item>
        <a-form-item
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password v-model:value="formState.password" placeholder="密码">
             <template #prefix><LockOutlined /></template>
          </a-input-password>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" block :loading="loading">
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../store/auth';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import apiClient from '../../api';

const formState = reactive({
  username: '',
  password: '',
});
const loading = ref(false);
const router = useRouter();
const authStore = useAuthStore();

const handleLogin = async () => {
  loading.value = true;
  try {
    // 使用JSON格式发送登录请求
    const response = await apiClient.post('/api/admin/login', {
      username: formState.username,
      password: formState.password,
    });

    // 后端返回包含token的响应
    const { token, username, roles } = response.data;
    
    // 保存认证信息到store
    authStore.login(token, { username, roles });
    
    router.push('/');
    message.success('登录成功！');

  } catch (error) {
    message.error(error.response?.data?.message || '登录失败，请检查您的凭据');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}
.login-card {
  width: 400px;
}
</style> 