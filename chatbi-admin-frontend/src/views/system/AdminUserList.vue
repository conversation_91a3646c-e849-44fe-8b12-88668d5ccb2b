<template>
  <div>
    <a-page-header
        title="管理员设置"
        sub-title="创建和管理后台系统操作员账号"
    />
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAddNew">新建管理员</a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="userList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'role'">
                <a-tag :color="record.role === 'super_admin' ? 'gold' : 'blue'">
                    {{ record.role === 'super_admin' ? '超级管理员' : '管理员' }}
                </a-tag>
            </template>
            <template v-if="column.key === 'enabled'">
                <a-switch :checked="!!record.enabled" @change="(checked) => onStatusChange(record, checked)" />
            </template>
            <template v-if="column.key === 'created_at'">
                {{ new Date(record.created_at).toLocaleString() }}
            </template>
            <template v-if="column.key === 'action'">
                 <a-space>
                    <a @click="handleEdit(record)">编辑</a>
                    <a-popconfirm title="确定删除该管理员吗?" @confirm="handleDelete(record.id)">
                        <a style="color: red">删除</a>
                    </a-popconfirm>
                </a-space>
            </template>
        </template>
      </a-table>
    </a-card>
    
    <a-modal v-model:open="isModalVisible" :title="isEditing ? '编辑管理员' : '新建管理员'" @ok="handleOk" :confirm-loading="modalLoading">
        <a-form ref="formRef" :model="formState" layout="vertical" class="mt-4">
            <a-form-item label="姓名" name="full_name" :rules="[{ required: true, message: '姓名不能为空' }]">
                <a-input v-model:value="formState.full_name" placeholder="请输入真实姓名" />
            </a-form-item>
            <a-form-item label="登录账号" name="username" :rules="[{ required: true, message: '登录账号不能为空' }]">
                <a-input v-model:value="formState.username" placeholder="请输入登录账号" :disabled="isEditing" />
            </a-form-item>
            <a-form-item v-if="!isEditing" label="初始密码" name="password" :rules="[{ required: true, message: '密码不能为空' }, { min: 6, message: '密码至少为6位' }]">
                <a-input-password v-model:value="formState.password" placeholder="请输入初始密码（至少6位）" />
            </a-form-item>
            <a-form-item label="角色" name="role" :rules="[{ required: true, message: '必须选择一个角色' }]">
                <a-radio-group v-model:value="formState.role">
                    <a-radio value="admin">管理员</a-radio>
                    <a-radio value="super_admin">超级管理员</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { userApi } from '../../api/user.js';
import { message } from 'ant-design-vue';

const columns = [
  { title: '姓名', dataIndex: 'full_name', key: 'full_name' },
  { title: '登录账号', dataIndex: 'username', key: 'username' },
  { title: '角色', key: 'role' },
  { title: '状态', key: 'enabled' },
  { title: '创建时间', key: 'created_at' },
  { title: '操作', key: 'action' },
];

const userList = ref([]);
const loading = ref(false);
const pagination = reactive({ current: 1, pageSize: 10, total: 0 });

const isModalVisible = ref(false);
const modalLoading = ref(false);
const isEditing = ref(false);
const formRef = ref();
const formState = reactive({
    id: null,
    username: '',
    full_name: '',
    password: '',
    role: 'admin',
});

const fetchData = async () => {
    loading.value = true;
    try {
        const params = { page: pagination.current - 1, size: pagination.pageSize };
        const res = await userApi.getUsers(params);
        userList.value = res.data.content;
        pagination.total = res.data.totalElements;
    } catch (error) {
        console.error("获取用户列表失败:", error);
        message.error("加载数据失败！");
    } finally {
        loading.value = false;
    }
};

onMounted(fetchData);

const handleTableChange = (p) => {
  pagination.current = p.current;
  pagination.pageSize = p.pageSize;
  fetchData();
};

const handleAddNew = () => {
    isEditing.value = false;
    Object.assign(formState, { id: null, username: '', full_name: '', password: '', role: 'admin' });
    isModalVisible.value = true;
};

const handleEdit = (record) => {
    isEditing.value = true;
    Object.assign(formState, {
        id: record.id,
        username: record.username,
        full_name: record.full_name,
        password: '', // 编辑时不显示密码
        role: record.role
    });
    isModalVisible.value = true;
};

const onStatusChange = async (user, checked) => {
    try {
        await userApi.updateUserStatus(user.id, checked);
        message.success("状态更新成功");
        user.enabled = checked;
    } catch (error) {
        console.error("状态更新失败:", error);
        message.error("状态更新失败");
    }
};

const handleDelete = async (id) => {
    try {
        await userApi.deleteUser(id);
        message.success("删除成功");
        fetchData();
    } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败");
    }
};

const handleOk = async () => {
    try {
        await formRef.value.validate();
        modalLoading.value = true;
        if (isEditing.value) {
            // 编辑模式下，只更新姓名和角色
            const updateData = {
                full_name: formState.full_name,
                role: formState.role
            };
            await userApi.updateUser(formState.id, updateData);
            message.success("更新成功");
        } else {
            await userApi.createUser(formState);
            message.success("创建成功");
        }
        isModalVisible.value = false;
        fetchData();
    } catch (error) {
        console.error("表单提交失败:", error);
        message.error("操作失败");
    } finally {
        modalLoading.value = false;
    }
};
</script>

<style scoped>
.table-operations {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}
.mt-4 {
  margin-top: 16px;
}
</style> 