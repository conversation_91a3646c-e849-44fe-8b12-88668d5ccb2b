import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';

import App from './App.vue'
import router from './router'
// import store from './store'   // Uncomment if you have Vuex/Pinia setup

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd);

// app.use(store)  // Uncomment if you have Vuex/Pinia setup

app.mount('#app')
