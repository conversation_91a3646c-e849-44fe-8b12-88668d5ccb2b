#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatBI 查询范例数据生成脚本
目标：生成符合新的NLU Agent提示词模板的查询范例数据
包含数据库插入和向量库生成
"""

import os
import sys
import json
import time
import logging
import mysql.connector
import requests
from datetime import datetime
from typing import List, Dict, Optional
from pymilvus import connections, Collection, utility
import getpass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'generate_query_examples_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QueryExampleGenerator:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',  # 将在运行时输入
            'database': 'chatbi_metadata'
        }
        
        self.milvus_config = {
            'host': 'localhost',
            'port': '19530'
        }
        
        # 从 application.yml 读取的配置
        self.dashscope_api_key = "sk-d37335951c6744ba80c147a8110acced"
        self.model_name = "text-embedding-v4"
        self.embedding_api_url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
        
        # 向量维度 (从application.yml中的配置)
        self.vector_dimension = 1024
        
        # 集合配置
        self.collection_name = 'query_examples'
        
    def get_new_format_examples(self) -> List[Dict]:
        """生成符合新格式的查询范例数据"""
        examples = [
            # DATA_QUERY 类型示例
            {
                'user_question': '北京上海本月的销售额',
                'target_query_representation': {
                    "intent": "DATA_QUERY",
                    "entities": {
                        "metrics": ["销售额"],
                        "dimensions_to_group": ["城市"],
                        "filters": [
                            {"fieldName": "城市", "operator": "IN", "value": ["北京", "上海"]},
                            {"fieldName": "日期", "operator": "BETWEEN", "value": ["2024-12-01", "2024-12-31"]}
                        ],
                        "sort_by": [],
                        "limit": 10
                    },
                    "queryType": "AGGREGATION_QUERY",
                    "targetDatasetId": 1,
                    "datasetMatchConfidence": "HIGH",
                    "internal_context": {
                        "originalQuery": "北京上海本月的销售额",
                        "mergedContext": "北京上海本月的销售额"
                    }
                },
                'target_dataset_id': 1,
                'difficulty_level': 'Easy',
                'notes': '多城市聚合查询'
            },
            {
                'user_question': '销售额最高的10个产品',
                'target_query_representation': {
                    "intent": "DATA_QUERY",
                    "entities": {
                        "metrics": ["销售额"],
                        "dimensions_to_group": ["产品名称"],
                        "filters": [],
                        "sort_by": [{"fieldName": "销售额", "order": "DESC"}],
                        "limit": 10
                    },
                    "queryType": "RANKING_QUERY",
                    "targetDatasetId": 1,
                    "datasetMatchConfidence": "HIGH",
                    "internal_context": {
                        "originalQuery": "销售额最高的10个产品",
                        "mergedContext": "销售额最高的10个产品"
                    }
                },
                'target_dataset_id': 1,
                'difficulty_level': 'Medium',
                'notes': '产品排名查询'
            },
            {
                'user_question': '最近30天销售额趋势',
                'target_query_representation': {
                    "intent": "DATA_QUERY",
                    "entities": {
                        "metrics": ["销售额"],
                        "dimensions_to_group": ["日期"],
                        "filters": [
                            {"fieldName": "日期", "operator": "BETWEEN", "value": ["CURRENT_DATE-30", "CURRENT_DATE"]}
                        ],
                        "sort_by": [{"fieldName": "日期", "order": "ASC"}],
                        "limit": 30
                    },
                    "queryType": "TREND_QUERY",
                    "targetDatasetId": 1,
                    "datasetMatchConfidence": "HIGH",
                    "internal_context": {
                        "originalQuery": "最近30天销售额趋势",
                        "mergedContext": "最近30天销售额趋势"
                    }
                },
                'target_dataset_id': 1,
                'difficulty_level': 'Medium',
                'notes': '时间趋势查询'
            },
            # CLARIFICATION_NEEDED 类型示例
            {
                'user_question': '北京的收入',
                'target_query_representation': {
                    "intent": "CLARIFICATION_NEEDED",
                    "response": "我了解您想查看北京的收入数据。请问您想查看哪个时间段的数据？",
                    "clarificationOptions": [
                        {"optionText": "查看北京本月的收入"},
                        {"optionText": "查看北京上个月的收入"},
                        {"optionText": "查看北京最近一年的收入"}
                    ],
                    "internal_context": {
                        "originalQuery": "北京的收入",
                        "mergedContext": "北京的收入"
                    }
                },
                'target_dataset_id': 1,
                'difficulty_level': 'Easy',
                'notes': '缺少时间信息的澄清示例'
            },
            {
                'user_question': '各城市销售额',
                'target_query_representation': {
                    "intent": "CLARIFICATION_NEEDED",
                    "response": "我了解您想查看各城市的销售额。请问您想查看哪个时间段的数据？",
                    "clarificationOptions": [
                        {"optionText": "查看各城市本月的销售额"},
                        {"optionText": "查看各城市今年的销售额"},
                        {"optionText": "查看各城市去年的销售额"}
                    ],
                    "internal_context": {
                        "originalQuery": "各城市销售额",
                        "mergedContext": "各城市销售额"
                    }
                },
                'target_dataset_id': 1,
                'difficulty_level': 'Easy',
                'notes': '聚合查询缺少时间信息'
            },
            # DATA_UNAVAILABLE 类型示例
            {
                'user_question': '用户增长指标',
                'target_query_representation': {
                    "intent": "DATA_UNAVAILABLE",
                    "response": "您查询的'用户增长指标'不在可查询的数据范围内，或者您没有访问该数据的权限。",
                    "internal_context": {
                        "originalQuery": "用户增长指标",
                        "mergedContext": "用户增长指标"
                    }
                },
                'target_dataset_id': None,
                'difficulty_level': 'Easy',
                'notes': '数据不可用示例'
            },
            # GREETING 类型示例
            {
                'user_question': '你好',
                'target_query_representation': {
                    "intent": "GREETING",
                    "response": "您好！很高兴为您服务，请问有什么可以帮您分析的吗？",
                    "internal_context": {
                        "originalQuery": "你好",
                        "mergedContext": "你好"
                    }
                },
                'target_dataset_id': None,
                'difficulty_level': 'Easy',
                'notes': '问候语示例'
            },
            {
                'user_question': '谢谢',
                'target_query_representation': {
                    "intent": "GREETING",
                    "response": "不客气！如果您还有其他数据分析需求，随时告诉我。",
                    "internal_context": {
                        "originalQuery": "谢谢",
                        "mergedContext": "谢谢"
                    }
                },
                'target_dataset_id': None,
                'difficulty_level': 'Easy',
                'notes': '感谢回复示例'
            }
        ]
        
        return examples
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """调用Dashscope API生成向量"""
        try:
            headers = {
                'Authorization': f'Bearer {self.dashscope_api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "model": self.model_name,
                "input": {
                    "texts": [text]
                },
                "parameters": {
                    "text_type": "document"
                }
            }
            
            response = requests.post(self.embedding_api_url, headers=headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('output') and result['output'].get('embeddings'):
                    embedding = result['output']['embeddings'][0]['embedding']
                    if len(embedding) == self.vector_dimension:
                        return embedding
                    else:
                        logger.error(f"向量维度不匹配: 期望{self.vector_dimension}, 实际{len(embedding)}")
                        return None
                else:
                    logger.error(f"API响应格式错误: {result}")
                    return None
            else:
                logger.error(f"API调用失败: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"获取向量失败: {e}")
            return None
    
    def setup_mysql_connection(self) -> bool:
        """设置MySQL连接"""
        try:
            password = getpass.getpass("请输入MySQL root密码: ")
            self.mysql_config['password'] = password
            
            # 测试连接
            conn = mysql.connector.connect(**self.mysql_config)
            conn.close()
            logger.info("✅ MySQL连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            return False
    
    def setup_milvus_connection(self) -> bool:
        """设置Milvus连接"""
        try:
            connections.connect(
                alias="default",
                host=self.milvus_config['host'],
                port=self.milvus_config['port']
            )
            logger.info("✅ Milvus连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Milvus连接失败: {e}")
            return False
    
    def clear_existing_data(self) -> bool:
        """清空现有数据"""
        try:
            # 清空数据库
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM query_examples")
            conn.commit()
            conn.close()
            logger.info("🗑️ 清空数据库中的查询范例数据")
            
            # 清空向量库
            if utility.has_collection(self.collection_name):
                collection = Collection(self.collection_name)
                collection.delete(expr="id > 0")
                collection.flush()
                logger.info("🗑️ 清空向量库中的查询范例数据")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 清空数据失败: {e}")
            return False
    
    def build_rich_text_for_example(self, example: Dict) -> str:
        """为查询示例构建丰富的文本用于向量生成"""
        try:
            # 解析JSON表示
            json_repr = example.get('target_query_representation', {})
            
            # 构建丰富的文本描述
            rich_text_parts = []
            
            # 用户问题
            rich_text_parts.append(f"用户问题: {example.get('user_question', '')}")
            
            # 查询意图
            intent = json_repr.get('intent', 'DATA_QUERY')
            rich_text_parts.append(f"查询意图: {intent}")
            
            # 查询类型
            query_type = json_repr.get('queryType', '')
            if query_type:
                rich_text_parts.append(f"查询类型: {query_type}")
            
            # 指标信息
            entities = json_repr.get('entities', {})
            if entities.get('metrics'):
                rich_text_parts.append(f"查询指标: {', '.join(entities['metrics'])}")
            
            # 维度信息
            if entities.get('dimensions_to_group'):
                rich_text_parts.append(f"分组维度: {', '.join(entities['dimensions_to_group'])}")
            
            # 筛选条件
            if entities.get('filters'):
                filter_desc = []
                for filter_item in entities['filters']:
                    filter_desc.append(f"{filter_item.get('fieldName', '')} {filter_item.get('operator', '')} {filter_item.get('value', '')}")
                rich_text_parts.append(f"筛选条件: {'; '.join(filter_desc)}")
            
            # 难度级别
            difficulty = example.get('difficulty_level', '')
            if difficulty:
                rich_text_parts.append(f"难度级别: {difficulty}")
            
            # 备注信息
            notes = example.get('notes', '')
            if notes:
                rich_text_parts.append(f"备注: {notes}")
            
            return ' | '.join(rich_text_parts)
            
        except Exception as e:
            logger.error(f"构建丰富文本失败: {e}")
            return example.get('user_question', '')
    
    def insert_database_records(self, examples: List[Dict]) -> bool:
        """插入数据库记录"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            success_count = 0
            failed_count = 0
            
            for example in examples:
                try:
                    # 将JSON转换为字符串
                    json_str = json.dumps(example['target_query_representation'], ensure_ascii=False)
                    
                    # 插入数据库
                    insert_query = """
                    INSERT INTO query_examples (
                        user_question, 
                        target_query_representation, 
                        target_dataset_id, 
                        difficulty_level, 
                        notes, 
                        status
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    
                    cursor.execute(insert_query, (
                        example['user_question'],
                        json_str,
                        example['target_dataset_id'],
                        example['difficulty_level'],
                        example['notes'],
                        'verified'
                    ))
                    
                    success_count += 1
                    logger.info(f"✅ 插入示例: {example['user_question'][:50]}...")
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ 插入示例失败: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"📊 数据库插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return failed_count == 0
            
        except Exception as e:
            logger.error(f"❌ 数据库插入失败: {e}")
            return False
    
    def get_inserted_examples(self) -> List[Dict]:
        """获取刚插入的示例数据（包含自动生成的ID）"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            query = """
            SELECT id, user_question, target_query_representation, 
                   target_dataset_id, difficulty_level, notes
            FROM query_examples 
            ORDER BY id DESC
            LIMIT 20
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            
            examples = []
            for row in results:
                example = dict(zip(columns, row))
                examples.append(example)
            
            conn.close()
            return examples
            
        except Exception as e:
            logger.error(f"❌ 获取插入的示例失败: {e}")
            return []
    
    def insert_vector_records(self, examples: List[Dict]) -> bool:
        """插入向量记录"""
        try:
            if not utility.has_collection(self.collection_name):
                logger.error(f"❌ 集合 {self.collection_name} 不存在")
                return False
                
            collection = Collection(self.collection_name)
            
            # 准备向量数据
            ids = []
            vectors = []
            success_count = 0
            fail_count = 0
            
            for example in examples:
                try:
                    # 构建丰富文本
                    rich_text = self.build_rich_text_for_example(example)
                    
                    # 生成向量
                    vector = self.get_embedding(rich_text)
                    
                    if vector:
                        ids.append(example['id'])
                        vectors.append(vector)
                        success_count += 1
                        logger.info(f"✅ 生成向量 ID={example['id']}: {example.get('user_question', '')[:50]}...")
                        
                        # API限流保护
                        time.sleep(0.1)
                    else:
                        fail_count += 1
                        logger.error(f"❌ 向量生成失败 ID={example['id']}")
                        
                except Exception as e:
                    fail_count += 1
                    logger.error(f"❌ 处理示例失败 ID={example.get('id', 'unknown')}: {e}")
            
            # 批量插入向量
            if ids:
                data = [ids, vectors]
                collection.insert(data)
                collection.flush()
                logger.info(f"📊 向量库插入完成: 成功 {success_count} 条, 失败 {fail_count} 条")
                
                # 创建索引
                index_params = {
                    "metric_type": "COSINE",
                    "index_type": "IVF_FLAT",
                    "params": {"nlist": 100}
                }
                collection.create_index(field_name="vector", index_params=index_params)
                logger.info("📦 创建向量索引完成")
                
                return fail_count == 0
            else:
                logger.error("❌ 没有有效的向量数据可插入")
                return False
                
        except Exception as e:
            logger.error(f"❌ 向量库插入失败: {e}")
            return False
    
    def run_generation(self):
        """运行生成流程"""
        logger.info("🚀 开始生成新的查询范例数据...")
        
        # 1. 设置连接
        if not self.setup_mysql_connection():
            return False
            
        if not self.setup_milvus_connection():
            return False
        
        # 2. 获取新格式的示例数据
        examples = self.get_new_format_examples()
        logger.info(f"📊 准备生成 {len(examples)} 条新的查询示例")
        
        # 3. 确认操作
        confirm = input("确认要清空现有数据并生成新数据吗？(y/N): ")
        if confirm.lower() != 'y':
            logger.info("❌ 用户取消操作")
            return False
        
        # 4. 清空现有数据
        logger.info("🔄 步骤1: 清空现有数据...")
        if not self.clear_existing_data():
            logger.error("❌ 清空数据失败")
            return False
        
        # 5. 插入数据库记录
        logger.info("🔄 步骤2: 插入数据库记录...")
        if not self.insert_database_records(examples):
            logger.error("❌ 数据库插入失败")
            return False
        
        # 6. 获取插入的数据（包含ID）
        logger.info("🔄 步骤3: 获取插入的数据...")
        inserted_examples = self.get_inserted_examples()
        if not inserted_examples:
            logger.error("❌ 获取插入的数据失败")
            return False
        
        # 7. 插入向量记录
        logger.info("🔄 步骤4: 生成并插入向量记录...")
        if not self.insert_vector_records(inserted_examples):
            logger.error("❌ 向量库插入失败")
            return False
        
        logger.info("🎉 新的查询范例数据生成完成!")
        return True

def main():
    """主函数"""
    try:
        generator = QueryExampleGenerator()
        success = generator.run_generation()
        
        if success:
            logger.info("✅ 全部生成完成!")
            return 0
        else:
            logger.error("❌ 生成失败!")
            return 1
            
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 