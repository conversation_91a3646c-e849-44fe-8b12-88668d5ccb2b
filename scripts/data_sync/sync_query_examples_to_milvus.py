#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步query_examples数据到Milvus向量数据库

使用方法：
1. 确保Milvus服务正在运行
2. 安装依赖：pip install pymilvus mysql-connector-python dashscope
3. 运行脚本：python sync_query_examples_to_milvus.py
"""

import mysql.connector
from pymilvus import (
    connections,
    Collection,
    FieldSchema,
    CollectionSchema,
    DataType,
    utility
)
import dashscope
from dashscope import TextEmbedding
import os

# 配置参数
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root123456',
    'database': 'chatbi_metadata'
}

MILVUS_CONFIG = {
    'host': 'localhost',
    'port': '19530'
}

COLLECTION_NAME = 'query_examples'
VECTOR_DIMENSION = 1536  # 通义千问embedding维度

# 设置通义千问API key (需要从环境变量或直接设置)
# dashscope.api_key = os.getenv('DASHSCOPE_API_KEY', 'your_api_key_here')
dashscope.api_key = 'sk-cd9fcac29a694e248c44db6275e37c35'

def get_embedding(text):
    """使用通义千问生成文本嵌入向量"""
    try:
        resp = TextEmbedding.call(
            model='text-embedding-v2',
            input=text
        )
        if resp.status_code == 200:
            return resp.output['embeddings'][0]['embedding']
        else:
            print(f"获取嵌入向量失败: {resp}")
            return None
    except Exception as e:
        print(f"获取嵌入向量出错: {e}")
        return None

def create_milvus_collection():
    """创建或获取Milvus集合"""
    if utility.has_collection(COLLECTION_NAME):
        print(f"集合 '{COLLECTION_NAME}' 已存在")
        return Collection(COLLECTION_NAME)
    
    print(f"创建集合 '{COLLECTION_NAME}'...")
    
    # 定义字段
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
        FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=VECTOR_DIMENSION)
    ]
    
    # 创建schema
    schema = CollectionSchema(fields=fields, description="Query examples collection")
    
    # 创建集合
    collection = Collection(name=COLLECTION_NAME, schema=schema)
    
    # 创建索引
    print("创建向量索引...")
    index_params = {
        "metric_type": "L2",
        "index_type": "IVF_FLAT",
        "params": {"nlist": 1024}
    }
    collection.create_index(field_name="vector", index_params=index_params)
    
    return collection

def sync_query_examples():
    """同步query_examples数据到Milvus"""
    
    # 连接MySQL
    print("连接MySQL数据库...")
    mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = mysql_conn.cursor(dictionary=True)
    
    try:
        # 连接Milvus
        print(f"连接Milvus (Host: {MILVUS_CONFIG['host']}, Port: {MILVUS_CONFIG['port']})...")
        connections.connect("default", host=MILVUS_CONFIG['host'], port=MILVUS_CONFIG['port'])
        
        # 创建或获取集合
        collection = create_milvus_collection()
        
        # 查询所有query_examples
        query = """
            SELECT id, user_question, target_query_representation, 
                   target_dataset_id, difficulty_level, notes 
            FROM query_examples
        """
        cursor.execute(query)
        examples = cursor.fetchall()
        
        print(f"找到 {len(examples)} 条QueryExample记录")
        
        # 准备数据
        ids = []
        embeddings = []
        success_count = 0
        fail_count = 0
        
        for example in examples:
            print(f"\n处理ID={example['id']}: {example['user_question'][:50]}...")
            
            # 生成嵌入向量
            embedding = get_embedding(example['user_question'])
            if embedding:
                ids.append(example['id'])
                embeddings.append(embedding)
                success_count += 1
                print(f"✓ 成功生成向量")
            else:
                fail_count += 1
                print(f"✗ 生成向量失败")
        
        # 批量插入到Milvus
        if ids:
            print(f"\n将 {len(ids)} 条数据插入Milvus...")
            collection.insert([ids, embeddings])
            collection.flush()
            print("✓ 数据插入成功")
            
            # 加载集合到内存
            collection.load()
            print("✓ 集合已加载到内存")
        
        # 输出统计
        print(f"\n=== 同步完成 ===")
        print(f"成功: {success_count} 条")
        print(f"失败: {fail_count} 条")
        print(f"总计: {len(examples)} 条")
        
        # 测试搜索
        if success_count > 0:
            print("\n=== 测试向量搜索 ===")
            test_queries = ["北京的销售额", "同比增长", "排名前五"]
            
            for query_text in test_queries:
                print(f"\n搜索: '{query_text}'")
                query_embedding = get_embedding(query_text)
                if query_embedding:
                    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
                    results = collection.search(
                        data=[query_embedding],
                        anns_field="vector",
                        param=search_params,
                        limit=3
                    )
                    
                    if results[0]:
                        print("找到相似示例:")
                        for hit in results[0]:
                            # 查询对应的问题文本
                            cursor.execute(
                                "SELECT user_question FROM query_examples WHERE id = %s",
                                (hit.id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                print(f"  - ID: {hit.id}, 距离: {hit.distance:.4f}")
                                print(f"    问题: {result['user_question']}")
                    else:
                        print("  未找到相似示例")
        
    except Exception as e:
        print(f"同步过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        cursor.close()
        mysql_conn.close()
        connections.disconnect("default")
        print("\n已断开数据库连接")

if __name__ == "__main__":
    sync_query_examples() 