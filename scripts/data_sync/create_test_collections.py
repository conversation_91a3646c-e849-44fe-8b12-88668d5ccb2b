#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建多个测试向量库来对比不同相似性度量算法
"""

import mysql.connector
import json
from pymilvus import connections, Collection, utility, FieldSchema, CollectionSchema, DataType
import hashlib
import numpy as np
from typing import List, Dict

def get_embedding(text: str, normalize: bool = True) -> List[float]:
    """获取文本的向量表示"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    # 重复hash值来生成1536维向量
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
    
    # 归一化处理
    if normalize:
        vector = np.array(vector)
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        return vector.tolist()
    
    return vector

def create_test_collections():
    """创建不同度量算法的测试集合"""
    
    # 连接Milvus
    try:
        connections.connect("default", host='localhost', port='19530')
        print("✅ Milvus连接成功")
    except Exception as e:
        print(f"❌ Milvus连接失败: {e}")
        return
    
    # 定义测试集合配置
    test_configs = [
        {
            "collection_name": "query_examples_l2_test",
            "metric_type": "L2",
            "index_type": "IVF_FLAT",
            "index_params": {"nlist": 128},
            "search_params": {"nprobe": 10},
            "description": "L2欧几里得距离测试集合"
        },
        {
            "collection_name": "query_examples_cosine_test", 
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "index_params": {"nlist": 128},
            "search_params": {"nprobe": 10},
            "description": "COSINE余弦相似度测试集合"
        },
        {
            "collection_name": "query_examples_ip_test",
            "metric_type": "IP", 
            "index_type": "IVF_FLAT",
            "index_params": {"nlist": 128},
            "search_params": {"nprobe": 10},
            "description": "IP内积测试集合"
        }
    ]
    
    # 连接MySQL获取数据
    try:
        conn = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='chatbi_metadata'
        )
        print("✅ MySQL连接成功")
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return
    
    # 获取查询示例数据
    cursor = conn.cursor()
    query = """
    SELECT qe.id, qe.user_question, qe.target_query_representation, 
           qe.difficulty_level, qd.dataset_name 
    FROM query_examples qe 
    LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
    ORDER BY qe.id
    """
    cursor.execute(query)
    results = cursor.fetchall()
    columns = [desc[0] for desc in cursor.description]
    
    examples = []
    for row in results:
        example = dict(zip(columns, row))
        examples.append(example)
    
    print(f"📊 获取到 {len(examples)} 条查询示例")
    
    # 为每种度量算法创建测试集合
    for config in test_configs:
        print(f"\n{'='*60}")
        print(f"创建测试集合: {config['collection_name']}")
        print(f"度量算法: {config['metric_type']}")
        print(f"描述: {config['description']}")
        print(f"{'='*60}")
        
        # 删除已存在的集合
        if utility.has_collection(config["collection_name"]):
            print(f"⚠️  删除已存在的集合: {config['collection_name']}")
            utility.drop_collection(config["collection_name"])
        
        # 创建新集合
        collection = create_collection_with_metric(config)
        if collection is None:
            continue
            
        # 插入数据
        success_count = insert_test_data(collection, examples, config)
        print(f"✅ 成功插入 {success_count} 条数据到 {config['collection_name']}")
        
        # 创建索引
        create_index(collection, config)
        
        # 加载集合
        collection.load()
        print(f"✅ 集合 {config['collection_name']} 已加载，可以进行搜索")
    
    conn.close()
    print(f"\n🎉 所有测试集合创建完成！")

def create_collection_with_metric(config):
    """创建指定度量类型的集合"""
    try:
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1536),
            FieldSchema(name="difficulty_level", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="dataset_name", dtype=DataType.VARCHAR, max_length=200)
        ]
        
        # 创建schema
        schema = CollectionSchema(
            fields=fields, 
            description=config["description"]
        )
        
        # 创建集合
        collection = Collection(
            name=config["collection_name"], 
            schema=schema
        )
        
        print(f"✅ 成功创建集合: {config['collection_name']}")
        return collection
        
    except Exception as e:
        print(f"❌ 创建集合失败: {e}")
        return None

def insert_test_data(collection, examples, config):
    """插入测试数据"""
    try:
        ids = []
        vectors = []
        difficulty_levels = []
        dataset_names = []
        
        for example in examples:
            # 根据度量类型决定是否归一化
            normalize = config["metric_type"] in ["COSINE", "IP"]
            
            # 构建丰富文本（模拟我们的向量化策略）
            rich_text = build_rich_text(example)
            vector = get_embedding(rich_text, normalize=normalize)
            
            ids.append(example['id'])
            vectors.append(vector)
            difficulty_levels.append(example.get('difficulty_level', ''))
            dataset_names.append(example.get('dataset_name', ''))
        
        # 批量插入
        data = [
            ids,
            vectors,
            difficulty_levels,
            dataset_names
        ]
        
        collection.insert(data)
        collection.flush()
        
        return len(ids)
        
    except Exception as e:
        print(f"❌ 插入数据失败: {e}")
        return 0

def build_rich_text(example):
    """构建用于向量化的丰富文本"""
    parts = []
    
    # 1. 核心用户问题
    if example.get('user_question'):
        parts.append(f"用户问题: {example['user_question']}")
        
    # 2. 关联数据集
    if example.get('dataset_name'):
        parts.append(f"关联数据集: {example['dataset_name']}")
        
    # 3. 查询类型
    if example.get('difficulty_level'):
        parts.append(f"查询类型: {example['difficulty_level']}")
        
    # 4. 从查询表示中提取关键信息
    if example.get('target_query_representation'):
        query_repr = example['target_query_representation']
        try:
            data = json.loads(query_repr)
            if 'entities' in data:
                entities = data['entities']
                if 'metrics' in entities:
                    parts.append(f"核心指标: {', '.join(entities['metrics'])}")
                if 'dimensions_to_group' in entities:
                    dims = entities['dimensions_to_group']
                    # 翻译维度名
                    translated_dims = []
                    for dim in dims:
                        translations = {
                            'city': '城市',
                            'product_name': '产品',
                            'category': '品类',
                            'date': '日期',
                            'month': '月份',
                            'year': '年份'
                        }
                        translated_dims.append(translations.get(dim, dim))
                    parts.append(f"分析维度: {', '.join(translated_dims)}")
        except:
            pass
    
    return '\n'.join(parts)

def create_index(collection, config):
    """创建索引"""
    try:
        index_params = {
            "metric_type": config["metric_type"],
            "index_type": config["index_type"],
            "params": config["index_params"]
        }
        
        collection.create_index(
            field_name="vector",
            index_params=index_params
        )
        
        print(f"✅ 成功创建 {config['metric_type']} 索引")
        
    except Exception as e:
        print(f"❌ 创建索引失败: {e}")

def test_all_collections():
    """测试所有集合的搜索效果"""
    print(f"\n{'='*80}")
    print("测试所有集合的搜索效果")
    print(f"{'='*80}")
    
    # 连接MySQL
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    test_cases = [
        {
            "query": "各品类去年的总销售",
            "description": "品类销售查询",
            "expected_contains": ["品类", "销售额", "去年"]
        },
        {
            "query": "北京销售额",
            "description": "地区销售查询", 
            "expected_contains": ["北京", "销售额"]
        },
        {
            "query": "产品排名",
            "description": "排名查询",
            "expected_contains": ["产品", "排名", "最高"]
        }
    ]
    
    collections_to_test = [
        "query_examples_l2_test",
        "query_examples_cosine_test", 
        "query_examples_ip_test"
    ]
    
    # 为每个测试案例测试所有集合
    for test_case in test_cases:
        print(f"\n📋 测试案例: {test_case['description']}")
        print(f"查询: '{test_case['query']}'")
        print(f"期望包含: {test_case['expected_contains']}")
        print("-" * 60)
        
        for collection_name in collections_to_test:
            if not utility.has_collection(collection_name):
                print(f"⚠️  集合 {collection_name} 不存在，跳过")
                continue
                
            metric_type = collection_name.split('_')[-2].upper()
            print(f"\n🔍 {metric_type} 算法结果:")
            
            try:
                collection = Collection(collection_name)
                collection.load()
                
                # 根据度量类型决定是否归一化
                normalize = metric_type in ["COSINE", "IP"]
                query_vector = get_embedding(test_case['query'], normalize=normalize)
                
                search_params = {
                    "metric_type": metric_type,
                    "params": {"nprobe": 10}
                }
                
                results = collection.search(
                    data=[query_vector],
                    anns_field="vector",
                    param=search_params,
                    limit=3,
                    output_fields=["id", "difficulty_level", "dataset_name"]
                )
                
                if results and len(results[0]) > 0:
                    for i, result in enumerate(results[0], 1):
                        # 获取用户问题
                        cursor = conn.cursor()
                        cursor.execute(
                            "SELECT user_question FROM query_examples WHERE id = %s",
                            (result.id,)
                        )
                        row = cursor.fetchone()
                        question = row[0] if row else "未知"
                        
                        # 计算相关性
                        relevance = calculate_relevance(question, test_case['expected_contains'])
                        relevance_emoji = "⭐" if relevance > 0 else ""
                        
                        distance_or_score = result.distance if metric_type == "L2" else result.distance
                        print(f"  {i}. {question} {relevance_emoji}")
                        print(f"     ID: {result.id}, 距离/得分: {distance_or_score:.4f}")
                        print(f"     类型: {result.entity.get('difficulty_level', 'N/A')}")
                        print(f"     相关性: {relevance}/3")
                else:
                    print("  未找到结果")
                    
            except Exception as e:
                print(f"  搜索失败: {e}")
    
    conn.close()

def calculate_relevance(question, expected_terms):
    """计算相关性得分"""
    score = 0
    question_lower = question.lower()
    for term in expected_terms:
        if term in question_lower:
            score += 1
    return score

if __name__ == "__main__":
    print("🚀 开始创建测试向量库...")
    create_test_collections()
    
    print(f"\n⏰ 等待索引构建完成...")
    import time
    time.sleep(5)
    
    print(f"\n🧪 开始测试搜索效果...")
    test_all_collections()
