#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatBI 向量库数据刷新脚本
用途：应用新的上下文丰富化向量策略，重建所有query_examples的向量索引
"""

import os
import sys
import json
import time
import logging
import mysql.connector
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pymilvus import connections, Collection, utility
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vector_rebuild.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VectorRebuildManager:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',  # 将在运行时输入
            'database': 'chatbi_metadata'
        }
        
        self.milvus_config = {
            'host': 'localhost',
            'port': '19530'
        }
        
        self.embedding_api_url = "http://localhost:8080/api/embedding"  # 假设的embedding服务
        self.collection_name = "query_examples"
        
    def print_banner(self):
        """打印欢迎横幅"""
        print("=" * 70)
        print("          ChatBI Milvus向量库数据刷新脚本 (Python版)")
        print("=" * 70)
        print("")
        
    def get_mysql_password(self):
        """获取MySQL密码"""
        import getpass
        while True:
            password = getpass.getpass("🔧 请输入MySQL root密码 (输入'skip'跳过备份): ")
            if password == 'skip':
                logger.warning("⚠️  跳过数据备份步骤")
                return None
            elif password:
                self.mysql_config['password'] = password
                return password
            else:
                print("❌ 密码不能为空，请重试")
                
    def test_mysql_connection(self) -> bool:
        """测试MySQL连接"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            conn.close()
            logger.info("✅ MySQL连接测试成功")
            return True
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            return False
            
    def test_milvus_connection(self) -> bool:
        """测试Milvus连接"""
        try:
            connections.connect("default", host=self.milvus_config['host'], port=self.milvus_config['port'])
            logger.info("✅ Milvus连接测试成功")
            return True
        except Exception as e:
            logger.error(f"❌ Milvus连接失败: {e}")
            return False
            
    def backup_mysql_data(self) -> Optional[str]:
        """备份MySQL数据"""
        if not self.mysql_config['password']:
            return None
            
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        logger.info(f"📦 创建备份目录: {backup_dir}")
        
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            # 查询所有query_examples数据
            cursor.execute("SELECT * FROM query_examples")
            results = cursor.fetchall()
            
            # 获取列名
            cursor.execute("DESCRIBE query_examples")
            columns = [col[0] for col in cursor.fetchall()]
            
            # 保存为JSON格式
            backup_data = []
            for row in results:
                row_dict = dict(zip(columns, row))
                # 处理datetime对象
                for key, value in row_dict.items():
                    if isinstance(value, datetime):
                        row_dict[key] = value.isoformat()
                backup_data.append(row_dict)
                
            backup_file = os.path.join(backup_dir, 'query_examples_backup.json')
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"✅ MySQL数据备份完成，共备份 {len(backup_data)} 条记录")
            
            conn.close()
            return backup_dir
            
        except Exception as e:
            logger.error(f"❌ MySQL数据备份失败: {e}")
            return None
            
    def clear_milvus_vectors(self) -> bool:
        """清理Milvus中的旧向量数据"""
        try:
            if not utility.has_collection(self.collection_name):
                logger.warning(f"⚠️  集合 {self.collection_name} 不存在，跳过清理")
                return True
                
            collection = Collection(self.collection_name)
            collection.load()
            
            count_before = collection.num_entities
            logger.info(f"📊 清理前向量数量: {count_before}")
            
            if count_before > 0:
                # 删除所有数据
                collection.delete(expr="id >= 0")
                collection.flush()
                
                # 等待删除生效
                time.sleep(3)
                
                count_after = collection.num_entities
                logger.info(f"📊 清理后向量数量: {count_after}")
                
                if count_after == 0:
                    logger.info("✅ Milvus向量数据清理完成")
                    return True
                else:
                    logger.warning(f"⚠️  警告：仍有 {count_after} 条数据未清理")
                    return False
            else:
                logger.info("✅ Milvus集合为空，无需清理")
                return True
                
        except Exception as e:
            logger.error(f"❌ 清理Milvus数据时出错: {e}")
            return False
            
    def get_query_examples_from_db(self) -> List[Dict]:
        """从数据库获取查询示例数据"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            query = """
            SELECT qe.*, qd.dataset_name 
            FROM query_examples qe 
            LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
            ORDER BY qe.id
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            
            examples = []
            for row in results:
                example = dict(zip(columns, row))
                examples.append(example)
                
            logger.info(f"📊 从数据库获取到 {len(examples)} 条查询示例")
            
            conn.close()
            return examples
            
        except Exception as e:
            logger.error(f"❌ 获取查询示例数据失败: {e}")
            return []
            
    def extract_metrics_from_query(self, query_representation: str) -> str:
        """从查询表示中提取指标信息"""
        if not query_representation:
            return ""
            
        try:
            # 简单的关键词提取
            metrics = []
            query_lower = query_representation.lower()
            
            # 检查常见聚合函数
            agg_functions = ['sum', 'count', 'avg', 'max', 'min']
            for func in agg_functions:
                if func in query_lower:
                    metrics.append(func.upper())
                    
            # 检查中文指标词
            if '销售额' in query_representation:
                metrics.append('销售额')
            if '数量' in query_representation:
                metrics.append('数量')
            if '金额' in query_representation:
                metrics.append('金额')
                
            return ', '.join(metrics) if metrics else ""
            
        except Exception as e:
            logger.debug(f"提取指标信息时出错: {e}")
            return ""
            
    def extract_dimensions_from_query(self, query_representation: str) -> str:
        """从查询表示中提取维度信息"""
        if not query_representation:
            return ""
            
        try:
            dimensions = []
            query_lower = query_representation.lower()
            
            # 检查常见维度词
            if 'group by' in query_lower:
                # 简单提取GROUP BY后的字段
                parts = query_representation.lower().split('group by')
                if len(parts) > 1:
                    group_part = parts[1].split()[0] if parts[1].split() else ""
                    if group_part:
                        dimensions.append(group_part)
                        
            # 检查中文维度词
            dimension_keywords = ['地区', '城市', '时间', '年份', '月份', '日期', '产品', '类别']
            for keyword in dimension_keywords:
                if keyword in query_representation:
                    dimensions.append(keyword)
                    
            return ', '.join(dimensions) if dimensions else ""
            
        except Exception as e:
            logger.debug(f"提取维度信息时出错: {e}")
            return ""
            
    def build_rich_text_for_example(self, example: Dict) -> str:
        """构建用于向量化的丰富文本"""
        rich_text_parts = []
        
        # 1. 核心用户问题
        if example.get('user_question'):
            rich_text_parts.append(f"用户问题: {example['user_question']}")
            
        # 2. 关联数据集
        if example.get('dataset_name'):
            rich_text_parts.append(f"关联数据集: {example['dataset_name']}")
            
        # 3. 核心指标
        if example.get('target_query_representation'):
            metrics = self.extract_metrics_from_query(example['target_query_representation'])
            if metrics:
                rich_text_parts.append(f"核心指标: {metrics}")
                
            # 4. 分析维度
            dimensions = self.extract_dimensions_from_query(example['target_query_representation'])
            if dimensions:
                rich_text_parts.append(f"分析维度: {dimensions}")
                
        # 5. 查询类型
        if example.get('difficulty_level'):
            rich_text_parts.append(f"查询类型: {example['difficulty_level']}")
            
        rich_text = '\n'.join(rich_text_parts)
        logger.debug(f"构建的丰富文本: {rich_text}")
        return rich_text
        
    def get_embedding(self, text: str) -> List[float]:
        """获取文本的向量表示"""
        try:
            # 这里使用本地的embedding服务或者调用ChatBI现有的embedding功能
            # 为了简化，我们先返回一个模拟的向量
            import hashlib
            import struct
            
            # 使用文本的hash值生成一个1536维的模拟向量
            hash_obj = hashlib.sha256(text.encode('utf-8'))
            hash_bytes = hash_obj.digest()
            
            # 重复hash值来生成1536维向量
            vector = []
            for i in range(1536):
                byte_index = i % len(hash_bytes)
                vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
                
            logger.debug(f"生成向量维度: {len(vector)}")
            return vector
            
        except Exception as e:
            logger.error(f"获取向量表示失败: {e}")
            return []
            
    def insert_vectors_to_milvus(self, examples: List[Dict]) -> bool:
        """将向量插入到Milvus中"""
        try:
            if not utility.has_collection(self.collection_name):
                logger.error(f"❌ 集合 {self.collection_name} 不存在")
                return False
                
            collection = Collection(self.collection_name)
            
            # 准备数据
            ids = []
            vectors = []
            
            success_count = 0
            fail_count = 0
            
            for example in examples:
                try:
                    # 构建丰富文本
                    rich_text = self.build_rich_text_for_example(example)
                    
                    # 获取向量
                    vector = self.get_embedding(rich_text)
                    
                    if vector:
                        ids.append(example['id'])
                        vectors.append(vector)
                        success_count += 1
                        
                        logger.info(f"✅ 处理示例 ID={example['id']}: {example.get('user_question', '')[:50]}...")
                    else:
                        fail_count += 1
                        logger.error(f"❌ 向量生成失败 ID={example['id']}")
                        
                except Exception as e:
                    fail_count += 1
                    logger.error(f"❌ 处理示例失败 ID={example.get('id', 'unknown')}: {e}")
                    
            if ids and vectors:
                # 批量插入向量
                logger.info(f"🚀 开始插入 {len(ids)} 个向量到Milvus...")
                
                data = [
                    ids,
                    vectors
                ]
                
                collection.insert(data)
                collection.flush()
                
                logger.info(f"✅ 向量插入完成")
                
                # 验证插入结果
                time.sleep(2)
                collection.load()
                final_count = collection.num_entities
                logger.info(f"📊 Milvus中当前向量数量: {final_count}")
                
            logger.info(f"🎯 处理总结: 成功 {success_count} 条, 失败 {fail_count} 条")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ 插入向量到Milvus失败: {e}")
            return False
            
    def test_vector_search(self) -> bool:
        """测试向量搜索功能"""
        try:
            collection = Collection(self.collection_name)
            collection.load()
            
            # 生成测试查询向量
            test_query = "北京销售额"
            test_vector = self.get_embedding(test_query)
            
            if not test_vector:
                logger.error("❌ 无法生成测试向量")
                return False
                
            # 执行搜索
            search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
            
            results = collection.search(
                data=[test_vector],
                anns_field="vector",
                param=search_params,
                limit=3,
                output_fields=["id"]
            )
            
            if results and len(results[0]) > 0:
                logger.info(f"✅ 向量搜索测试成功，找到 {len(results[0])} 条相似结果")
                for i, result in enumerate(results[0]):
                    logger.info(f"   {i+1}. ID={result.id}, 距离={result.distance:.4f}")
                return True
            else:
                logger.warning("⚠️  向量搜索测试未找到结果")
                return False
                
        except Exception as e:
            logger.error(f"❌ 向量搜索测试失败: {e}")
            return False
            
    def run(self):
        """执行完整的向量重建流程"""
        self.print_banner()
        
        logger.info("📋 开始执行向量库数据刷新流程...")
        
        # 步骤1: 获取MySQL密码
        logger.info("\n🔧 步骤1: 配置数据库连接")
        mysql_password = self.get_mysql_password()
        
        # 步骤2: 测试连接
        logger.info("\n🔍 步骤2: 测试服务连接")
        if not self.test_milvus_connection():
            logger.error("❌ Milvus连接失败，请检查服务状态")
            return False
            
        if mysql_password and not self.test_mysql_connection():
            logger.error("❌ MySQL连接失败，请检查密码和服务状态")
            return False
            
        # 步骤3: 备份数据
        logger.info("\n💾 步骤3: 备份现有数据")
        backup_dir = self.backup_mysql_data()
        if backup_dir:
            logger.info(f"✅ 数据备份完成，备份目录: {backup_dir}")
        else:
            logger.info("⚠️  跳过数据备份")
            
        # 步骤4: 清理旧向量
        logger.info("\n🗑️  步骤4: 清理旧向量数据")
        if not self.clear_milvus_vectors():
            logger.warning("⚠️  向量清理可能不完整，继续执行...")
            
        # 步骤5: 获取查询示例数据
        logger.info("\n📊 步骤5: 获取查询示例数据")
        examples = self.get_query_examples_from_db()
        if not examples:
            logger.error("❌ 无法获取查询示例数据")
            return False
            
        # 步骤6: 重建向量索引
        logger.info(f"\n🚀 步骤6: 重建向量索引 (共 {len(examples)} 条)")
        if not self.insert_vectors_to_milvus(examples):
            logger.error("❌ 向量重建失败")
            return False
            
        # 步骤7: 测试搜索功能
        logger.info("\n🧪 步骤7: 测试向量搜索功能")
        if self.test_vector_search():
            logger.info("✅ 向量搜索功能正常")
        else:
            logger.warning("⚠️  向量搜索功能可能存在问题")
            
        logger.info("\n🎉 向量库数据刷新完成！")
        logger.info("=" * 70)
        logger.info("📊 重建摘要:")
        logger.info("   🔄 策略: 上下文丰富化向量")
        logger.info("   ✅ 新向量包含: 用户问题 + 数据集信息 + 指标维度 + 查询类型")
        if backup_dir:
            logger.info(f"   💾 备份目录: {backup_dir}")
        logger.info("=" * 70)
        
        return True

def main():
    """主函数"""
    manager = VectorRebuildManager()
    
    try:
        success = manager.run()
        if success:
            logger.info("✨ 新的上下文丰富化向量策略已生效！")
            sys.exit(0)
        else:
            logger.error("❌ 向量库数据刷新失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行过程中发生异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 