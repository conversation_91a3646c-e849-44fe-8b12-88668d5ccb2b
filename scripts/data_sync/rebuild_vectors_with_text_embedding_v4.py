#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChatBI 向量库数据重建脚本
用途：使用 text-embedding-v4 模型重建所有向量数据
支持：business_terms 和 query_examples 两个集合
"""

import os
import sys
import json
import time
import logging
import mysql.connector
import requests
from datetime import datetime
from typing import List, Dict, Optional
from pymilvus import connections, Collection, utility
import getpass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'vector_rebuild_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VectorRebuildService:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',  # 将在运行时输入
            'database': 'chatbi_metadata'
        }
        
        self.milvus_config = {
            'host': 'localhost',
            'port': '19530'
        }
        
        # 从 application.yml 读取的配置
        self.dashscope_api_key = "sk-d37335951c6744ba80c147a8110acced"
        self.model_name = "text-embedding-v4"
        self.embedding_api_url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
        
        # 集合配置
        self.collections = {
            'business_terms': {
                'name': 'business_terms',
                'vector_field': 'vector',
                'id_field': 'id'
            },
            'query_examples': {
                'name': 'query_examples', 
                'vector_field': 'vector',
                'id_field': 'id'
            }
        }
        
    def print_banner(self):
        """打印欢迎横幅"""
        print("=" * 80)
        print("           ChatBI 向量库数据重建脚本 (text-embedding-v4)")
        print("=" * 80)
        print(f"🤖 嵌入模型: {self.model_name}")
        print(f"🔗 API地址: {self.embedding_api_url}")
        print(f"📊 向量维度: 1024 (text-embedding-v4)")
        print(f"🗄️  数据库: {self.mysql_config['database']}")
        print("=" * 80)
        print("")
        
    def get_mysql_password(self):
        """获取MySQL密码"""
        password = getpass.getpass("🔧 请输入MySQL root密码 (直接回车表示空密码): ")
        self.mysql_config['password'] = password
        logger.info(f"✅ 密码配置完成: {'空密码' if not password else '***'}")
        return password
        
    def test_connections(self) -> bool:
        """测试所有连接"""
        logger.info("🔍 测试服务连接...")
        
        # 测试MySQL连接
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            conn.close()
            logger.info("✅ MySQL连接测试成功")
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            return False
            
        # 测试Milvus连接
        try:
            connections.connect("default", 
                               host=self.milvus_config['host'], 
                               port=self.milvus_config['port'])
            logger.info("✅ Milvus连接测试成功")
        except Exception as e:
            logger.error(f"❌ Milvus连接失败: {e}")
            return False
            
        # 测试Dashscope API
        test_result = self.get_embedding("测试文本")
        if test_result and len(test_result) > 0:
            logger.info(f"✅ Dashscope API测试成功，向量维度: {len(test_result)}")
            return True
        else:
            logger.error("❌ Dashscope API测试失败")
            return False
            
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """调用 text-embedding-v4 获取向量"""
        try:
            headers = {
                "Authorization": f"Bearer {self.dashscope_api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model_name,
                "input": {
                    "texts": [text]
                },
                "parameters": {
                    "text_type": "query"
                }
            }
            
            response = requests.post(self.embedding_api_url, 
                                   headers=headers, 
                                   json=payload, 
                                   timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if "output" in result and "embeddings" in result["output"]:
                    embeddings = result["output"]["embeddings"]
                    if embeddings and len(embeddings) > 0:
                        vector = embeddings[0]["embedding"]
                        logger.debug(f"✅ 向量生成成功，维度: {len(vector)}")
                        return vector
                        
            logger.error(f"❌ API调用失败，状态码: {response.status_code}")
            logger.error(f"❌ 响应内容: {response.text}")
            return None
            
        except Exception as e:
            logger.error(f"❌ 调用embedding API失败: {e}")
            return None
            
    def clear_collection(self, collection_name: str) -> bool:
        """清理指定集合的所有向量数据"""
        try:
            if not utility.has_collection(collection_name):
                logger.warning(f"⚠️  集合 {collection_name} 不存在，跳过清理")
                return True
                
            collection = Collection(collection_name)
            collection.load()
            
            count_before = collection.num_entities
            logger.info(f"📊 {collection_name} 清理前向量数量: {count_before}")
            
            if count_before > 0:
                # 删除所有数据
                collection.delete(expr="id >= 0")
                collection.flush()
                
                # 等待删除生效
                time.sleep(3)
                
                count_after = collection.num_entities
                logger.info(f"📊 {collection_name} 清理后向量数量: {count_after}")
                
                if count_after == 0:
                    logger.info(f"✅ {collection_name} 向量数据清理完成")
                    return True
                else:
                    logger.warning(f"⚠️  {collection_name} 仍有 {count_after} 条数据未清理")
                    return False
            else:
                logger.info(f"✅ {collection_name} 集合为空，无需清理")
                return True
                
        except Exception as e:
            logger.error(f"❌ 清理 {collection_name} 数据时出错: {e}")
            return False
            
    def get_business_terms_from_db(self) -> List[Dict]:
        """从数据库获取业务术语数据"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            query = """
            SELECT id, business_term, context_description, created_at, updated_at
            FROM business_terminology 
            ORDER BY id
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            terms = []
            for row in results:
                term = dict(zip(columns, row))
                terms.append(term)
                
            logger.info(f"📊 从数据库获取到 {len(terms)} 条业务术语")
            conn.close()
            return terms
            
        except Exception as e:
            logger.error(f"❌ 获取业务术语数据失败: {e}")
            return []
            
    def get_query_examples_from_db(self) -> List[Dict]:
        """从数据库获取查询示例数据"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            query = """
            SELECT qe.*, qd.dataset_name 
            FROM query_examples qe 
            LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
            ORDER BY qe.id
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            examples = []
            for row in results:
                example = dict(zip(columns, row))
                examples.append(example)
                
            logger.info(f"📊 从数据库获取到 {len(examples)} 条查询示例")
            conn.close()
            return examples
            
        except Exception as e:
            logger.error(f"❌ 获取查询示例数据失败: {e}")
            return []
            
    def build_text_for_term(self, term: Dict) -> str:
        """构建业务术语的向量化文本"""
        parts = []
        
        if term.get('business_term'):
            parts.append(term['business_term'])
            
        if term.get('context_description'):
            parts.append(term['context_description'])
            
        text = "\n".join(parts)
        logger.debug(f"术语向量文本: {text}")
        return text
        
    def build_rich_text_for_example(self, example: Dict) -> str:
        """构建查询示例的丰富向量化文本"""
        parts = []
        
        try:
            # 1. 核心用户问题
            if example.get('user_question'):
                parts.append(f"用户问题: {example['user_question']}")
                
            # 2. 关联数据集
            if example.get('dataset_name'):
                parts.append(f"关联数据集: {example['dataset_name']}")
                
            # 3. 查询类型
            if example.get('difficulty_level'):
                parts.append(f"查询类型: {example['difficulty_level']}")
                
            # 4. 从查询表示中提取关键信息
            if example.get('target_query_representation'):
                try:
                    data = json.loads(example['target_query_representation'])
                    if isinstance(data, dict) and data.get('entities'):
                        entities = data['entities']
                        
                        # 提取核心指标
                        if entities.get('metrics'):
                            metrics = entities['metrics']
                            if isinstance(metrics, list) and metrics:
                                parts.append(f"核心指标: {', '.join(metrics)}")
                                
                        # 提取分析维度
                        if entities.get('dimensions_to_group'):
                            dimensions = entities['dimensions_to_group']
                            if isinstance(dimensions, list) and dimensions:
                                # 翻译维度名为中文
                                dimension_translations = {
                                    "city": "城市", "product_name": "产品", "category": "品类",
                                    "date": "日期", "month": "月份", "year": "年份"
                                }
                                translated_dims = [dimension_translations.get(dim, dim) for dim in dimensions]
                                parts.append(f"分析维度: {', '.join(translated_dims)}")
                                
                except (json.JSONDecodeError, TypeError) as e:
                    logger.debug(f"解析查询表示失败: {e}")
                    
            text = "\n".join(parts)
            logger.debug(f"示例丰富文本: {text}")
            return text
            
        except Exception as e:
            logger.error(f"构建丰富文本失败，回退到用户问题: {e}")
            return example.get('user_question', '')
            
    def rebuild_business_terms(self) -> bool:
        """重建业务术语向量"""
        logger.info("🔄 重建业务术语向量...")
        
        # 获取数据
        terms = self.get_business_terms_from_db()
        if not terms:
            logger.warning("⚠️  没有业务术语数据需要处理")
            return True
            
        # 检查集合
        collection_name = self.collections['business_terms']['name']
        if not utility.has_collection(collection_name):
            logger.error(f"❌ 集合 {collection_name} 不存在")
            return False
            
        collection = Collection(collection_name)
        
        # 处理向量
        ids = []
        vectors = []
        success_count = 0
        fail_count = 0
        
        for term in terms:
            try:
                text = self.build_text_for_term(term)
                vector = self.get_embedding(text)
                
                if vector:
                    ids.append(term['id'])
                    vectors.append(vector)
                    success_count += 1
                    logger.info(f"✅ 处理术语 ID={term['id']}: {term.get('business_term', '')}")
                    
                    # API限流保护
                    time.sleep(0.1)
                else:
                    fail_count += 1
                    logger.error(f"❌ 术语向量生成失败 ID={term['id']}")
                    
            except Exception as e:
                fail_count += 1
                logger.error(f"❌ 处理术语失败 ID={term.get('id', 'unknown')}: {e}")
                
        # 批量插入
        if ids and vectors:
            try:
                logger.info(f"🚀 批量插入 {len(ids)} 个业务术语向量...")
                # 使用真实MySQL ID
                texts = [self.build_text_for_term(term) for term in terms[:len(vectors)]]
                data = [ids, vectors, texts]
                collection.insert(data)
                collection.flush()
                
                # 验证结果
                time.sleep(2)
                collection.load()
                final_count = collection.num_entities
                logger.info(f"📊 业务术语向量数量: {final_count}")
                
            except Exception as e:
                logger.error(f"❌ 批量插入业务术语向量失败: {e}")
                return False
                
        logger.info(f"🎯 业务术语处理总结: 成功 {success_count} 条, 失败 {fail_count} 条")
        return success_count > 0 or len(terms) == 0
        
    def rebuild_query_examples(self) -> bool:
        """重建查询示例向量"""
        logger.info("🔄 重建查询示例向量...")
        
        # 获取数据
        examples = self.get_query_examples_from_db()
        if not examples:
            logger.warning("⚠️  没有查询示例数据需要处理")
            return True
            
        # 检查集合
        collection_name = self.collections['query_examples']['name']
        if not utility.has_collection(collection_name):
            logger.error(f"❌ 集合 {collection_name} 不存在")
            return False
            
        collection = Collection(collection_name)
        
        # 处理向量
        ids = []
        vectors = []
        success_count = 0
        fail_count = 0
        
        for example in examples:
            try:
                rich_text = self.build_rich_text_for_example(example)
                vector = self.get_embedding(rich_text)
                
                if vector:
                    ids.append(example['id'])
                    vectors.append(vector)
                    success_count += 1
                    logger.info(f"✅ 处理示例 ID={example['id']}: {example.get('user_question', '')[:50]}...")
                    
                    # API限流保护
                    time.sleep(0.1)
                else:
                    fail_count += 1
                    logger.error(f"❌ 示例向量生成失败 ID={example['id']}")
                    
            except Exception as e:
                fail_count += 1
                logger.error(f"❌ 处理示例失败 ID={example.get('id', 'unknown')}: {e}")
                
        # 批量插入
        if ids and vectors:
            try:
                logger.info(f"🚀 批量插入 {len(ids)} 个查询示例向量...")
                # 使用真实MySQL ID
                texts = [self.build_rich_text_for_example(example) for example in examples[:len(vectors)]]
                data = [ids, vectors, texts]
                collection.insert(data)
                collection.flush()
                
                # 验证结果
                time.sleep(2)
                collection.load()
                final_count = collection.num_entities
                logger.info(f"📊 查询示例向量数量: {final_count}")
                
            except Exception as e:
                logger.error(f"❌ 批量插入查询示例向量失败: {e}")
                return False
                
        logger.info(f"🎯 查询示例处理总结: 成功 {success_count} 条, 失败 {fail_count} 条")
        return success_count > 0
        
    def test_vector_search(self) -> bool:
        """测试向量搜索功能"""
        logger.info("🧪 测试向量搜索功能...")
        
        try:
            # 测试查询示例搜索
            collection = Collection(self.collections['query_examples']['name'])
            collection.load()
            
            test_query = "北京销售额"
            test_vector = self.get_embedding(test_query)
            
            if not test_vector:
                logger.error("❌ 无法生成测试向量")
                return False
                
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
            
            results = collection.search(
                data=[test_vector],
                anns_field="vector", 
                param=search_params,
                limit=3,
                output_fields=["id"]
            )
            
            if results and len(results[0]) > 0:
                logger.info(f"✅ 查询示例搜索测试成功，找到 {len(results[0])} 条相似结果")
                for i, result in enumerate(results[0]):
                    logger.info(f"   {i+1}. ID={result.id}, 相似度={result.distance:.4f}")
            else:
                logger.warning("⚠️  查询示例搜索测试未找到结果")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 向量搜索测试失败: {e}")
            return False
            
    def run(self):
        """执行完整的向量重建流程"""
        self.print_banner()
        
        logger.info("📋 开始执行向量库重建流程...")
        
        # 步骤1: 获取密码
        logger.info("\n🔧 步骤1: 配置数据库连接")
        self.get_mysql_password()
        
        # 步骤2: 测试连接
        logger.info("\n🔍 步骤2: 测试服务连接")
        if not self.test_connections():
            logger.error("❌ 服务连接测试失败，请检查配置")
            return False
            
        # 步骤3: 清理旧向量
        logger.info("\n🗑️  步骤3: 清理旧向量数据")
        
        # 清理业务术语向量
        if not self.clear_collection('business_terms'):
            logger.warning("⚠️  业务术语向量清理可能不完整")
            
        # 清理查询示例向量  
        if not self.clear_collection('query_examples'):
            logger.warning("⚠️  查询示例向量清理可能不完整")
            
        # 步骤4: 重建业务术语向量
        logger.info("\n🚀 步骤4: 重建业务术语向量")
        if not self.rebuild_business_terms():
            logger.error("❌ 业务术语向量重建失败")
            return False
            
        # 步骤5: 重建查询示例向量
        logger.info("\n🚀 步骤5: 重建查询示例向量") 
        if not self.rebuild_query_examples():
            logger.error("❌ 查询示例向量重建失败")
            return False
            
        # 步骤6: 测试搜索功能
        logger.info("\n🧪 步骤6: 测试向量搜索功能")
        if self.test_vector_search():
            logger.info("✅ 向量搜索功能正常")
        else:
            logger.warning("⚠️  向量搜索功能可能存在问题")
            
        # 完成总结
        logger.info("\n🎉 向量库重建完成！")
        logger.info("=" * 80)
        logger.info("📊 重建摘要:")
        logger.info("   🤖 模型: text-embedding-v4")
        logger.info("   🔄 策略: 上下文丰富化向量")
        logger.info("   ✅ 包含集合: business_terms, query_examples")
        logger.info("=" * 80)
        
        return True

def main():
    """主入口函数"""
    try:
        service = VectorRebuildService()
        success = service.run()
        
        if success:
            print("\n✨ 向量库重建成功完成！")
            print("💡 提示：可以启动ChatBI应用测试新的向量搜索效果")
            sys.exit(0)
        else:
            print("\n❌ 向量库重建失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行过程中发生异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 