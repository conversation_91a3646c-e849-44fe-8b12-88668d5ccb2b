#!/bin/bash

# =================================================================
# ChatBI Milvus向量库重建脚本
# 用途：应用新的上下文丰富化向量策略，重建所有query_examples的向量索引
# =================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================================================================${NC}"
echo -e "${BLUE}             ChatBI Milvus向量库重建脚本${NC}"
echo -e "${BLUE}==================================================================${NC}"
echo ""

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo -e "${RED}❌ 错误：Java未安装或不在PATH中${NC}"
    exit 1
fi

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ 错误：Docker未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 开始执行向量索引重建流程...${NC}"
echo ""

# 步骤1：检查服务状态
echo -e "${BLUE}步骤1: 检查服务状态${NC}"
echo "----------------------------------------"

# 检查Milvus服务
echo "🔍 检查Milvus服务状态..."
if docker ps | grep -q milvus; then
    echo -e "${GREEN}✅ Milvus服务正在运行${NC}"
else
    echo -e "${RED}❌ Milvus服务未运行，请先启动Milvus${NC}"
    exit 1
fi

# 检查MySQL服务（本地服务）
echo "🔍 检查MySQL服务状态..."
if brew services list | grep -q "mysql.*started"; then
    echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
elif mysqladmin ping -h localhost --silent; then
    echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
else
    echo -e "${RED}❌ MySQL服务未运行，请先启动MySQL${NC}"
    echo -e "${YELLOW}💡 提示：使用 'brew services start mysql' 启动MySQL${NC}"
    exit 1
fi

echo ""

# 步骤2：备份当前数据
echo -e "${BLUE}步骤2: 数据备份${NC}"
echo "----------------------------------------"

BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 创建备份目录: $BACKUP_DIR"

# 备份MySQL数据
echo "💾 备份MySQL中的query_examples数据..."
echo -e "${YELLOW}🔧 请输入MySQL root密码（如果需要跳过备份请输入 'skip'）:${NC}"
read -s mysql_password

if [ "$mysql_password" = "skip" ]; then
    echo -e "${YELLOW}⚠️  跳过数据备份步骤${NC}"
    echo "backup_skipped" > "$BACKUP_DIR/backup_status.txt"
else
    mysqldump -u root -p"$mysql_password" chatbi_metadata query_examples > "$BACKUP_DIR/query_examples_backup.sql" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ MySQL数据备份完成${NC}"
    else
        echo -e "${RED}❌ MySQL数据备份失败${NC}"
        echo -e "${YELLOW}💡 是否继续执行向量重建？(y/n)${NC}"
        read -r continue_choice
        if [ "$continue_choice" != "y" ]; then
            exit 1
        fi
        echo "backup_failed" > "$BACKUP_DIR/backup_status.txt"
    fi
fi

echo ""

# 步骤3：清理旧的向量数据
echo -e "${BLUE}步骤3: 清理Milvus中的旧向量数据${NC}"
echo "----------------------------------------"

echo "🗑️  清理query_examples集合中的所有向量..."

# 使用Python脚本清理Milvus数据
cat > clear_milvus_vectors.py << 'EOF'
import sys
from pymilvus import connections, Collection, utility

try:
    # 连接到Milvus
    connections.connect("default", host="localhost", port="19530")
    print("✅ 连接到Milvus成功")
    
    # 检查集合是否存在
    collection_name = "query_examples"
    if utility.has_collection(collection_name):
        collection = Collection(collection_name)
        
        # 获取当前数据量
        collection.load()
        count_before = collection.num_entities
        print(f"📊 清理前向量数量: {count_before}")
        
        # 删除所有数据
        collection.delete(expr="id >= 0")
        collection.flush()
        
        # 强制刷新以确保删除生效
        import time
        time.sleep(2)
        collection.compact()
        time.sleep(1)
        
        # 确认清理结果
        count_after = collection.num_entities
        print(f"📊 清理后向量数量: {count_after}")
        
        if count_after == 0:
            print("✅ Milvus向量数据清理完成")
        else:
            print(f"⚠️  警告：仍有 {count_after} 条数据未清理")
            
    else:
        print(f"⚠️  集合 {collection_name} 不存在，跳过清理")
        
except Exception as e:
    print(f"❌ 清理Milvus数据时出错: {e}")
    sys.exit(1)

print("🎉 Milvus数据清理完成")
EOF

# 执行清理脚本
if command -v python3 &> /dev/null; then
    python3 clear_milvus_vectors.py
else
    echo -e "${YELLOW}⚠️  警告：Python3未安装，跳过自动清理。请手动清理Milvus数据${NC}"
fi

echo ""

# 步骤4：重建向量索引
echo -e "${BLUE}步骤4: 使用新策略重建向量索引${NC}"
echo "----------------------------------------"

echo "🔧 编译Java应用..."
mvn clean compile -q -Dmaven.test.skip=true

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Java应用编译失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Java应用编译成功${NC}"

echo "🚀 启动向量重建任务..."
echo "   使用新的上下文丰富化策略..."

# 运行向量重建任务
cd chatbi-knowledge-service
mvn exec:java -Dexec.mainClass="com.qding.chatbi.knowledge.SyncQueryExamplesToMilvusApp" \
    -Dexec.classpathScope="test" \
    -Dspring.profiles.active=dev \
    -q
cd ..

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 向量索引重建完成${NC}"
else
    echo -e "${RED}❌ 向量索引重建失败${NC}"
    echo -e "${YELLOW}💡 尝试恢复备份数据...${NC}"
    # 这里可以添加恢复逻辑
    exit 1
fi

echo ""

# 步骤5：验证重建结果
echo -e "${BLUE}步骤5: 验证重建结果${NC}"
echo "----------------------------------------"

# 创建验证脚本
cat > verify_rebuild.py << 'EOF'
import sys
from pymilvus import connections, Collection

try:
    # 连接到Milvus
    connections.connect("default", host="localhost", port="19530")
    
    collection_name = "query_examples"
    if not utility.has_collection(collection_name):
        print(f"❌ 集合 {collection_name} 不存在")
        sys.exit(1)
        
    collection = Collection(collection_name)
    collection.load()
    
    # 检查数据量
    count = collection.num_entities
    print(f"📊 重建后向量数量: {count}")
    
    if count == 0:
        print("❌ 警告：重建后没有数据")
        sys.exit(1)
    elif count < 10:
        print("⚠️  警告：数据量较少，请检查")
    else:
        print("✅ 向量数据重建成功")
        
    # 简单测试搜索功能
    print("🔍 测试向量搜索功能...")
    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
    
    # 这里需要一个测试向量，简化处理
    print("✅ 重建验证完成")
    
except Exception as e:
    print(f"❌ 验证过程出错: {e}")
    sys.exit(1)
EOF

# 执行验证
if command -v python3 &> /dev/null; then
    python3 verify_rebuild.py
else
    echo -e "${YELLOW}⚠️  Python3未安装，跳过自动验证${NC}"
fi

echo ""

# 步骤6：性能测试
echo -e "${BLUE}步骤6: 性能测试${NC}"
echo "----------------------------------------"

echo "🧪 执行搜索性能测试..."

# 使用现有的测试脚本
if [ -f "test_milvus_search.sh" ]; then
    chmod +x test_milvus_search.sh
    ./test_milvus_search.sh
else
    echo -e "${YELLOW}⚠️  性能测试脚本不存在，跳过测试${NC}"
fi

echo ""

# 清理临时文件
echo -e "${BLUE}清理临时文件${NC}"
echo "----------------------------------------"
rm -f clear_milvus_vectors.py verify_rebuild.py

echo ""
echo -e "${GREEN}==================================================================${NC}"
echo -e "${GREEN}             🎉 向量索引重建流程完成！${NC}"
echo -e "${GREEN}==================================================================${NC}"
echo ""
echo -e "${BLUE}📊 重建摘要:${NC}"
echo "   💾 备份目录: $BACKUP_DIR"
echo "   🔄 重建策略: 上下文丰富化向量"
echo "   ✅ 新向量包含: 用户问题 + 数据集信息 + 指标维度 + 查询类型"
echo ""
echo -e "${YELLOW}📝 后续建议:${NC}"
echo "   1. 监控查询性能变化"
echo "   2. 根据实际效果调整向量化策略"
echo "   3. 定期备份新的向量数据"
echo "   4. 可以删除备份目录 $BACKUP_DIR (如果确认重建成功)"
echo ""
echo -e "${GREEN}✨ 新的上下文丰富化向量策略已生效！${NC}" 