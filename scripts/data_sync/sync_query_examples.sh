#!/bin/bash

# 同步query_examples数据到Milvus的Shell脚本
# 前提：管理后台服务必须已经启动

API_BASE="http://localhost:8081/api/admin"
USERNAME="admin"
PASSWORD="password"

echo "=== 开始同步query_examples到Milvus ==="

# 1. 登录获取token
echo "正在登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "登录失败，请确保管理后台服务已启动"
  exit 1
fi

echo "登录成功，获取到token"

# 2. 获取所有query_examples
echo "获取query_examples列表..."
EXAMPLES_RESPONSE=$(curl -s -X GET "$API_BASE/knowledge-base/examples?size=100" \
  -H "Authorization: Bearer $TOKEN")

# 3. 提取所有的ID
IDS=$(echo $EXAMPLES_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$IDS" ]; then
  echo "没有找到任何query_examples"
  exit 0
fi

# 统计数量
TOTAL=$(echo "$IDS" | wc -l | tr -d ' ')
SUCCESS=0
FAILED=0

echo "找到 $TOTAL 条记录，开始同步..."

# 4. 逐个更新（触发Milvus同步）
for ID in $IDS; do
  # 获取单个example的详情
  EXAMPLE=$(echo $EXAMPLES_RESPONSE | grep -o "{[^}]*\"id\":$ID[^}]*}" | head -1)
  
  # 提取字段
  USER_QUESTION=$(echo $EXAMPLE | grep -o '"userQuestion":"[^"]*' | cut -d'"' -f4)
  TARGET_QUERY=$(echo $EXAMPLE | grep -o '"targetQueryRepresentation":"[^"]*' | cut -d'"' -f4)
  DATASET_ID=$(echo $EXAMPLE | grep -o '"targetDatasetId":[0-9]*' | cut -d':' -f2)
  DIFFICULTY=$(echo $EXAMPLE | grep -o '"difficultyLevel":"[^"]*' | cut -d'"' -f4)
  NOTES=$(echo $EXAMPLE | grep -o '"notes":"[^"]*' | cut -d'"' -f4)
  
  echo -n "同步ID=$ID: ${USER_QUESTION:0:50}... "
  
  # 构建更新请求的JSON
  UPDATE_JSON=$(cat <<EOF
{
  "userQuestion": "$USER_QUESTION",
  "targetQueryRepresentation": "$TARGET_QUERY",
  "targetDatasetId": $DATASET_ID,
  "difficultyLevel": "$DIFFICULTY",
  "notes": "$NOTES"
}
EOF
)
  
  # 发送更新请求
  UPDATE_RESPONSE=$(curl -s -X PUT "$API_BASE/knowledge-base/examples/$ID" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "$UPDATE_JSON" \
    -w "\n%{http_code}")
  
  HTTP_CODE=$(echo "$UPDATE_RESPONSE" | tail -n 1)
  
  if [ "$HTTP_CODE" = "200" ]; then
    echo "✓ 成功"
    ((SUCCESS++))
  else
    echo "✗ 失败 (HTTP $HTTP_CODE)"
    ((FAILED++))
  fi
done

echo ""
echo "=== 同步完成 ==="
echo "成功: $SUCCESS 条"
echo "失败: $FAILED 条"
echo "总计: $TOTAL 条" 