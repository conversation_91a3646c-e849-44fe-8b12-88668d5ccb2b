#!/bin/bash

# ChatBI 向量库重建运行脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}       ChatBI 向量库重建工具${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""

# 检查Python环境
echo -e "${YELLOW}🔍 检查Python环境...${NC}"

if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装，请先安装Python3${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Python3 已安装: $(python3 --version)${NC}"

# 检查必要的Python包
echo -e "${YELLOW}🔍 检查Python依赖包...${NC}"

required_packages=("pymilvus" "mysql-connector-python" "requests")
missing_packages=()

for package in "${required_packages[@]}"; do
    if ! python3 -c "import ${package}" 2>/dev/null; then
        missing_packages+=("${package}")
    fi
done

if [ ${#missing_packages[@]} -ne 0 ]; then
    echo -e "${YELLOW}⚠️  缺少依赖包: ${missing_packages[*]}${NC}"
    echo -e "${BLUE}📦 正在安装缺少的依赖包...${NC}"
    
    for package in "${missing_packages[@]}"; do
        echo -e "${YELLOW}安装 ${package}...${NC}"
        if [ "$package" = "mysql-connector-python" ]; then
            pip3 install mysql-connector-python
        else
            pip3 install "$package"
        fi
        
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ 安装 ${package} 失败${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✅ 所有依赖包安装完成${NC}"
else
    echo -e "${GREEN}✅ 所有依赖包已安装${NC}"
fi

echo ""

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"

# 检查MySQL
if ! command -v mysql &> /dev/null; then
    echo -e "${YELLOW}⚠️  MySQL客户端未安装，跳过MySQL连接测试${NC}"
else
    if mysqladmin ping -h localhost -u root --silent 2>/dev/null; then
        echo -e "${GREEN}✅ MySQL服务正在运行${NC}"
    else
        echo -e "${RED}❌ MySQL服务未运行或连接失败${NC}"
        echo -e "${YELLOW}💡 请确保MySQL服务已启动并可以连接${NC}"
    fi
fi

# 检查Milvus (简单端口检查)
if timeout 3 bash -c "echo >/dev/tcp/localhost/19530" 2>/dev/null; then
    echo -e "${GREEN}✅ Milvus服务正在运行 (端口19530可达)${NC}"
else
    echo -e "${RED}❌ Milvus服务未运行或端口19530不可达${NC}"
    echo -e "${YELLOW}💡 请确保Milvus服务已启动${NC}"
fi

echo ""

# 确认执行
echo -e "${YELLOW}⚠️  警告：此操作将删除现有的向量数据并重新生成${NC}"
echo -e "${YELLOW}📋 重建内容包括：${NC}"
echo -e "${YELLOW}   - business_terms 集合的所有向量${NC}"
echo -e "${YELLOW}   - query_examples 集合的所有向量${NC}"
echo ""
echo -e "${BLUE}🤖 使用模型：text-embedding-v4${NC}"
echo -e "${BLUE}🔄 向量化策略：上下文丰富化${NC}"
echo ""

read -p "确认继续执行重建？(y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo -e "${YELLOW}❌ 用户取消操作${NC}"
    exit 0
fi

echo ""
echo -e "${GREEN}🚀 开始执行向量重建...${NC}"
echo ""

# 运行Python重建脚本
python3 rebuild_vectors_with_text_embedding_v4.py

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}============================================${NC}"
    echo -e "${GREEN}       ✨ 向量重建成功完成！${NC}"
    echo -e "${GREEN}============================================${NC}"
    echo ""
    echo -e "${BLUE}📝 后续建议：${NC}"
    echo -e "${BLUE}   1. 启动ChatBI应用测试向量搜索效果${NC}"
    echo -e "${BLUE}   2. 观察查询匹配精度的变化${NC}"
    echo -e "${BLUE}   3. 根据效果调整向量化策略${NC}"
    echo ""
else
    echo ""
    echo -e "${RED}============================================${NC}"
    echo -e "${RED}       ❌ 向量重建失败${NC}"
    echo -e "${RED}============================================${NC}"
    echo ""
    echo -e "${YELLOW}💡 故障排查建议：${NC}"
    echo -e "${YELLOW}   1. 检查MySQL连接和密码${NC}"
    echo -e "${YELLOW}   2. 检查Milvus服务状态${NC}"
    echo -e "${YELLOW}   3. 检查Dashscope API密钥${NC}"
    echo -e "${YELLOW}   4. 查看日志文件了解详细错误信息${NC}"
    echo ""
fi 