#!/bin/bash

# ChatBI 查询范例更新脚本
# 生成符合新的NLU Agent提示词模板的查询范例数据

echo "🚀 ChatBI 查询范例数据更新工具"
echo "=================================="
echo "目标：生成符合新NLU Agent提示词模板的查询范例数据"
echo "向量维度：1024维 (text-embedding-v4模型)"
echo ""

# 检查Python环境
echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查依赖包
echo "📦 检查依赖包..."
dependencies=("mysql-connector-python" "pymilvus" "requests")

for dep in "${dependencies[@]}"; do
    if ! python3 -c "import ${dep//-/_}" 2>/dev/null; then
        echo "⚠️  缺少依赖包 $dep，正在安装..."
        pip3 install $dep
        if [ $? -ne 0 ]; then
            echo "❌ 安装 $dep 失败，请手动安装"
            exit 1
        fi
    else
        echo "✅ $dep 已安装"
    fi
done

# 检查MySQL服务
echo "🔍 检查MySQL服务..."
if ! nc -z localhost 3306 2>/dev/null; then
    echo "❌ MySQL服务不可达，请确保MySQL正在运行"
    exit 1
else
    echo "✅ MySQL服务正常"
fi

# 检查Milvus服务
echo "🔍 检查Milvus服务..."
if ! nc -z localhost 19530 2>/dev/null; then
    echo "❌ Milvus服务不可达，请确保Milvus正在运行"
    exit 1
else
    echo "✅ Milvus服务正常"
fi

echo ""
echo "⚠️  注意事项："
echo "1. 此操作将清空现有的查询范例数据"
echo "2. 生成的新数据符合新的JSON格式要求"
echo "3. 向量将使用text-embedding-v4模型重新生成"
echo "4. 需要输入MySQL root密码"
echo ""

read -p "确认要继续吗？(y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

echo ""
echo "🚀 开始执行更新..."
python3 update_query_examples_json_format.py

exit_code=$?
if [ $exit_code -eq 0 ]; then
    echo ""
    echo "🎉 查询范例数据更新完成！"
    echo "新的数据已符合NLU Agent提示词模板要求"
else
    echo ""
    echo "❌ 更新失败，请检查日志文件"
fi

exit $exit_code 