import sys
from pymilvus import connections, Collection, utility

try:
    # 连接到Milvus
    connections.connect("default", host="localhost", port="19530")
    print("✅ 连接到Milvus成功")
    
    # 检查集合是否存在
    collection_name = "query_examples"
    if utility.has_collection(collection_name):
        collection = Collection(collection_name)
        
        # 获取当前数据量
        collection.load()
        count_before = collection.num_entities
        print(f"📊 清理前向量数量: {count_before}")
        
        # 删除所有数据
        collection.delete(expr="id >= 0")
        collection.flush()
        
        # 强制刷新以确保删除生效
        import time
        time.sleep(2)
        collection.compact()
        time.sleep(1)
        
        # 确认清理结果
        count_after = collection.num_entities
        print(f"📊 清理后向量数量: {count_after}")
        
        if count_after == 0:
            print("✅ Milvus向量数据清理完成")
        else:
            print(f"⚠️  警告：仍有 {count_after} 条数据未清理")
            
    else:
        print(f"⚠️  集合 {collection_name} 不存在，跳过清理")
        
except Exception as e:
    print(f"❌ 清理Milvus数据时出错: {e}")
    sys.exit(1)

print("🎉 Milvus数据清理完成")
