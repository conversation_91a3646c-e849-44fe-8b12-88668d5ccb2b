#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Milvus向量库中的数据并与MySQL进行对比
"""

try:
    from pymilvus import connections, Collection, utility
    import mysql.connector
    PYMILVUS_AVAILABLE = True
except ImportError:
    PYMILVUS_AVAILABLE = False
    print("警告: pymilvus 或 mysql-connector-python 未安装")

# 配置参数
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',  # 空密码
    'database': 'chatbi_metadata'
}

MILVUS_CONFIG = {
    'host': 'localhost',
    'port': '19530'
}

COLLECTION_NAME = 'query_examples'

def check_mysql_data():
    """检查MySQL中的数据"""
    print("=== 检查MySQL数据 ===")
    try:
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = mysql_conn.cursor(dictionary=True)
        
        # 查询所有记录
        cursor.execute("SELECT id, user_question, difficulty_level FROM query_examples ORDER BY id")
        mysql_records = cursor.fetchall()
        
        print(f"MySQL中总共有 {len(mysql_records)} 条记录:")
        for record in mysql_records:
            print(f"  ID: {record['id']} - {record['user_question'][:40]}... ({record['difficulty_level']})")
        
        cursor.close()
        mysql_conn.close()
        return mysql_records
        
    except Exception as e:
        print(f"检查MySQL数据失败: {e}")
        return []

def check_milvus_data():
    """检查Milvus中的数据"""
    print(f"\n=== 检查Milvus数据 ===")
    
    if not PYMILVUS_AVAILABLE:
        print("无法检查Milvus数据，因为pymilvus未安装")
        return []
    
    try:
        # 连接Milvus
        connections.connect("default", host=MILVUS_CONFIG['host'], port=MILVUS_CONFIG['port'])
        
        # 检查集合是否存在
        if not utility.has_collection(COLLECTION_NAME):
            print(f"集合 '{COLLECTION_NAME}' 不存在")
            return []
        
        # 获取集合
        collection = Collection(COLLECTION_NAME)
        
        # 获取集合统计信息
        collection.load()
        print(f"集合 '{COLLECTION_NAME}' 存在")
        
        # 查询所有数据的ID
        results = collection.query(
            expr="id > 0",  # 查询所有记录
            output_fields=["id"],
            limit=1000
        )
        
        print(f"Milvus中总共有 {len(results)} 条记录:")
        ids = sorted([record['id'] for record in results])
        print(f"  ID列表: {ids}")
        
        connections.disconnect("default")
        return ids
        
    except Exception as e:
        print(f"检查Milvus数据失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def compare_data(mysql_records, milvus_ids):
    """对比MySQL和Milvus的数据"""
    print(f"\n=== 数据对比 ===")
    
    mysql_ids = [record['id'] for record in mysql_records]
    mysql_ids_set = set(mysql_ids)
    milvus_ids_set = set(milvus_ids)
    
    print(f"MySQL记录数: {len(mysql_ids)}")
    print(f"Milvus记录数: {len(milvus_ids)}")
    
    # 检查缺失的记录
    missing_in_milvus = mysql_ids_set - milvus_ids_set
    extra_in_milvus = milvus_ids_set - mysql_ids_set
    
    if missing_in_milvus:
        print(f"⚠️  在Milvus中缺失的记录ID: {sorted(missing_in_milvus)}")
        print("对应的问题:")
        for record in mysql_records:
            if record['id'] in missing_in_milvus:
                print(f"  ID {record['id']}: {record['user_question'][:50]}...")
    
    if extra_in_milvus:
        print(f"⚠️  在Milvus中多出的记录ID: {sorted(extra_in_milvus)}")
    
    if not missing_in_milvus and not extra_in_milvus:
        print("✅ 数据完全同步！MySQL和Milvus中的ID完全一致")
    else:
        print(f"❌ 数据不同步！缺失: {len(missing_in_milvus)}条, 多出: {len(extra_in_milvus)}条")

def main():
    print("检查向量库数据与数据库的对应关系")
    print("=" * 50)
    
    # 检查MySQL数据
    mysql_records = check_mysql_data()
    
    # 检查Milvus数据
    milvus_ids = check_milvus_data()
    
    # 对比数据
    if mysql_records:
        compare_data(mysql_records, milvus_ids)
    
    print("\n检查完成！")

if __name__ == "__main__":
    main() 