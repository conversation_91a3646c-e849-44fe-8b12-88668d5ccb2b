#!/bin/bash

echo "==========================================="
echo "   检查query_examples数据同步状态"
echo "==========================================="

# 1. 检查MySQL数据
echo ""
echo "1. 📊 MySQL数据库状态："
echo "----------------------------------------"
mysql_result=$(mysql -uroot -sN -e "SELECT COUNT(*) FROM chatbi_metadata.query_examples;" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "   ✓ MySQL连接成功"
    echo "   📝 记录总数: $mysql_result"
    
    echo ""
    echo "   📄 所有记录ID和问题预览:"
    mysql -uroot -e "
    SELECT 
        id,
        LEFT(user_question, 35) as question_preview,
        difficulty_level
    FROM chatbi_metadata.query_examples 
    ORDER BY id;
    " 2>/dev/null | while read line; do
        echo "     $line"
    done
else
    echo "   ✗ MySQL连接失败"
fi

# 2. 检查Milvus服务
echo ""
echo "2. 🔌 Milvus服务状态："
echo "----------------------------------------"
milvus_status=$(docker ps --filter "name=milvus" --format "table {{.Names}}\t{{.Status}}" | grep -v NAMES)
if [ -n "$milvus_status" ]; then
    echo "   ✓ Milvus容器运行中:"
    echo "     $milvus_status"
else
    echo "   ✗ Milvus容器未运行"
fi

# 3. 检查Milvus连接
echo ""
echo "3. 🧪 测试Milvus连接："
echo "----------------------------------------"
# 使用Python简单测试连接（如果可用）
python3 -c "
import sys
try:
    from pymilvus import connections, utility
    try:
        connections.connect('default', host='localhost', port='19530')
        # 检查query_examples集合
        if utility.has_collection('query_examples'):
            from pymilvus import Collection
            collection = Collection('query_examples')
            collection.load()
            print('   ✓ Milvus连接成功')
            print('   ✓ query_examples集合存在')
            
            # 尝试查询数据量
            try:
                stats = collection.num_entities
                print(f'   📝 Milvus中的记录数: {stats}')
            except Exception as e:
                print(f'   ⚠️  获取记录数失败: {e}')
        else:
            print('   ✓ Milvus连接成功')
            print('   ⚠️  query_examples集合不存在')
        connections.disconnect('default')
    except Exception as e:
        print(f'   ✗ Milvus连接失败: {e}')
except ImportError:
    print('   ⚠️  pymilvus未安装，无法测试连接')
    # 尝试使用telnet测试端口
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 19530))
        sock.close()
        if result == 0:
            print('   ✓ Milvus端口19530可达')
        else:
            print('   ✗ Milvus端口19530不可达')
    except Exception as e:
        print(f'   ✗ 端口测试失败: {e}')
" 2>/dev/null

# 4. 通过现有API测试向量搜索
echo ""
echo "4. 🔍 测试向量搜索功能："
echo "----------------------------------------"
# 测试KnowledgePersistenceService的搜索功能
echo "   正在测试搜索相似示例..."

# 创建测试用的临时Java类
cat > TestMilvusSearch.java << 'EOF'
import java.io.*;
import java.net.*;

public class TestMilvusSearch {
    public static void main(String[] args) {
        try {
            // 模拟向量搜索请求 - 这里只是测试连接
            URL url = new URL("http://localhost:8081/actuator/health");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(3000);
            int responseCode = conn.getResponseCode();
            
            if (responseCode == 200) {
                System.out.println("   ✓ 主应用服务运行中");
            } else {
                System.out.println("   ⚠️  主应用服务响应异常: " + responseCode);
            }
        } catch (Exception e) {
            System.out.println("   ✗ 主应用服务不可达: " + e.getMessage());
        }
    }
}
EOF

java TestMilvusSearch.java 2>/dev/null
rm -f TestMilvusSearch.java TestMilvusSearch.class

# 5. 总结
echo ""
echo "5. 📋 同步状态总结："
echo "----------------------------------------"

# 从前面的检查结果判断
if [ "$mysql_result" -gt 0 ] 2>/dev/null; then
    echo "   📊 MySQL: ✓ 有 $mysql_result 条query_examples记录"
else
    echo "   📊 MySQL: ✗ 无法获取数据或无记录"
fi

if [ -n "$milvus_status" ]; then
    echo "   🔌 Milvus: ✓ 服务运行中"
else
    echo "   🔌 Milvus: ✗ 服务未运行"
fi

echo ""
echo "⚡ 建议操作："
echo "----------------------------------------"
echo "   1. 如果Milvus中无数据，请执行数据同步"
echo "   2. 可使用管理后台逐条编辑保存来触发同步"
echo "   3. 或者通过API批量同步数据"

echo ""
echo "===========================================" 