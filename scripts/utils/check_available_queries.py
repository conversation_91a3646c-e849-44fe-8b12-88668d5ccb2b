#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
import json
from typing import List, Dict

def analyze_available_queries():
    """分析现有的查询示例数据"""
    
    # 连接数据库
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    cursor = conn.cursor()
    
    # 获取所有查询示例
    query = """
    SELECT qe.id, qe.user_question, qe.target_query_representation, 
           qe.difficulty_level, qd.dataset_name 
    FROM query_examples qe 
    LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
    ORDER BY qe.id
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    columns = [desc[0] for desc in cursor.description]
    
    print("📊 ChatBI查询示例数据分析")
    print("=" * 80)
    
    categories = {}
    
    for row in results:
        example = dict(zip(columns, row))
        
        print(f"\n🔍 ID: {example['id']}")
        print(f"问题: {example['user_question']}")
        print(f"难度: {example['difficulty_level']}")
        print(f"数据集: {example['dataset_name']}")
        
        # 分析查询表示
        if example['target_query_representation']:
            try:
                query_data = json.loads(example['target_query_representation'])
                if 'entities' in query_data:
                    entities = query_data['entities']
                    if 'metrics' in entities:
                        print(f"指标: {', '.join(entities['metrics'])}")
                    if 'dimensions_to_group' in entities:
                        print(f"维度: {', '.join(entities['dimensions_to_group'])}")
            except:
                pass
        
        # 分类统计
        category = categorize_query(example['user_question'])
        if category not in categories:
            categories[category] = []
        categories[category].append(example)
        
        print("-" * 50)
    
    # 显示分类统计
    print(f"\n📈 查询分类统计:")
    print("=" * 50)
    
    for category, queries in categories.items():
        print(f"\n{category} ({len(queries)}个):")
        for query in queries:
            print(f"  - ID:{query['id']} {query['user_question']}")
    
    conn.close()
    
    return categories

def categorize_query(question: str) -> str:
    """对查询进行分类"""
    question_lower = question.lower()
    
    if any(word in question_lower for word in ['排名', '最高', '最大', '最多', '前', 'top']):
        return "排名分析"
    elif any(word in question_lower for word in ['品类', '分类', '类别']):
        return "品类分析"  
    elif any(word in question_lower for word in ['城市', '地区', '北京', '上海', '区域']):
        return "地区分析"
    elif any(word in question_lower for word in ['趋势', '变化', '增长', '月']):
        return "趋势分析"
    elif any(word in question_lower for word in ['对比', '比较', '比例']):
        return "对比分析"
    elif any(word in question_lower for word in ['列出', '显示', '查看']):
        return "列表查询"
    elif any(word in question_lower for word in ['总', '累计', '汇总']):
        return "汇总分析"
    else:
        return "其他"

def create_targeted_test():
    """基于实际数据创建针对性测试"""
    
    categories = analyze_available_queries()
    
    print(f"\n🎯 基于实际数据的测试用例:")
    print("=" * 80)
    
    # 针对每个分类创建测试
    test_cases = []
    
    for category, queries in categories.items():
        if len(queries) >= 2:  # 至少有2个查询的分类才创建测试
            # 选择一个查询作为测试目标
            target_query = queries[0]
            similar_queries = [q['id'] for q in queries[1:]]
            
            test_case = {
                "query": target_query['user_question'],
                "category": category,
                "expected_ids": [target_query['id']] + similar_queries[:2],
                "target_id": target_query['id']
            }
            
            test_cases.append(test_case)
            
            print(f"\n测试用例 - {category}:")
            print(f"  查询: {target_query['user_question']}")
            print(f"  期望命中: {test_case['expected_ids']}")
    
    return test_cases

if __name__ == "__main__":
    test_cases = create_targeted_test()
    
    # 可以保存测试用例供后续使用
    import json
    with open('targeted_test_cases.json', 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 已生成 {len(test_cases)} 个针对性测试用例")
    print("💾 测试用例已保存到 targeted_test_cases.json")
