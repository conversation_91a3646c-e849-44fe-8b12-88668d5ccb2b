#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
深入分析为什么ID:35没有在top3中出现
"""

import mysql.connector
from pymilvus import connections, Collection, utility
import hashlib
import numpy as np
from typing import List, Dict

def get_embedding(text: str, normalize: bool = True) -> List[float]:
    """获取文本的向量表示"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
    
    if normalize:
        vector = np.array(vector)
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        return vector.tolist()
    
    return vector

def deep_analysis():
    """深入分析查询匹配问题"""
    
    test_query = "各类产品去年的总销售"
    expected_best_match_id = 35  # "各品类去年的总销售额"
    
    print("🔬 深入分析查询匹配问题")
    print("=" * 80)
    print(f"查询问题: '{test_query}'")
    print(f"期望最佳匹配: ID:{expected_best_match_id}")
    print("=" * 80)
    
    # 连接服务
    connections.connect("default", host='localhost', port='19530')
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    # 获取期望匹配的详细信息
    cursor = conn.cursor()
    cursor.execute(
        "SELECT user_question, target_query_representation FROM query_examples WHERE id = %s",
        (expected_best_match_id,)
    )
    row = cursor.fetchone()
    if row:
        expected_question, expected_repr = row
        print(f"\n📋 期望最佳匹配详情:")
        print(f"ID:{expected_best_match_id} - {expected_question}")
        if expected_repr:
            print(f"查询表示: {expected_repr}")
    
    # 比较向量化内容
    print(f"\n📊 向量化内容对比:")
    print("-" * 60)
    
    # 构建查询的向量化文本
    query_vector_text = f"用户问题: {test_query}"
    print(f"查询向量化文本:")
    print(f"  {query_vector_text}")
    
    # 构建期望匹配的向量化文本
    if row:
        expected_vector_text = build_rich_text_for_id(expected_best_match_id, conn)
        print(f"\nID:{expected_best_match_id}向量化文本:")
        print(f"  {expected_vector_text}")
    
    # 测试所有算法的完整排名
    algorithms = ["COSINE", "L2", "IP"]
    
    for algo in algorithms:
        print(f"\n{'='*60}")
        print(f"🔍 {algo}算法 - 完整Top10排名")
        print(f"{'='*60}")
        
        collection_name = f"query_examples_{algo.lower()}_test"
        if algo == "COSINE":
            # 也测试生产库
            test_algorithm_ranking(test_query, "query_examples", algo, conn, expected_best_match_id)
            print(f"\n{'-'*40}")
            print(f"测试集合:")
        
        if utility.has_collection(collection_name):
            test_algorithm_ranking(test_query, collection_name, algo, conn, expected_best_match_id)
        else:
            print(f"⚠️  集合 {collection_name} 不存在")
    
    # 分析字符级别相似性
    print(f"\n📝 字符级别相似性分析:")
    print("-" * 60)
    analyze_text_similarity(test_query, expected_question)
    
    conn.close()

def build_rich_text_for_id(example_id: int, conn) -> str:
    """为指定ID构建丰富文本"""
    
    cursor = conn.cursor()
    query = """
    SELECT qe.user_question, qe.target_query_representation, 
           qe.difficulty_level, qd.dataset_name 
    FROM query_examples qe 
    LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
    WHERE qe.id = %s
    """
    cursor.execute(query, (example_id,))
    row = cursor.fetchone()
    
    if not row:
        return ""
    
    user_question, target_query_repr, difficulty_level, dataset_name = row
    
    parts = []
    
    # 1. 核心用户问题
    if user_question:
        parts.append(f"用户问题: {user_question}")
        
    # 2. 关联数据集
    if dataset_name:
        parts.append(f"关联数据集: {dataset_name}")
        
    # 3. 查询类型
    if difficulty_level:
        parts.append(f"查询类型: {difficulty_level}")
        
    # 4. 从查询表示中提取关键信息
    if target_query_repr:
        try:
            import json
            data = json.loads(target_query_repr)
            if 'entities' in data:
                entities = data['entities']
                if 'metrics' in entities:
                    parts.append(f"核心指标: {', '.join(entities['metrics'])}")
                if 'dimensions_to_group' in entities:
                    dims = entities['dimensions_to_group']
                    # 翻译维度名
                    translated_dims = []
                    for dim in dims:
                        translations = {
                            'city': '城市',
                            'product_name': '产品',
                            'category': '品类',
                            'date': '日期',
                            'month': '月份',
                            'year': '年份'
                        }
                        translated_dims.append(translations.get(dim, dim))
                    parts.append(f"分析维度: {', '.join(translated_dims)}")
        except:
            pass
    
    return '\n'.join(parts)

def test_algorithm_ranking(query: str, collection_name: str, algo: str, conn, expected_id: int):
    """测试算法排名"""
    
    try:
        collection = Collection(collection_name)
        collection.load()
        
        # 生成查询向量
        normalize = algo in ["COSINE", "IP"]
        query_vector = get_embedding(query, normalize=normalize)
        
        # 搜索参数
        search_params = {
            "metric_type": algo,
            "params": {"nprobe": 10}
        }
        
        # 执行搜索
        results = collection.search(
            data=[query_vector],
            anns_field="vector",
            param=search_params,
            limit=16,  # 获取所有结果
            output_fields=["id", "difficulty_level"]
        )
        
        print(f"集合: {collection_name}")
        
        if results and len(results[0]) > 0:
            expected_found = False
            expected_rank = None
            
            for i, result in enumerate(results[0], 1):
                # 获取问题文本
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT user_question FROM query_examples WHERE id = %s",
                    (result.id,)
                )
                row = cursor.fetchone()
                question = row[0] if row else "未知"
                
                # 标记期望的结果
                if result.id == expected_id:
                    expected_found = True
                    expected_rank = i
                    marker = "🎯"
                else:
                    marker = "  "
                
                distance_label = "距离" if algo == "L2" else "相似度"
                
                if i <= 10:  # 只显示top10
                    print(f"{marker} {i:2d}. ID:{result.id} - {question}")
                    print(f"      {distance_label}: {result.distance:.4f}")
            
            if expected_found:
                print(f"\n✅ 期望结果ID:{expected_id}排名: #{expected_rank}")
            else:
                print(f"\n❌ 期望结果ID:{expected_id}未在结果中找到")
                
        else:
            print("❌ 未找到任何结果")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def analyze_text_similarity(query1: str, query2: str):
    """分析文本相似性"""
    
    print(f"查询1: '{query1}'")
    print(f"查询2: '{query2}'")
    
    # 计算字符重叠
    chars1 = set(query1)
    chars2 = set(query2)
    common_chars = chars1.intersection(chars2)
    char_similarity = len(common_chars) / max(len(chars1), len(chars2))
    
    print(f"字符相似度: {char_similarity:.2f}")
    print(f"公共字符: {sorted(common_chars)}")
    
    # 计算词汇重叠
    words1 = set(query1.replace("的", "").replace("总", ""))
    words2 = set(query2.replace("的", "").replace("总", ""))
    common_words = words1.intersection(words2)
    word_similarity = len(common_words) / max(len(words1), len(words2))
    
    print(f"词汇相似度: {word_similarity:.2f}")
    print(f"公共词汇: {sorted(common_words)}")
    
    # 关键差异
    diff1 = words1 - words2
    diff2 = words2 - words1
    print(f"查询1独有: {sorted(diff1)}")
    print(f"查询2独有: {sorted(diff2)}")

if __name__ == "__main__":
    deep_analysis()
