#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化向量匹配效果
针对"各品类去年的总销售额"匹配不准确的问题进行优化
"""

import mysql.connector
import json
import hashlib
import numpy as np
from pymilvus import connections, Collection, utility
from typing import List, Dict
import re

class VectorMatchingOptimizer:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',
            'database': 'chatbi_metadata'
        }
        
        # 增强的业务关键词权重
        self.business_keywords = {
            # 高权重核心业务词汇
            "销售额": 2.0, "收入": 2.0, "订单数": 2.0, "产品": 1.8, "品类": 1.8,
            "城市": 1.5, "地区": 1.5, "北京": 1.5, "上海": 1.5,
            "去年": 1.8, "今年": 1.5, "本月": 1.5, "上月": 1.5,
            "趋势": 1.6, "变化": 1.6, "增长": 1.6, "排名": 1.6,
            "最高": 1.4, "最低": 1.4, "总": 1.6, "累计": 1.6,
            "对比": 1.4, "分析": 1.3, "查询": 1.2, "数据": 1.2,
            # 中等权重
            "每日": 1.2, "月度": 1.2, "年度": 1.2, "同比": 1.4, "环比": 1.4,
            "占比": 1.3, "比例": 1.3, "汇总": 1.2, "统计": 1.2
        }
        
    def connect_services(self):
        """连接服务"""
        try:
            connections.connect('default', host='localhost', port='19530')
            self.mysql_conn = mysql.connector.connect(**self.mysql_config)
            return True
        except Exception as e:
            print(f'❌ 连接失败: {e}')
            return False
    
    def get_enhanced_embedding(self, text: str, normalize: bool = True) -> List[float]:
        """
        生成增强的向量表示
        特别优化对业务关键词的敏感度
        """
        # 计算关键词权重
        keyword_boost = 1.0
        matched_keywords = []
        
        for keyword, weight in self.business_keywords.items():
            if keyword in text:
                keyword_boost += (weight - 1.0) * 0.1  # 适度增强，避免过度
                matched_keywords.append(keyword)
        
        # 基础哈希向量生成
        hash_methods = [
            hashlib.md5(text.encode('utf-8')).digest(),
            hashlib.sha1(text.encode('utf-8')).digest(),
            hashlib.sha256(text.encode('utf-8')).digest()
        ]
        
        # 生成1536维向量
        vector = []
        for i in range(1536):
            hash_idx = i % len(hash_methods)
            byte_idx = i % len(hash_methods[hash_idx])
            base_value = float(hash_methods[hash_idx][byte_idx]) / 255.0 - 0.5
            
            # 应用关键词增强
            enhanced_value = base_value * keyword_boost
            
            # 添加一些噪声来模拟语义相似性
            if i % 7 == 0 and matched_keywords:  # 每7个维度增强一次
                enhanced_value *= 1.1
            
            vector.append(enhanced_value)
        
        # 归一化
        if normalize:
            vector = np.array(vector)
            norm = np.linalg.norm(vector)
            if norm > 0:
                vector = vector / norm
            return vector.tolist()
        
        return vector
    
    def rebuild_vectors_with_enhancement(self):
        """使用增强算法重建向量"""
        
        try:
            collection = Collection('query_examples')
            
            # 清理现有数据
            collection.delete("id >= 0")
            collection.flush()
            print("✅ 清理现有向量数据")
            
            # 获取查询示例数据
            cursor = self.mysql_conn.cursor()
            query = """
            SELECT qe.id, qe.user_question, qe.target_query_representation, 
                   qe.difficulty_level, qd.dataset_name 
            FROM query_examples qe 
            LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
            ORDER BY qe.id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            ids = []
            vectors = []
            difficulty_levels = []
            dataset_names = []
            
            print(f"📊 重建 {len(results)} 条向量索引（使用增强算法）...")
            
            for row in results:
                example = dict(zip(columns, row))
                
                # 构建丰富文本
                rich_text = self.build_rich_text_for_example(example)
                
                # 使用增强算法生成向量
                vector = self.get_enhanced_embedding(rich_text)
                
                ids.append(example['id'])
                vectors.append(vector)
                difficulty_levels.append(example.get('difficulty_level', ''))
                dataset_names.append(example.get('dataset_name', ''))
                
                print(f"  ✅ ID {example['id']}: {example['user_question']}")
                if example['id'] == 35:  # 特别关注目标示例
                    print(f"     🎯 目标示例丰富文本: {rich_text}")
            
            # 批量插入
            if ids:
                data = [ids, vectors, difficulty_levels, dataset_names]
                collection.insert(data)
                collection.flush()
                
                print(f"✅ 重建完成，共 {len(ids)} 条记录")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 重建失败: {e}")
            return False
    
    def build_rich_text_for_example(self, example: Dict) -> str:
        """构建丰富文本"""
        parts = []
        
        if example.get('user_question'):
            parts.append(f"用户问题: {example['user_question']}")
        if example.get('dataset_name'):
            parts.append(f"关联数据集: {example['dataset_name']}")
        if example.get('difficulty_level'):
            parts.append(f"查询类型: {example['difficulty_level']}")
        
        if example.get('target_query_representation'):
            try:
                data = json.loads(example['target_query_representation'])
                if 'entities' in data:
                    entities = data['entities']
                    if 'metrics' in entities and entities['metrics']:
                        parts.append(f"核心指标: {', '.join(entities['metrics'])}")
                    if 'dimensions_to_group' in entities and entities['dimensions_to_group']:
                        dims = entities['dimensions_to_group']
                        translated_dims = []
                        for dim in dims:
                            translations = {
                                'city': '城市', 'product_name': '产品', 'category': '品类',
                                'date': '日期', 'month': '月份', 'year': '年份'
                            }
                            translated_dims.append(translations.get(dim, dim))
                        parts.append(f"分析维度: {', '.join(translated_dims)}")
            except:
                pass
        
        return '\n'.join(parts)
    
    def test_enhanced_matching(self):
        """测试增强后的匹配效果"""
        
        collection = Collection('query_examples')
        
        test_queries = [
            {
                "query": "各品类去年的总销售额",
                "expected_id": 35,
                "description": "目标查询 - 品类销售分析"
            },
            {
                "query": "北京销售额",
                "expected_id": 5,
                "description": "地区销售查询"
            },
            {
                "query": "销售额最高的产品排名",
                "expected_id": 9,
                "description": "产品排名查询"
            }
        ]
        
        print(f"\n🔍 测试增强后的匹配效果:")
        print("="*60)
        
        total_score = 0
        for test in test_queries:
            print(f"\n📋 {test['description']}")
            print(f"查询: '{test['query']}'")
            print(f"期望命中ID: {test['expected_id']}")
            
            # 生成查询向量
            query_vector = self.get_enhanced_embedding(test['query'])
            
            # 搜索
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
            results = collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=5,
                output_fields=["id", "difficulty_level"]
            )
            
            if results and len(results[0]) > 0:
                cursor = self.mysql_conn.cursor()
                found_expected = False
                expected_rank = 0
                
                for i, result in enumerate(results[0], 1):
                    cursor.execute(
                        "SELECT user_question FROM query_examples WHERE id = %s",
                        (result.id,)
                    )
                    row = cursor.fetchone()
                    question = row[0] if row else "未知"
                    
                    if result.id == test['expected_id']:
                        found_expected = True
                        expected_rank = i
                        status = "🎯"
                    else:
                        status = "📄"
                    
                    print(f"  {status} 第{i}名 ID:{result.id} - {question} (相似度: {result.distance:.4f})")
                
                # 计算得分
                if found_expected:
                    if expected_rank == 1:
                        score = 1.0
                    elif expected_rank == 2:
                        score = 0.6
                    elif expected_rank <= 3:
                        score = 0.3
                    else:
                        score = 0.1
                    print(f"  ✅ 期望结果排名第{expected_rank}，得分: {score:.1f}")
                else:
                    score = 0.0
                    print(f"  ❌ 期望结果未出现在前5名，得分: {score:.1f}")
                
                total_score += score
            else:
                print(f"  ❌ 未找到任何结果")
        
        avg_score = total_score / len(test_queries)
        print(f"\n📊 总体评估:")
        print(f"  平均得分: {avg_score:.2f} / 1.0")
        if avg_score >= 0.8:
            print(f"  🎉 优秀！匹配效果很好")
        elif avg_score >= 0.6:
            print(f"  ✅ 良好，匹配效果有所改善")
        elif avg_score >= 0.4:
            print(f"  ⚠️  一般，仍需进一步优化")
        else:
            print(f"  ❌ 较差，需要重新调整策略")
        
        return avg_score
    
    def run_optimization(self):
        """执行完整优化流程"""
        
        print("🚀 开始向量匹配优化")
        print("🎯 目标：提升'各品类去年的总销售额'的匹配准确性")
        print("="*60)
        
        if not self.connect_services():
            return False
        
        try:
            # 1. 重建向量（使用增强算法）
            print("\n1️⃣  重建向量索引...")
            if not self.rebuild_vectors_with_enhancement():
                print("❌ 重建失败")
                return False
            
            # 2. 测试匹配效果
            print("\n2️⃣  测试匹配效果...")
            score = self.test_enhanced_matching()
            
            # 3. 提供建议
            print("\n3️⃣  优化建议:")
            if score < 0.6:
                print("  🔧 建议进一步调整关键词权重")
                print("  🔧 考虑使用真实的Qwen向量模型")
                print("  🔧 增加更多训练数据")
            else:
                print("  ✅ 当前优化效果良好")
                print("  🔧 可以应用到生产环境")
            
            return True
            
        except Exception as e:
            print(f"❌ 优化过程失败: {e}")
            return False
        finally:
            if hasattr(self, 'mysql_conn'):
                self.mysql_conn.close()

def main():
    """主函数"""
    optimizer = VectorMatchingOptimizer()
    
    try:
        success = optimizer.run_optimization()
        if success:
            print("\n✨ 向量匹配优化完成！")
        else:
            print("\n❌ 优化失败")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行异常: {e}")

if __name__ == "__main__":
    main() 