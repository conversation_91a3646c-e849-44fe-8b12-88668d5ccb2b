#!/bin/bash

echo "=== 检查数据同步状态 ==="

# 检查MySQL数据
echo "1. MySQL数据库中的记录："
mysql -uroot -e "
SELECT 
    COUNT(*) as total_count,
    GROUP_CONCAT(id ORDER BY id) as all_ids
FROM chatbi_metadata.query_examples;
"

echo ""
echo "2. MySQL中的记录详情："
mysql -uroot -e "
SELECT 
    id, 
    LEFT(user_question, 40) as question_preview,
    difficulty_level
FROM chatbi_metadata.query_examples 
ORDER BY id;
"

echo ""
echo "3. 检查Milvus集合状态..."

# 尝试通过curl调用现有服务检查搜索功能
echo "4. 测试向量搜索功能（如果数据已同步）..."

# 创建临时测试文件
cat > temp_test_search.json << 'EOF'
{
  "userQuery": "北京销售额",
  "conversationId": "test-conv-001",
  "datasetId": 1
}
EOF

echo "测试查询: '北京销售额'"
curl -s -X POST "http://localhost:8081/api/chat" \
  -H "Content-Type: application/json" \
  -d @temp_test_search.json 2>/dev/null | head -c 200

echo ""
echo ""

# 删除临时文件
rm -f temp_test_search.json

echo "=== 检查完成 ==="
echo ""
echo "说明："
echo "- 如果向量搜索返回有意义的结果，说明数据已同步"
echo "- 如果返回错误或空结果，说明数据未同步到Milvus" 