#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试向量搜索相关性
模拟实际的搜索场景，检查返回结果的相关性
"""

import mysql.connector
import json
from pymilvus import connections, Collection, utility
import hashlib
from typing import List, Dict

def get_embedding(text: str) -> List[float]:
    """获取文本的向量表示（模拟我们Python脚本中的逻辑）"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    # 重复hash值来生成1536维向量
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
        
    return vector

def test_search_queries():
    """测试不同的搜索查询"""
    
    # 连接Milvus
    try:
        connections.connect("default", host='localhost', port='19530')
        print("✅ Milvus连接成功")
    except Exception as e:
        print(f"❌ Milvus连接失败: {e}")
        return
    
    # 连接MySQL
    try:
        conn = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='chatbi_metadata'
        )
        print("✅ MySQL连接成功")
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return
    
    collection_name = "query_examples"
    collection = Collection(collection_name)
    collection.load()
    
    print(f"📊 向量集合中共有 {collection.num_entities} 条记录")
    
    # 测试查询案例
    test_cases = [
        {
            "query": "各品类去年的总销售",
            "expected_keywords": ["品类", "销售额", "category", "去年"],
            "description": "用户实际搜索的查询"
        },
        {
            "query": "各品类去年的总销售额",
            "expected_keywords": ["品类", "销售额", "category"],
            "description": "完整版本的查询"
        },
        {
            "query": "品类销售分析",
            "expected_keywords": ["品类", "销售额", "category"],
            "description": "语义相似的查询"
        },
        {
            "query": "北京销售额",
            "expected_keywords": ["北京", "销售额", "城市"],
            "description": "地区相关查询"
        },
        {
            "query": "产品排名",
            "expected_keywords": ["产品", "排名", "product"],
            "description": "排名相关查询"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试案例 {i}: {test_case['description']}")
        print(f"查询: '{test_case['query']}'")
        print(f"期望关键词: {test_case['expected_keywords']}")
        
        # 执行搜索
        search_results = search_similar_examples(collection, conn, test_case['query'], 5)
        
        print(f"\n搜索结果 (共{len(search_results)}条):")
        for j, result in enumerate(search_results, 1):
            print(f"  {j}. [{result['id']}] {result['user_question']}")
            print(f"     类型: {result['difficulty_level']}, 数据集: {result['dataset_name']}")
            
            # 分析相关性
            relevance_score = calculate_relevance(test_case, result)
            print(f"     相关性评分: {relevance_score:.2f}")
            print()
    
    conn.close()

def search_similar_examples(collection, conn, query_text: str, top_k: int = 5):
    """搜索相似示例"""
    # 生成查询向量
    query_vector = get_embedding(query_text)
    
    # 执行向量搜索
    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
    
    results = collection.search(
        data=[query_vector],
        anns_field="vector",
        param=search_params,
        limit=top_k,
        output_fields=["id"]
    )
    
    # 获取结果ID
    result_ids = []
    distances = []
    if results and len(results[0]) > 0:
        for result in results[0]:
            result_ids.append(result.id)
            distances.append(result.distance)
    
    # 从MySQL获取详细信息
    if result_ids:
        cursor = conn.cursor()
        placeholders = ','.join(['%s'] * len(result_ids))
        query = f"""
        SELECT qe.id, qe.user_question, qe.difficulty_level, qd.dataset_name,
               qe.target_query_representation
        FROM query_examples qe 
        LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
        WHERE qe.id IN ({placeholders})
        """
        cursor.execute(query, result_ids)
        results = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        
        # 转换为字典并保持顺序
        result_dict = {}
        for row in results:
            example = dict(zip(columns, row))
            result_dict[example['id']] = example
            
        # 按搜索结果顺序返回
        ordered_results = []
        for i, result_id in enumerate(result_ids):
            if result_id in result_dict:
                example = result_dict[result_id]
                example['distance'] = distances[i]
                ordered_results.append(example)
                
        return ordered_results
    
    return []

def calculate_relevance(test_case, result):
    """计算相关性评分"""
    score = 0.0
    
    # 检查关键词匹配
    query_lower = test_case['query'].lower()
    question_lower = result['user_question'].lower()
    
    # 用户问题匹配
    for keyword in test_case['expected_keywords']:
        if keyword.lower() in question_lower:
            score += 1.0
    
    # 查询表示匹配
    if result.get('target_query_representation'):
        repr_lower = result['target_query_representation'].lower()
        for keyword in test_case['expected_keywords']:
            if keyword.lower() in repr_lower:
                score += 0.5
    
    # 语义相似性（简单版本）
    common_words = set(query_lower.split()) & set(question_lower.split())
    score += len(common_words) * 0.3
    
    return min(score, 5.0)  # 最高5分

if __name__ == "__main__":
    test_search_queries()
