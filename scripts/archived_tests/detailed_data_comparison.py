#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细对比MySQL和Milvus中的query_examples数据
"""

try:
    from pymilvus import connections, Collection
    import mysql.connector
    HAS_DEPS = True
except ImportError:
    HAS_DEPS = False
    print("⚠️  需要安装依赖: pip install pymilvus mysql-connector-python")

def get_mysql_data():
    """获取MySQL中的数据"""
    try:
        mysql_conn = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='chatbi_metadata'
        )
        cursor = mysql_conn.cursor(dictionary=True)
        cursor.execute("SELECT id, user_question FROM query_examples ORDER BY id")
        data = cursor.fetchall()
        cursor.close()
        mysql_conn.close()
        return data
    except Exception as e:
        print(f"❌ MySQL查询失败: {e}")
        return []

def get_milvus_data():
    """获取Milvus中的数据"""
    try:
        connections.connect('default', host='localhost', port='19530')
        collection = Collection('query_examples')
        collection.load()
        
        # 查询所有ID
        results = collection.query(
            expr="id >= 0",
            output_fields=["id"],
            limit=1000
        )
        
        connections.disconnect('default')
        return [r['id'] for r in results]
    except Exception as e:
        print(f"❌ Milvus查询失败: {e}")
        return []

def compare_data():
    """对比数据"""
    print("=" * 60)
    print("         详细数据对比分析")
    print("=" * 60)
    
    if not HAS_DEPS:
        return
    
    # 获取数据
    print("\n🔍 正在获取MySQL数据...")
    mysql_data = get_mysql_data()
    mysql_ids = {item['id'] for item in mysql_data}
    
    print("🔍 正在获取Milvus数据...")
    milvus_ids = set(get_milvus_data())
    
    # 统计
    print(f"\n📊 数据统计:")
    print(f"   MySQL记录数: {len(mysql_ids)}")
    print(f"   Milvus记录数: {len(milvus_ids)}")
    
    # 对比分析
    print(f"\n📋 对比分析:")
    
    # 在MySQL中但不在Milvus中的
    missing_in_milvus = mysql_ids - milvus_ids
    if missing_in_milvus:
        print(f"   ⚠️  在Milvus中缺失的记录 ({len(missing_in_milvus)}条):")
        for record in mysql_data:
            if record['id'] in missing_in_milvus:
                print(f"      ID {record['id']}: {record['user_question'][:50]}...")
    else:
        print(f"   ✅ 所有MySQL记录都在Milvus中存在")
    
    # 在Milvus中但不在MySQL中的
    extra_in_milvus = milvus_ids - mysql_ids
    if extra_in_milvus:
        print(f"   ⚠️  在Milvus中多余的记录 ({len(extra_in_milvus)}条):")
        sorted_extra = sorted(extra_in_milvus)
        print(f"      ID列表: {sorted_extra}")
    else:
        print(f"   ✅ Milvus中没有多余记录")
    
    # 完全匹配的记录
    common_ids = mysql_ids & milvus_ids
    print(f"   ✅ 两边都存在的记录: {len(common_ids)}条")
    
    # 总结
    print(f"\n🎯 同步状态总结:")
    if missing_in_milvus or extra_in_milvus:
        print(f"   ❌ 数据不同步!")
        print(f"      - 缺失: {len(missing_in_milvus)}条")
        print(f"      - 多余: {len(extra_in_milvus)}条")
        print(f"      - 匹配: {len(common_ids)}条")
        
        print(f"\n💡 建议解决方案:")
        if missing_in_milvus:
            print(f"   1. 同步缺失的{len(missing_in_milvus)}条记录到Milvus")
        if extra_in_milvus:
            print(f"   2. 清理Milvus中多余的{len(extra_in_milvus)}条记录")
    else:
        print(f"   ✅ 数据完全同步! 两边都有{len(common_ids)}条记录")

if __name__ == "__main__":
    compare_data() 