#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试向量化内容的脚本
检查我们为每个查询示例生成的向量文本内容
"""

import mysql.connector
import json
import re

def test_vector_content():
    # 连接数据库
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    cursor = conn.cursor()
    
    # 获取查询示例数据
    query = """
    SELECT qe.id, qe.user_question, qe.target_query_representation, 
           qe.difficulty_level, qd.dataset_name 
    FROM query_examples qe 
    LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
    ORDER BY qe.id
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    columns = [desc[0] for desc in cursor.description]
    
    print("=" * 80)
    print("ChatBI 向量化内容测试")
    print("=" * 80)
    
    for row in results:
        example = dict(zip(columns, row))
        
        print(f"\n📋 示例 ID: {example['id']}")
        print(f"   用户问题: {example['user_question']}")
        
        # 构建丰富文本（模拟Java代码逻辑）
        rich_text = build_rich_text_for_example(example)
        
        print(f"   向量化文本:")
        for line in rich_text.split('\n'):
            print(f"     {line}")
        
        # 分析查询表示内容
        if example['target_query_representation']:
            analyze_query_representation(example['target_query_representation'])
        
        print("   " + "-" * 60)
    
    conn.close()

def build_rich_text_for_example(example):
    """模拟Java中的buildRichTextForExample方法"""
    rich_text_parts = []
    
    # 1. 核心用户问题
    if example.get('user_question'):
        rich_text_parts.append(f"用户问题: {example['user_question']}")
        
    # 2. 关联数据集
    if example.get('dataset_name'):
        rich_text_parts.append(f"关联数据集: {example['dataset_name']}")
        
    # 3. 核心指标
    if example.get('target_query_representation'):
        metrics = extract_metrics_from_query(example['target_query_representation'])
        if metrics:
            rich_text_parts.append(f"核心指标: {metrics}")
            
        # 4. 分析维度
        dimensions = extract_dimensions_from_query(example['target_query_representation'])
        if dimensions:
            rich_text_parts.append(f"分析维度: {dimensions}")
            
    # 5. 查询类型
    if example.get('difficulty_level'):
        rich_text_parts.append(f"查询类型: {example['difficulty_level']}")
        
    return '\n'.join(rich_text_parts)

def extract_metrics_from_query(query_representation):
    """提取指标信息"""
    try:
        if '"metrics"' in query_representation:
            # 尝试解析JSON
            data = json.loads(query_representation)
            if 'entities' in data and 'metrics' in data['entities']:
                return ', '.join(data['entities']['metrics'])
    except:
        pass
    
    # 简单文本匹配
    metrics = []
    if '销售额' in query_representation:
        metrics.append('销售额')
    if '订单数' in query_representation:
        metrics.append('订单数')
    if '数量' in query_representation:
        metrics.append('数量')
    
    return ', '.join(metrics)

def extract_dimensions_from_query(query_representation):
    """提取维度信息"""
    try:
        if '"dimensions_to_group"' in query_representation:
            # 尝试解析JSON
            data = json.loads(query_representation)
            if 'entities' in data and 'dimensions_to_group' in data['entities']:
                return ', '.join(data['entities']['dimensions_to_group'])
    except:
        pass
    
    # 简单文本匹配
    dimensions = []
    if 'city' in query_representation:
        dimensions.append('城市')
    if 'product_name' in query_representation:
        dimensions.append('产品')
    if 'date' in query_representation:
        dimensions.append('时间')
    
    return ', '.join(dimensions)

def analyze_query_representation(query_repr):
    """分析查询表示的结构"""
    print(f"   查询表示分析:")
    
    try:
        data = json.loads(query_repr)
        
        if 'intent' in data:
            print(f"     意图: {data['intent']}")
        if 'queryType' in data:
            print(f"     查询类型: {data['queryType']}")
        if 'entities' in data:
            entities = data['entities']
            if 'metrics' in entities:
                print(f"     指标: {entities['metrics']}")
            if 'dimensions_to_group' in entities:
                print(f"     分组维度: {entities['dimensions_to_group']}")
            if 'filters' in entities:
                print(f"     过滤条件: {len(entities['filters'])}个")
                
    except Exception as e:
        print(f"     JSON解析失败: {e}")
        print(f"     原始内容长度: {len(query_repr)} 字符")

if __name__ == "__main__":
    test_vector_content()
