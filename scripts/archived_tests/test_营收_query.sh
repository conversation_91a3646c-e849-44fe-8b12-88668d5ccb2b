#!/bin/bash

echo "🧪 开始测试'营收'查询传输情况..."
echo "================================================"

# 测试1: 直接用curl测试"营收"
echo "📝 测试1: 使用curl直接发送'营收'查询"
curl -X POST http://localhost:8081/api/v1/chatbi/conversation/query \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-001", 
    "queryText": "营收",
    "sessionId": "debug-test-session"
  }' \
  -v

echo ""
echo ""

# 测试2: 测试空字符串
echo "📝 测试2: 使用curl发送空查询（对比）"
curl -X POST http://localhost:8081/api/v1/chatbi/conversation/query \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-001", 
    "queryText": "",
    "sessionId": "debug-test-session"
  }' \
  -v

echo ""
echo ""

# 测试3: 测试其他中文字符
echo "📝 测试3: 使用curl发送其他中文查询"
curl -X POST http://localhost:8081/api/v1/chatbi/conversation/query \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-001", 
    "queryText": "销售额",
    "sessionId": "debug-test-session"
  }' \
  -v

echo ""
echo "================================================"
echo "✅ 测试完成！"
echo "💡 请检查应用程序日志中的以下内容："
echo "   1. ConversationController: '查询内容: {}'"
echo "   2. ConversationManagerImpl: '用户查询内容: {}'"
echo "   3. 如果这些日志显示为空，说明问题在前端传输"
echo "   4. 如果日志正常，问题可能在后续处理步骤" 