#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细的算法对比分析脚本
"""

import mysql.connector
import json
from pymilvus import connections, Collection, utility
import hashlib
import numpy as np
from typing import List, Dict, Tuple
import pandas as pd

def get_embedding(text: str, normalize: bool = True) -> List[float]:
    """获取文本的向量表示"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
    
    if normalize:
        vector = np.array(vector)
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        return vector.tolist()
    
    return vector

def run_detailed_comparison():
    """运行详细的算法对比分析"""
    
    # 连接服务
    connections.connect("default", host='localhost', port='19530')
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    print("🔬 详细算法对比分析")
    print("=" * 80)
    
    # 测试用例，包含更多样化的查询
    test_cases = [
        {
            "id": 1,
            "query": "各品类去年的总销售额",
            "category": "品类分析",
            "expected_keywords": ["品类", "销售额", "去年", "总"],
            "expected_id_hints": [35],  # 各品类去年的总销售额
            "difficulty": "Medium"
        },
        {
            "id": 2,
            "query": "北京销售额",
            "category": "地区分析", 
            "expected_keywords": ["北京", "销售额"],
            "expected_id_hints": [34, 5],  # 北京上海本月的销售额, 北京的收入
            "difficulty": "Easy"
        },
        {
            "id": 3,
            "query": "销售额最高的产品排名",
            "category": "排名分析",
            "expected_keywords": ["产品", "排名", "最高", "销售额"],
            "expected_id_hints": [9],  # 销售额最高的10个产品
            "difficulty": "Medium"
        },
        {
            "id": 4,
            "query": "今年月度销售趋势",
            "category": "趋势分析",
            "expected_keywords": ["今年", "月", "销售", "趋势", "变化"],
            "expected_id_hints": [33, 32],  # 今年每月销售额变化, 最近30天销售额趋势
            "difficulty": "Medium"
        },
        {
            "id": 5,
            "query": "昨天订单列表",
            "category": "简单查询",
            "expected_keywords": ["昨天", "订单"],
            "expected_id_hints": [36],  # 列出昨天的所有订单
            "difficulty": "Easy"
        }
    ]
    
    # 算法配置
    algorithms = [
        {
            "name": "L2",
            "collection": "query_examples_l2_test",
            "normalize": False,
            "metric_type": "L2"
        },
        {
            "name": "COSINE", 
            "collection": "query_examples_cosine_test",
            "normalize": True,
            "metric_type": "COSINE"
        },
        {
            "name": "IP",
            "collection": "query_examples_ip_test", 
            "normalize": True,
            "metric_type": "IP"
        }
    ]
    
    # 存储结果
    results = []
    
    # 为每个测试用例运行所有算法
    for test_case in test_cases:
        print(f"\n📋 测试用例 {test_case['id']}: {test_case['query']}")
        print(f"类别: {test_case['category']} | 期望难度: {test_case['difficulty']}")
        print(f"期望关键词: {test_case['expected_keywords']}")
        print("-" * 60)
        
        test_results = {"test_case": test_case}
        
        for algo in algorithms:
            print(f"\n🔍 {algo['name']} 算法:")
            
            try:
                # 搜索
                search_result = search_with_algorithm(
                    test_case['query'], 
                    algo, 
                    conn, 
                    top_k=5
                )
                
                # 评估结果
                evaluation = evaluate_search_result(
                    search_result, 
                    test_case, 
                    conn
                )
                
                test_results[algo['name']] = {
                    "search_result": search_result,
                    "evaluation": evaluation
                }
                
                # 打印结果
                print_algorithm_result(search_result, evaluation, algo['name'])
                
            except Exception as e:
                print(f"  ❌ 搜索失败: {e}")
                test_results[algo['name']] = {"error": str(e)}
        
        results.append(test_results)
    
    # 生成综合分析报告
    print("\n" + "=" * 80)
    print("📊 综合分析报告")
    print("=" * 80)
    
    generate_summary_report(results)
    
    conn.close()

def search_with_algorithm(query: str, algo_config: dict, conn, top_k: int = 5) -> List[Dict]:
    """使用指定算法搜索"""
    
    collection = Collection(algo_config['collection'])
    collection.load()
    
    # 生成查询向量
    query_vector = get_embedding(query, normalize=algo_config['normalize'])
    
    # 搜索参数
    search_params = {
        "metric_type": algo_config['metric_type'],
        "params": {"nprobe": 10}
    }
    
    # 执行搜索
    search_results = collection.search(
        data=[query_vector],
        anns_field="vector",
        param=search_params,
        limit=top_k,
        output_fields=["id", "difficulty_level", "dataset_name"]
    )
    
    # 格式化结果
    formatted_results = []
    if search_results and len(search_results[0]) > 0:
        for result in search_results[0]:
            # 获取用户问题
            cursor = conn.cursor()
            cursor.execute(
                "SELECT user_question FROM query_examples WHERE id = %s",
                (result.id,)
            )
            row = cursor.fetchone()
            question = row[0] if row else "未知"
            
            formatted_results.append({
                "id": result.id,
                "question": question,
                "distance": result.distance,
                "difficulty": result.entity.get('difficulty_level', 'N/A'),
                "dataset": result.entity.get('dataset_name', 'N/A')
            })
    
    return formatted_results

def evaluate_search_result(search_result: List[Dict], test_case: Dict, conn) -> Dict:
    """评估搜索结果质量"""
    
    if not search_result:
        return {
            "keyword_match_score": 0,
            "position_score": 0,
            "difficulty_match": False,
            "expected_id_found": False,
            "total_score": 0
        }
    
    # 1. 关键词匹配评分
    keyword_scores = []
    for result in search_result:
        question = result['question'].lower()
        matched_keywords = sum(1 for keyword in test_case['expected_keywords'] 
                             if keyword in question)
        keyword_score = matched_keywords / len(test_case['expected_keywords'])
        keyword_scores.append(keyword_score)
    
    avg_keyword_score = sum(keyword_scores) / len(keyword_scores)
    
    # 2. 位置评分（前面的结果权重更高）
    position_scores = []
    for i, score in enumerate(keyword_scores):
        position_weight = 1.0 / (i + 1)  # 位置权重
        position_scores.append(score * position_weight)
    
    position_score = sum(position_scores)
    
    # 3. 难度匹配
    top_result = search_result[0]
    difficulty_match = top_result['difficulty'] == test_case['difficulty']
    
    # 4. 期望ID命中
    found_ids = [result['id'] for result in search_result]
    expected_id_found = any(expected_id in found_ids 
                          for expected_id in test_case.get('expected_id_hints', []))
    
    # 5. 综合评分
    total_score = (
        avg_keyword_score * 0.4 +  # 关键词匹配 40%
        position_score * 0.3 +     # 位置评分 30%
        (1.0 if difficulty_match else 0.0) * 0.15 +  # 难度匹配 15%
        (1.0 if expected_id_found else 0.0) * 0.15   # 期望ID命中 15%
    )
    
    return {
        "keyword_match_score": avg_keyword_score,
        "position_score": position_score,
        "difficulty_match": difficulty_match,
        "expected_id_found": expected_id_found,
        "total_score": total_score,
        "top_result_id": top_result['id'],
        "top_result_question": top_result['question']
    }

def print_algorithm_result(search_result: List[Dict], evaluation: Dict, algo_name: str):
    """打印算法结果"""
    
    if not search_result:
        print("  未找到结果")
        return
    
    for i, result in enumerate(search_result[:3], 1):
        distance_label = "距离" if algo_name == "L2" else "相似度"
        print(f"  {i}. {result['question']}")
        print(f"     ID: {result['id']}, {distance_label}: {result['distance']:.4f}")
        print(f"     难度: {result['difficulty']}")
    
    print(f"  📈 评分:")
    print(f"     关键词匹配: {evaluation['keyword_match_score']:.2f}")
    print(f"     位置评分: {evaluation['position_score']:.2f}")
    print(f"     难度匹配: {'✅' if evaluation['difficulty_match'] else '❌'}")
    print(f"     期望命中: {'✅' if evaluation['expected_id_found'] else '❌'}")
    print(f"     综合评分: {evaluation['total_score']:.2f}")

def generate_summary_report(results: List[Dict]):
    """生成综合分析报告"""
    
    algorithms = ["L2", "COSINE", "IP"]
    
    # 计算平均得分
    algo_scores = {algo: [] for algo in algorithms}
    
    print("\n1️⃣ 各测试用例详细得分:")
    print("-" * 50)
    
    for result in results:
        test_case = result['test_case']
        print(f"\n测试用例: {test_case['query']}")
        
        for algo in algorithms:
            if algo in result and 'evaluation' in result[algo]:
                score = result[algo]['evaluation']['total_score']
                algo_scores[algo].append(score)
                print(f"  {algo:>8}: {score:.3f}")
            else:
                print(f"  {algo:>8}: 错误")
    
    print(f"\n2️⃣ 算法平均表现:")
    print("-" * 50)
    
    best_algo = None
    best_score = 0
    
    for algo in algorithms:
        if algo_scores[algo]:
            avg_score = sum(algo_scores[algo]) / len(algo_scores[algo])
            print(f"{algo:>8}: {avg_score:.3f} (基于 {len(algo_scores[algo])} 个测试)")
            
            if avg_score > best_score:
                best_score = avg_score
                best_algo = algo
        else:
            print(f"{algo:>8}: 无有效结果")
    
    print(f"\n3️⃣ 结论和建议:")
    print("-" * 50)
    
    if best_algo:
        print(f"🏆 表现最好的算法: {best_algo} (平均得分: {best_score:.3f})")
        
        if best_algo == "COSINE":
            print("✅ COSINE算法在文本语义匹配方面表现最佳")
            print("   推荐: 将生产环境切换为COSINE算法")
        elif best_algo == "IP":
            print("✅ IP算法表现良好，适合归一化向量")
            print("   推荐: 可以考虑使用IP算法作为备选方案")
        else:
            print("⚠️  L2算法表现最好，但这可能不是最优选择")
            print("   建议: 检查测试数据和评估标准")
    
    # 具体建议
    print(f"\n4️⃣ 技术实施建议:")
    print("-" * 50)
    print("1. 基于测试结果，建议采用COSINE算法")
    print("2. 在KnowledgeBaseTools中更新度量类型")
    print("3. 重建生产环境的向量索引")
    print("4. 实施A/B测试验证改进效果")

if __name__ == "__main__":
    run_detailed_comparison()
