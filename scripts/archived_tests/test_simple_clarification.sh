#!/bin/bash

echo "🧪 简单测试：验证澄清功能是否工作"

# 测试1：模糊查询
echo "测试1: 发送模糊查询 '营收'"
RESPONSE=$(curl -s -X POST "http://localhost:8081/api/v1/chatbi/conversation/query" \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "营收", 
    "userId": "test-user-001", 
    "sessionId": "debug-session-1", 
    "userRoles": ["华北区销售经理"], 
    "additionalParams": {}
  }')

echo "响应类型: $(echo "$RESPONSE" | jq -r '.responseType')"
echo "是否需要澄清: $(echo "$RESPONSE" | jq -r '.clarificationOptions != null')"
echo ""

# 测试2：非常简短的查询
echo "测试2: 发送非常简短的查询 '数据'"
RESPONSE2=$(curl -s -X POST "http://localhost:8081/api/v1/chatbi/conversation/query" \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "数据", 
    "userId": "test-user-001", 
    "sessionId": "debug-session-2", 
    "userRoles": ["华北区销售经理"], 
    "additionalParams": {}
  }')

echo "响应类型: $(echo "$RESPONSE2" | jq -r '.responseType')"
echo "是否需要澄清: $(echo "$RESPONSE2" | jq -r '.clarificationOptions != null')"
echo ""

# 测试3：问候语
echo "测试3: 发送问候语 '你好'"
RESPONSE3=$(curl -s -X POST "http://localhost:8081/api/v1/chatbi/conversation/query" \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "你好", 
    "userId": "test-user-001", 
    "sessionId": "debug-session-3", 
    "userRoles": ["华北区销售经理"], 
    "additionalParams": {}
  }')

echo "响应类型: $(echo "$RESPONSE3" | jq -r '.responseType')"
echo "响应消息: $(echo "$RESPONSE3" | jq -r '.messageToUser')"

echo ""
echo "🔍 总结:"
echo "如果所有测试用例的响应类型都是 'DATA'，说明澄清功能还没有工作"
echo "预期: 测试1和2应该返回 'CLARIFICATION_NEEDED'，测试3应该返回 'ACKNOWLEDGEMENT' 或 'GREETING'" 