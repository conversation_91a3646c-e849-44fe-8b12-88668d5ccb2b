#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于真实数据的精确算法对比测试
"""

import mysql.connector
import json
from pymilvus import connections, Collection, utility
import hashlib
import numpy as np
from typing import List, Dict, Tu<PERSON>

def get_embedding(text: str, normalize: bool = True) -> List[float]:
    """获取文本的向量表示"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
    
    if normalize:
        vector = np.array(vector)
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        return vector.tolist()
    
    return vector

def run_precise_test():
    """运行精确的算法对比测试"""
    
    # 连接服务
    connections.connect("default", host='localhost', port='19530')
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    print("🎯 基于真实数据的精确算法对比")
    print("=" * 80)
    
    # 定义测试用例（基于真实数据）
    test_cases = [
        {
            "name": "地区分析测试",
            "queries": [
                {"text": "北京的收入", "expected_ids": [5, 6, 34, 39]},
                {"text": "上海销售额", "expected_ids": [34, 6, 5]},
                {"text": "各城市收入对比", "expected_ids": [6, 8, 34, 39]},
            ]
        },
        {
            "name": "排名分析测试", 
            "queries": [
                {"text": "销售额最高的产品", "expected_ids": [9, 8, 41]},
                {"text": "城市销售排名", "expected_ids": [8, 6, 41]},
                {"text": "产品销售排行榜", "expected_ids": [9, 8]},
            ]
        },
        {
            "name": "趋势分析测试",
            "queries": [
                {"text": "销售额趋势", "expected_ids": [32, 33, 37, 38]},
                {"text": "月度变化", "expected_ids": [33, 32, 37, 38]},
                {"text": "同比增长", "expected_ids": [37, 38, 33]},
            ]
        },
        {
            "name": "品类分析测试",
            "queries": [
                {"text": "各品类销售额", "expected_ids": [35]},
                {"text": "分类销售分析", "expected_ids": [35]},
            ]
        }
    ]
    
    # 算法配置
    algorithms = [
        {"name": "L2", "collection": "query_examples_l2_test", "normalize": False},
        {"name": "COSINE", "collection": "query_examples_cosine_test", "normalize": True},
        {"name": "IP", "collection": "query_examples_ip_test", "normalize": True}
    ]
    
    # 存储所有结果
    all_results = {}
    
    # 为每个测试分类运行算法对比
    for test_case in test_cases:
        print(f"\n{'='*60}")
        print(f"🧪 {test_case['name']}")
        print(f"{'='*60}")
        
        case_results = {}
        
        for query_info in test_case['queries']:
            query_text = query_info['text']
            expected_ids = query_info['expected_ids']
            
            print(f"\n📋 查询: '{query_text}'")
            print(f"期望命中ID: {expected_ids}")
            print("-" * 40)
            
            query_results = {}
            
            # 测试每个算法
            for algo in algorithms:
                try:
                    results = search_with_algorithm(query_text, algo, conn, top_k=5)
                    evaluation = evaluate_results(results, expected_ids)
                    
                    query_results[algo['name']] = {
                        'results': results,
                        'evaluation': evaluation
                    }
                    
                    print(f"\n🔍 {algo['name']} 算法:")
                    print_detailed_results(results, evaluation, expected_ids)
                    
                except Exception as e:
                    print(f"❌ {algo['name']} 算法失败: {e}")
                    query_results[algo['name']] = {'error': str(e)}
            
            case_results[query_text] = query_results
        
        all_results[test_case['name']] = case_results
    
    # 生成综合分析
    print(f"\n" + "="*80)
    print("📊 综合分析报告")
    print(f"="*80)
    
    generate_comprehensive_report(all_results)
    
    conn.close()

def search_with_algorithm(query: str, algo_config: dict, conn, top_k: int = 5) -> List[Dict]:
    """使用指定算法搜索"""
    
    collection = Collection(algo_config['collection'])
    collection.load()
    
    # 生成查询向量
    query_vector = get_embedding(query, normalize=algo_config['normalize'])
    
    # 搜索参数
    search_params = {
        "metric_type": algo_config['name'],
        "params": {"nprobe": 10}
    }
    
    # 执行搜索
    search_results = collection.search(
        data=[query_vector],
        anns_field="vector",
        param=search_params,
        limit=top_k,
        output_fields=["id", "difficulty_level", "dataset_name"]
    )
    
    # 格式化结果
    formatted_results = []
    if search_results and len(search_results[0]) > 0:
        for result in search_results[0]:
            # 获取用户问题
            cursor = conn.cursor()
            cursor.execute(
                "SELECT user_question FROM query_examples WHERE id = %s",
                (result.id,)
            )
            row = cursor.fetchone()
            question = row[0] if row else "未知"
            
            formatted_results.append({
                "id": result.id,
                "question": question,
                "distance": result.distance,
                "difficulty": result.entity.get('difficulty_level', 'N/A')
            })
    
    return formatted_results

def evaluate_results(results: List[Dict], expected_ids: List[int]) -> Dict:
    """评估搜索结果"""
    
    if not results:
        return {
            "precision_at_1": 0,
            "precision_at_3": 0,
            "precision_at_5": 0,
            "recall": 0,
            "mrr": 0,  # Mean Reciprocal Rank
            "found_ids": []
        }
    
    found_ids = [result['id'] for result in results]
    
    # Precision@K
    def precision_at_k(k):
        if k > len(results):
            k = len(results)
        top_k_ids = found_ids[:k]
        hits = sum(1 for id in top_k_ids if id in expected_ids)
        return hits / k if k > 0 else 0
    
    # Recall
    hits = sum(1 for id in found_ids if id in expected_ids)
    recall = hits / len(expected_ids) if expected_ids else 0
    
    # MRR (Mean Reciprocal Rank)
    mrr = 0
    for i, id in enumerate(found_ids):
        if id in expected_ids:
            mrr = 1.0 / (i + 1)
            break
    
    return {
        "precision_at_1": precision_at_k(1),
        "precision_at_3": precision_at_k(3),
        "precision_at_5": precision_at_k(5),
        "recall": recall,
        "mrr": mrr,
        "found_ids": found_ids
    }

def print_detailed_results(results: List[Dict], evaluation: Dict, expected_ids: List[int]):
    """打印详细结果"""
    
    for i, result in enumerate(results[:3], 1):
        hit_marker = "✅" if result['id'] in expected_ids else "❌"
        print(f"  {i}. {hit_marker} ID:{result['id']} - {result['question']}")
        print(f"     距离/相似度: {result['distance']:.4f}, 难度: {result['difficulty']}")
    
    print(f"  📊 评估指标:")
    print(f"     P@1: {evaluation['precision_at_1']:.2f}")
    print(f"     P@3: {evaluation['precision_at_3']:.2f}")
    print(f"     召回率: {evaluation['recall']:.2f}")
    print(f"     MRR: {evaluation['mrr']:.2f}")

def generate_comprehensive_report(all_results: Dict):
    """生成综合分析报告"""
    
    algorithms = ["L2", "COSINE", "IP"]
    
    # 收集所有评估指标
    algo_metrics = {algo: {"p1": [], "p3": [], "recall": [], "mrr": []} for algo in algorithms}
    
    print("\n1️⃣ 各测试分类详细分析:")
    print("-" * 60)
    
    for test_name, case_results in all_results.items():
        print(f"\n📋 {test_name}:")
        
        for query, query_results in case_results.items():
            print(f"  查询: {query}")
            
            for algo in algorithms:
                if algo in query_results and 'evaluation' in query_results[algo]:
                    eval_data = query_results[algo]['evaluation']
                    algo_metrics[algo]["p1"].append(eval_data['precision_at_1'])
                    algo_metrics[algo]["p3"].append(eval_data['precision_at_3'])
                    algo_metrics[algo]["recall"].append(eval_data['recall'])
                    algo_metrics[algo]["mrr"].append(eval_data['mrr'])
                    
                    print(f"    {algo:>8}: P@1={eval_data['precision_at_1']:.2f}, "
                          f"P@3={eval_data['precision_at_3']:.2f}, "
                          f"R={eval_data['recall']:.2f}, "
                          f"MRR={eval_data['mrr']:.2f}")
    
    print(f"\n2️⃣ 算法平均性能对比:")
    print("-" * 60)
    
    best_scores = {}
    
    for algo in algorithms:
        if algo_metrics[algo]["p1"]:
            avg_p1 = sum(algo_metrics[algo]["p1"]) / len(algo_metrics[algo]["p1"])
            avg_p3 = sum(algo_metrics[algo]["p3"]) / len(algo_metrics[algo]["p3"])
            avg_recall = sum(algo_metrics[algo]["recall"]) / len(algo_metrics[algo]["recall"])
            avg_mrr = sum(algo_metrics[algo]["mrr"]) / len(algo_metrics[algo]["mrr"])
            
            # 综合得分
            composite_score = (avg_p1 * 0.3 + avg_p3 * 0.3 + avg_recall * 0.2 + avg_mrr * 0.2)
            best_scores[algo] = composite_score
            
            print(f"{algo:>8}: P@1={avg_p1:.3f}, P@3={avg_p3:.3f}, "
                  f"Recall={avg_recall:.3f}, MRR={avg_mrr:.3f}, "
                  f"综合={composite_score:.3f}")
    
    print(f"\n3️⃣ 最终结论:")
    print("-" * 60)
    
    if best_scores:
        best_algo = max(best_scores, key=best_scores.get)
        best_score = best_scores[best_algo]
        
        print(f"🏆 表现最佳算法: {best_algo} (综合得分: {best_score:.3f})")
        
        if best_algo == "COSINE":
            print("✅ 推荐使用COSINE算法")
            print("   - 在文本语义匹配方面表现最佳")
            print("   - 适合处理归一化向量")
        elif best_algo == "IP":
            print("✅ 推荐使用IP算法")
            print("   - 在归一化向量上表现良好")
            print("   - 计算效率较高")
        else:
            print("⚠️  L2算法得分最高")
            print("   - 可能受到测试数据特征影响")
            print("   - 建议进一步验证")
        
        print(f"\n4️⃣ 实施建议:")
        print("-" * 60)
        print(f"1. 立即切换到 {best_algo} 算法")
        print(f"2. 重建生产环境向量索引")
        print(f"3. 实施A/B测试验证效果")
        print(f"4. 监控查询匹配准确性")

if __name__ == "__main__":
    run_precise_test()
