#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用Qwen语义向量替换哈希向量的测试
"""

import mysql.connector
import json
from pymilvus import connections, Collection, utility, FieldSchema, CollectionSchema, DataType
import requests
import os
from typing import List, Dict
import time

class QwenEmbeddingClient:
    """Qwen语义向量客户端"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_embedding(self, text: str) -> List[float]:
        """获取文本的Qwen语义向量"""
        
        if not self.api_key:
            print("⚠️  未找到DASHSCOPE_API_KEY，使用模拟向量")
            return self._get_mock_embedding(text)
        
        try:
            payload = {
                "model": "text-embedding-v1",
                "input": {
                    "texts": [text]
                }
            }
            
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if "output" in result and "embeddings" in result["output"]:
                    embeddings = result["output"]["embeddings"]
                    if embeddings and len(embeddings) > 0:
                        return embeddings[0]["embedding"]
            
            print(f"⚠️  Qwen API调用失败，状态码: {response.status_code}")
            return self._get_mock_embedding(text)
            
        except Exception as e:
            print(f"⚠️  Qwen API调用异常: {e}")
            return self._get_mock_embedding(text)
    
    def _get_mock_embedding(self, text: str) -> List[float]:
        """生成模拟的语义向量（改进版哈希向量）"""
        import hashlib
        import numpy as np
        
        # 使用多种哈希算法增加语义相似性
        hash_methods = [
            hashlib.md5(text.encode('utf-8')).digest(),
            hashlib.sha1(text.encode('utf-8')).digest(),
            hashlib.sha256(text.encode('utf-8')).digest()
        ]
        
        # 基于关键词的权重调整
        keywords = ["销售", "产品", "品类", "北京", "上海", "城市", "去年", "本月", "趋势", "排名", "最高", "总", "累计"]
        keyword_weights = []
        for keyword in keywords:
            weight = 1.0 if keyword in text else 0.1
            keyword_weights.append(weight)
        
        # 生成1536维向量
        vector = []
        for i in range(1536):
            # 使用多个哈希值
            hash_idx = i % len(hash_methods)
            byte_idx = i % len(hash_methods[hash_idx])
            base_value = float(hash_methods[hash_idx][byte_idx]) / 255.0 - 0.5
            
            # 应用关键词权重
            keyword_idx = i % len(keyword_weights)
            weighted_value = base_value * keyword_weights[keyword_idx]
            
            vector.append(weighted_value)
        
        # 归一化
        vector = np.array(vector)
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        
        return vector.tolist()

def test_qwen_vs_hash_vectors():
    """对比Qwen语义向量和哈希向量的效果"""
    
    print("🧪 Qwen语义向量 vs 哈希向量对比测试")
    print("=" * 80)
    
    # 连接服务
    connections.connect("default", host='localhost', port='19530')
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    # 初始化Qwen客户端
    qwen_client = QwenEmbeddingClient()
    
    # 测试查询
    test_query = "各类产品去年的总销售"
    
    print(f"\n📋 测试查询: '{test_query}'")
    print("-" * 60)
    
    # 创建Qwen语义向量测试集合
    qwen_collection = create_qwen_test_collection()
    if qwen_collection:
        rebuild_with_qwen_vectors(qwen_collection, conn, qwen_client)
        
        print(f"\n🔍 Qwen语义向量结果:")
        test_search_performance(test_query, qwen_collection, conn, qwen_client, "Qwen")
    
    # 对比现有的COSINE集合
    if utility.has_collection("query_examples"):
        collection = Collection("query_examples")
        collection.load()
        
        print(f"\n🔍 当前哈希向量结果:")
        test_search_performance(test_query, collection, conn, qwen_client, "Hash", use_qwen=False)
    
    conn.close()

def create_qwen_test_collection():
    """创建Qwen语义向量测试集合"""
    
    collection_name = "query_examples_qwen_test"
    
    # 删除已存在的集合
    if utility.has_collection(collection_name):
        utility.drop_collection(collection_name)
        print(f"��️  删除已存在的集合: {collection_name}")
    
    try:
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=1536),
            FieldSchema(name="difficulty_level", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="dataset_name", dtype=DataType.VARCHAR, max_length=200)
        ]
        
        # 创建schema
        schema = CollectionSchema(
            fields=fields, 
            description="Qwen语义向量测试集合"
        )
        
        # 创建集合
        collection = Collection(name=collection_name, schema=schema)
        
        print(f"✅ 成功创建Qwen测试集合: {collection_name}")
        return collection
        
    except Exception as e:
        print(f"❌ 创建Qwen测试集合失败: {e}")
        return None

def rebuild_with_qwen_vectors(collection, conn, qwen_client):
    """使用Qwen语义向量重建数据"""
    
    try:
        # 获取查询示例数据
        cursor = conn.cursor()
        query = """
        SELECT qe.id, qe.user_question, qe.target_query_representation, 
               qe.difficulty_level, qd.dataset_name 
        FROM query_examples qe 
        LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
        ORDER BY qe.id
        """
        cursor.execute(query)
        results = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        
        ids = []
        vectors = []
        difficulty_levels = []
        dataset_names = []
        
        print(f"📊 使用Qwen语义向量处理 {len(results)} 条查询示例...")
        
        for row in results:
            example = dict(zip(columns, row))
            
            # 构建丰富文本
            rich_text = build_rich_text_for_qwen(example)
            
            print(f"  🔄 处理ID {example['id']}: {example['user_question'][:30]}...")
            
            # 使用Qwen生成语义向量
            vector = qwen_client.get_embedding(rich_text)
            
            if vector and len(vector) == 1536:
                ids.append(example['id'])
                vectors.append(vector)
                difficulty_levels.append(example.get('difficulty_level', ''))
                dataset_names.append(example.get('dataset_name', ''))
                print(f"    ✅ 向量生成成功")
            else:
                print(f"    ❌ 向量生成失败")
            
            # 避免API限流
            time.sleep(0.1)
        
        if ids:
            # 批量插入
            data = [ids, vectors, difficulty_levels, dataset_names]
            collection.insert(data)
            collection.flush()
            
            # 创建COSINE索引
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            
            collection.create_index(field_name="vector", index_params=index_params)
            collection.load()
            
            print(f"✅ 成功插入 {len(ids)} 条Qwen语义向量")
        
    except Exception as e:
        print(f"❌ Qwen向量重建失败: {e}")

def build_rich_text_for_qwen(example):
    """为Qwen构建语义丰富的文本"""
    
    # 对于语义向量，我们可以使用更自然的语言
    parts = []
    
    # 核心用户问题
    if example.get('user_question'):
        parts.append(example['user_question'])
        
    # 从查询表示中提取业务含义
    if example.get('target_query_representation'):
        try:
            data = json.loads(example['target_query_representation'])
            if 'entities' in data:
                entities = data['entities']
                
                # 添加指标信息
                if 'metrics' in entities:
                    metrics = entities['metrics']
                    for metric in metrics:
                        if metric == '销售额':
                            parts.append("销售金额分析")
                        elif metric == '订单数':
                            parts.append("订单数量统计")
                
                # 添加维度信息
                if 'dimensions_to_group' in entities:
                    dims = entities['dimensions_to_group']
                    for dim in dims:
                        if dim == 'city':
                            parts.append("城市地区维度")
                        elif dim == 'category':
                            parts.append("产品品类分类")
                        elif dim == 'product_name':
                            parts.append("产品名称维度")
                        elif dim == 'date':
                            parts.append("时间日期维度")
                        elif dim == 'month':
                            parts.append("月份时间维度")
        except:
            pass
    
    # 添加数据集上下文
    if example.get('dataset_name'):
        parts.append(f"数据来源{example['dataset_name']}")
    
    return ' '.join(parts)

def test_search_performance(query, collection, conn, qwen_client, method_name, use_qwen=True):
    """测试搜索性能"""
    
    try:
        # 生成查询向量
        if use_qwen:
            query_vector = qwen_client.get_embedding(query)
        else:
            # 使用当前的哈希方法
            import hashlib
            import numpy as np
            hash_obj = hashlib.sha256(query.encode('utf-8'))
            hash_bytes = hash_obj.digest()
            vector = []
            for i in range(1536):
                byte_index = i % len(hash_bytes)
                vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
            vector = np.array(vector)
            norm = np.linalg.norm(vector)
            if norm > 0:
                vector = vector / norm
            query_vector = vector.tolist()
        
        # 搜索参数
        search_params = {
            "metric_type": "COSINE",
            "params": {"nprobe": 10}
        }
        
        # 执行搜索
        results = collection.search(
            data=[query_vector],
            anns_field="vector",
            param=search_params,
            limit=5,
            output_fields=["id", "difficulty_level"]
        )
        
        if results and len(results[0]) > 0:
            print(f"📊 {method_name}向量 Top 3 结果:")
            
            expected_keywords = ["品类", "产品", "销售", "总", "去年"]
            total_relevance = 0
            
            for i, result in enumerate(results[0][:3], 1):
                # 获取问题文本
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT user_question FROM query_examples WHERE id = %s",
                    (result.id,)
                )
                row = cursor.fetchone()
                question = row[0] if row else "未知"
                
                # 计算相关性
                matched_keywords = [kw for kw in expected_keywords if kw in question]
                relevance_score = len(matched_keywords) / len(expected_keywords)
                total_relevance += relevance_score
                
                relevance_emoji = "🎯" if relevance_score >= 0.4 else "⚠️" if relevance_score >= 0.2 else "❌"
                
                print(f"  {i}. {relevance_emoji} ID:{result.id} - {question}")
                print(f"     相似度: {result.distance:.4f} | 匹配: {matched_keywords}")
            
            avg_relevance = total_relevance / 3
            print(f"  📈 平均相关性: {avg_relevance:.2f}")
            
            if avg_relevance >= 0.6:
                print(f"  评价: ✅ 优秀")
            elif avg_relevance >= 0.4:
                print(f"  评价: ⚠️ 良好")
            else:
                print(f"  评价: ❌ 需要改进")
        else:
            print(f"❌ {method_name}向量未找到结果")
            
    except Exception as e:
        print(f"❌ {method_name}向量搜索失败: {e}")

if __name__ == "__main__":
    print("🔑 请确保设置了DASHSCOPE_API_KEY环境变量")
    print("   或者脚本将使用改进的模拟向量进行测试")
    print()
    
    test_qwen_vs_hash_vectors()
