#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试特定查询："各类产品去年的总销售"
"""

import mysql.connector
from pymilvus import connections, Collection, utility
import hashlib
import numpy as np
from typing import List, Dict

def get_embedding(text: str, normalize: bool = True) -> List[float]:
    """获取文本的向量表示"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
    
    if normalize:
        vector = np.array(vector)
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        return vector.tolist()
    
    return vector

def test_specific_query():
    """测试特定查询"""
    
    test_query = "各类产品去年的总销售"
    
    print("🔍 测试查询分析")
    print("=" * 80)
    print(f"查询问题: '{test_query}'")
    print("=" * 80)
    
    # 连接服务
    connections.connect("default", host='localhost', port='19530')
    conn = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='',
        database='chatbi_metadata'
    )
    
    # 测试配置
    test_configs = [
        {
            "name": "L2算法",
            "collection": "query_examples_l2_test", 
            "normalize": False,
            "metric_type": "L2"
        },
        {
            "name": "COSINE算法",
            "collection": "query_examples_cosine_test",
            "normalize": True, 
            "metric_type": "COSINE"
        },
        {
            "name": "IP算法", 
            "collection": "query_examples_ip_test",
            "normalize": True,
            "metric_type": "IP"
        },
        {
            "name": "新COSINE生产库",
            "collection": "query_examples",
            "normalize": True,
            "metric_type": "COSINE"
        }
    ]
    
    # 分析查询特征
    print(f"\n📋 查询特征分析:")
    print("-" * 50)
    analyze_query_features(test_query)
    
    # 查找最相关的示例
    print(f"\n📊 数据库中相关示例:")
    print("-" * 50)
    find_relevant_examples(conn)
    
    # 测试各个算法
    for config in test_configs:
        print(f"\n{'='*60}")
        print(f"🧪 {config['name']}")
        print(f"{'='*60}")
        
        if not utility.has_collection(config['collection']):
            print(f"⚠️  集合 {config['collection']} 不存在，跳过测试")
            continue
            
        try:
            results = search_with_config(test_query, config, conn)
            analyze_results(results, test_query)
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    conn.close()

def analyze_query_features(query: str):
    """分析查询特征"""
    
    features = []
    query_lower = query.lower()
    
    if any(word in query_lower for word in ['各类', '分类', '类别', '品类', '类型']):
        features.append("分类分析")
    if any(word in query_lower for word in ['产品', '商品']):
        features.append("产品维度")
    if any(word in query_lower for word in ['去年', '上年', '年度']):
        features.append("时间维度：年度")
    if any(word in query_lower for word in ['总', '合计', '累计', '汇总']):
        features.append("聚合分析")
    if any(word in query_lower for word in ['销售', '收入', '营收']):
        features.append("销售指标")
    
    print(f"识别特征: {', '.join(features)}")
    
    # 期望匹配的类型
    expected_keywords = ["品类", "产品", "销售", "总", "去年"]
    print(f"期望关键词: {expected_keywords}")

def find_relevant_examples(conn):
    """查找相关示例"""
    
    cursor = conn.cursor()
    
    # 查找包含相关关键词的示例
    query = """
    SELECT id, user_question, difficulty_level 
    FROM query_examples 
    WHERE user_question LIKE '%品类%' 
       OR user_question LIKE '%产品%' 
       OR user_question LIKE '%去年%'
       OR user_question LIKE '%总%'
    ORDER BY id
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    
    print("相关示例:")
    for row in results:
        id, question, difficulty = row
        print(f"  ID:{id} - {question} ({difficulty})")
    
    if not results:
        print("  未找到直接相关的示例")

def search_with_config(query: str, config: dict, conn) -> List[Dict]:
    """使用指定配置搜索"""
    
    collection = Collection(config['collection'])
    collection.load()
    
    # 生成查询向量
    query_vector = get_embedding(query, normalize=config['normalize'])
    
    # 搜索参数
    search_params = {
        "metric_type": config['metric_type'],
        "params": {"nprobe": 10}
    }
    
    # 执行搜索
    search_results = collection.search(
        data=[query_vector],
        anns_field="vector", 
        param=search_params,
        limit=5,
        output_fields=["id", "difficulty_level", "dataset_name"]
    )
    
    # 格式化结果
    formatted_results = []
    if search_results and len(search_results[0]) > 0:
        for i, result in enumerate(search_results[0]):
            # 获取用户问题
            cursor = conn.cursor()
            cursor.execute(
                "SELECT user_question FROM query_examples WHERE id = %s",
                (result.id,)
            )
            row = cursor.fetchone()
            question = row[0] if row else "未知"
            
            formatted_results.append({
                "rank": i + 1,
                "id": result.id,
                "question": question,
                "distance": result.distance,
                "difficulty": result.entity.get('difficulty_level', 'N/A'),
                "dataset": result.entity.get('dataset_name', 'N/A')
            })
    
    return formatted_results

def analyze_results(results: List[Dict], original_query: str):
    """分析搜索结果"""
    
    if not results:
        print("❌ 未找到任何结果")
        return
    
    print(f"📊 Top 3 搜索结果:")
    print("-" * 50)
    
    # 期望关键词
    expected_keywords = ["品类", "产品", "销售", "总", "去年"]
    
    total_relevance_score = 0
    
    for result in results[:3]:
        question = result['question'].lower()
        
        # 计算关键词匹配
        matched_keywords = []
        for keyword in expected_keywords:
            if keyword in question:
                matched_keywords.append(keyword)
        
        relevance_score = len(matched_keywords) / len(expected_keywords)
        total_relevance_score += relevance_score
        
        # 相关性标记
        relevance_emoji = "🎯" if relevance_score >= 0.4 else "⚠️" if relevance_score >= 0.2 else "❌"
        
        distance_label = "距离" if "L2" in str(result.get('distance', 0)) else "相似度"
        
        print(f"{result['rank']}. {relevance_emoji} ID:{result['id']} - {result['question']}")
        print(f"   {distance_label}: {result['distance']:.4f} | 难度: {result['difficulty']}")
        print(f"   匹配关键词: {matched_keywords} ({len(matched_keywords)}/{len(expected_keywords)})")
        print(f"   相关性评分: {relevance_score:.2f}")
        print()
    
    # 整体评估
    avg_relevance = total_relevance_score / 3
    print(f"📈 整体评估:")
    print(f"   平均相关性: {avg_relevance:.2f}")
    
    if avg_relevance >= 0.6:
        print(f"   评价: ✅ 优秀 - 匹配度很高")
    elif avg_relevance >= 0.4:
        print(f"   评价: ⚠️ 良好 - 匹配度中等")
    elif avg_relevance >= 0.2:
        print(f"   评价: ⚠️ 一般 - 匹配度较低")
    else:
        print(f"   评价: ❌ 较差 - 匹配度很低")

if __name__ == "__main__":
    test_specific_query()
