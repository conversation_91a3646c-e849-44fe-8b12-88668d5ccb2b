#!/bin/bash

# NLU 澄清功能测试脚本
# 用于验证修复后的 NluAgentImpl 是否能正确判断澄清需求

echo "🧪 开始测试 NLU 澄清功能..."

# 设置服务器地址
BASE_URL="http://localhost:8081"

# 测试用例1：模糊查询 - 应该要求澄清
echo "📝 测试用例1: 模糊查询 '营收' - 期望要求澄清"
RESPONSE1=$(curl -s -X POST "${BASE_URL}/api/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "营收",
    "userContext": {
      "userId": "test-user-001",
      "sessionId": "'$(uuidgen)'",
      "userRoles": ["华北区销售经理"],
      "clientInfo": {}
    }
  }')

echo "响应: $RESPONSE1" | jq .
echo ""

# 测试用例2：具体查询 - 不应该要求澄清
echo "📝 测试用例2: 具体查询 '查询本月北京地区的销售额' - 期望直接执行"
RESPONSE2=$(curl -s -X POST "${BASE_URL}/api/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "查询本月北京地区的销售额",
    "userContext": {
      "userId": "test-user-001", 
      "sessionId": "'$(uuidgen)'",
      "userRoles": ["华北区销售经理"],
      "clientInfo": {}
    }
  }')

echo "响应: $RESPONSE2" | jq .
echo ""

# 测试用例3：问候语 - 不应该要求澄清
echo "📝 测试用例3: 问候语 '你好' - 期望直接回复"
RESPONSE3=$(curl -s -X POST "${BASE_URL}/api/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "你好",
    "userContext": {
      "userId": "test-user-001",
      "sessionId": "'$(uuidgen)'",
      "userRoles": ["华北区销售经理"], 
      "clientInfo": {}
    }
  }')

echo "响应: $RESPONSE3" | jq .
echo ""

# 分析测试结果
echo "🔍 分析测试结果:"
echo "================================================"

# 检查测试用例1是否要求澄清
if echo "$RESPONSE1" | jq -r '.responseType' | grep -q "CLARIFICATION_NEEDED"; then
    echo "✅ 测试用例1通过: 模糊查询正确要求澄清"
else
    echo "❌ 测试用例1失败: 模糊查询未要求澄清"
fi

# 检查测试用例2是否直接执行
if echo "$RESPONSE2" | jq -r '.responseType' | grep -q "DATA"; then
    echo "✅ 测试用例2通过: 具体查询正确直接执行"
else
    echo "❌ 测试用例2失败: 具体查询未直接执行"
fi

# 检查测试用例3是否直接回复
if echo "$RESPONSE3" | jq -r '.responseType' | grep -q "ACKNOWLEDGEMENT\|GREETING"; then
    echo "✅ 测试用例3通过: 问候语正确直接回复"
else
    echo "❌ 测试用例3失败: 问候语处理异常"
fi

echo "================================================"
echo "🏁 测试完成!" 