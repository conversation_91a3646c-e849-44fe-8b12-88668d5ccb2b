#!/bin/bash

# 测试SQL生成修复效果
# 重现"成本"字段的SQL生成错误问题

echo "🧪 测试SQL生成修复效果..."

BASE_URL="http://localhost:8081"

# 生成唯一的会话ID
SESSION_ID=$(uuidgen)
echo "📝 使用会话ID: $SESSION_ID"

echo ""
echo "==========================================="
echo "🔥 测试场景：SQL生成修复验证"
echo "==========================================="

# 第一步：用户查询"成本"
echo "步骤1: 用户查询 '成本'"
RESPONSE1=$(curl -s -X POST "${BASE_URL}/api/v1/chatbi/conversation/query" \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "成本",
    "userId": "test-user-001",
    "sessionId": "'$SESSION_ID'",
    "userRoles": ["华北区销售经理"],
    "additionalParams": {}
  }')

echo "AI响应类型: $(echo "$RESPONSE1" | jq -r '.responseType')"
echo "AI消息: $(echo "$RESPONSE1" | jq -r '.messageToUser')"
echo ""

# 第二步：用户提供地理维度
sleep 2
echo "步骤2: 用户提供 '上海，天津'"
RESPONSE2=$(curl -s -X POST "${BASE_URL}/api/v1/chatbi/conversation/query" \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "上海，天津",
    "userId": "test-user-001",
    "sessionId": "'$SESSION_ID'",
    "userRoles": ["华北区销售经理"],
    "additionalParams": {}
  }')

echo "AI响应类型: $(echo "$RESPONSE2" | jq -r '.responseType')"
echo "AI消息: $(echo "$RESPONSE2" | jq -r '.messageToUser')"
echo ""

# 第三步：用户提供时间维度
sleep 2
echo "步骤3: 用户提供 '去年的'"
RESPONSE3=$(curl -s -X POST "${BASE_URL}/api/v1/chatbi/conversation/query" \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "去年的",
    "userId": "test-user-001",
    "sessionId": "'$SESSION_ID'",
    "userRoles": ["华北区销售经理"],
    "additionalParams": {}
  }')

RESPONSE_TYPE=$(echo "$RESPONSE3" | jq -r '.responseType')
echo "AI响应类型: $RESPONSE_TYPE"
echo "AI消息: $(echo "$RESPONSE3" | jq -r '.messageToUser')"

# 检查是否修复成功
if [ "$RESPONSE_TYPE" = "ERROR" ]; then
    ERROR_MSG=$(echo "$RESPONSE3" | jq -r '.messageToUser')
    if [[ $ERROR_MSG == *"Unknown column"* ]] || [[ $ERROR_MSG == *"sales_amount * 0.7"* ]]; then
        echo ""
        echo "❌ 发现问题！SQL生成仍然存在错误："
        echo "   错误信息: $ERROR_MSG"
        echo "   问题：AI可能仍在给计算表达式加反引号"
        echo ""
        echo "🔍 建议检查："
        echo "   1. Prompt模板是否正确更新"
        echo "   2. LLM是否理解了新的指令"
        echo "   3. 可能需要重启服务以加载新的prompt"
    else
        echo ""
        echo "⚠️ 出现了其他类型的错误："
        echo "   错误信息: $ERROR_MSG"
    fi
elif [ "$RESPONSE_TYPE" = "DATA_RESULT" ]; then
    echo ""
    echo "✅ 修复成功！系统正常返回了数据结果"
    echo "   这说明SQL生成已经修复，不再给表达式加反引号了"
elif [ "$RESPONSE_TYPE" = "CLARIFICATION_NEEDED" ]; then
    echo ""
    echo "ℹ️ 系统要求澄清，这可能是正常行为"
    echo "   这说明SQL生成错误已经不再出现"
else
    echo ""
    echo "🤔 收到了意外的响应类型: $RESPONSE_TYPE"
fi

echo ""
echo "=========================================="
echo "测试完成"
echo "==========================================" 