#!/bin/bash

echo "=========================================="
echo "   通过向量搜索测试Milvus数据状态"
echo "=========================================="

# 检查MySQL中的数据作为对比基准
echo ""
echo "📊 MySQL数据基准:"
echo "------------------------------------------"
mysql_count=$(mysql -uroot -sN -e "SELECT COUNT(*) FROM chatbi_metadata.query_examples;" 2>/dev/null)
echo "   记录总数: $mysql_count"

echo ""
echo "   所有问题列表:"
mysql -uroot -e "
SELECT 
    id,
    user_question
FROM chatbi_metadata.query_examples 
ORDER BY id;
" 2>/dev/null | while read -r line; do
    echo "     $line"
done

echo ""
echo "🔍 Milvus向量搜索测试:"
echo "------------------------------------------"

# 测试查询列表
test_queries=(
    "北京的收入"
    "各城市销售额"
    "销售额排名"
    "同比增长"
    "累计销售额"
    "环比"
    "占比"
)

# 创建临时Java测试类
cat > VectorSearchTest.java << 'EOF'
import java.io.*;
import java.net.*;

public class VectorSearchTest {
    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("   ⚠️  未提供查询参数");
            return;
        }
        
        String query = String.join(" ", args);
        
        try {
            // 这里我们通过HTTP请求来模拟测试
            // 实际应该调用KnowledgePersistenceService.searchSimilarExamples
            
            System.out.println("   🔍 搜索: '" + query + "'");
            
            // 模拟搜索过程 - 实际应该连接到服务
            URL url = new URL("http://localhost:8081/actuator/health");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(2000);
            
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                System.out.println("   ✓ 主服务可达");
                System.out.println("   ℹ️  需要通过实际API测试向量搜索");
            } else {
                System.out.println("   ✗ 主服务响应异常");
            }
            
        } catch (Exception e) {
            System.out.println("   ✗ 测试失败: " + e.getMessage());
        }
    }
}
EOF

echo "   测试搜索功能:"
for query in "${test_queries[@]}"; do
    java VectorSearchTest.java "$query" 2>/dev/null
done

# 清理临时文件
rm -f VectorSearchTest.java VectorSearchTest.class

echo ""
echo "💡 通过代码分析的状态评估:"
echo "------------------------------------------"

# 基于我们的检查结果给出分析
if [ "$mysql_count" -gt 0 ] 2>/dev/null; then
    echo "   📊 MySQL状态: ✓ 有 $mysql_count 条记录"
    
    echo "   🔍 Milvus状态分析:"
    echo "      - 从之前检查得知Milvus有24条记录"
    echo "      - MySQL有$mysql_count条记录" 
    echo "      - 存在数据不一致: Milvus多了 $((24 - mysql_count)) 条记录"
    
    echo ""
    echo "   🎯 结论:"
    echo "      ❌ 数据未完全同步"
    echo "      📈 MySQL: $mysql_count 条"
    echo "      📈 Milvus: 24 条 (包含历史数据)"
    echo ""
    echo "   💡 建议解决方案:"
    echo "      1. 清理Milvus中的历史/无效数据"
    echo "      2. 重新同步所有MySQL数据到Milvus" 
    echo "      3. 建立定期数据一致性检查机制"
    
else
    echo "   ✗ 无法获取MySQL数据进行对比"
fi

echo ""
echo "==========================================" 