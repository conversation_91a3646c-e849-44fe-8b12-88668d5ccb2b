#!/bin/bash

echo "🧪 开始测试利润率查询，观察DEBUG日志..."
echo "================================================"

# 发送查询请求
curl -X POST http://localhost:8081/api/v1/chatbi/conversation/query \
  -H "Content-Type: application/json" \
  -d '{
    "queryText": "基于澄清选项 \"查询 (利润 / 销售额) * 100% 作为利润率\"，请继续处理我的原始查询",
    "userId": "test-user-001",
    "sessionId": "debug-test-session",
    "additionalParams": {
      "userRoles": ["华北区销售经理"]
    }
  }' \
  -v

echo ""
echo "================================================"
echo "✅ 查询请求已发送！"
echo "💡 请查看应用程序日志以获取详细的DEBUG信息。"
echo "🔍 特别关注以下日志内容："
echo "   - DataAccessTools.executeQuery 的详细SQL内容"
echo "   - DataAccessServiceImpl 的JDBC URL和连接信息"
echo "   - HikariDataSource 的配置详情"
echo "   - 实际执行的SQL语句" 