#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试不同向量相似性度量算法的效果
比较 L2、COSINE、IP (内积) 三种算法
"""

import mysql.connector
import json
from pymilvus import connections, Collection, utility
import hashlib
from typing import List, Dict
import numpy as np

def get_embedding(text: str) -> List[float]:
    """获取文本的向量表示（模拟我们Python脚本中的逻辑）"""
    hash_obj = hashlib.sha256(text.encode('utf-8'))
    hash_bytes = hash_obj.digest()
    
    # 重复hash值来生成1536维向量
    vector = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        vector.append(float(hash_bytes[byte_index]) / 255.0 - 0.5)
        
    # 对于COSINE和IP算法，需要归一化
    vector = np.array(vector)
    norm = np.linalg.norm(vector)
    if norm > 0:
        vector = vector / norm
    
    return vector.tolist()

def test_different_metrics():
    """测试不同的相似性度量算法"""
    
    # 连接Milvus
    try:
        connections.connect("default", host='localhost', port='19530')
        print("✅ Milvus连接成功")
    except Exception as e:
        print(f"❌ Milvus连接失败: {e}")
        return
    
    # 连接MySQL
    try:
        conn = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='chatbi_metadata'
        )
        print("✅ MySQL连接成功")
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return
    
    collection_name = "query_examples"
    collection = Collection(collection_name)
    collection.load()
    
    print(f"📊 向量集合中共有 {collection.num_entities} 条记录")
    
    # 测试查询案例
    test_cases = [
        {
            "query": "各品类去年的总销售",
            "description": "品类销售查询",
            "expected_match": "各品类去年的总销售额"
        },
        {
            "query": "北京销售额",
            "description": "地区销售查询",
            "expected_match": "北京的收入"
        },
        {
            "query": "产品排名",
            "description": "排名查询",
            "expected_match": "销售额最高的10个产品"
        }
    ]
    
    # 测试不同的度量算法
    metrics = ["L2", "COSINE", "IP"]
    
    for metric in metrics:
        print(f"\n{'='*60}")
        print(f"测试度量算法: {metric}")
        print(f"{'='*60}")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case['description']}")
            print(f"查询: '{test_case['query']}'")
            print(f"期望匹配: {test_case['expected_match']}")
            
            # 执行搜索
            search_results = search_with_metric(collection, conn, test_case['query'], metric, 5)
            
            print(f"\n{metric} 算法搜索结果:")
            best_match_found = False
            for j, result in enumerate(search_results, 1):
                is_expected = test_case['expected_match'] in result['user_question']
                marker = " ⭐" if is_expected else ""
                distance_or_score = result.get('distance', result.get('score', 'N/A'))
                
                print(f"  {j}. [{result['id']}] {result['user_question']}{marker}")
                print(f"     距离/得分: {distance_or_score:.4f}, 类型: {result['difficulty_level']}")
                
                if is_expected and j <= 3:  # 前3名找到期望匹配
                    best_match_found = True
            
            if best_match_found:
                print("     ✅ 在前3名中找到期望匹配")
            else:
                print("     ❌ 未在前3名中找到期望匹配")
    
    conn.close()

def search_with_metric(collection, conn, query_text: str, metric_type: str, top_k: int = 5):
    """使用指定度量算法搜索相似示例"""
    # 生成查询向量
    query_vector = get_embedding(query_text)
    
    # 根据度量类型设置搜索参数
    if metric_type == "L2":
        search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
    elif metric_type == "COSINE":
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
    elif metric_type == "IP":
        search_params = {"metric_type": "IP", "params": {"nprobe": 10}}
    else:
        raise ValueError(f"不支持的度量类型: {metric_type}")
    
    try:
        results = collection.search(
            data=[query_vector],
            anns_field="vector",
            param=search_params,
            limit=top_k,
            output_fields=["id"]
        )
        
        # 获取结果ID和距离/得分
        result_ids = []
        distances = []
        if results and len(results[0]) > 0:
            for result in results[0]:
                result_ids.append(result.id)
                distances.append(result.distance)
        
        # 从MySQL获取详细信息
        if result_ids:
            cursor = conn.cursor()
            placeholders = ','.join(['%s'] * len(result_ids))
            query = f"""
            SELECT qe.id, qe.user_question, qe.difficulty_level, qd.dataset_name
            FROM query_examples qe 
            LEFT JOIN queryable_datasets qd ON qe.target_dataset_id = qd.id
            WHERE qe.id IN ({placeholders})
            """
            cursor.execute(query, result_ids)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            # 转换为字典并保持顺序
            result_dict = {}
            for row in results:
                example = dict(zip(columns, row))
                result_dict[example['id']] = example
                
            # 按搜索结果顺序返回
            ordered_results = []
            for i, result_id in enumerate(result_ids):
                if result_id in result_dict:
                    example = result_dict[result_id]
                    if metric_type == "L2":
                        example['distance'] = distances[i]
                    else:
                        example['score'] = distances[i]  # 对于COSINE和IP，这实际是相似性得分
                    ordered_results.append(example)
                    
            return ordered_results
        
    except Exception as e:
        print(f"     搜索失败: {e}")
        return []
    
    return []

def compare_algorithm_performance():
    """比较算法性能总结"""
    print(f"\n{'='*80}")
    print("向量相似性度量算法比较总结")
    print(f"{'='*80}")
    
    print("\n📊 算法特点:")
    print("1. L2 (欧几里得距离):")
    print("   - 计算点之间的直线距离")
    print("   - 对向量长度敏感")
    print("   - 距离越小越相似")
    print("   - 适用场景: 数值型数据，关注绝对差异")
    
    print("\n2. COSINE (余弦相似度):")
    print("   - 计算向量夹角的余弦值")
    print("   - 对向量长度不敏感，只关注方向")
    print("   - 值越大越相似(范围 -1 到 1)")
    print("   - 适用场景: 文本向量，关注语义相似性")
    
    print("\n3. IP (内积):")
    print("   - 计算向量的点积")
    print("   - 同时考虑方向和大小")
    print("   - 值越大越相似")
    print("   - 适用场景: 推荐系统，需要考虑向量大小")
    
    print("\n💡 对于ChatBI文本查询的建议:")
    print("- 首选: COSINE (余弦相似度)")
    print("  原因: 文本向量主要关注语义方向，不关注向量长度")
    print("- 备选: IP (内积)")
    print("  原因: 在某些情况下可能提供更好的排序")
    print("- 不推荐: L2 (欧几里得距离)")
    print("  原因: 对文本向量的绝对数值差异过于敏感")

if __name__ == "__main__":
    test_different_metrics()
    compare_algorithm_performance()
