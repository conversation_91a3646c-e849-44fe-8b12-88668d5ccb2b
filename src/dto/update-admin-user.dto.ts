export class UpdateAdminUserDto {
  @IsOptional()
  @IsString()
  @Length(6, 20)
  password?: string;

  @IsOptional()
  @IsEmail()
  @Length(5, 255)
  email?: string;

  @IsOptional()
  @IsString()
  @Length(11, 20)
  @Matches(/^[0-9+]+$/)
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  @Length(2, 100)
  fullName?: string;

  @IsOptional()
  @IsString()
  @Length(5, 50)
  @IsIn(['admin', 'super_admin'])
  role?: string;

  @IsOptional()
  @IsString()
  @Length(5, 50)
  @IsIn(['active', 'inactive', 'locked'])
  status?: string;
} 