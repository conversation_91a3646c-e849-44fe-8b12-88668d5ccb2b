export class CreateAdminUserDto {
  @IsString()
  @Length(3, 100)
  username: string;

  @IsString()
  @Length(6, 20)
  password: string;

  @IsEmail()
  @Length(5, 255)
  email: string;

  @IsOptional()
  @IsString()
  @Length(11, 20)
  @Matches(/^[0-9+]+$/)
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  @Length(2, 100)
  fullName?: string;

  @IsOptional()
  @IsString()
  @Length(5, 50)
  @IsIn(['admin', 'super_admin'])
  role?: string;
} 