export class AdminUser {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100, unique: true })
  username: string;

  @Column({ length: 255 })
  passwordHash: string;

  @Column({ length: 255, unique: true })
  email: string;

  @Column({ length: 20, unique: true, nullable: true })
  phoneNumber: string;

  @Column({ length: 100, nullable: true })
  fullName: string;

  @Column({ length: 50, default: 'admin' })
  role: string;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ length: 50, default: 'active' })
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 