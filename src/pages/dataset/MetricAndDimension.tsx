import React, { useState } from 'react';
import { Button, Modal, message } from 'antd';
import { SyncOutlined } from '@ant-design/icons';
import SelectFieldStep from './components/CreateDatasetSteps/SelectField';
import { useDatasetDetail } from '@/hooks/useDatasetDetail';
import { addFieldsToDataset } from '@/services/dataset';

const MetricAndDimension = () => {
    const { dataset, fields, refresh } = useDatasetDetail();
    const [isSyncModalVisible, setIsSyncModalVisible] = useState(false);

    const handleSync = () => {
        if (!dataset) {
            message.warn('无法获取数据集信息，无法同步');
            return;
        }
        setIsSyncModalVisible(true);
    };

    const handleSyncCancel = () => {
        setIsSyncModalVisible(false);
    };

    const handleSyncFinish = async (newFields) => {
        if (!newFields || newFields.length === 0) {
            message.info('您没有选择任何新的字段');
            setIsSyncModalVisible(false);
            return;
        }
        try {
            await addFieldsToDataset(dataset.id, newFields);
            message.success('同步成功！');
            setIsSyncModalVisible(false);
            refresh();
        } catch (error) {
            message.error('同步失败，请重试');
            console.error('Failed to sync fields:', error);
        }
    };

    return (
        <div>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', marginBottom: '16px' }}>
                <Button type="primary" icon={<SyncOutlined />} onClick={handleSync}>
                    从数据源同步
                </Button>
            </div>
            
            {isSyncModalVisible && (
                <Modal
                    title="从数据源同步字段"
                    open={isSyncModalVisible}
                    onCancel={handleSyncCancel}
                    width={1000}
                    footer={null}
                    destroyOnClose
                >
                    <SelectFieldStep
                        isSyncMode={true}
                        datasourceId={dataset.datasourceId}
                        tableName={dataset.tableName}
                        existingFields={fields}
                        onFinish={handleSyncFinish}
                        onCancel={handleSyncCancel}
                    />
                </Modal>
            )}
        </div>
    );
};

export default MetricAndDimension; 