import React, { useState, useEffect } from 'react';
import { Table, Button, Spin, message, Alert } from 'antd';
// 假设您有一个 API 服务用于获取数据源表的全部字段
import { fetchAllTableFields } from '@/services/datasource';

const SelectFieldStep = ({
    isSyncMode = false,
    datasourceId,
    tableName,
    existingFields = [], // 在同步模式下传入
    onFinish,
    onCancel,
}) => {
    const [allFields, setAllFields] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchFields = async () => {
            if (!datasourceId || !tableName) {
                message.error('缺少数据源信息，无法加载字段');
                setLoading(false);
                return;
            }
            setLoading(true);
            try {
                // 从数据源获取所有字段
                const fieldsFromSource = await fetchAllTableFields(datasourceId, tableName);
                setAllFields(fieldsFromSource);

                if (isSyncMode) {
                    // 预先选中已存在的字段
                    const existingFieldNames = existingFields.map(f => f.technical_name); // 请根据您的数据结构调整 'technical_name'
                    const initialSelectedKeys = fieldsFromSource
                        .filter(f => existingFieldNames.includes(f.name))
                        .map(f => f.name); // 假设字段的唯一标识是 'name'
                    setSelectedRowKeys(initialSelectedKeys);
                }
            } catch (error) {
                message.error('加载数据表字段失败');
                console.error('Failed to fetch table fields:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchFields();
    }, [datasourceId, tableName, isSyncMode, existingFields]);

    const handleConfirm = () => {
        const selectedFields = allFields.filter(f => selectedRowKeys.includes(f.name));

        if (isSyncMode) {
            const existingFieldNames = existingFields.map(f => f.technical_name);
            const newFieldsToAdd = selectedFields.filter(f => !existingFieldNames.includes(f.name));
            onFinish(newFieldsToAdd);
        } else {
            // 在新建数据集流程中的原始行为
            onFinish(selectedFields);
        }
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: (keys) => setSelectedRowKeys(keys),
        getCheckboxProps: (record) => ({
            // 在同步模式下，禁用已存在的字段的勾选框
            disabled: isSyncMode && existingFields.some(f => f.technical_name === record.name),
        }),
    };

    const columns = [
        {
            title: '字段名',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '字段类型',
            dataIndex: 'type',
            key: 'type',
        },
        {
            title: '描述',
            dataIndex: 'comment',
            key: 'comment',
        },
    ];

    return (
        <Spin spinning={loading}>
            {isSyncMode && (
                <Alert
                    message="您正在从数据源同步字段。已存在的字段已被默认勾选且不可更改。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                />
            )}
            <Table
                rowKey="name"
                rowSelection={rowSelection}
                dataSource={allFields}
                columns={columns}
                pagination={false}
                scroll={{ y: 400 }}
            />
            <div style={{ marginTop: 24, textAlign: 'right' }}>
                <Button onClick={onCancel} style={{ marginRight: 8 }}>
                    取消
                </Button>
                <Button type="primary" onClick={handleConfirm}>
                    {isSyncMode ? '添加新字段' : '下一步'}
                </Button>
            </div>
        </Spin>
    );
};

export default SelectFieldStep; 