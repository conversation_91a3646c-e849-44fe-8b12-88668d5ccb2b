package com.qding.chatbi.api.controller;

import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.agent.memory.ChatMemoryProvider;
import com.qding.chatbi.agent.service.*;
import com.qding.chatbi.common.dto.*;
import com.qding.chatbi.common.enums.ResponseType;
import com.qding.chatbi.metadata.service.PermissionService;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.ChatMemory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 支持进度跟踪的对话服务
 * 在真实处理过程中提供进度回调
 */
@Service
public class ProgressTrackingConversationService {

    private static final Logger log = LoggerFactory.getLogger(ProgressTrackingConversationService.class);

    @Autowired
    private ChatMemoryProvider chatMemoryProvider;
    
    @Autowired
    private NluAgent nluAgent;
    
    @Autowired
    private QueryPlannerAgent queryPlannerAgent;
    
    @Autowired
    private DataRetrievalAgent dataRetrievalAgent;
    
    @Autowired
    private ResponseGenerator responseGenerator;
    
    @Autowired
    private PermissionService permissionService;

    /**
     * 处理用户查询并提供真实进度反馈
     */
    public AgentResponse processUserQueryWithProgress(UserQueryRequest request, UserContextDTO userContext, 
                                                    ProgressCallback progressCallback) {
        long totalStartTime = System.currentTimeMillis();
        
        try {
            // 1. 初始化阶段 (0-5%)
            progressCallback.updateProgress(0, "开始处理您的查询...");
            
            // 基本验证
            if (request == null || request.getQueryText() == null || request.getQueryText().trim().isEmpty()) {
                throw new IllegalArgumentException("查询参数无效");
            }
            
            progressCallback.updateProgress(5, "参数验证完成");
            
            // 2. 获取会话记忆 (5-10%)
            String userRole = userContext != null ? userContext.getUserRole() : null;
            ChatMemory chatMemory = chatMemoryProvider.getMemory(userContext.getSessionId());
            
            // 添加当前用户消息到记忆
            UserMessage currentUserMessage = UserMessage.from(request.getQueryText());
            chatMemory.add(currentUserMessage);
            
            progressCallback.updateProgress(10, "会话上下文准备完成");
            
            // 3. NLU理解阶段 (10-40%)
            long nluStartTime = System.currentTimeMillis();
            progressCallback.updateProgress(15, "开始理解您的查询意图...");
            progressCallback.sendThinking("正在分析查询语义，识别关键实体和意图...");
            
            StructuredQueryIntent intent;
            try {
                List<DatasetInfoDTO> availableDatasets = permissionService.getPermittedDatasetInfos(userRole);
                String chatHistory = getFormattedHistory(chatMemory);
                
                progressCallback.updateProgress(20, "获取可用数据集信息...");
                progressCallback.sendThinking("分析用户权限和可访问的数据集...");
                
                intent = nluAgent.understand(request.getQueryText(), userContext.getSessionId(), 
                                           chatHistory, availableDatasets, userRole);
                
                long nluTime = System.currentTimeMillis() - nluStartTime;
                progressCallback.updateProgress(35, String.format("意图理解完成 (耗时: %dms)", nluTime));
                
                log.info("NLU解析完成: 意图={}, 耗时={}ms", intent.getIntent(), nluTime);
                
            } catch (Exception e) {
                log.error("NLU Agent处理失败: {}", e.getMessage(), e);
                return createErrorResponse("理解您的查询时发生错误，请稍后重试。", userContext, chatMemory);
            }
            
            // 4. 权限检查 (35-40%)
            progressCallback.updateProgress(38, "检查数据访问权限...");
            List<Long> permittedDatasetIds = permissionService.getPermittedDatasetIds(userRole);
            if (permittedDatasetIds.isEmpty() && !"GREETING".equals(intent.getIntent())) {
                String message = "您当前的账户没有查询数据的权限，请联系管理员。";
                return createSimpleResponse(message, userContext, chatMemory);
            }
            
            progressCallback.updateProgress(40, "权限检查完成");
            
            // 5. 处理不同类型的意图
            if ("CLARIFICATION_NEEDED".equals(intent.getIntent())) {
                progressCallback.updateProgress(100, "需要进一步澄清");
                return createClarificationResponse(intent, userContext, chatMemory);
            } else if ("GREETING".equals(intent.getIntent())) {
                progressCallback.updateProgress(100, "问候处理完成");
                return createSimpleResponse("您好！有什么可以帮您的吗？", userContext, chatMemory);
            }
            
            // 6. SQL生成阶段 (40-65%)
            long sqlStartTime = System.currentTimeMillis();
            progressCallback.updateProgress(45, "开始生成数据查询语句...");
            progressCallback.sendSqlGeneration("根据您的查询意图生成SQL语句...");
            
            QueryPlan queryPlan;
            try {
                queryPlan = queryPlannerAgent.planAndGenerateSql(intent, userContext);
                long sqlTime = System.currentTimeMillis() - sqlStartTime;
                progressCallback.updateProgress(60, String.format("SQL生成完成 (耗时: %dms)", sqlTime));
                
                log.info("SQL生成完成: 耗时={}ms", sqlTime);
                
            } catch (Exception e) {
                log.error("SQL生成失败: {}", e.getMessage(), e);
                return createErrorResponse("生成查询语句时发生错误。", userContext, chatMemory);
            }
            
            // 7. 数据检索阶段 (65-85%)
            long dataStartTime = System.currentTimeMillis();
            progressCallback.updateProgress(70, "开始执行数据查询...");
            progressCallback.sendDataRetrieval("正在从数据库检索相关数据...");
            
            QueryResult queryResult;
            try {
                queryResult = dataRetrievalAgent.retrieve(queryPlan, userContext);
                long dataTime = System.currentTimeMillis() - dataStartTime;
                progressCallback.updateProgress(80, String.format("数据检索完成 (耗时: %dms)", dataTime));
                
                log.info("数据检索完成: 耗时={}ms, 行数={}", dataTime, 
                        queryResult.getRows() != null ? queryResult.getRows().size() : 0);
                
            } catch (Exception e) {
                log.error("数据检索失败: {}", e.getMessage(), e);
                return createErrorResponse("执行数据查询时发生错误。", userContext, chatMemory);
            }
            
            // 8. 响应生成阶段 (85-95%)
            long responseStartTime = System.currentTimeMillis();
            progressCallback.updateProgress(85, "开始生成智能分析结果...");
            progressCallback.sendResponseGeneration("正在生成数据分析和可视化建议...");
            
            AgentResponse response;
            try {
                if (queryResult != null && (queryResult.getErrorMessage() == null || queryResult.getErrorMessage().isEmpty())) {
                    response = responseGenerator.generate(queryResult, intent);
                    response.setSessionId(userContext.getSessionId());
                    
                    if (queryPlan.getThoughtProcess() != null) {
                        response.setThoughtProcess(queryPlan.getThoughtProcess());
                    }
                } else {
                    response = new AgentResponse();
                    response.setSessionId(userContext.getSessionId());
                    response.setResponseType(ResponseType.ERROR);
                    response.setMessageToUser(queryResult != null && queryResult.getErrorMessage() != null ? 
                                            queryResult.getErrorMessage() : "查询执行失败");
                }
                
                long responseTime = System.currentTimeMillis() - responseStartTime;
                progressCallback.updateProgress(95, String.format("分析结果生成完成 (耗时: %dms)", responseTime));
                
                log.info("响应生成完成: 耗时={}ms", responseTime);
                
            } catch (Exception e) {
                log.error("响应生成失败: {}", e.getMessage(), e);
                return createErrorResponse("生成分析结果时发生错误。", userContext, chatMemory);
            }
            
            // 9. 完成 (95-100%)
            long totalTime = System.currentTimeMillis() - totalStartTime;
            progressCallback.updateProgress(100, String.format("查询处理完成 (总耗时: %dms)", totalTime));
            
            // 保存AI响应到记忆
            if (response.getMessageToUser() != null) {
                chatMemory.add(AiMessage.from(response.getMessageToUser()));
            }
            
            log.info("查询处理完成: 总耗时={}ms, 响应类型={}", totalTime, response.getResponseType());
            
            return response;
            
        } catch (Exception e) {
            log.error("查询处理过程中发生异常", e);
            return createErrorResponse("处理查询时发生错误: " + e.getMessage(), userContext, null);
        }
    }
    
    private AgentResponse createErrorResponse(String message, UserContextDTO userContext, ChatMemory chatMemory) {
        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.ERROR);
        response.setMessageToUser(message);
        
        if (chatMemory != null) {
            chatMemory.add(AiMessage.from(message));
        }
        
        return response;
    }
    
    private AgentResponse createSimpleResponse(String message, UserContextDTO userContext, ChatMemory chatMemory) {
        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.ACKNOWLEDGEMENT);
        response.setMessageToUser(message);
        
        chatMemory.add(AiMessage.from(message));
        
        return response;
    }
    
    private AgentResponse createClarificationResponse(StructuredQueryIntent intent, UserContextDTO userContext, ChatMemory chatMemory) {
        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.CLARIFICATION_NEEDED);
        response.setMessageToUser(intent.getResponse());
        response.setClarificationOptions(intent.getClarificationOptions());
        
        chatMemory.add(AiMessage.from(intent.getResponse()));
        
        return response;
    }
    
    private String getFormattedHistory(ChatMemory chatMemory) {
        // 简化的历史格式化，实际实现应该更复杂
        StringBuilder history = new StringBuilder();
        chatMemory.messages().forEach(message -> {
            if (message.type().toString().equals("USER")) {
                history.append("USER: ").append(message.text()).append("\n");
            } else if (message.type().toString().equals("AI")) {
                history.append("AI: ").append(message.text()).append("\n");
            }
        });
        return history.toString();
    }
}
