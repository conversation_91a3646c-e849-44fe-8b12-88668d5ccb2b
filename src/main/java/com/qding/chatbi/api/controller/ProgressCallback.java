package com.qding.chatbi.api.controller;

/**
 * SSE进度回调接口
 * 用于在查询处理过程中实时反馈进度
 */
public interface ProgressCallback {
    
    /**
     * 更新进度状态
     * @param progress 进度百分比 (0-100)
     * @param message 状态描述
     */
    void updateProgress(int progress, String message);
    
    /**
     * 发送思考过程
     * @param thought 思考内容
     */
    void sendThinking(String thought);
    
    /**
     * 发送SQL生成进度
     * @param message SQL生成信息
     */
    void sendSqlGeneration(String message);
    
    /**
     * 发送数据检索进度
     * @param message 数据检索信息
     */
    void sendDataRetrieval(String message);
    
    /**
     * 发送响应生成进度
     * @param message 响应生成信息
     */
    void sendResponseGeneration(String message);
    
    /**
     * 检查是否应该继续处理
     * @return true 如果应该继续，false 如果应该停止
     */
    boolean shouldContinue();
}
