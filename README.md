# **Chat BI 产品架构设计**

## **1\. 引言**

本项目旨在设计并开发一款创新的 Chat BI (Business Intelligence) 产品。核心目标是通过用户与 AI Agent 的自然语言对话，智能地将用户输入转化为数据库查询语句，执行查询并将结果反馈给用户。产品设计的关键原则包括：

* **简洁性**: 通过预先设计和管理 AI 可访问的数据模型，简化查询逻辑，确保 AI 生成的查询语句高效且准确。  
* **智能性**: AI Agent 能够准确理解用户意图，包括处理复杂的查询和企业内部的特定术语（“黑话”）。  
* **可扩展性**: 系统设计应具备良好的扩展能力，能够方便地增加新的数据表、支持新的业务场景以及不断优化 AI 的理解能力。  
* **用户友好**: 提供直观的交互方式，降低用户获取数据的门槛。  
* **安全性**: 通过基于角色的访问控制，确保用户只能访问其权限范围内的数据。  
* **可追溯性**: 记录用户与AI的交互历史，便于审计和问题排查。

本文档将详细阐述 Chat BI 产品的架构设计、核心模块、关键技术点以及开发建议。

## **2\. 核心架构**

为了实现上述目标，Chat BI 产品将采用模块化的架构设计，主要包括以下几个核心组件：

graph LR  
    subgraph 用户端 (User Interface)  
        A\[聊天界面\]  
    end

    subgraph 后端服务 (Backend Services)  
        B\[API 网关\]  
        C\[AI Agent 核心\]  
        D\[自然语言理解 (NLU) 模块\]  
        E\[查询生成模块\]  
        F\[数据模型与权限管理器\]  
        G\[企业黑话词典模块\]  
        H\[查询示例库模块\]  
        I\[数据库连接与执行模块\]  
        UserAuth\[用户认证与角色服务\]  
    end

    subgraph 数据层 (Data Layer)  
        J\[目标数据库/数据仓库\]  
        K\[元数据与配置存储 (MySQL)\]  
    end

    UserAuth \-- 用户角色 \--\> C

    A \-- 用户输入 \--\> B  
    B \-- 请求 \--\> C  
    C \-- 文本 \--\> D  
    D \-- 解析意图/实体 \--\> C  
    C \-- 意图/实体/上下文/用户角色 \--\> E  
    F \-- 可查询数据集(已过滤权限)与字段信息 \--\> E  
    F \-- 数据库配置信息 \--\> I  
    G \-- 黑话映射 \--\> D  
    H \-- 查询范例 \--\> E  
    H \-- 查询范例 \--\> D  
    E \-- 生成的查询语句 \--\> I  
    I \-- 连接/执行 \--\> J  
    J \-- 查询结果 \--\> I  
    I \-- 结果 \--\> C  
    C \-- 格式化结果 \--\> B  
    B \-- 结果 \--\> A  
    C \-- 保存对话历史 \--\> K

    F \-- 管理 \--\> K\[元数据存储: QueryableDatasets, DatasetColumns, DatabaseSourceConfigs, Roles, DatasetAccessPermissions, ChatMessages\]  
    G \-- 管理 \--\> K\[元数据存储: BusinessTerminology\]  
    H \-- 管理 \--\> K\[元数据存储: QueryExamples\]

    style A fill:\#f9f,stroke:\#333,stroke-width:2px  
    style J fill:\#ccf,stroke:\#333,stroke-width:2px  
    style K fill:\#ccf,stroke:\#333,stroke-width:2px  
    style C fill:\#lightgreen,stroke:\#333,stroke-width:2px  
    style UserAuth fill:\#ffcc99,stroke:\#333,stroke-width:2px

**组件说明:**

* **用户端 (User Interface):**  
  * **聊天界面 (Chat Interface):** 用户与 AI Agent 进行自然语言交互的入口。负责接收用户输入、展示 AI 回复和查询结果。  
* **后端服务 (Backend Services):**  
  * **API 网关 (API Gateway):** 作为所有请求的统一入口，负责请求路由、认证、限流等。  
  * **用户认证与角色服务 (User Authentication & Role Service):** 负责用户身份验证，并提供当前用户的角色信息。此服务可能与企业现有的身份认证系统集成。  
  * **AI Agent 核心 (AI Agent Core):** 整个系统的“大脑”，负责协调各个模块完成用户的请求。获取用户角色信息，管理对话状态，调用 NLU、查询生成等模块。在处理过程中会结合用户权限进行判断。**同时，AI Agent 核心也负责将用户与AI的交互历史持久化到“元数据与配置存储”中。**  
  * **自然语言理解 (NLU) 模块 (Natural Language Understanding Module):**  
    * 负责解析用户的自然语言输入，识别用户意图、提取关键实体（如查询的指标、维度、过滤条件等）。  
    * 需要结合“企业黑话词典”来理解特定术语。  
  * **查询生成模块 (Query Generation Module):**  
    * 根据 NLU 模块解析出的意图和实体，结合“数据模型与权限管理器”提供的**经过用户权限过滤后**的“可查询数据集与字段信息”，生成相应的数据库查询语句（如 SQL）。  
    * 会参考“查询示例库”来提高生成查询的准确性和效率，**同样会考虑示例所涉及数据是否在用户权限范围内**。  
  * **数据模型与权限管理器 (Data Model & Permission Manager):** 负责管理和提供对存储在“元数据与配置存储”中的核心元数据表的访问，主要包括**可查询数据集 (QueryableDatasets)**、**数据集字段 (DatasetColumns)**、**数据库源配置 (DatabaseSourceConfigs)** 以及新增的 **角色 (Roles)** 和 **数据集访问权限 (DatasetAccessPermissions)**。**它会根据请求用户的角色信息，过滤可访问的数据集和字段。**  
  * **企业黑话词典模块 (Enterprise Jargon Dictionary Module):** 负责管理和查询“业务述词对照表 (BusinessTerminology)”。  
  * **查询示例库模块 (Query Example Library Module):** 负责管理和查询“业务查询示例表 (QueryExamples)”。  
  * **数据库连接与执行模块 (Database Connection & Execution Module):**  
    * 负责连接目标数据库/数据仓库。获取连接配置信息时会参考“数据模型与权限管理器”提供的“数据库配置表”。  
    * 执行由“查询生成模块”生成的查询语句。  
    * 获取并初步处理查询结果。  
* **数据层 (Data Layer):**  
  * **目标数据库/数据仓库 (Target Database/Data Warehouse):** 存储业务数据的实际数据库，AI Agent 将基于此进行查询。  
  * **元数据与配置存储 (MySQL) (Metadata & Configuration Storage):** 用于存储系统配置信息，包括**可查询数据集定义、数据集字段定义、数据库源配置、角色定义、数据集访问权限配置、聊天消息历史**、企业黑话词典内容、查询示例等。

## **3\. 数据模型设计**

为了让 AI Agent 能够理解可查询的数据并生成简单有效的查询语句，我们将设计一套结构化的元数据表来精确描述数据。这些表共同构成了 AI 进行决策的基础，并存储在“元数据与配置存储 (MySQL)”中。核心思路是以“可查询的数据集/视图”为中心，将必要的 JOIN 逻辑前置到数据集的定义中，并在此基础上实现基于角色的访问控制和对话历史记录。

**3.1 目标**

* **简洁的元数据结构**: 减少抽象层级，使 AI 更易理解可查询的数据范围和内容。  
* **结构化与规范化**: 采用关系型数据库的设计原则，便于维护、查询和保证数据一致性。  
* **语义化增强**: 通过丰富的描述、业务名称和同义词，帮助 AI 理解数据含义。  
* **元数据驱动**: AI 的所有查询行为都基于这些元数据表。  
* **易于扩展**: 添加新的数据源或可查询数据集时，只需在相应表格中增加记录。  
* **责任明确**: 数据模型设计者负责定义好用、已处理内部关联的“可查询数据集”，AI 负责基于这些数据集理解用户意图。  
* **权限控制**: 实现基于用户角色的对“可查询数据集”的访问控制。  
* **交互追溯**: 存储用户与AI的对话历史，用于审计、分析和优化。

**3.2 元数据表结构定义**

以下是构成我们 Chat BI 产品核心元数据的主要表格：

**3.2.1 数据库源配置表 (DatabaseSourceConfigs)**

* **用途**: 统一管理所有可连接的数据库源的配置信息。  
* **字段**:  
  * database\_source\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 数据库源唯一标识。  
  * source\_name (VARCHAR, 唯一): 数据库源的业务名称 (e.g., "生产环境MySQL集群", "数据仓库Hive")。  
  * database\_type (VARCHAR): 数据库类型 (e.g., "MySQL", "PostgreSQL", "SQLServer", "Oracle", "Hive", "Presto", "ClickHouse")。  
  * connection\_parameters (TEXT/JSON, **敏感信息需特殊处理**): 存储数据库连接所需的全部参数，采用 JSON 格式。具体键值对应数据库类型而定。  
    * **示例 (MySQL):**  
      {  
        "host": "your\_mysql\_host",  
        "port": 3306,  
        "username": "your\_username",  
        "password\_ref": "path\_to\_mysql\_password\_in\_vault",  
        "database\_name": "your\_database",  
        "jdbc\_url\_template": "jdbc:mysql://{host}:{port}/{database\_name}?user={username}\&password={password\_actual}",  
        "ssl\_mode": "preferred",  
        "additional\_properties": {  
          "useUnicode": "true",  
          "characterEncoding": "utf8"  
        }  
      }

    * **示例 (PostgreSQL with SSL):**  
      {  
        "host": "your\_pg\_host",  
        "port": 5432,  
        "username": "your\_username",  
        "password\_ref": "pg\_password\_secret\_key",  
        "database\_name": "your\_pg\_database",  
        "ssl\_mode": "verify-full",  
        "ssl\_root\_cert\_path": "/path/to/root.crt",  
        "ssl\_client\_cert\_path": "/path/to/client.crt",  
        "ssl\_client\_key\_path": "/path/to/client.key.pem",  
        "additional\_properties": {  
          "currentSchema": "public"  
        }  
      }

  * description (TEXT, 可选): 关于此数据源的描述。  
  * status (ENUM, e.g., 'active', 'inactive', 'maintenance'): 数据源当前状态。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。  
* **注意**: connection\_parameters JSON 对象内部的敏感信息（如密码、私钥内容或路径）不应明文存储。应使用加密存储、环境变量、安全的密钥管理服务（如 HashiCorp Vault, AWS KMS, Azure Key Vault）等方式进行管理。字段如 password\_ref 或直接的 password 键（如果数据库驱动需要）应指向这些安全存储的引用或由系统在运行时动态注入。连接模块在构建实际连接时，需要解析此 JSON 并安全地获取凭证。

**3.2.2 可查询数据集表 (QueryableDatasets)**

* **用途**: 定义用户可以通过自然语言直接查询的“数据集”。每个数据集代表了一个业务主题或一个已经处理了内部关联的、可直接用于分析的数据视图。  
* **字段**:  
  * dataset\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 数据集唯一标识。  
  * dataset\_name (VARCHAR, 唯一): 数据集的业务名称 (e.g., "每日产品销售汇总", "用户行为日志")。  
  * description (TEXT): 数据集的详细业务描述，说明其覆盖的业务范围和主要内容。  
  * database\_source\_id (外键, 关联 DatabaseSourceConfigs.database\_source\_id): 此数据集所属的数据库源ID。  
  * base\_object\_name (VARCHAR): 此数据集在数据库中的基础对象名称。这可以是一个物理表名、视图名，或者一个代表复杂查询的标识符（如果 base\_object\_type 是 QUERY）。  
  * base\_object\_type (ENUM: "TABLE", "VIEW", "QUERY"): 基础对象的类型。  
    * 如果是 "TABLE" 或 "VIEW"，base\_object\_name 就是数据库中的表名或视图名。  
    * 如果是 "QUERY"，base\_object\_name 可能是一个引用名，其对应的SQL查询定义存储在 technical\_definition 字段中。  
  * technical\_definition (TEXT, 可选): 当 base\_object\_type 为 "QUERY" 时，此处存储定义该数据集的完整 SQL 查询语句。对于 "TABLE" 或 "VIEW"，此字段可为空或存储额外技术说明。  
  * synonyms (TEXT/JSON): 数据集的别名或常用称呼 (e.g., \["销售数据", "产品销量"\])。  
  * refresh\_frequency (VARCHAR, 可选): 数据集的数据更新频率描述 (e.g., "每日", "每小时", "实时")。  
  * data\_owner (VARCHAR, 可选): 数据责任人或团队。  
  * status (ENUM, e.g., 'active', 'inactive', 'deprecated'): 数据集当前状态。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。

**3.2.3 数据集字段表 (DatasetColumns)**

* **用途**: 定义每个“可查询数据集”中可供用户查询的具体字段（包括维度、度量和时间属性）。  
* **字段**:  
  * column\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 字段唯一标识。  
  * dataset\_id (外键, 关联 QueryableDatasets.dataset\_id): 所属数据集的ID。  
  * column\_name (VARCHAR): 字段的业务名称 (e.g., "产品名称", "总销售额", "用户注册城市", "订单日期")。**在同一 dataset\_id 内应唯一。**  
  * description (TEXT): 字段的详细描述及计算口径（如果适用）。  
  * technical\_name\_or\_expression (VARCHAR): 字段在 QueryableDatasets 的 base\_object\_name (表/视图)中的实际列名，或者是一个基于该基础对象的计算表达式 (e.g., actual\_column\_name, price \* quantity, EXTRACT(YEAR FROM order\_date))。  
  * semantic\_type (ENUM: "Metric", "Dimension", "Time"): 语义类型。  
  * data\_type (VARCHAR): 字段最终呈现给用户的数据类型 (e.g., "NUMBER", "STRING", "DATE", "BOOLEAN", "PERCENTAGE")。  
  * allowed\_aggregations (TEXT/JSON, 可选): 对于 semantic\_type \= "Metric"，允许的聚合函数列表 (e.g., \["SUM", "AVG", "COUNT", "MIN", "MAX"\])。对于维度或时间，此项通常为 NULL 或空。  
  * synonyms (TEXT/JSON): 字段的别名 (e.g., \["GMV", "成交总额"\] for "总销售额")。  
  * is\_filterable (BOOLEAN, DEFAULT true): 是否可用于查询的过滤条件。  
  * is\_groupable (BOOLEAN, DEFAULT true): 是否可用于查询的分组依据 (通常用于维度和时间)。  
  * is\_default\_metric (BOOLEAN, DEFAULT false): 是否为该数据集的默认度量 (例如，当用户只说“查xx维度”时，可以默认带上这个度量)。  
  * possible\_values\_query (TEXT, 可选): 对于某些枚举类型的维度，提供一个查询语句（基于其所属数据集的基础对象）来获取其可选值列表 (e.g., SELECT DISTINCT city\_name FROM {base\_object\_name\_of\_dataset})。  
  * formatting\_hint (TEXT/JSON, 可选): 关于如何展示此字段值的建议 (e.g., {"type": "currency", "symbol": "$", "decimal\_places": 2} or {"date\_format": "YYYY-MM-DD"})。  
  * status (ENUM, e.g., 'active', 'inactive'): 字段当前状态。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。

**3.2.4 角色表 (Roles)**

* **用途**: 定义系统中的用户角色。  
* **字段**:  
  * role\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 角色唯一标识。  
  * role\_name (VARCHAR, 唯一): 角色名称 (e.g., "SalesManager", "MarketingAnalyst", "RegionalDirector")。  
  * description (TEXT, 可选): 角色的描述。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。  
* **注意**: 实际的用户到角色的映射（例如，一个 UserRoleAssignments 表，包含 user\_id 和 role\_id）由“用户认证与角色服务”管理，该服务向 AI Agent 核心提供当前用户的角色信息。

**3.2.5 数据集访问权限表 (DatasetAccessPermissions)**

* **用途**: 定义哪个角色可以访问哪个“可查询数据集”。访问控制在此层面进行，不细化到字段级别。  
* **字段**:  
  * permission\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 权限记录唯一标识。  
  * role\_id (外键, 关联 Roles.role\_id): 角色的ID。  
  * dataset\_id (外键, 关联 QueryableDatasets.dataset\_id): 可查询数据集的ID。  
  * can\_access (BOOLEAN, DEFAULT true): 明确表示是否允许访问。通常情况下，如果存在一条记录，即表示允许访问。此字段可用于未来可能的“拒绝”场景，或保持设计一致性。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。  
* **约束**: (role\_id, dataset\_id) 组合应唯一。

**3.2.6 业务述词对照表 (BusinessTerminology)**

* **用途**: 存储企业内部的专业术语、缩写、别名（“黑话”）及其与标准数据集、字段或特定值的对应关系。这是“企业黑话词典模块”的核心数据。  
* **字段**:  
  * term\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 术语唯一标识。  
  * business\_term (VARCHAR, 唯一): 业务术语/黑话 (e.g., "GMV", "月活", "客单价")。  
  * standard\_reference\_type (ENUM: "QueryableDataset", "DatasetColumn", "FilterValue", "GeneralConcept"): 映射到的标准参考类型。  
  * standard\_reference\_id (VARCHAR, 可选): 映射到的标准参考对象的ID (e.g., QueryableDatasets.dataset\_id, DatasetColumns.column\_id)。如果类型是 "FilterValue"，这里可能是标准值。  
  * standard\_reference\_name (VARCHAR): 映射到的标准参考对象的名称 (用于可读性)。  
  * context\_description (TEXT, 可选): 此术语使用的上下文或补充说明。  
  * status (ENUM, e.g., 'active', 'pending\_review'): 术语状态。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。  
* **注意**: standard\_reference\_id 现在会引用 QueryableDatasets.dataset\_id 或 DatasetColumns.column\_id。

**3.2.7 业务查询示例表 (QueryExamples)**

* **用途**: 存储高质量的“用户自然语言问题”到“结构化查询意图”或“目标SQL”的映射示例。这是“查询示例库模块”的核心数据，用于AI模型训练、Few-shot learning 及辅助查询生成。  
* **字段**:  
  * example\_id (主键, e.g., INT AUTO\_INCREMENT, UUID): 示例唯一标识。  
  * user\_question (TEXT): 用户提出的自然语言问题 (e.g., "最近一个月上海地区的平均客单价是多少？")。  
  * target\_query\_representation (TEXT/JSON): 对应的结构化查询意图 (e.g., JSON 描述选择的数据集、字段、过滤器、聚合) 或最终的SQL语句。  
  * target\_dataset\_id (外键, 可选, 关联 QueryableDatasets.dataset\_id): 此查询主要关联的数据集。  
  * involved\_column\_ids (TEXT/JSON, 可选): 查询涉及的字段ID列表 (关联 DatasetColumns.column\_id)。  
  * notes (TEXT, 可选): 关于此示例的说明、难点或应用场景。  
  * difficulty\_level (ENUM, 可选: "Easy", "Medium", "Hard"): 示例难度。  
  * status (ENUM, e.g., 'verified', 'needs\_review'): 示例状态。  
  * created\_at (TIMESTAMP): 创建时间。  
  * updated\_at (TIMESTAMP): 最后更新时间。  
* **注意**: target\_model\_id 已更新为 target\_dataset\_id，involved\_item\_ids 已更新为 involved\_column\_ids。AI Agent 在使用这些示例时，会校验当前用户角色是否有权限访问示例中涉及的 target\_dataset\_id。

**3.2.8 聊天消息历史表 (ChatMessages)**

* **用途**: 存储用户与AI Agent之间的每一条交互消息，用于审计、问题排查、AI性能分析等。  
* **存储位置**: MySQL数据库 (与元数据表在同一实例或逻辑分组)。  
* **字段**:  
  * message\_id (BIGINT, 主键, AUTO\_INCREMENT): 消息唯一标识。  
  * session\_id (VARCHAR(255), 索引): 标识该消息所属的会话。  
  * user\_id (VARCHAR(255), 索引, 可选): 发起或参与此次对话的用户标识。  
  * sender\_type (VARCHAR(50)): 消息发送者类型 (e.g., "USER", "AI\_AGENT")。  
  * message\_text (TEXT): 用户输入的原始文本，或 AI Agent 回复的主要文本内容。  
  * message\_timestamp (TIMESTAMP, DEFAULT CURRENT\_TIMESTAMP, 索引): 消息创建或接收的时间戳。  
  * response\_type (VARCHAR(50), 可选): 对于AI回复，其响应类型 (对应 ResponseType 枚举)。  
  * structured\_response\_data (JSON, 可选): AI回复中包含的结构化数据 (如 QueryResult, ClarificationOption列表) 的JSON序列化字符串。  
  * error\_code (VARCHAR(50), 可选): 如果AI回复是错误类型，记录错误码 (对应 ErrorCode 枚举)。  
  * error\_message\_detail (TEXT, 可选): 详细的错误信息。  
  * processing\_duration\_ms (INT, 可选): AI处理此条用户请求并生成回复的耗时（毫秒）。  
  * llm\_model\_used (VARCHAR(100), 可选): 如果AI调用了LLM，记录所使用的模型名称。  
  * created\_at (TIMESTAMP, DEFAULT CURRENT\_TIMESTAMP): 记录创建时间 (可与message\_timestamp合并或根据具体审计需求区分)。  
  * updated\_at (TIMESTAMP, DEFAULT CURRENT\_TIMESTAMP ON UPDATE CURRENT\_TIMESTAMP): 记录更新时间。

**3.3 管理与扩展**

* **管理界面**: 建议为这些元数据表开发一个后台管理界面，方便数据管理员或业务分析师维护这些信息。重点是 QueryableDatasets、DatasetColumns、Roles 和 DatasetAccessPermissions 的定义与配置。  
* **版本控制**: 考虑对元数据变更引入版本控制机制。  
* **缓存机制**: AI Agent 核心模块（或数据模型与权限管理器）在启动时可以加载常用元数据（包括权限信息）到内存缓存，并定期刷新或通过消息通知更新，以提高查询性能和权限校验效率。  
* **自动化元数据辅助生成**:  
  * 对于 base\_object\_type 为 "TABLE" 或 "VIEW" 的 QueryableDatasets，可以开发工具扫描其在 DatabaseSourceConfigs 中对应数据源的 Schema (e.g., information\_schema)，来辅助填充 DatasetColumns 中的字段（如 technical\_name\_or\_expression, data\_type）。管理员仍需补充业务名称、语义类型等。  
  * 监控底层视图或表的变更，提示管理员更新相关的 QueryableDatasets 和 DatasetColumns 定义。  
* **API 接口**: 数据模型与权限管理器应提供清晰的 API 接口，供 NLU 模块、查询生成模块等其他组件按需获取元数据（已根据用户角色过滤）。

## **4\. 代码工程架构**

本节描述建议的 Chat BI 项目代码工程结构，基于 Java Spring Boot (后端) 和 Vue.js (前端管理界面)，并采用多模块 Maven/Gradle 项目组织方式。

chatbi-service-parent/  (项目根目录, 父POM或build.gradle)  
├── pom.xml  (或 build.gradle, settings.gradle)  
├── .gitignore  
├── README.md  
│  
├── chatbi-common/  (通用模块: DTOs, Enums, 工具类, 常量等)  
│   ├── src/  
│   │   ├── main/  
│   │   │   ├── java/  
│   │   │   │   └── com/qding/chatbi/common/  
│   │   │   │       ├── dto/  (Data Transfer Objects)  
│   │   │   │       │   ├── UserQueryRequest.java  
│   │   │   │       │   ├── AgentResponse.java  
│   │   │   │       │   ├── QueryResult.java  
│   │   │   │       │   └── ... (其他模块间共享的DTO)  
│   │   │   │       ├── enums/ (共享枚举)  
│   │   │   │       │   ├── SemanticType.java  
│   │   │   │       │   ├── DatabaseType.java  
│   │   │   │       │   └── ...  
│   │   │   │       ├── exception/ (自定义通用异常)  
│   │   │   │       ├── util/ (通用工具类)  
│   │   │   │       └── constant/ (通用常量)  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
├── chatbi-api/  (对外核心API定义模块: 如对话接口)  
│   ├── src/  
│   │   ├── main/  
│   │   │   └── java/  
│   │   │       └── com/qding/chatbi/api/  
│   │   │           └── controller/ (如果直接在此模块实现简单Controller)  
│   │   │           └── vo/ (View Objects, 接口特定的请求/响应结构)  
│   │   │           └── service/ (接口定义，实现在agent-core)  
│   │   │               └── ConversationApi.java  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
├── chatbi-agent-core/  (AI Agent 核心逻辑模块)  
│   ├── src/  
│   │   ├── main/  
│   │   │   ├── java/  
│   │   │   │   └── com/qding/chatbi/agent/  
│   │   │   │       ├── config/ (Spring Boot 配置, LangChain4j 配置)  
│   │   │   │       ├── entity/ (JPA实体，如ChatMessage)  
│   │   │   │       │   └── ChatMessage.java  
│   │   │   │       ├── repository/ (JPA仓库，如ChatMessageRepository)  
│   │   │   │       │   └── ChatMessageRepository.java  
│   │   │   │       ├── service/ (核心服务实现)  
│   │   │   │       │   ├── ConversationServiceImpl.java (实现 chatbi-api 中的接口)  
│   │   │   │       │   ├── NluAgent.java (或 NluService)  
│   │   │   │       │   ├── QueryPlannerAgent.java (或 QueryGenerationService)  
│   │   │   │       │   ├── DataRetrievalAgent.java  
│   │   │   │       │   ├── ConversationManager.java  
│   │   │   │       │   ├── ConversationHistoryService.java (接口)  
│   │   │   │       │   └── impl/ (包)  
│   │   │   │       │       └── ConversationHistoryServiceImpl.java (实现)  
│   │   │   │       ├── chain/ (LangChain4j Chains 定义)  
│   │   │   │       ├── tool/ (LangChain4j Tools 定义, 调用其他服务)  
│   │   │   │       ├── memory/ (LangChain4j Memory 实现)  
│   │   │   │       ├── prompt/ (Prompt 模板管理)  
│   │   │   │       └── ChatBiAgentApplication.java (Spring Boot 启动类)  
│   │   │   └── resources/  
│   │   │       ├── application.yml (或 application.properties)  
│   │   │       └── prompts/ (存放Prompt模板文件)  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
├── chatbi-knowledge-service/ (知识库服务模块: 封装Milvus访问)  
│   ├── src/  
│   │   ├── main/  
│   │   │   ├── java/  
│   │   │   │   └── com/qding/chatbi/knowledge/  
│   │   │   │       ├── config/ (Milvus 连接配置)  
│   │   │   │       ├── service/  
│   │   │   │       │   ├── VectorSearchService.java  
│   │   │   │       │   └── TerminologyService.java  
│   │   │   │       ├── repository/ (如果需要更复杂的Milvus操作封装)  
│   │   │   │       └── KnowledgeServiceApplication.java (可独立部署或作为库)  
│   │   │   └── resources/  
│   │   │       └── application.yml  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
├── chatbi-metadata-service/ (元数据与权限服务模块: 封装对MySQL元数据表的访问)  
│   ├── src/  
│   │   ├── main/  
│   │   │   ├── java/  
│   │   │   │   └── com/qding/chatbi/metadata/  
│   │   │   │       ├── config/ (数据库配置, JPA/MyBatis配置)  
│   │   │   │       ├── entity/ (JPA Entities 或 MyBatis Mappers 对应的POJO)  
│   │   │   │       │   ├── QueryableDatasetEntity.java  
│   │   │   │       │   ├── DatasetColumnEntity.java  
│   │   │   │       │   ├── RoleEntity.java  
│   │   │   │       │   └── ...  
│   │   │   │       ├── repository/ (Spring Data JPA Repositories 或 MyBatis Mappers)  
│   │   │   │       ├── service/  
│   │   │   │       │   ├── DatasetMetadataService.java  
│   │   │   │       │   ├── PermissionService.java  
│   │   │   │       │   └── RoleService.java  
│   │   │   │       └── MetadataServiceApplication.java (可独立部署或作为库)  
│   │   │   └── resources/  
│   │   │       └── application.yml  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
├── chatbi-data-access-service/ (目标业务数据库访问服务模块)  
│   ├── src/  
│   │   ├── main/  
│   │   │   ├── java/  
│   │   │   │   └── com/qding/chatbi/dataaccess/  
│   │   │   │       ├── config/ (动态数据源配置)  
│   │   │   │       ├── service/  
│   │   │   │       │   └── QueryExecutionService.java (根据数据源类型执行SQL)  
│   │   │   │       └── DataAccessServiceApplication.java (可独立部署或作为库)  
│   │   │   └── resources/  
│   │   │       └── application.yml  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
├── chatbi-admin-backend/ (后台管理系统后端服务)  
│   ├── src/  
│   │   ├── main/  
│   │   │   ├── java/  
│   │   │   │   └── com/qding/chatbi/admin/  
│   │   │   │       ├── config/ (安全配置, Spring Boot 配置)  
│   │   │   │       ├── controller/ (RESTful API 供管理前端调用)  
│   │   │   │       │   ├── DatasetAdminController.java  
│   │   │   │       │   ├── RoleAdminController.java  
│   │   │   │       │   ├── PermissionAdminController.java  
│   │   │   │       │   ├── TerminologyAdminController.java  
│   │   │   │       │   └── QueryExampleAdminController.java  
│   │   │   │       ├── service/ (调用 chatbi-metadata-service, chatbi-knowledge-service 等)  
│   │   │   │       └── AdminBackendApplication.java (Spring Boot 启动类)  
│   │   │   └── resources/  
│   │   │       └── application.yml  
│   │   └── test/  
│   │       └── java/  
│   └── pom.xml (或 build.gradle)  
│  
└── chatbi-admin-frontend/ (后台管理系统前端 Vue.js 项目)  
    ├── public/  
    │   ├── index.html  
    │   └── ...  
    ├── src/  
    │   ├── assets/  
    │   ├── components/ (Vue 组件)  
    │   │   ├── dataset/  
    │   │   ├── role/  
    │   │   └── ...  
    │   ├── views/ (Vue 页面/路由组件)  
    │   │   ├── DatasetManagement.vue  
    │   │   ├── RoleManagement.vue  
    │   │   └── ...  
    │   ├── router/  
    │   │   └── index.js  
    │   ├── store/ (Vuex/Pinia 状态管理)  
    │   │   └── index.js  
    │   ├── services/ (API 调用封装)  
    │   ├── App.vue  
    │   └── main.js  
    ├── package.json  
    ├── vue.config.js (或 vite.config.js)  
    └── ... (其他前端配置文件)

**结构说明:**

* **chatbi-service-parent**: Maven/Gradle 的父项目，用于统一管理依赖版本和模块。  
* **chatbi-common**: 存放所有或多个模块共享的类，如 DTOs（数据传输对象）、枚举、自定义异常、工具类和常量。这有助于避免代码重复和版本冲突。  
* **chatbi-api**: 定义核心的对外 API 接口，例如供外部 IM 系统调用的对话服务接口。可以只包含接口定义，具体实现在 chatbi-agent-core 中。  
* **chatbi-agent-core**: 这是 AI Agent 的核心业务逻辑实现。  
  * 包含 Spring Boot 应用启动类。  
  * config: Spring Boot 和 LangChain4j 的相关配置。  
  * entity, repository, service: JPA 实体、仓库和核心服务实现，包括对话历史的持久化。  
  * chain, tool, memory, prompt: LangChain4j 的核心概念，用于构建 LLM 应用流程。  
  * resources/prompts: 存放 Prompt 模板文件，便于管理和版本控制。  
* **chatbi-knowledge-service**: 封装对向量数据库 Milvus 的访问逻辑。提供查询相似示例、术语解释等服务。它可以是一个独立的微服务，也可以是一个被 chatbi-agent-core 调用的库。  
* **chatbi-metadata-service**: 封装对存储在 MySQL 中的元数据表（如 QueryableDatasets, DatasetColumns, Roles, DatasetAccessPermissions 等）的 CRUD 操作和业务逻辑。同样，可以是独立服务或库。  
* **chatbi-data-access-service**: 负责实际执行由 AI Agent 生成的 SQL 查询到目标业务数据库。处理不同数据库类型的连接和方言问题。  
* **chatbi-admin-backend**: 后台管理系统的后端服务，提供 RESTful API 给前端。它会调用 chatbi-metadata-service 和 chatbi-knowledge-service 来管理相关数据。  
* **chatbi-admin-frontend**: 基于 Vue.js 的后台管理界面前端项目，用户通过此界面维护元数据、权限、示例等。

**考虑点:**

* **服务拆分与部署**: chatbi-knowledge-service, chatbi-metadata-service, chatbi-data-access-service 可以根据实际需求和负载情况，选择作为独立的微服务部署，或者作为库被 chatbi-agent-core 或 chatbi-admin-backend直接依赖和调用。初期为了简化，可以先作为库集成。  
* **通信方式**: 如果拆分成独立微服务，模块间的通信方式（如 RESTful API, gRPC, 消息队列）需要进一步设计。  
* **LangChain4j 的具体使用**: 在 chatbi-agent-core 中，如何有效地组织 LangChain4j 的 Chains, Tools, Models, Prompts 等将是实现智能对话的关键。

## **5\. 后台管理功能梳理**

为了支持系统的配置、维护和监控，后台管理系统需要提供以下核心功能：

1. **数据库源管理 (DatabaseSourceConfigs)**:  
   * 创建、查看、编辑、删除数据库连接配置。  
   * 提供连接测试功能，确保配置的有效性。  
   * 安全存储和管理连接凭证。  
2. **可查询数据集管理 (QueryableDatasets)**:  
   * 创建、查看、编辑、删除“可查询数据集”的定义，包括：  
     * 业务名称、描述、同义词。  
     * 关联的数据库源 (database\_source\_id)。  
     * 基础对象名称 (base\_object\_name) 和类型 (base\_object\_type)。  
     * 技术定义（当类型为 QUERY 时，输入 SQL）。  
     * 数据更新频率、数据责任人等辅助信息。  
   * 管理数据集的状态（激活、停用、废弃）。  
3. **数据集字段管理 (DatasetColumns)**:  
   * 针对每个“可查询数据集”，管理其包含的字段：  
     * 创建、查看、编辑、删除字段定义。  
     * 字段的业务名称、描述。  
     * 技术名称或表达式 (technical\_name\_or\_expression)。  
     * 语义类型 (semantic\_type)：维度、指标、时间。  
     * 数据类型 (data\_type)。  
     * 允许的聚合操作 (allowed\_aggregations，主要针对指标)。  
     * 同义词、是否可筛选/分组、是否默认指标。  
     * 可选值查询语句 (possible\_values\_query)、格式化提示 (formatting\_hint)。  
   * （可选）提供从数据库表/视图自动导入字段元数据的辅助功能。  
4. **角色管理 (Roles)**:  
   * 创建、查看、编辑、删除用户角色。  
   * 定义角色的名称和描述。  
5. **权限分配管理 (DatasetAccessPermissions)**:  
   * 为已定义的角色分配或撤销对特定“可查询数据集”的访问权限。  
   * 提供清晰的界面（如权限矩阵）来展示和管理角色与数据集之间的权限关系。  
6. **业务述词对照表管理 (BusinessTerminology)**:  
   * 创建、查看、编辑、删除企业“黑话”、业务术语及其标准映射。  
   * 指定术语映射到的标准参考类型（数据集、字段等）和具体ID。  
   * 管理术语的审核状态。  
7. **业务查询示例管理 (QueryExamples)**:  
   * 创建、查看、编辑、删除“用户问题-查询意图/SQL”的示例对。  
   * 管理示例的详细内容，包括用户问题、目标查询表示、关联的数据集和字段。  
   * 对示例进行分类（如难度级别）和状态管理（审核、验证）。  
   * （可选）支持批量导入、导出查询示例。  
8. **用户管理与角色分配 (如果系统自行管理用户)**:  
   * 创建、查看、编辑、禁用用户账户。  
   * 为用户分配一个或多个角色。  
9. **聊天记录查看 (可选，用于审计与排查)**:  
   * 提供界面根据条件（如 session\_id, user\_id, 时间范围）查询聊天消息历史。  
   * 展示消息的详细内容，包括用户输入、AI回复、时间戳等。  
   * **注意**: 此功能需严格控制访问权限。  
10. **系统监控与日志 (可选，但推荐)**:  
    * 查看 AI Agent 服务的运行状态和关键指标（如请求量、响应时间、错误率）。  
    * 查询和分析用户与 AI 的交互日志、生成的 SQL 查询日志。  
    * 监控与外部服务（LLM、数据库、向量库）的连接状态。

这些管理功能将通过 chatbi-admin-backend 提供的 API 和 chatbi-admin-frontend 构建的 Web 界面来实现。

## **6\. 下一步讨论**

我们已经定义了产品的数据模型、核心架构、代码工程结构、后台管理功能以及聊天消息存储方案。接下来，我们可以深入探讨以下具体实施层面的问题：

1. **AI Agent 核心工作流程**:  
   * 用户问题如何依次经过 NLU Agent、Query Planner Agent、Data Retrieval Agent？  
   * LangChain4j 中的 Chains, Tools, Memory 具体如何组织和应用？  
   * 如何处理澄清式对话和多轮对话？  
   * **聊天历史如何有效地整合进 Memory 和 Prompt 中？**  
2. **核心 API 接口设计**:  
   * ConversationApi 的详细请求和响应结构。  
   * 后台管理模块中各个 Controller 的 API 设计。  
3. **Prompt Engineering**:  
   * 为 NLU、SQL 生成等环节设计高质量的 Prompt 模板。  
   * 如何结合元数据（数据集、字段描述、同义词）和查询示例来构建有效的 Prompt？  
4. **向量数据库 (Milvus) 的应用**:  
   * user\_question 和 business\_term 如何向量化？  
   * 如何设计高效的相似性搜索策略？  
5. **数据同步与缓存策略**:  
   * 元数据（特别是 QueryableDatasets, DatasetColumns, DatasetAccessPermissions）变更后，如何通知或使 AI Agent 及时感知？  
   * AI Agent 核心模块的缓存设计。  
6. **安全性详细设计**:  
   * API 认证授权机制（如 JWT）。  
   * DatabaseSourceConfigs 中连接凭证的安全存储与使用方案。  
   * 防止 Prompt 注入和不安全的 SQL 生成。  
   * **聊天记录的访问权限控制。**  
7. **部署架构**:  
   * 各个模块（特别是可独立部署的服务）的部署方案（Docker, Kubernetes等）。  
   * 环境配置管理（开发、测试、生产）。  
8. **聊天记录管理策略**:  
   * 数据保留时长、归档策略。  
   * 如何处理大规模聊天数据的存储和查询性能。

您觉得我们接下来应该聚焦于哪个方面？