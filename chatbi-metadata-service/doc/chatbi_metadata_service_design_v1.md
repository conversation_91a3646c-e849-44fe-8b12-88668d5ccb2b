# **Chat BI \- Metadata Service详细设计 (chatbi-metadata-service)**

### **1\. 定位与用途**

chatbi-metadata-service 是一个核心的后台服务模块，它的主要职责是**管理和提供所有与数据相关的元数据和权限信息**。它是AI Agent能够理解数据、规划查询和校验权限的基础。

在我们的架构中，其他服务（如chatbi-agent-core）会通过调用本模块提供的服务接口来获取所需信息。

**主要职责包括**:

* 提供对 QueryableDatasets（可查询数据集）、DatasetColumns（数据集字段）和 DatabaseSourceConfigs（数据源配置）表的CRUD操作。  
* 提供对 Roles（角色）和 DatasetAccessPermissions（数据集访问权限）表的CRUD操作。  
* 封装元数据查询逻辑，例如“根据用户角色获取其所有可访问的数据集列表”。  
* (可选) 实现缓存机制，以提高元数据查询的性能。

### **2\. 模块依赖与包结构**

* **依赖**: chatbi-common (为了使用共享的DTO和枚举), spring-boot-starter-data-jpa (用于数据库操作)。  
* **包结构**:  
  com.qding.chatbi.metadata  
  ├── entity/         // 映射所有元数据表的JPA实体  
  ├── repository/     // Spring Data JPA仓库接口  
  ├── service/        // 服务接口  
  │   └── impl/       // 服务实现类  
  └── (MetadataServiceApplication.java) // 仅用于独立测试，在最终应用中不作为启动入口

* **部署方式**: 本模块将被编译为JAR包，作为库被 chatbi-application 模块直接依赖和调用。

### **3\. entity/ 和 repository/ 包**

* **entity/**: 这个包下会包含所有元数据表的JPA实体类，例如：  
  * DatabaseSourceConfig.java  
  * QueryableDataset.java  
  * DatasetColumn.java  
  * Role.java  
  * DatasetAccessPermission.java  
  * 这些实体类的字段应与 chatbi\_database\_schema\_v1 文档中定义的表结构一一对应。  
* **repository/**: 这个包下会包含每个实体对应的Spring Data JPA仓库接口，例如：  
  * QueryableDatasetRepository.java  
  * RoleRepository.java  
  * DatasetAccessPermissionRepository.java  
  * 这些仓库接口会继承 JpaRepository，并可以定义自定义的查询方法。

### **4\. service/ 包 \- 核心服务设计**

我们将定义两个主要的服务接口来划分职责：DatasetMetadataService 和 PermissionService。

#### **4.1. DatasetMetadataService.java (接口)**

* **用途**: 负责管理和查询所有与数据集、字段和数据源相关的元数据。  
* **核心方法**:  
  package com.qding.chatbi.metadata.service;

  import com.qding.chatbi.common.dto.DatasetInfoDTO;  
  import java.util.List;

  public interface DatasetMetadataService {  
      DatasetInfoDTO getDatasetDetails(Long datasetId);  
      DatasetInfoDTO getDatasetDetailsByName(String datasetName);  
      List\<DatasetInfoDTO\> getAllDatasetSummaries();  
      // ... (未来可能还有创建、更新、删除数据集的后台管理方法)  
  }

#### **4.2. PermissionService.java (接口)**

* **用途**: 负责所有与角色和权限相关的业务逻辑。  
* **核心方法**:  
  package com.qding.chatbi.metadata.service;

  import java.util.List;

  public interface PermissionService {  
      boolean hasDatasetAccess(List\<String\> roleNames, Long datasetId);  
      List\<Long\> getPermittedDatasetIds(List\<String\> roleNames);  
      // ... (未来可能还有创建角色、分配权限等后台管理方法)  
  }

#### **4.3. 服务实现类 (impl/)**

* **DatasetMetadataServiceImpl.java**:  
  * 会注入 QueryableDatasetRepository, DatasetColumnRepository 等。  
  * 实现 getDatasetDetails 等方法时，会从数据库中查询对应的实体，然后**组装**成 DatasetInfoDTO 和 ColumnInfoDTO 返回。  
* **PermissionServiceImpl.java**:  
  * 会注入 RoleRepository 和 DatasetAccessPermissionRepository。  
  * 实现 hasDatasetAccess 等方法，处理具体的权限校验逻辑。

### **5\. 关于后台管理API (Controller)**

根据我们的“单体应用，逻辑分层”架构，所有用于后台管理的API端点（即Controller）都应该放在 **chatbi-admin-backend** 模块中。

* **chatbi-admin-backend** 模块会依赖 chatbi-metadata-service 模块。  
* 在 chatbi-admin-backend 中创建的Controller（例如 MetadataAdminController.java, PermissionAdminController.java）会注入本模块定义的 DatasetMetadataService 和 PermissionService 接口，并调用它们的方法来完成对元数据的增删改查。  
* 因此，**本模块 (chatbi-metadata-service) 不需要包含 controller 包**。

### **6\. 缓存策略**

元数据通常不频繁变动，但会被AI Agent频繁查询。因此，非常适合引入缓存来提升性能。

* **实现方式**: 使用Spring Cache (@EnableCaching, @Cacheable, @CacheEvict)。  
* **缓存位置**: 在 DatasetMetadataServiceImpl 和 PermissionServiceImpl 的查询方法上添加 @Cacheable 注解。  
  // 在 DatasetMetadataServiceImpl.java 中  
  @Override  
  @Cacheable(value \= "metadataCache", key \= "'datasetDetails\_id\_'.concat(\#datasetId)")  
  public DatasetInfoDTO getDatasetDetails(Long datasetId) {  
      // ... 数据库查询逻辑 ...  
  }

* **缓存失效**: 当后台管理功能更新了元数据时（例如，通过 chatbi-admin-backend 的接口），需要调用包含 @CacheEvict 注解的方法来清除相应的缓存。  
* **缓存存储**: 可以在 chatbi-application 模块的 AppConfig 中配置使用本地缓存（如Caffeine）或分布式缓存（如Redis）。