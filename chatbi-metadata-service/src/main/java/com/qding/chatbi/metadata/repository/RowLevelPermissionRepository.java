package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.RowLevelPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 行级权限数据访问层
 */
@Repository
public interface RowLevelPermissionRepository extends JpaRepository<RowLevelPermission, Long> {

    /**
     * 根据角色名称和数据集ID查找行级权限
     */
    @Query("SELECT rlp FROM RowLevelPermission rlp " +
           "JOIN rlp.role r " +
           "WHERE r.roleName = :roleName " +
           "AND rlp.dataset.id = :datasetId " +
           "AND rlp.enabled = true " +
           "ORDER BY rlp.priority ASC")
    List<RowLevelPermission> findByRoleNameAndDatasetId(@Param("roleName") String roleName, 
                                                        @Param("datasetId") Long datasetId);

    /**
     * 根据多个角色名称和数据集ID查找行级权限
     */
    @Query("SELECT rlp FROM RowLevelPermission rlp " +
           "JOIN rlp.role r " +
           "WHERE r.roleName IN :roleNames " +
           "AND rlp.dataset.id = :datasetId " +
           "AND rlp.enabled = true " +
           "ORDER BY rlp.priority ASC")
    List<RowLevelPermission> findByRoleNamesAndDatasetId(@Param("roleNames") List<String> roleNames, 
                                                         @Param("datasetId") Long datasetId);

    /**
     * 根据数据集ID查找所有行级权限
     */
    @Query("SELECT rlp FROM RowLevelPermission rlp " +
           "WHERE rlp.dataset.id = :datasetId " +
           "AND rlp.enabled = true " +
           "ORDER BY rlp.priority ASC")
    List<RowLevelPermission> findByDatasetId(@Param("datasetId") Long datasetId);

    /**
     * 根据角色ID查找行级权限
     */
    @Query("SELECT rlp FROM RowLevelPermission rlp " +
           "WHERE rlp.role.id = :roleId " +
           "AND rlp.enabled = true " +
           "ORDER BY rlp.priority ASC")
    List<RowLevelPermission> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 检查特定角色对数据集是否有行级权限
     */
    @Query("SELECT COUNT(rlp) > 0 FROM RowLevelPermission rlp " +
           "JOIN rlp.role r " +
           "WHERE r.roleName = :roleName " +
           "AND rlp.dataset.id = :datasetId " +
           "AND rlp.enabled = true")
    boolean hasRowLevelPermission(@Param("roleName") String roleName, 
                                 @Param("datasetId") Long datasetId);
}
