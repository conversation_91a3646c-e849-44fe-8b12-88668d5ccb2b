package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

/**
 * 行级权限实体
 * 定义角色对特定数据集的行级访问控制规则
 */
@Entity
@Table(name = "row_level_permissions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"role_id", "dataset_id", "permission_name"})
})
@Data
public class RowLevelPermission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的角色
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    /**
     * 关联的数据集
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id", nullable = false)
    private QueryableDataset dataset;

    /**
     * 权限名称，用于标识不同的权限规则
     * 例如: "region_filter", "department_filter", "project_filter"
     */
    @Column(name = "permission_name", nullable = false, length = 100)
    private String permissionName;

    /**
     * 权限描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 过滤条件类型
     * COLUMN_VALUE: 基于列值过滤
     * SQL_CONDITION: 自定义SQL条件
     * USER_ATTRIBUTE: 基于用户属性过滤
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "filter_type", nullable = false)
    private FilterType filterType;

    /**
     * 过滤字段名称
     * 当filterType为COLUMN_VALUE或USER_ATTRIBUTE时使用
     */
    @Column(name = "filter_column", length = 100)
    private String filterColumn;

    /**
     * 过滤操作符
     * EQUALS, IN, NOT_IN, LIKE, GREATER_THAN, LESS_THAN, BETWEEN等
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "filter_operator")
    private FilterOperator filterOperator;

    /**
     * 过滤值
     * 支持多种格式:
     * - 单个值: "华北区"
     * - 多个值: ["华北区", "华东区"]
     * - 用户属性引用: "${user.department}"
     * - SQL表达式: "project_id IN (SELECT project_id FROM user_projects WHERE user_id = '${user.id}')"
     */
    @Lob
    @Column(name = "filter_values", columnDefinition = "TEXT")
    private String filterValues;

    /**
     * 自定义SQL条件
     * 当filterType为SQL_CONDITION时使用
     * 例如: "region = '${user.region}' AND department = '${user.department}'"
     */
    @Lob
    @Column(name = "sql_condition", columnDefinition = "TEXT")
    private String sqlCondition;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 优先级，数字越小优先级越高
     */
    @Column(name = "priority")
    private Integer priority = 0;

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    /**
     * 过滤条件类型枚举
     */
    public enum FilterType {
        COLUMN_VALUE,    // 基于列值过滤
        SQL_CONDITION,   // 自定义SQL条件
        USER_ATTRIBUTE   // 基于用户属性过滤
    }

    /**
     * 过滤操作符枚举
     */
    public enum FilterOperator {
        EQUALS,          // =
        NOT_EQUALS,      // !=
        IN,              // IN
        NOT_IN,          // NOT IN
        LIKE,            // LIKE
        NOT_LIKE,        // NOT LIKE
        GREATER_THAN,    // >
        GREATER_EQUAL,   // >=
        LESS_THAN,       // <
        LESS_EQUAL,      // <=
        BETWEEN,         // BETWEEN
        IS_NULL,         // IS NULL
        IS_NOT_NULL      // IS NOT NULL
    }
}
