package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.Date;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "query_examples")
@Data
public class QueryExample {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_question", nullable = false, columnDefinition = "TEXT")
    private String userQuestion;

    @Column(name = "target_query_representation", nullable = false, columnDefinition = "TEXT")
    private String targetQueryRepresentation;

    @Column(name = "target_dataset_id")
    private Long targetDatasetId;

    @Column(name = "involved_column_ids", columnDefinition = "JSON")
    private String involvedColumnIds;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Column(name = "difficulty_level", length = 50)
    private String difficultyLevel;

    @Column(name = "status", nullable = false, length = 50)
    private String status = "verified";

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;
}