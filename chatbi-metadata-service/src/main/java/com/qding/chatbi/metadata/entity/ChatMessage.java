package com.qding.chatbi.metadata.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.util.Date;

/**
 * Entity class representing a chat message.
 */
@Entity
@Table(name = "chat_messages", indexes = {
        @Index(name = "idx_session_id", columnList = "session_id"),
        @Index(name = "idx_user_id", columnList = "user_id"),
        @Index(name = "idx_message_timestamp", columnList = "message_timestamp")
})
@Getter
@Setter
@ToString
@NoArgsConstructor
@EqualsAndHashCode(of = "id") // Usually, equality is determined by ID.
public class ChatMessage {

    /**
     * Unique identifier for the message.
     * Maps to `message_id` BIGINT, PRIMARY KEY, AUTO_INCREMENT.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * ID of the session this message belongs to.
     */
    @Column(nullable = false, length = 255)
    private String sessionId;

    /**
     * ID of the user who initiated or participated in the conversation.
     */
    @Column(length = 255)
    private String userId;

    /**
     * Type of the sender, e.g., "USER", "AI_AGENT".
     */
    @Column(nullable = false, length = 50)
    private String senderType;

    /**
     * The raw text content of the message.
     */
    @Lob
    @Column(nullable = false, columnDefinition = "TEXT")
    private String messageText;

    /**
     * Timestamp when the message was created or received.
     */
    @Column(nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date messageTimestamp;

    /**
     * (For AI messages) The type of the response, e.g., "DATA_RESULT", "CLARIFICATION_NEEDED".
     */
    @Column(length = 50)
    private String responseType;

    /**
     * (For AI messages) Structured response data, such as query results or clarification options,
     * serialized as a JSON string.
     */
    
    @Column(columnDefinition = "JSON")
    private String structuredResponseData;

    /**
     * (For AI error messages) The error code.
     */
    @Column(length = 50)
    private String errorCode;

    /**
     * (For AI error messages) Detailed error message.
     */
    @Lob
    @Column(columnDefinition = "TEXT")
    private String errorMessageDetail;

    /**
     * (For AI messages) The processing duration in milliseconds.
     */
    @Column(name = "processing_duration_ms")
    private Integer processingDurationMs;

    /**
     * (For AI messages) The name of the LLM model used.
     */
    @Column(length = 100)
    private String llmModelUsed;

    /**
     * Timestamp of record creation, automatically managed by Hibernate.
     */
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false, updatable = false)
    private Date createdAt;

    /**
     * Timestamp of the last record update, automatically managed by Hibernate.
     */
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    private Date updatedAt;
}
