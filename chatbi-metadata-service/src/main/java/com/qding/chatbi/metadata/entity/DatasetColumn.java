package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Entity
@Table(name = "dataset_columns", uniqueConstraints = {
        @UniqueConstraint(columnNames = { "dataset_id", "column_name" })
})
@Getter
@Setter
public class DatasetColumn {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id", nullable = false)
    private QueryableDataset dataset;

    @Column(name = "column_name", nullable = false)
    private String columnName;

    @Lob
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "technical_name_or_expression", nullable = false, length = 1024)
    private String technicalNameOrExpression;

    @Column(name = "semantic_type", nullable = false, length = 50)
    private String semanticType;

    @Column(name = "data_type", nullable = false, length = 50)
    private String dataType;

    @Column(name = "allowed_aggregations", columnDefinition = "JSON")
    private String allowedAggregations;

    @Column(name = "synonyms", columnDefinition = "JSON")
    private String synonyms;

    @Column(name = "is_filterable", nullable = false)
    private boolean filterable = true;

    @Column(name = "is_groupable", nullable = false)
    private boolean groupable = true;

    @Column(name = "is_default_metric", nullable = false)
    private boolean defaultMetric = false;

    @Column(name = "is_computed", nullable = false)
    private boolean computed = false;

    @Lob
    @Column(name = "possible_values_query", columnDefinition = "TEXT")
    private String possibleValuesQuery;

    @Column(name = "formatting_hint", columnDefinition = "JSON")
    private String formattingHint;

    @Column(name = "status", nullable = false, length = 50)
    private String status = "active";

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;
}
