package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.Date;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "business_terminology")
@Data
public class BusinessTerminology {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "business_term", nullable = false, unique = true)
    private String businessTerm;

    @Column(name = "standard_reference_type", nullable = false)
    private String standardReferenceType;

    @Column(name = "standard_reference_id")
    private String standardReferenceId; // Storing as String to be flexible

    @Column(name = "standard_reference_name")
    private String standardReferenceName;

    @Lob
    @Column(name = "context_description", nullable = false)
    private String contextDescription;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Date updatedAt;
}