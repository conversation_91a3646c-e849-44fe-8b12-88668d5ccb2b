package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.Date;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "queryable_datasets")
@Data
public class Dataset {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "dataset_name", nullable = false)
    private String datasetName;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "database_source_id", nullable = false)
    private Long databaseSourceId;

    @Column(name = "base_object_name", nullable = false)
    private String baseObjectName;

    @Column(name = "base_object_type", nullable = false)
    private String baseObjectType;

    @Column(name = "technical_definition", columnDefinition = "TEXT")
    private String technicalDefinition;

    @Column(name = "synonyms", columnDefinition = "JSON")
    private String synonyms;

    @Column(name = "refresh_frequency")
    private String refreshFrequency;

    @Column(name = "data_owner")
    private String dataOwner;

    @Column(name = "status", nullable = false)
    private String status = "active";

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 兼容旧字段名，可能在某些地方还在使用
    @Column(name = "object_type")
    private String objectType;

    @Column(name = "source_sql", columnDefinition = "TEXT")
    private String sourceSql;

    @Column(name = "source_table_name")
    private String sourceTableName;
} 