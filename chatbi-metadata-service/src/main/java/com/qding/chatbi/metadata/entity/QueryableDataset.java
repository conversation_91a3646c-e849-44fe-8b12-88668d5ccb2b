package com.qding.chatbi.metadata.entity;

import com.qding.chatbi.common.enums.BaseObjectType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "queryable_datasets")
@Getter
@Setter
public class QueryableDataset {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "dataset_name", nullable = false, unique = true)
    private String datasetName;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "database_source_id", nullable = false)
    private Long databaseSourceId;

    @Column(name = "base_object_name", nullable = false)
    private String baseObjectName;

    @Column(name = "base_object_type", nullable = false, length = 50)
    private String baseObjectType;

    @Lob
    @Column(name = "technical_definition", columnDefinition = "TEXT")
    private String technicalDefinition;

    @Column(name = "synonyms", columnDefinition = "JSON")
    private String synonyms;

    @Column(name = "refresh_frequency", length = 100)
    private String refreshFrequency;

    @Column(name = "data_owner", length = 100)
    private String dataOwner;

    @Column(name = "status", nullable = false, length = 50)
    private String status = "active";

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    @OneToMany(mappedBy = "dataset", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<DatasetColumn> columns = new ArrayList<>();
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "database_source_id", insertable = false, updatable = false)
    private DatabaseSourceConfig dataSource;

    @OneToMany(mappedBy = "dataset", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<DatasetAccessPermission> permissions = new HashSet<>();
}
