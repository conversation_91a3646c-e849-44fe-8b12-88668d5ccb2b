package com.qding.chatbi.metadata.service.impl;

import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.metadata.entity.Role;
import com.qding.chatbi.metadata.repository.DatasetAccessPermissionRepository;
import com.qding.chatbi.metadata.repository.RoleRepository;
import com.qding.chatbi.metadata.service.DatasetMetadataService;
import com.qding.chatbi.metadata.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class PermissionServiceImpl implements PermissionService {

    private final RoleRepository roleRepository;
    private final DatasetAccessPermissionRepository permissionRepository;
    private final DatasetMetadataService datasetMetadataService;

    /**
     * 获取用户有权访问的数据集ID列表
     * 注意：此方法现在只返回status=active的数据集ID
     */
    @Override
    public List<Long> getPermittedDatasetIds(String roleName) {
        if (roleName == null || roleName.trim().isEmpty()) {
            return Collections.emptyList();
        }
        Long roleId = getRoleId(roleName);
        if (roleId == null) {
            return Collections.emptyList();
        }
        return permissionRepository.findDatasetIdsByRoleIds(Collections.singletonList(roleId));
    }

    /**
     * 获取用户有权访问的数据集详细信息列表
     * 注意：此方法现在只返回status=active的数据集和字段
     */
    @Override
    public List<DatasetInfoDTO> getPermittedDatasetInfos(String roleName) {
        List<Long> datasetIds = getPermittedDatasetIds(roleName);
        if (datasetIds.isEmpty()) {
            return Collections.emptyList();
        }
        return datasetIds.stream()
                .map(datasetMetadataService::getDatasetInfoById)
                .collect(Collectors.toList());
    }

    @Override
    public boolean hasDatasetAccess(String roleName, Long datasetId) {
        if (roleName == null || roleName.trim().isEmpty()) {
            return false;
        }
        Long roleId = getRoleId(roleName);
        if (roleId == null) {
            return false;
        }
        return permissionRepository.existsByRoleIdInAndDataset_Id(Collections.singletonList(roleId), datasetId);
    }

    private Long getRoleId(String roleName) {
        Role role = roleRepository.findByRoleName(roleName);
        return role != null ? role.getId() : null;
    }
}
