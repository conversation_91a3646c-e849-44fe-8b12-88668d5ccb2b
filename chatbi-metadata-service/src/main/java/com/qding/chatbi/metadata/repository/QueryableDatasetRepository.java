package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.QueryableDataset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface QueryableDatasetRepository extends JpaRepository<QueryableDataset, Long> {

    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns WHERE qd.id = :id")
    Optional<QueryableDataset> findByIdWithColumns(Long id);

    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns WHERE qd.datasetName = :name")
    Optional<QueryableDataset> findByNameWithColumns(String name);

    /**
     * 查找数据集并同时加载字段和数据源信息
     * 
     * @param id 数据集ID
     * @return 包含字段和数据源信息的数据集
     */
    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns c LEFT JOIN FETCH qd.dataSource WHERE qd.id = :id")
    Optional<QueryableDataset> findByIdWithColumnsAndDataSource(Long id);

    /**
     * 获取所有活跃状态的数据集
     * 
     * @return 活跃状态的数据集列表
     */
    @Query("SELECT qd FROM QueryableDataset qd WHERE qd.status = 'active'")
    List<QueryableDataset> findAllActive();

    /**
     * 根据ID列表查找活跃状态的数据集，并加载字段信息
     * 
     * @param ids 数据集ID列表
     * @return 活跃状态的数据集列表
     */
    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns c WHERE qd.id IN :ids AND qd.status = 'active' AND (c.status = 'active' OR c.status IS NULL)")
    List<QueryableDataset> findActiveByIdsWithColumns(List<Long> ids);

    /**
     * 检查指定名称的数据集是否已存在
     * 
     * @param datasetName 数据集名称
     * @return 如果存在返回true，否则返回false
     */
    boolean existsByDatasetName(String datasetName);

    /**
     * 检查指定名称的数据集是否已存在，但排除指定ID的数据集（用于更新时检查）
     * 
     * @param datasetName 数据集名称
     * @param id          要排除的数据集ID
     * @return 如果存在返回true，否则返回false
     */
    boolean existsByDatasetNameAndIdNot(String datasetName, Long id);

    /**
     * 根据ID查找活跃状态的数据集，并加载活跃状态的字段
     * 
     * @param id 数据集ID
     * @return 活跃状态的数据集（包含活跃字段）
     */
    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns c WHERE qd.id = :id AND qd.status = 'active' AND (c.status = 'active' OR c.status IS NULL)")
    Optional<QueryableDataset> findActiveByIdWithActiveColumns(Long id);

    /**
     * 根据名称查找活跃状态的数据集，并加载活跃状态的字段
     * 
     * @param name 数据集名称
     * @return 活跃状态的数据集（包含活跃字段）
     */
    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns c WHERE qd.datasetName = :name AND qd.status = 'active' AND (c.status = 'active' OR c.status IS NULL)")
    Optional<QueryableDataset> findActiveByNameWithActiveColumns(String name);

    /**
     * 查找活跃状态的数据集并同时加载活跃字段和数据源信息
     * 
     * @param id 数据集ID
     * @return 包含活跃字段和数据源信息的活跃数据集
     */
    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns c LEFT JOIN FETCH qd.dataSource WHERE qd.id = :id AND qd.status = 'active' AND (c.status = 'active' OR c.status IS NULL)")
    Optional<QueryableDataset> findActiveByIdWithActiveColumnsAndDataSource(Long id);

    /**
     * 获取所有活跃状态的数据集
     * 
     * @return 活跃状态的数据集列表
     */
    @Query("SELECT qd FROM QueryableDataset qd WHERE qd.status = 'active'")
    List<QueryableDataset> findAllActiveDatasets();

    /**
     * 根据ID列表查找活跃状态的数据集，并加载活跃状态的字段信息
     * 
     * @param ids 数据集ID列表
     * @return 活跃状态的数据集列表（包含活跃字段）
     */
    @Query("SELECT qd FROM QueryableDataset qd LEFT JOIN FETCH qd.columns c WHERE qd.id IN :ids AND qd.status = 'active' AND (c.status = 'active' OR c.status IS NULL)")
    List<QueryableDataset> findActiveByIdsWithActiveColumns(List<Long> ids);

    /**
     * 根据状态查找数据集
     * 
     * @param status 数据集状态
     * @return 指定状态的数据集列表
     */
    @Query("SELECT qd FROM QueryableDataset qd WHERE qd.status = :status")
    List<QueryableDataset> findByStatus(String status);
}