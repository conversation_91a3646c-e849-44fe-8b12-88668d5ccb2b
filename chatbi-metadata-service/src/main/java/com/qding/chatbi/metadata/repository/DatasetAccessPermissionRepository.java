package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.DatasetAccessPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * Repository for {@link DatasetAccessPermission} entity.
 */
@Repository
public interface DatasetAccessPermissionRepository extends JpaRepository<DatasetAccessPermission, Long> {

    @Query("SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END FROM DatasetAccessPermission p WHERE p.role.id IN :roleIds AND p.dataset.id = :datasetId AND p.dataset.status = 'active'")
    boolean existsByRoleIdInAndDataset_Id(List<Long> roleIds, Long datasetId);

    @Query("SELECT p.dataset.id FROM DatasetAccessPermission p WHERE p.role.id IN :roleIds AND p.dataset.status = 'active'")
    List<Long> findDatasetIdsByRoleIds(List<Long> roleIds);

    List<DatasetAccessPermission> findByRoleId(Long roleId);

    /**
     * 根据角色ID删除所有权限记录。
     * 
     * @param roleId 角色ID
     */
    @Transactional
    void deleteByRoleId(Long roleId);

    /**
     * 根据数据集ID删除所有相关的权限记录。
     * 使用自定义查询以避免命名解析问题。
     */
    @Modifying
    @Query("DELETE FROM DatasetAccessPermission p WHERE p.dataset.id = :datasetId")
    void deleteByDatasetId(Long datasetId);

    List<DatasetAccessPermission> findByDataset_Id(Long datasetId);
}