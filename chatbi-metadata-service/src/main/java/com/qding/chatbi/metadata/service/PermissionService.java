package com.qding.chatbi.metadata.service;

import java.util.List;
import com.qding.chatbi.common.dto.DatasetInfoDTO;

public interface PermissionService {

    /**
     * 检查指定角色是否有权访问特定数据集。
     * 
     * @param roleName  用户的角色名称。
     * @param datasetId 要检查的数据集ID。
     * @return 如果角色有权访问，则返回true，否则返回false。
     */
    boolean hasDatasetAccess(String roleName, Long datasetId);

    /**
     * 根据用户角色，获取所有该用户有权访问的数据集ID列表。
     * 
     * @param roleName 用户的角色名称。
     * @return 有权限的数据集ID列表。
     */
    List<Long> getPermittedDatasetIds(String roleName);

    /**
     * 根据用户角色获取其有权限访问的数据集的详细信息列表
     * 
     * @param roleName 角色名称
     * @return 数据集信息DTO列表
     */
    List<DatasetInfoDTO> getPermittedDatasetInfos(String roleName);
}