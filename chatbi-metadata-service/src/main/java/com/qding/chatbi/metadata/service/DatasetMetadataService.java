package com.qding.chatbi.metadata.service;

import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.metadata.entity.DatabaseSourceConfig;
import java.util.List;

public interface DatasetMetadataService {
    DatasetInfoDTO getDatasetDetails(Long datasetId);

    DatasetInfoDTO getDatasetDetailsByName(String datasetName);

    List<DatasetInfoDTO> getAllDatasetSummaries();

    List<DatasetInfoDTO> getDatasetInfoByUserRole(String userRole);

    DatasetInfoDTO getDatasetInfoById(Long datasetId);

    DatabaseSourceConfig getDatabaseSourceConfigById(Long id);

    /**
     * 获取活跃状态的数据集详情（包含活跃字段）
     * 
     * @param datasetId 数据集ID
     * @return 活跃状态的数据集详情
     */
    DatasetInfoDTO getActiveDatasetDetails(Long datasetId);

    /**
     * 根据名称获取活跃状态的数据集详情（包含活跃字段）
     * 
     * @param datasetName 数据集名称
     * @return 活跃状态的数据集详情
     */
    DatasetInfoDTO getActiveDatasetDetailsByName(String datasetName);

    /**
     * 获取所有活跃状态的数据集摘要
     * 
     * @return 活跃状态的数据集摘要列表
     */
    List<DatasetInfoDTO> getAllActiveDatasetSummaries();

    /**
     * 根据ID列表获取活跃状态的数据集详情（包含活跃字段）
     * 
     * @param datasetIds 数据集ID列表
     * @return 活跃状态的数据集详情列表
     */
    List<DatasetInfoDTO> getActiveDatasetDetailsByIds(List<Long> datasetIds);
}
