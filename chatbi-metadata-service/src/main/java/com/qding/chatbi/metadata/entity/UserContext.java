package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

/**
 * 用户上下文实体
 * 存储用户的业务属性，用于行级权限过滤
 */
@Entity
@Table(name = "user_contexts")
@Data
public class UserContext {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false, unique = true, length = 100)
    private String userId;

    /**
     * 用户姓名
     */
    @Column(name = "user_name", length = 100)
    private String userName;

    /**
     * 部门
     */
    @Column(name = "department", length = 100)
    private String department;

    /**
     * 区域
     */
    @Column(name = "region", length = 100)
    private String region;

    /**
     * 城市
     */
    @Column(name = "city", length = 100)
    private String city;

    /**
     * 职位
     */
    @Column(name = "position", length = 100)
    private String position;

    /**
     * 级别
     */
    @Column(name = "level", length = 50)
    private String level;

    /**
     * 上级用户ID
     */
    @Column(name = "manager_id", length = 100)
    private String managerId;

    /**
     * 组织ID
     */
    @Column(name = "organization_id", length = 100)
    private String organizationId;

    /**
     * 成本中心
     */
    @Column(name = "cost_center", length = 100)
    private String costCenter;

    /**
     * 扩展属性（JSON格式）
     * 用于存储其他自定义属性
     */
    @Lob
    @Column(name = "extended_attributes", columnDefinition = "TEXT")
    private String extendedAttributes;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;
}
