package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {

    /**
     * 根据角色名称进行不区分大小写的模糊查询，并返回分页结果。
     */
    Page<Role> findByRoleNameContainingIgnoreCase(String roleName, Pageable pageable);

    /**
     * 根据角色名称列表查找角色
     */
    List<Role> findByRoleNameIn(List<String> roleNames);

    /**
     * 根据角色名称查找单个角色
     */
    Role findByRoleName(String roleName);
}