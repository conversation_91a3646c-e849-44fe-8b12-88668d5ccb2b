package com.qding.chatbi.metadata.service.impl;

import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.enums.SemanticType;
import com.qding.chatbi.metadata.entity.DatasetColumn;
import com.qding.chatbi.metadata.entity.QueryableDataset;
import com.qding.chatbi.metadata.entity.DatabaseSourceConfig;
import com.qding.chatbi.metadata.repository.QueryableDatasetRepository;
import com.qding.chatbi.metadata.repository.DatabaseSourceConfigRepository;
import com.qding.chatbi.metadata.service.DatasetMetadataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Collections;

@Service
@Transactional(readOnly = true)
public class DatasetMetadataServiceImpl implements DatasetMetadataService {

    private final QueryableDatasetRepository datasetRepository;
    private final DatabaseSourceConfigRepository databaseSourceConfigRepository;

    @Autowired
    public DatasetMetadataServiceImpl(QueryableDatasetRepository datasetRepository,
            DatabaseSourceConfigRepository databaseSourceConfigRepository) {
        this.datasetRepository = datasetRepository;
        this.databaseSourceConfigRepository = databaseSourceConfigRepository;
    }

    @Override
    @Cacheable(value = "metadataCache", key = "'datasetDetails_id_'.concat(#datasetId)")
    public DatasetInfoDTO getDatasetDetails(Long datasetId) {
        return datasetRepository.findByIdWithColumns(datasetId)
                .map(this::convertToFullDTO)
                .orElse(null);
    }

    @Override
    @Cacheable(value = "metadataCache", key = "'datasetDetails_name_'.concat(#datasetName)")
    public DatasetInfoDTO getDatasetDetailsByName(String datasetName) {
        return datasetRepository.findByNameWithColumns(datasetName)
                .map(this::convertToFullDTO)
                .orElse(null);
    }

    @Override
    @Cacheable("metadataCache")
    public List<DatasetInfoDTO> getAllDatasetSummaries() {
        return getAllActiveDatasetSummaries();
    }

    /**
     * 获取活跃状态的数据集详情（包含活跃字段）
     * 
     * @param datasetId 数据集ID
     * @return 活跃状态的数据集详情
     */
    @Cacheable(value = "metadataCache", key = "'activeDatasetDetails_id_'.concat(#datasetId)")
    public DatasetInfoDTO getActiveDatasetDetails(Long datasetId) {
        return datasetRepository.findActiveByIdWithActiveColumns(datasetId)
                .map(this::convertToFullDTO)
                .orElse(null);
    }

    /**
     * 根据名称获取活跃状态的数据集详情（包含活跃字段）
     * 
     * @param datasetName 数据集名称
     * @return 活跃状态的数据集详情
     */
    @Cacheable(value = "metadataCache", key = "'activeDatasetDetails_name_'.concat(#datasetName)")
    public DatasetInfoDTO getActiveDatasetDetailsByName(String datasetName) {
        return datasetRepository.findActiveByNameWithActiveColumns(datasetName)
                .map(this::convertToFullDTO)
                .orElse(null);
    }

    /**
     * 获取所有活跃状态的数据集摘要
     * 
     * @return 活跃状态的数据集摘要列表
     */
    @Cacheable("metadataCache")
    public List<DatasetInfoDTO> getAllActiveDatasetSummaries() {
        return datasetRepository.findAllActiveDatasets().stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID列表获取活跃状态的数据集详情（包含活跃字段）
     * 
     * @param datasetIds 数据集ID列表
     * @return 活跃状态的数据集详情列表
     */
    public List<DatasetInfoDTO> getActiveDatasetDetailsByIds(List<Long> datasetIds) {
        if (datasetIds == null || datasetIds.isEmpty()) {
            return Collections.emptyList();
        }

        return datasetRepository.findActiveByIdsWithActiveColumns(datasetIds).stream()
                .map(this::convertToFullDTO)
                .collect(Collectors.toList());
    }

    private DatasetInfoDTO convertToSummaryDTO(QueryableDataset entity) {
        DatasetInfoDTO dto = new DatasetInfoDTO();
        dto.setDatasetId(entity.getId());
        dto.setDatasetName(entity.getDatasetName());
        dto.setDescription(entity.getDescription());
        dto.setDatabaseSourceId(entity.getDatabaseSourceId());
        dto.setTableName(entity.getBaseObjectName()); // 使用base_object_name作为表名

        // 🎯 新增：从数据库源配置获取数据库类型
        if (entity.getDataSource() != null) {
            dto.setDatabaseType(entity.getDataSource().getDatabaseType().toString());
        } else if (entity.getDatabaseSourceId() != null) {
            // 如果懒加载的dataSource为null，则通过repository查询
            DatabaseSourceConfig sourceConfig = databaseSourceConfigRepository.findById(entity.getDatabaseSourceId())
                    .orElse(null);
            if (sourceConfig != null) {
                dto.setDatabaseType(sourceConfig.getDatabaseType().toString());
            }
        }

        return dto;
    }

    private DatasetInfoDTO convertToFullDTO(QueryableDataset entity) {
        DatasetInfoDTO dto = convertToSummaryDTO(entity);
        if (entity.getColumns() != null) {
            dto.setColumns(entity.getColumns().stream()
                    .map(this::convertColumnToDTO)
                    .collect(Collectors.toList()));
        }
        return dto;
    }

    private ColumnInfoDTO convertColumnToDTO(DatasetColumn columnEntity) {
        ColumnInfoDTO dto = new ColumnInfoDTO();
        dto.setColumnId(columnEntity.getId());
        dto.setColumnName(columnEntity.getColumnName());
        dto.setDescription(columnEntity.getDescription());
        dto.setTechnicalNameOrExpression(columnEntity.getTechnicalNameOrExpression());
        dto.setDataType(columnEntity.getDataType());
        dto.setSemanticType(SemanticType.valueOf(columnEntity.getSemanticType().toUpperCase()));
        dto.setIsComputed(columnEntity.isComputed()); // 设置是否为计算字段
        // In a real implementation, you would parse the JSON string for
        // allowedAggregations
        // dto.setAllowedAggregations(parseJsonToList(columnEntity.getAllowedAggregations()));
        return dto;
    }

    @Override
    public List<DatasetInfoDTO> getDatasetInfoByUserRole(String userRole) {
        // 这个方法的实现应该委托给权限服务
        // 在这里我们返回所有活跃数据集，实际权限检查在权限服务中进行
        return getAllActiveDatasetSummaries();
    }

    @Override
    public DatasetInfoDTO getDatasetInfoById(Long datasetId) {
        return getActiveDatasetDetails(datasetId);
    }

    @Override
    public DatabaseSourceConfig getDatabaseSourceConfigById(Long id) {
        return databaseSourceConfigRepository.findById(id).orElse(null);
    }
}
