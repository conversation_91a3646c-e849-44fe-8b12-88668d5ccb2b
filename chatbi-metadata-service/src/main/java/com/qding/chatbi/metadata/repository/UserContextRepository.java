package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.UserContext;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户上下文数据访问层
 */
@Repository
public interface UserContextRepository extends JpaRepository<UserContext, Long> {

    /**
     * 根据用户ID查找用户上下文
     */
    Optional<UserContext> findByUserId(String userId);

    /**
     * 根据用户ID查找启用的用户上下文
     */
    Optional<UserContext> findByUserIdAndEnabledTrue(String userId);

    /**
     * 检查用户是否存在
     */
    boolean existsByUserId(String userId);
}
