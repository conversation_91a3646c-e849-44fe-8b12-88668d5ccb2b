package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.ChatMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * ChatMessage实体的Spring Data JPA仓库接口。
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    /**
     * @param sessionId 会话ID。
     * @return 属于该会话的聊天消息列表。
     */
    List<ChatMessage> findBySessionIdOrderByMessageTimestampAsc(String sessionId);

    /**
     * @param pageable 分页参数，可以限制返回的记录数量。
     * @return 一个分页的聊天消息列表。
     */
    Page<ChatMessage> findBySessionId(String sessionId, Pageable pageable);

    /**
     * @param pageable 分页参数。
     * @return 一个分页的聊天消息列表。
     */
    Page<ChatMessage> findByUserIdOrderByMessageTimestampDesc(String userId, Pageable pageable);

    /**
     * @param startTime 开始时间。
     * @return 符合条件的聊天消息列表。
     */
    List<ChatMessage> findBySessionIdAndMessageTimestampGreaterThanEqualOrderByMessageTimestampAsc(String sessionId, Date startTime);

} 