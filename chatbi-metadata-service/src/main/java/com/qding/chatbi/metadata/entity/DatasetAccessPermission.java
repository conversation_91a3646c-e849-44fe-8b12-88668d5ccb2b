package com.qding.chatbi.metadata.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Entity
@Table(name = "dataset_access_permissions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"role_id", "dataset_id"})
})
@Data
public class DatasetAccessPermission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id", nullable = false)
    private QueryableDataset dataset;

    @Transient
    public Long getDatasetId() {
        return dataset != null ? dataset.getId() : null;
    }

    @Transient
    public void setDatasetId(Long datasetId) {
        if (this.dataset == null) {
            this.dataset = new QueryableDataset();
        }
        this.dataset.setId(datasetId);
    }
    
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    // 默认构造函数
    public DatasetAccessPermission() {
        this.role = new Role();
        this.dataset = new QueryableDataset();
    }
}
