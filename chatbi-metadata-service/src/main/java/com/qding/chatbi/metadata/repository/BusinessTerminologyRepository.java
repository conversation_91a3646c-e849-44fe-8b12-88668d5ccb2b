package com.qding.chatbi.metadata.repository;

import com.qding.chatbi.metadata.entity.BusinessTerminology;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 业务术语 Repository
 */
@Repository
public interface BusinessTerminologyRepository extends JpaRepository<BusinessTerminology, Long> {
    List<BusinessTerminology> findByBusinessTerm(String businessTerm);
} 