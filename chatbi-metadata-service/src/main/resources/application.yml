# Spring Boot application configuration for chatbi-metadata-service
# Example:
# server:
#   port: 8083
# spring:
#   application:
#     name: chatbi-metadata-service
#   datasource:
#     url: ***************************************************************************
#     username: root
#     password:
#     driver-class-name: com.mysql.cj.jdbc.Driver
#   jpa:
#     hibernate:
#       ddl-auto: update # Or validate, none for production
#     show-sql: true
#     properties:
#       hibernate:
#         dialect: org.hibernate.dialect.MySQLDialect

server:
  port: 8083
spring:
  application:
    name: chatbi-metadata-service
  datasource:
    url: ***************************************************************************
    username: root
    password:
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
