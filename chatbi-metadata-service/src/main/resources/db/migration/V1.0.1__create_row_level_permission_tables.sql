-- 用户上下文表
CREATE TABLE user_contexts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL UNIQUE COMMENT '用户ID',
    user_name VARCHAR(100) COMMENT '用户姓名',
    department VARCHAR(100) COMMENT '部门',
    region VARCHAR(100) COMMENT '区域',
    city VARCHAR(100) COMMENT '城市',
    position VARCHAR(100) COMMENT '职位',
    level VARCHAR(50) COMMENT '级别',
    manager_id VARCHAR(100) COMMENT '上级用户ID',
    organization_id VARCHAR(100) COMMENT '组织ID',
    cost_center VARCHAR(100) COMMENT '成本中心',
    extended_attributes TEXT COMMENT '扩展属性(JSON格式)',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_department (department),
    INDEX idx_region (region)
) COMMENT '用户上下文表';

-- 行级权限表
CREATE TABLE row_level_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    dataset_id BIGINT NOT NULL COMMENT '数据集ID',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    description TEXT COMMENT '权限描述',
    filter_type ENUM('COLUMN_VALUE', 'SQL_CONDITION', 'USER_ATTRIBUTE') NOT NULL COMMENT '过滤条件类型',
    filter_column VARCHAR(100) COMMENT '过滤字段名称',
    filter_operator ENUM('EQUALS', 'NOT_EQUALS', 'IN', 'NOT_IN', 'LIKE', 'NOT_LIKE', 'GREATER_THAN', 'GREATER_EQUAL', 'LESS_THAN', 'LESS_EQUAL', 'BETWEEN', 'IS_NULL', 'IS_NOT_NULL') COMMENT '过滤操作符',
    filter_values TEXT COMMENT '过滤值(JSON格式)',
    sql_condition TEXT COMMENT '自定义SQL条件',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    priority INT DEFAULT 0 COMMENT '优先级，数字越小优先级越高',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_role_dataset_permission (role_id, dataset_id, permission_name),
    INDEX idx_role_id (role_id),
    INDEX idx_dataset_id (dataset_id),
    INDEX idx_enabled (enabled),
    INDEX idx_priority (priority)
) COMMENT '行级权限表';

-- 插入示例数据

-- 示例用户上下文
INSERT INTO user_contexts (user_id, user_name, department, region, city, position, level, organization_id) VALUES
('user001', '张三', '销售部', '华北区', '北京', '销售经理', 'M2', 'ORG001'),
('user002', '李四', '销售部', '华东区', '上海', '销售代表', 'P1', 'ORG001'),
('user003', '王五', '财务部', '华北区', '北京', '财务经理', 'M2', 'ORG001'),
('user004', '赵六', '人事部', '华南区', '深圳', 'HR专员', 'P2', 'ORG001');

-- 示例行级权限规则（假设角色ID和数据集ID已存在）
-- 销售人员只能看到自己区域的数据
INSERT INTO row_level_permissions (role_id, dataset_id, permission_name, description, filter_type, filter_column, filter_operator, filter_values, enabled, priority) VALUES
(1, 1, 'region_filter', '销售人员只能查看自己区域的数据', 'USER_ATTRIBUTE', 'region', 'EQUALS', '${user.region}', TRUE, 1);

-- 部门经理可以看到自己部门的数据
INSERT INTO row_level_permissions (role_id, dataset_id, permission_name, description, filter_type, filter_column, filter_operator, filter_values, enabled, priority) VALUES
(2, 1, 'department_filter', '部门经理只能查看自己部门的数据', 'USER_ATTRIBUTE', 'department', 'EQUALS', '${user.department}', TRUE, 1);

-- 高级管理层可以看到指定区域的数据
INSERT INTO row_level_permissions (role_id, dataset_id, permission_name, description, filter_type, filter_column, filter_operator, filter_values, enabled, priority) VALUES
(3, 1, 'senior_manager_filter', '高级管理层可以查看华北区和华东区数据', 'COLUMN_VALUE', 'region', 'IN', '["华北区", "华东区"]', TRUE, 1);

-- 财务人员使用自定义SQL条件
INSERT INTO row_level_permissions (role_id, dataset_id, permission_name, description, filter_type, sql_condition, enabled, priority) VALUES
(4, 1, 'finance_filter', '财务人员可以查看已审核的数据', 'SQL_CONDITION', 'status = ''已审核'' AND department IN (''销售部'', ''市场部'')', TRUE, 1);
