package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 用户查询请求数据传输对象 (DTO)。
 * 封装了从外部系统发送给AI Agent核心服务的用户查询请求。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class UserQueryRequest {

    /**
     * 发起查询的用户ID。
     * 此字段为必填项。
     */
    @NotBlank(message = "用户ID (userId) 不能为空")
    private String userId;

    /**
     * 当前对话的会话ID。
     * 首次请求时可为空，后端服务会生成并返回。
     * 后续请求应携带此ID以维持对话上下文。
     */
    private String sessionId;

    /**
     * 用户输入的原始自然语言查询文本。
     * 此字段为必填项。
     */
    @NotBlank(message = "查询文本 (queryText) 不能为空")
    private String queryText;

    /**
     * (可选) 一个灵活的键值对集合，用于传递附加信息。
     * 例如: {"clarifiedOptionId": "...", "timeZone": "Asia/Shanghai"}
     */
    private Map<String, Object> additionalParams;

}
