package com.qding.chatbi.common.exception;

import com.qding.chatbi.common.enums.ErrorCode;

/**
 * 项目中所有自定义业务异常的基类。
 */
public class ChatBiException extends RuntimeException {

    /**
     * 错误码 (可选): 对应 ErrorCode 中的 code。
     */
    private String errorCode;
    /**
     * 错误消息。
     */
    private String errorMessage;

    /**
     * 构造方法。
     * @param errorMessage 错误消息。
     */
    public ChatBiException(String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
    }

    /**
     * 构造方法。
     * @param errorMessage 错误消息。
     * @param cause 原始异常。
     */
    public ChatBiException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorMessage = errorMessage;
    }

    /**
     * 构造方法。
     * @param errorCode 错误码枚举。
     */
    public ChatBiException(ErrorCode errorCode) {
        super(errorCode.getDescription());
        this.errorCode = errorCode.getCode();
        this.errorMessage = errorCode.getDescription();
    }

    /**
     * 构造方法。
     * @param errorCode 错误码枚举。
     * @param cause 原始异常。
     */
    public ChatBiException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getDescription(), cause);
        this.errorCode = errorCode.getCode();
        this.errorMessage = errorCode.getDescription();
    }

    /**
     * 构造方法。
     * @param errorCode 错误码枚举。
     * @param customMessage 自定义错误消息 (将覆盖ErrorCode中的默认描述)。
     */
    public ChatBiException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.errorCode = errorCode.getCode();
        this.errorMessage = customMessage;
    }

    /**
     * 构造方法。
     * @param errorCode 错误码枚举。
     * @param customMessage 自定义错误消息 (将覆盖ErrorCode中的默认描述)。
     * @param cause 原始异常。
     */
    public ChatBiException(ErrorCode errorCode, String customMessage, Throwable cause) {
        super(customMessage, cause);
        this.errorCode = errorCode.getCode();
        this.errorMessage = customMessage;
    }

    /**
     * 获取错误码。
     * @return 错误码字符串。
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取封装的错误消息。
     * @return 错误消息字符串。
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 获取错误消息 (覆盖 RuntimeException 的 getMessage)。
     * @return 错误消息字符串。
     */
    @Override
    public String getMessage() {
        return errorMessage;
    }
}
