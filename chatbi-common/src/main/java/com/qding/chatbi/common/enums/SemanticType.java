package com.qding.chatbi.common.enums;

/**
 * 列的语义类型枚举
 * DIMENSION: 维度，通常是分类或时间等文本/日期类型字段，用于分组。 e.g., '城市', '日期'
 * METRIC: 指标，通常是数值类型字段，用于聚合计算。 e.g., '销售额', '用户数'
 */
public enum SemanticType {

    /**
     * 指标 (可进行聚合计算的数值)
     * 例如: 销售额, 订单量
     */
    METRIC,

    /**
     * 普通维度 (用于分组、筛选的分类或文本)
     * 例如: 商品品类, 渠道来源
     */
    DIMENSION,

    /**
     * 时间维度 (专门用于标识日期和时间的维度)
     * 新增: 用于更智能的时间序列分析和图表推荐
     * 例如: 日期, 月份
     */
    TIME_DIMENSION,

    /**
     * 地理维度 (专门用于标识地理位置信息的维度)
     * 新增: 为未来的地图可视化做准备
     * 例如: 国家, 城市
     */
    GEO_DIMENSION;
}