package com.qding.chatbi.common.exception;

import com.qding.chatbi.common.enums.ErrorCode;

/**
 * 用户尝试访问无权限资源时抛出。
 */
public class PermissionDeniedException extends ChatBiException {

    /**
     * 构造方法。使用默认的 ErrorCode.PERMISSION_DENIED。
     */
    public PermissionDeniedException() {
        super(ErrorCode.PERMISSION_DENIED);
    }

    /**
     * 构造方法。
     * @param message 自定义错误消息 (将覆盖 ErrorCode.PERMISSION_DENIED 的默认描述)。
     */
    public PermissionDeniedException(String message) {
        super(ErrorCode.PERMISSION_DENIED, message);
    }
}
