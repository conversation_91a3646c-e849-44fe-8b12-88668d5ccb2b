package com.qding.chatbi.common.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 显示建议 DTO
 * 包含图表类型、标题和配置信息
 */
@Data
public class DisplaySuggestion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 图表类型：table, kpi_card, line_chart, bar_chart, pie_chart 等
     * @deprecated 使用 displayStrategy 替代
     */
    @Deprecated
    private String chartType;

    /**
     * 展示标题
     */
    private String title;

    /**
     * 图表配置JSON（Chart.js格式）
     * @deprecated 使用 chartConfigJson 替代
     */
    @Deprecated
    private String chartConfigJson;

    /**
     * X轴字段列表
     * @deprecated 配置信息已移至JSON配置中
     */
    @Deprecated
    private List<String> xAxis;

    /**
     * Y轴字段列表  
     * @deprecated 配置信息已移至JSON配置中
     */
    @Deprecated
    private List<String> yAxis;

    // ========== 新的统一配置字段 ==========

    /**
     * 展示策略：text_only, both, table_only, chart_only, table_primary
     */
    private String displayStrategy;

    /**
     * 表格配置JSON
     */
    private String tableConfigJson;

    /**
     * 图表配置JSON（新版本，替代旧的chartConfigJson）
     */
    private String newChartConfigJson;

    /**
     * 数据可视化配置对象
     */
    private DataVisualizationConfig dataVisualizationConfig;

    // ========== 构造函数 ==========

    /**
     * 旧版本构造函数（保持兼容性）
     */
    @Deprecated
    public DisplaySuggestion(String chartType, String title, String chartConfigJson, String xAxis, List<String> yAxis) {
        this.chartType = chartType;
        this.title = title;
        this.chartConfigJson = chartConfigJson;
        this.xAxis = xAxis != null ? List.of(xAxis) : null;
        this.yAxis = yAxis;
        
        // 自动映射到新字段
        if ("table".equals(chartType)) {
            this.displayStrategy = "table_only";
        } else if (chartType != null && chartType.endsWith("_chart")) {
            this.displayStrategy = "chart_only";
            this.newChartConfigJson = chartConfigJson;
        } else {
            this.displayStrategy = "table_only";
        }
    }

    /**
     * 新版本构造函数（推荐使用）
     */
    public DisplaySuggestion(String displayStrategy, String title, String tableConfigJson, String chartConfigJson) {
        this.displayStrategy = displayStrategy;
        this.title = title;
        this.tableConfigJson = tableConfigJson;
        this.newChartConfigJson = chartConfigJson;
        
        // 创建数据可视化配置对象
        DataVisualizationConfig config = new DataVisualizationConfig();
        config.setSuccess(true);
        config.setDisplayStrategy(displayStrategy);
        config.setTitle(title);
        config.setTableConfigJson(tableConfigJson);
        config.setChartConfigJson(chartConfigJson);
        this.dataVisualizationConfig = config;
        
        // 为了兼容性，设置旧字段
        if ("table_only".equals(displayStrategy) || "table_primary".equals(displayStrategy)) {
            this.chartType = "table";
        } else if ("chart_only".equals(displayStrategy)) {
            this.chartType = "chart";
            this.chartConfigJson = chartConfigJson;
        } else if ("both".equals(displayStrategy)) {
            this.chartType = "both";
            this.chartConfigJson = chartConfigJson;
        } else if ("text_only".equals(displayStrategy)) {
            this.chartType = "text";
        }
    }

    /**
     * 默认构造函数
     */
    public DisplaySuggestion() {
    }

    // ========== 便利方法 ==========

    /**
     * 是否需要显示表格
     */
    public boolean shouldShowTable() {
        return "table_only".equals(displayStrategy) || "both".equals(displayStrategy) || "table_primary".equals(displayStrategy);
    }

    /**
     * 是否需要显示图表
     */
    public boolean shouldShowChart() {
        return "chart_only".equals(displayStrategy) || "both".equals(displayStrategy);
    }

    /**
     * 是否仅显示文本
     */
    public boolean isTextOnly() {
        return "text_only".equals(displayStrategy);
    }

    /**
     * 获取有效的图表配置JSON
     */
    public String getEffectiveChartConfigJson() {
        return newChartConfigJson != null ? newChartConfigJson : chartConfigJson;
    }
} 