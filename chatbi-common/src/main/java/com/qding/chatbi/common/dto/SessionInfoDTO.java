package com.qding.chatbi.common.dto;



import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 会话信息数据传输对象 (DTO)。
 * 用于封装一次完整对话会话的摘要信息或详细历史。
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class SessionInfoDTO {

    /**
     * 会话的唯一ID。
     */
    private String sessionId;

    /**
     * 发起该会话的用户ID。
     */
    private String userId;

    /**
     * 会话开始的时间 (第一条消息的时间戳)。
     */
    private Date startTime;

    /**
     * 会话最后一次有活动的时间 (最后一条消息的时间戳)。
     */
    private Date lastActivityTime;

    /**
     * (可选) 会话当前的状态，例如 "Active", "Closed"。
     */
    private String status;

    /**
     * 会话中包含的总消息数。
     */
    private int messageCount;

    /**
     * (可选) 本次会话的详细消息列表。
     * 每个元素都是一个ChatMessageDTO对象。
     */
    private List<ChatMessageDTO> messages;

}
/* 
public class SessionInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sessionId;
    private String userId;
    private Date startTime;
    private Date lastActivityTime;
    private Map<String, Object> sessionAttributes;
    // For example, a list of messages in the session
    // private List<MessageDTO> messages;

    // Constructors
    public SessionInfoDTO() {
    }

    public SessionInfoDTO(String sessionId, String userId, Date startTime, Date lastActivityTime, Map<String, Object> sessionAttributes) {
        this.sessionId = sessionId;
        this.userId = userId;
        this.startTime = startTime;
        this.lastActivityTime = lastActivityTime;
        this.sessionAttributes = sessionAttributes;
    }

    // Getters and Setters

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getLastActivityTime() {
        return lastActivityTime;
    }

    public void setLastActivityTime(Date lastActivityTime) {
        this.lastActivityTime = lastActivityTime;
    }

    public Map<String, Object> getSessionAttributes() {
        return sessionAttributes;
    }

    public void setSessionAttributes(Map<String, Object> sessionAttributes) {
        this.sessionAttributes = sessionAttributes;
    }

    @Override
    public String toString() {
        return "SessionInfoDTO{" +
                "sessionId='" + sessionId + '\'' +
                ", userId='" + userId + '\'' +
                ", startTime=" + startTime +
                ", lastActivityTime=" + lastActivityTime +
                ", sessionAttributes=" + sessionAttributes +
                '}';
    }
}
*/