package com.qding.chatbi.common.enums;

import lombok.Getter;

/**
 * 定义了数据集的基础对象的类型，区分数据集是基于物理表、视图还是自定义SQL。
 */
@Getter
public enum BaseObjectType {

    /**
     * 数据集直接映射到一个数据库物理表。
     */
    TABLE("物理表"),

    /**
     * 数据集直接映射到一个数据库视图。
     */
    VIEW("视图"),

    /**
     * 数据集是基于一个自定义的SQL查询。
     */
    QUERY("自定义查询");

    private final String description;

    BaseObjectType(String description) {
        this.description = description;
    }
}
