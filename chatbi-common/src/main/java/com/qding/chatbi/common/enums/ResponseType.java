package com.qding.chatbi.common.enums;

/**
 * 定义 AgentResponse 中的响应类型。
 */
public enum ResponseType {
    DATA_RESULT, // 查询成功并返回数据
    DATA, // 数据响应（ResponseGenerator使用）
    DATA_QUERY, // 数据查询响应
    NO_DATA, // 没有数据（ResponseGenerator使用）
    CLARIFICATION_NEEDED, // 需要用户进一步澄清
    DATA_UNAVAILABLE, // 请求的数据在系统中不存在
    ACKNOWLEDGEMENT, // 已收到，正在处理等简单确认
    ERROR, // 发生错误
    NO_PERMISSION, // 权限不足
    NO_DATA_FOUND // 查询成功但未找到数据
}
