package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询结果数据传输对象 (DTO)。
 * 封装了数据库查询执行后的表格化结果，以便于在API层展示和传输。
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class QueryResult {

    /**
     * (可选) 本次查询实际执行的SQL语句。
     * 主要用于调试、追溯或向特定角色的用户展示。
     */
    private String querySql;

    /**
     * 列的元数据信息列表。
     * 列表的顺序与 columnHeaders 和 rows 中每行数据的列顺序严格对应。
     * 这是智能可视化推荐的依据。
     */
    private List<ColumnMetadata> columnMetadata;

    /**
     * 查询结果的列名列表。
     * 列表的顺序定义了下面rows中每行数据的列顺序。
     * 例如: ["城市", "总销售额"]
     */
    private List<String> columnHeaders;

    /**
     * 查询结果的数据行。
     * 这是一个列表的列表，外层列表代表所有行，内层列表代表单行中的各个列数据。
     * 内层列表的数据顺序必须与 columnHeaders 的顺序严格对应。
     * 例如: [["北京", 12000.50], ["上海", 15000.75]]
     */
    private List<List<Object>> rows;

    /**
     * (可选) 查询结果的总行数。
     * 主要用于分页场景，告知客户端总共有多少条数据。
     */
    private Long totalRows;

    /**
     * (可选) 查询执行的摘要信息。
     * 例如: "查询耗时 1.25 秒，共返回 100 条记录。"
     */
    private String executionSummary;

    /**
     * (可选) 如果查询执行过程中发生错误，此字段用于记录错误信息。
     * 例如: "数据库连接超时" 或 "SQL语法错误"。
     */
    private String errorMessage;

}
