package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 可查询数据集信息数据传输对象 (DTO)。
 * 用于在服务间传递数据模型的摘要信息，通常包含数据集本身及其包含的字段列表。
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class DatasetInfoDTO {

    /**
     * 数据集的唯一ID。
     * 对应 queryable_datasets.id
     */
    private Long datasetId;

    /**
     * 数据集的业务名称。
     * 例如："每日产品销售汇总"。
     */
    private String datasetName;

    /**
     * 数据集的详细描述。
     */
    private String description;

    /**
     * 新增字段：数据库类型，例如 "MYSQL", "CLICKHOUSE"
     */
    private String databaseType;

    /**
     * 新增字段：数据库源ID
     */
    private Long databaseSourceId;

    /**
     * 新增字段：数据集对应的物理表名
     */
    private String tableName;

    /**
     * (可选) 该数据集包含的字段信息列表。
     * 每个元素都是一个ColumnInfoDTO对象。
     * @see com.qding.chatbi.common.dto.ColumnInfoDTO
     */
    private List<ColumnInfoDTO> columns;

}
