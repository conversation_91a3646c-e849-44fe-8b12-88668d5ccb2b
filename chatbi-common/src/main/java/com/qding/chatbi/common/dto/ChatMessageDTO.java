package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.qding.chatbi.common.enums.MessageType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天消息数据传输对象 (DTO)。
 * 用于在API层展示单条聊天消息，避免直接暴露JPA实体。
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class ChatMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息的唯一ID。
     */
    private String id;

    /**
     * 会话ID。
     */
    private String conversationId;

    /**
     * 消息的文本内容。
     */
    private String content;

    /**
     * 消息类型。
     */
    private MessageType messageType;

    /**
     * 用于存放结构化数据。
     */
    private Object data;

    /**
     * 消息发送者类型 (例如 "USER", "AI_AGENT")。
     */
    private Sender sender;

    /**
     * 创建时间。
     */
    private Long createTime;

    public enum Sender {
        USER,
        BOT
    }

    /**
     * 错误码。
     */
    private String errorCode;

    /**
     * 错误消息详情。
     */
    private String errorMessageDetail;

    /**
     * 使用的LLM模型。
     */
    private String llmModelUsed;

    /**
     * 处理持续时间（毫秒）。
     */
    private Integer processingDurationMs;

    /**
     * 创建时间。
     */
    private Date createdAt;

    /**
     * 更新时间。
     */
    private Date updatedAt;

}
