package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 澄清选项数据传输对象 (DTO)。
 * 当AI需要用户澄清意图时，提供的单个澄清选项。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class ClarificationOption {

    /**
     * 选项的唯一标识。
     * 用于后端识别用户选择了哪个选项。
     */
    private String optionId;

    /**
     * 展示给用户的选项文本。
     * 例如："查询最近一个月的销售额" 或 "查询北京地区的销售额"。
     */
    private String optionText;

    /**
     * (可选) 与此选项关联的附加信息。
     * 当用户选择此选项后，客户端可以将此payload回传给后端，
     * 以便后端进行更精确的处理。
     */
    private Map<String, Object> payload;

}
