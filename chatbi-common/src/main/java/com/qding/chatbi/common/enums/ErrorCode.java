package com.qding.chatbi.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Optional;

/**
 * 系统统一错误码枚举。
 * 定义了业务中可预见的错误类型。
 */
@Getter
@RequiredArgsConstructor
public enum ErrorCode {

    // --- 客户端错误 (4xx) ---
    INVALID_INPUT("BIE001", "无效输入参数"),
    UNAUTHENTICATED("BIE002", "用户未认证"),
    PERMISSION_DENIED("BIE003", "权限不足"),
    RESOURCE_NOT_FOUND("BIE004", "请求资源未找到"),

    // --- 服务端错误 (5xx) ---
    DATABASE_ERROR("BIE005", "数据库操作异常"),
    LLM_ERROR("BIE006", "大模型调用异常"),
    SQL_GENERATION_ERROR("BIE007", "SQL生成失败"),
    UNKNOWN_INTENT("BIE008", "无法理解的意图"),

    INTERNAL_SERVER_ERROR("BIE999", "系统内部错误");

    private final String code;
    private final String description;

    /**
     * 根据字符串错误码查找对应的ErrorCode枚举实例。
     *
     * @param code 错误码字符串，例如 "BIE001"。
     * @return 一个包含匹配的ErrorCode的Optional，如果找不到则为空。
     */
    public static Optional<ErrorCode> fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return Optional.empty();
        }
        return Arrays.stream(values())
                .filter(errorCode -> errorCode.getCode().equalsIgnoreCase(code))
                .findFirst();
    }
}
