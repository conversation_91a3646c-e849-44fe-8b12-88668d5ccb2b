package com.qding.chatbi.common.util;

/**
 * 提供字符串操作的常用方法。
 */
public class StringUtil {

    private StringUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 检查字符串是否为空白（null、空字符串或仅由空白字符组成）。
     * @param str 要检查的字符串，可以为 null。
     * @return 如果字符串为空白，则返回 true；否则返回 false。
     */
    public static boolean isBlank(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查字符串是否不为空白（非 null、非空且至少包含一个非空白字符）。
     * @param str 要检查的字符串，可以为 null。
     * @return 如果字符串不为空白，则返回 true；否则返回 false。
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 如果给定字符串不为空白，则返回该字符串；否则返回默认字符串。
     * @param str 要检查的字符串，可以为 null。
     * @param defaultStr 如果输入字符串为空白时要返回的默认字符串，可以为 null。
     * @return 如果输入字符串不为空白，则为输入字符串；否则为默认字符串。
     */
    public static String defaultIfBlank(String str, String defaultStr) {
        return isBlank(str) ? defaultStr : str;
    }
}
