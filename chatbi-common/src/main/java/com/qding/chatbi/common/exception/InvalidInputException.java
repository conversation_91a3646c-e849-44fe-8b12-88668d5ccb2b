package com.qding.chatbi.common.exception;

import com.qding.chatbi.common.enums.ErrorCode;

/**
 * 请求参数校验失败时抛出。
 */
public class InvalidInputException extends ChatBiException {

    /**
     * 构造方法。
     * @param message 附加的错误信息，将跟在 ErrorCode.INVALID_INPUT 的默认描述之后。
     */
    public InvalidInputException(String message) {
        super(ErrorCode.INVALID_INPUT, ErrorCode.INVALID_INPUT.getDescription() + ": " + message);
    }

    /**
     * 构造方法，直接使用自定义消息，不添加前缀。
     * @param message 完整的错误消息
     * @param useDirectMessage 如果为true，直接使用message作为错误消息，不添加前缀
     */
    public InvalidInputException(String message, boolean useDirectMessage) {
        super(ErrorCode.INVALID_INPUT, useDirectMessage ? message : ErrorCode.INVALID_INPUT.getDescription() + ": " + message);
    }

    /**
     * 构造方法。使用 ErrorCode.INVALID_INPUT 的默认描述。
     */
    public InvalidInputException() {
        super(ErrorCode.INVALID_INPUT);
    }
}
