package com.qding.chatbi.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 提供日期和时间处理的常用方法。
 */
public class DateUtil {

    private DateUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 使用指定的模式将 Date 对象格式化为字符串。
     * @param date 要格式化的 Date 对象。
     * @param pattern 用于格式化的模式 (例如："yyyy-MM-dd HH:mm:ss")。
     * @return 格式化后的日期字符串；如果输入的日期为 null，则返回 null。
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 使用指定的模式将日期字符串解析为 Date 对象。
     * @param dateStr 要解析的日期字符串。
     * @param pattern 日期字符串使用的模式 (例如："yyyy-MM-dd HH:mm:ss")。
     * @return 解析后的 Date 对象；如果解析失败或输入为 null/空，则返回 null。
     */
    public static Date parseDate(String dateStr, String pattern) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            // 可选：此处可以添加日志记录，例如使用日志框架记录解析异常。
            // System.err.println("使用模式 '" + pattern + "' 解析日期字符串 '" + dateStr + "' 失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前的日期和时间作为 LocalDateTime 对象。
     * @return 当前的 LocalDateTime。
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }
}
