package com.qding.chatbi.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 用户上下文数据传输对象 (DTO)。
 * 用于在服务调用链路中封装和传递与当前用户请求相关的所有上下文信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserContextDTO {

    /**
     * 用户的唯一标识。
     * 用于权限判断和日志记录。
     */
    private String userId;

    /**
     * 用户所拥有的角色。
     * 这是权限控制的核心依据。
     */
    private String userRole;

    /**
     * 当前对话的会话ID。
     * 用于追踪多轮对话的上下文。
     */
    private String sessionId;

    /**
     * (可选) 客户端相关信息。
     * 一个灵活的Map，可以存放IP地址、User-Agent、时区等。
     * 例如: {"clientIp": "127.0.0.1", "userAgent": "Mozilla/5.0...", "timeZone":
     * "Asia/Shanghai"}
     */
    private Map<String, String> clientInfo;

}
