package com.qding.chatbi.common.exception;

import com.qding.chatbi.common.enums.ErrorCode;

/**
 * 请求的资源不存在时抛出。
 */
public class ResourceNotFoundException extends ChatBiException {

    /**
     * 构造方法，生成类似 "[resourceName] with ID [resourceId] not found" 的消息。
     * @param resourceName 资源名称 (例如："Dataset", "User")。
     * @param resourceId 资源ID。
     */
    public ResourceNotFoundException(String resourceName, Object resourceId) {
        super(ErrorCode.RESOURCE_NOT_FOUND, resourceName + " with ID " + String.valueOf(resourceId) + " not found");
    }

    /**
     * 构造方法，允许使用完全自定义的错误消息。
     * @param message 自定义错误消息。
     */
    public ResourceNotFoundException(String message) {
        super(ErrorCode.RESOURCE_NOT_FOUND, message);
    }
}
