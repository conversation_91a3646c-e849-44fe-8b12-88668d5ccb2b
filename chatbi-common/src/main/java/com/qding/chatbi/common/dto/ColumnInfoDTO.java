package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.qding.chatbi.common.enums.SemanticType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据集字段信息数据传输对象 (DTO)。
 * 用于在服务间传递数据集中单个字段的详细元数据信息。
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class ColumnInfoDTO {

    /**
     * 字段的唯一ID。
     * 对应 dataset_columns.id
     */
    private Long columnId;

    /**
     * 字段的业务名称，也是主要向用户展示的名称。
     * 例如："总销售额", "用户注册城市"。
     */
    private String columnName;

    /**
     * 字段的详细描述和计算口径（如果适用）。
     */
    private String description;

    /**
     * 字段在数据库中的技术名称或计算表达式。
     * 例如："sale_amount", "price * quantity"。
     */
    private String technicalNameOrExpression;

    /**
     * 字段的语义类型。
     * @see com.qding.chatbi.common.enums.SemanticType
     */
    private SemanticType semanticType;

    /**
     * 字段呈现给用户的数据类型。
     * 例如："NUMBER", "STRING", "DATE", "BOOLEAN", "PERCENTAGE"。
     */
    private String dataType;

    /**
     * (可选) 对于指标 (METRIC) 类型的字段，允许的聚合函数列表。
     * 例如：["SUM", "AVG", "COUNT"]
     */
    private List<String> allowedAggregations;

    /**
     * 字段是否为计算字段。
     * 如果为 true，则 technicalNameOrExpression 存储的是计算表达式。
     */
    private Boolean isComputed;

}
