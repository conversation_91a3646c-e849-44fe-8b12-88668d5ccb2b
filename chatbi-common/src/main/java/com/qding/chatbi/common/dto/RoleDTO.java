package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 角色信息数据传输对象 (DTO)。
 * 用于在服务间传递角色信息。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class RoleDTO {

    /**
     * 角色的唯一ID。
     * 对应 roles.id
     */
    private Long roleId;

    /**
     * 角色名称。
     * 例如："Admin", "Developer", "Employee"。
     */
    private String roleName;

    /**
     * (可选) 角色的描述。
     */
    private String description;

}
