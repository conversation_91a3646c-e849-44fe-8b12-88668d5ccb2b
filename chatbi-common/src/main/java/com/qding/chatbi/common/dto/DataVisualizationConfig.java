package com.qding.chatbi.common.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 数据可视化配置
 * 包含表格和图表的统一配置
 */
@Data
public class DataVisualizationConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 展示策略：both, table_only, chart_only
     */
    private String displayStrategy;

    /**
     * 表格配置（JSON字符串）
     */
    private String tableConfigJson;

    /**
     * 图表配置（JSON字符串）
     */
    private String chartConfigJson;

    /**
     * 展示标题
     */
    private String title;

    /**
     * 是否成功生成配置
     */
    private boolean success;

    /**
     * 错误信息（如果生成失败）
     */
    private String errorMessage;
} 