package com.qding.chatbi.common.dto;

import com.qding.chatbi.common.enums.DataType;
import com.qding.chatbi.common.enums.SemanticType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 列元数据DTO。
 * 用于描述查询结果中每一列的详细信息，包括名称、语义类型和数据类型。
 * 这是实现智能可视化推荐的基础。
 */
@Data
@NoArgsConstructor
@Setter
public class ColumnMetadata {
    /**
     * 列的名称, e.g., "city"
     */
    private String name;

    /**
     * 列的易读中文名称
     */
    private String friendlyName;

    /**
     * 列的语义类型
     */
    private SemanticType semanticType;

    /**
     * 列的数据类型
     */
    private DataType dataType;

    public ColumnMetadata(String name, String friendlyName, SemanticType semanticType, DataType dataType) {
        this.name = name;
        this.friendlyName = friendlyName;
        this.semanticType = semanticType;
        this.dataType = dataType;
    }
} 