package com.qding.chatbi.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * JSON工具类，基于Jackson的ObjectMapper实现。
 * 提供静态方法进行JSON序列化和反序列化。
 */
@Slf4j
@Component // 将ObjectMapper作为Spring Bean管理，便于统一配置
public class JsonUtil {

    private static ObjectMapper objectMapper;

    // 通过Spring的依赖注入来初始化ObjectMapper，确保配置是统一的
    public JsonUtil(ObjectMapper objectMapper) {
        JsonUtil.objectMapper = objectMapper;
        // 进行一些自定义配置
        JsonUtil.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    /**
     * 将Java对象序列化为JSON字符串。
     *
     * @param object 要序列化的对象。
     * @return JSON字符串，如果序列化失败则返回null。
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Error serializing object to JSON string", e);
            return null;
        }
    }

    /**
     * 将JSON字符串反序列化为指定类型的Java对象。
     *
     * @param json  JSON字符串。
     * @param clazz 目标对象的Class。
     * @param <T>   目标对象的类型。
     * @return 反序列化后的Java对象，如果失败则返回null。
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (StringUtil.isBlank(json) || clazz == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("Error deserializing JSON string to object of class {}", clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串反序列化为复杂的泛型类型（如List<T>或Map<K, V>）。
     *
     * @param json         JSON字符串。
     * @param typeReference 包含泛型类型信息的TypeReference。
     * @param <T>          目标对象的类型。
     * @return 反序列化后的Java对象，如果失败则返回null。
     * @example fromJson(json, new TypeReference<List<MyObject>>() {});
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (StringUtil.isBlank(json) || typeReference == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            log.error("Error deserializing JSON string to complex type {}", typeReference.getType(), e);
            return null;
        }
    }
}
