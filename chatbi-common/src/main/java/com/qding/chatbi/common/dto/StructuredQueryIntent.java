package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 增强的结构化查询意图
 * 包含业务逻辑分析和技术执行指导
 */
/*
 * **格式一：当查询意图完整时，输出`DATA_QUERY`意图**
 * ```json
 * {
 * "intent": "DATA_QUERY",
 * "entities": {
 * "metrics": ["销售额"],
 * "dimensions_to_group": ["城市"],
 * "filters": [{"fieldName": "订单日期", "operator": "BETWEEN", "value":
 * "2024-01-01"}],
 * "sort_by": [{"fieldName": "销售额", "order": "DESC"}],
 * "limit": 10
 * },
 * "queryType": "RANKING_QUERY",
 * "targetDatasetId": 123,
 * "datasetMatchConfidence": "HIGH",
 * "internal_context": {
 * "originalQuery": "用户原始查询",
 * "mergedContext": "合并后的完整查询上下文"
 * }
 * }
 * ```
 ** 
 * 格式二：当需要澄清时，输出`CLARIFICATION_NEEDED`意图**
 * ```json
 * {
 * "intent": "CLARIFICATION_NEEDED",
 * "response": "您想查询什么时间范围的销售额？",
 * "clarificationOptions": [
 * {
 * "optionText": "查最近一个月的北京销售额"
 * },
 * {
 * "optionText": "查今年以来北京的销售额"
 * },
 * {
 * "optionText": "查2023年全年的北京销售额"
 * }
 * ],
 * "internal_context": {
 * "originalQuery": "查北京的销售额",
 * "mergedContext": "查北京的销售额"
 * }
 * }
 * ```
 ** 
 * 格式三：当数据不可用或权限不足时，输出`DATA_UNAVAILABLE`意图**
 * ```json
 * {
 * "intent": "DATA_UNAVAILABLE",
 * "response": "您查询的'用户增长指标'不在可查询的数据范围内，或者您没有访问该数据的权限。",
 * "internal_context": {
 * "originalQuery": "用户原始查询",
 * "mergedContext": "合并后的完整查询上下文"
 * }
 * }
 * ```
 ** 
 * 格式四：当用户进行闲聊或问候时，输出`GREETING`意图**
 * ```json
 * {
 * "intent": "GREETING",
 * "response": "你好！很高兴为您服务，请问有什么可以帮您分析的吗？",
 * "internal_context": {
 * "originalQuery": "你好",
 * "mergedContext": "你好"
 * }
 * }
 * ```
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StructuredQueryIntent {
    private String intent;
    private Map<String, Object> entities;
    // private boolean needsClarification;
    private List<ClarificationOption> clarificationOptions;
    private String targetDatasetHint;

    /**
     * 当 intent 为 GREETING, CLARIFICATION_NEEDED, DATA_UNAVAILABLE 时，
     * 提供给用户的标准回复文本。
     */
    private String response;

    /**
     * 内部上下文信息，用于调试和追踪。
     */
    private InternalContext internal_context;

    /**
     * 查询类型
     * DETAIL_QUERY: 明细查询，返回具体记录
     * AGGREGATION_QUERY: 聚合查询，返回统计结果
     * RANKING_QUERY: 排名查询，返回排序结果
     * TREND_QUERY: 趋势查询，返回时间序列
     * COMPARISON_QUERY: 对比查询，返回对比分析
     */
    private String queryType;

    /**
     * 是否需要聚合
     */
    private boolean aggregationRequired;

    /**
     * 期望的数据粒度 - 聚合字段列表
     * 如果为空或null，表示不需要聚合，返回明细数据
     * 如果有值，表示需要按这些字段进行聚合
     * 
     * 示例：
     * - ["category"] : 按商品类别聚合
     * - ["city", "category"] : 按城市和商品类别聚合
     * - ["DATE_FORMAT(dt, '%Y-%m')"] : 按月聚合
     * - [] 或 null : 不聚合，返回明细
     */
    private List<String> expectedGranularity;

    /**
     * 聚合级别描述（用于日志和调试）
     */
    private String granularityDescription;

    /**
     * 目标数据集ID（确定的）
     * 如果NLU能够确定唯一数据集，直接指定ID
     * 如果不能确定，设为null
     */
    private Long targetDatasetId;

    /**
     * 数据集匹配置信度
     * HIGH: 高置信度，所有字段都能匹配
     * MEDIUM: 中等置信度，核心字段能匹配
     * LOW: 低置信度，部分字段能匹配
     * NONE: 无法匹配任何数据集
     */
    private String datasetMatchConfidence;

    /**
     * 建议的聚合函数
     * SUM, COUNT, AVG, MAX, MIN, COUNT_DISTINCT
     */
    private String suggestedAggregationFunction;

    /**
     * 结果限制数量
     */
    private Integer limitCount;

    /**
     * 判断是否需要聚合操作
     */
    public boolean requiresAggregation() {
        return aggregationRequired && expectedGranularity != null && !expectedGranularity.isEmpty();
    }

    /**
     * 判断是否为排名查询
     */
    public boolean isRankingQuery() {
        return "RANKING_QUERY".equals(queryType);
    }

    /**
     * 判断是否为明细查询
     */
    public boolean isDetailQuery() {
        return "DETAIL_QUERY".equals(queryType);
    }

    /**
     * 获取聚合字段列表（向后兼容）
     */
    public List<String> getGroupByFields() {
        return expectedGranularity;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InternalContext {
        private String originalQuery;
        /**
         * 合并的上下文信息
         * 当AI从对话历史中合并多条相关信息时，此字段记录合并后的完整查询意图
         * 例如：用户先说"GMV"，后说"上海和北京今年" → mergedContext: "查询上海和北京今年的GMV"
         */
        private String mergedContext;
    }
}