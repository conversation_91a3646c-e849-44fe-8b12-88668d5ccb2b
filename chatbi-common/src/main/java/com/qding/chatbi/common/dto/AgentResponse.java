package com.qding.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qding.chatbi.common.enums.ResponseType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.io.Serializable;

/**
 * AI Agent的响应数据传输对象 (DTO)。
 * 封装了AI Agent核心服务返回给调用方的所有信息。
 * 已扩展以支持ResponseGenerator的功能需求。
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null值的字段
public class AgentResponse implements Serializable {

    private String type = "result";
    /**
     * 当前对话的会话ID。
     * 客户端应保存此ID并在后续请求中回传。
     */
    private String sessionId;

    /**
     * 响应类型。
     * 指示本次响应的性质，例如是返回数据、请求澄清还是报告错误。
     * 
     * @see com.qding.chatbi.common.enums.ResponseType
     */
    private ResponseType responseType;

    /**
     * 直接展示给用户的文本消息。
     * 这可以是AI的回答、提出的澄清问题、错误提示等。
     * ResponseGenerator的核心产出 - 对结果的自然语言摘要
     */
    private String messageToUser;

    /**
     * (可选) 查询结果。
     * 仅当 responseType 为 DATA 时，此字段才应有值。
     * ResponseGenerator的核心产出 - 原始的、未修改的查询结果数据
     */
    private QueryResult queryResult;

    /**
     * (可选) 前端展示建议。
     * ResponseGenerator的核心产出 - 指导前端如何渲染数据
     */
    private DisplaySuggestion displaySuggestion;

    /**
     * (可选) 澄清选项列表。
     * 仅当 responseType 为 CLARIFICATION_NEEDED 时，此字段才应有值。
     */
    private List<ClarificationOption> clarificationOptions;

    /**
     * (可选) 错误码。
     * 仅当 responseType 为 ERROR 时，此字段才应有值。
     * 
     * @see com.qding.chatbi.common.enums.ErrorCode
     */
    private String errorCode;

    /**
     * (可选) 从QueryPlan中继承的思考过程
     */
    private String thoughtProcess;

    /**
     * (可选) 用于调试或诊断的附加信息。
     * 此信息通常不直接展示给最终用户。
     * 例如: {"llmModel": "qwen-turbo", "executionTime": "1500ms"}
     */
    private Map<String, Object> diagnosticInfo;

    /**
     * 原始查询文本
     */
    private String originalQuery;

    /**
     * 响应是否成功
     */
    private boolean success = true;

    /**
     * 响应消息
     */
    private String responseMessage;

    /**
     * 总结信息
     */
    private String summary;

    // 为了向后兼容，保留原有的responseText getter/setter
    public String getResponseText() {
        return messageToUser;
    }

    public void setResponseText(String responseText) {
        this.messageToUser = responseText;
    }
}
