## **Chat BI \- Common 模块详细设计**

本模块 (chatbi-common) 旨在提供整个 Chat BI 项目共享的基础组件，包括数据传输对象 (DTOs)、枚举类型 (Enums)、自定义异常 (Exceptions)、通用工具类 (Utils) 和常量 (Constants)。

### **1\. 包结构**

com.qding.chatbi.common  
├── dto/  
├── enums/  
├── exception/  
├── util/  
└── constant/

### **2\. com.qding.chatbi.common.dto (Data Transfer Objects)**

此类用于在不同层或模块之间传输数据。

#### **2.1. UserQueryRequest.java**

* **用途**: 封装从外部IM系统（或前端）发送给AI Agent核心服务的用户查询请求。  
* **属性**:  
  * String userId: 发起查询的用户ID。  
  * String sessionId: 当前会话的ID，用于跟踪多轮对话。  
  * String queryText: 用户输入的原始自然语言查询文本。  
  * Map\<String, Object\> additionalParams (可选): 其他可能需要的附加参数 (例如，客户端类型、地理位置信息等)。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.2. AgentResponse.java**

* **用途**: 封装AI Agent核心服务返回给调用方（如IM系统或前端）的响应。  
* **属性**:  
  * String sessionId: 当前会话ID。  
  * ResponseType responseType: 响应类型 (枚举)。  
  * String messageToUser: 直接展示给用户的文本消息 (AI的回答、澄清问题、错误提示等)。  
  * QueryResult queryResult (可选): 当 responseType 为 DATA\_RESULT 时，包含查询结果。  
  * List\<ClarificationOption\> clarificationOptions (可选): 当 responseType 为 CLARIFICATION\_NEEDED 时，提供给用户的澄清选项。  
  * String errorCode (可选): 当 responseType 为 ERROR 时，携带错误码。  
  * Map\<String, Object\> diagnosticInfo (可选): 用于调试或诊断的附加信息。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.3. QueryResult.java**

* **用途**: 封装数据库查询执行后的结果，以便于展示和传输。  
* **属性**:  
  * String querySql (可选): 执行的SQL语句，用于追溯或展示。  
  * List\<String\> columnHeaders: 查询结果的列名列表。  
  * List\<List\<Object\>\> rows: 查询结果数据行，每行是一个对象列表，顺序与 columnHeaders 对应。  
  * long totalRows (可选): 如果分页，表示总行数。  
  * String executionSummary (可选): 执行摘要，如执行时间、影响行数等。  
  * String errorMessage (可选): 如果查询执行出错，记录错误信息。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.4. ClarificationOption.java**

* **用途**: 当AI需要用户澄清意图时，提供的单个澄清选项。  
* **属性**:  
  * String optionId: 选项的唯一标识。  
  * String optionText: 展示给用户的选项文本。  
  * Map\<String, Object\> optionPayload (可选): 用户选择此选项后，需要回传给AI Agent的附加信息。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.5. DatasetInfoDTO.java**

* **用途**: 在服务间传递“可查询数据集”的元数据摘要信息。  
* **属性**:  
  * Long datasetId: 数据集ID (对应 QueryableDatasets.dataset\_id)。  
  * String datasetName: 数据集业务名称。  
  * String description: 数据集描述。  
  * List\<ColumnInfoDTO\> columns: 该数据集包含的字段信息列表。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.6. ColumnInfoDTO.java**

* **用途**: 在服务间传递“数据集字段”的元数据摘要信息。  
* **属性**:  
  * Long columnId: 字段ID (对应 DatasetColumns.column\_id)。  
  * String columnName: 字段业务名称。  
  * String description: 字段描述。  
  * SemanticType semanticType: 语义类型 (使用 com.qding.chatbi.common.enums.SemanticType 枚举)。  
  * String dataType: 数据类型 (e.g., "NUMBER", "STRING", "DATE")。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.7. RoleDTO.java**

* **用途**: 传递角色信息。  
* **属性**:  
  * Long roleId: 角色ID。  
  * String roleName: 角色名称。  
  * String description: 角色描述。  
* **核心方法**: 标准的 Getters 和 Setters。

#### **2.8. UserContextDTO.java**

* **用途**: 封装进行AI Agent调用时传递的用户上下文信息。  
* **属性**:  
  * String userId: 用户ID。  
  * List\<String\> userRoles: 用户拥有的角色名称列表。  
  * String sessionId: 当前会话ID。  
  * Map\<String, String\> clientInfo: 客户端信息 (e.g., IP, User-Agent)。  
* **核心方法**: 标准的 Getters 和 Setters。

### **3\. com.qding.chatbi.common.enums (共享枚举)**

#### **3.1. ResponseType.java**

* **用途**: 定义 AgentResponse 中的响应类型。  
* **枚举值**:  
  * DATA\_RESULT // 查询成功并返回数据  
  * CLARIFICATION\_NEEDED // 需要用户进一步澄清  
  * ACKNOWLEDGEMENT // 已收到，正在处理等简单确认  
  * ERROR // 发生错误  
  * NO\_PERMISSION // 权限不足  
  * NO\_DATA\_FOUND // 查询成功但未找到数据

#### **3.2. SemanticType.java**

* **用途**: 定义 DatasetColumns 表中的 semantic\_type 及 ColumnInfoDTO 中的 semanticType 字段。  
* **枚举值**:  
  * METRIC // 指标  
  * DIMENSION // 维度  
  * TIME // 时间

#### **3.3. DatabaseType.java**

* **用途**: 定义 DatabaseSourceConfigs 表中的 database\_type。  
* **枚举值**:  
  * MYSQL // MySQL数据库  
  * POSTGRESQL // PostgreSQL数据库  
  * SQLSERVER // SQL Server数据库  
  * ORACLE // Oracle数据库  
  * HIVE // Apache Hive  
  * PRESTO // PrestoDB  
  * CLICKHOUSE // ClickHouse  
  * OTHER // 其他类型

#### **3.4. BaseObjectType.java**

* **用途**: 定义 QueryableDatasets 表中的 base\_object\_type。  
* **枚举值**:  
  * TABLE // 物理表  
  * VIEW // 视图  
  * QUERY // 自定义查询

#### **3.5. ErrorCode.java**

* **用途**: 定义统一的错误码及其描述。  
* **属性 (每个枚举实例)**:  
  * String code: 错误码字符串 (e.g., "BIE001")。  
  * String description: 错误描述。  
* **枚举值 (示例)**:  
  * INVALID\_INPUT("BIE001", "无效输入参数")  
  * UNAUTHENTICATED("BIE002", "用户未认证")  
  * PERMISSION\_DENIED("BIE003", "权限不足")  
  * RESOURCE\_NOT\_FOUND("BIE004", "请求资源未找到")  
  * DATABASE\_ERROR("BIE005", "数据库操作异常")  
  * LLM\_ERROR("BIE006", "大模型调用异常")  
  * INTERNAL\_SERVER\_ERROR("BIE999", "系统内部错误")  
* **核心方法**:  
  * String getCode()  
  * String getDescription()

### **4\. com.qding.chatbi.common.exception (自定义通用异常)**

所有自定义异常应继承自 RuntimeException 或特定的受检异常基类（如果适用）。

#### **4.1. ChatBiException.java**

* **用途**: 项目中所有自定义业务异常的基类。  
* **属性**:  
  * String errorCode (可选): 对应 ErrorCode 中的 code。  
  * String errorMessage: 错误消息。  
* **构造方法**:  
  * ChatBiException(String errorMessage)  
  * ChatBiException(String errorMessage, Throwable cause)  
  * ChatBiException(ErrorCode errorCode)  
  * ChatBiException(ErrorCode errorCode, Throwable cause)  
  * ChatBiException(ErrorCode errorCode, String customMessage)  
  * ChatBiException(ErrorCode errorCode, String customMessage, Throwable cause)  
* **核心方法**:  
  * String getErrorCode()

#### **4.2. PermissionDeniedException.java**

* **用途**: 用户尝试访问无权限资源时抛出。  
* **继承**: ChatBiException  
* **构造方法**:  
  * PermissionDeniedException() (使用默认的 ErrorCode.PERMISSION\_DENIED)  
  * PermissionDeniedException(String message)

#### **4.3. InvalidInputException.java**

* **用途**: 请求参数校验失败时抛出。  
* **继承**: ChatBiException  
* **构造方法**:  
  * InvalidInputException(String message) (使用默认的 ErrorCode.INVALID\_INPUT 并附加消息)

#### **4.4. ResourceNotFoundException.java**

* **用途**: 请求的资源不存在时抛出。  
* **继承**: ChatBiException  
* **构造方法**:  
  * ResourceNotFoundException(String resourceName, Object resourceId) (例如: "Dataset with ID 123 not found")

### **5\. com.qding.chatbi.common.util (通用工具类)**

这些类通常包含静态方法。

#### **5.1. JsonUtil.java**

* **用途**: 提供JSON序列化和反序列化的帮助方法。通常封装 Jackson 或 Gson。  
* **核心方法 (静态)**:  
  * static String toJson(Object object)  
  * static \<T\> T fromJson(String json, Class\<T\> clazz)  
  * static \<T\> T fromJson(String json, TypeReference\<T\> typeReference) (用于处理泛型集合)

#### **5.2. DateUtil.java**

* **用途**: 提供日期和时间处理的常用方法。  
* **核心方法 (静态, 示例)**:  
  * static String formatDate(Date date, String pattern)  
  * static Date parseDate(String dateStr, String pattern)  
  * static LocalDateTime now()

#### **5.3. StringUtil.java**

* **用途**: 提供字符串操作的常用方法。  
* **核心方法 (静态, 示例)**:  
  * static boolean isBlank(String str)  
  * static boolean isNotBlank(String str)  
  * static String defaultIfBlank(String str, String defaultStr)

### **6\. com.qding.chatbi.common.constant (通用常量)**

#### **6.1. CommonConstants.java**

* **用途**: 定义项目中广泛使用的常量。  
* **常量声明 (示例)**:  
  * public static final String TRACE\_ID\_HEADER \= "X-Trace-Id";  
  * public static final String DEFAULT\_USER \= "system";

以上是 chatbi-common 模块的详细设计。请您审阅，看看是否清晰、全面，是否能很好地作为 Jules 生成代码的 Prompt。我们可以针对任何一个类或细节进行更深入的讨论。