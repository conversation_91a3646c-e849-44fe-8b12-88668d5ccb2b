<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <parent>
        <groupId>com.qding.chatbi</groupId>
        <artifactId>chatbi-service</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-agent-core</artifactId>
    <packaging>jar</packaging>

    


    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- H2 Database for testing -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Internal Project Dependencies -->
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-metadata-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-knowledge-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-data-access-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- LangChain4j Dependencies (versions managed by BOM) -->
      
        <!-- https://mvnrepository.com/artifact/dev.langchain4j/langchain4j -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/dev.langchain4j/langchain4j-core -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-core</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/dev.langchain4j/langchain4j-milvus -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-milvus</artifactId>
        </dependency>
        <!-- Add DashScope Dependency -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-dashscope</artifactId>
        </dependency>
        <!-- OkHttp for custom client and logging -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <!-- Database Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Lombok (managed by parent POM) -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- Jackson databind (usually included with spring-boot-starter-web, but good to ensure) -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- JSR-250 Annotations -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>

    </dependencies>

</project>
