# ChatBI 行级权限控制设计文档

## 📋 概述

基于现有的 `UserRoleService` 接口，设计并实现了行级数据权限控制方案，确保用户只能查看其角色权限范围内的数据行。

## 🏗️ 架构设计

### 1. 核心组件

```
UserRoleService (现有)
    ↓
RowLevelSecurityManager (新增)
    ↓
QuerySecurityEnhancer (新增)
    ↓
增强后的SQL查询
```

### 2. 权限控制流程

```mermaid
graph TD
    A[用户查询请求] --> B[获取用户角色]
    B --> C[查找角色权限规则]
    C --> D[生成过滤条件]
    D --> E[注入SQL]
    E --> F[执行安全查询]
    F --> G[返回过滤数据]
```

## 🔧 实现方案

### 1. 权限规则类型

| 规则类型 | 说明 | 示例 |
|---------|------|------|
| DEPARTMENT | 基于部门过滤 | `department = '销售部'` |
| REGION | 基于区域过滤 | `region = '华北区'` |
| USER_ID | 基于用户ID过滤 | `created_by = 'user001'` |
| CUSTOM_SQL | 自定义SQL条件 | `status = '已审核' AND department = '${user.department}'` |
| MULTI_VALUE | 多值过滤 | `region IN ('华北区', '华东区')` |

### 2. 用户上下文属性

- **userId**: 用户ID
- **userName**: 用户姓名
- **department**: 部门
- **region**: 区域
- **managerId**: 上级用户ID

### 3. 权限规则配置示例

```java
// 销售人员：只能看自己区域的数据
ROLE_PERMISSION_RULES.put("销售人员", Arrays.asList(
    new RowLevelRule(RuleType.REGION, "region", null, null, null, null)
));

// 财务人员：可以看已审核的数据
ROLE_PERMISSION_RULES.put("财务人员", Arrays.asList(
    new RowLevelRule(RuleType.CUSTOM_SQL, null, null, 
        "status = '已审核' AND department = ${user.department}", null, null)
));

// 高级管理层：可以看指定区域的数据
ROLE_PERMISSION_RULES.put("高级管理层", Arrays.asList(
    new RowLevelRule(RuleType.MULTI_VALUE, "region", null, null, 
        Arrays.asList("华北区", "华东区", "华南区"), null)
));
```

## 🚀 使用方法

### 1. 在查询服务中集成

```java
@Service
public class DataQueryService {
    
    @Autowired
    private QuerySecurityEnhancer querySecurityEnhancer;
    
    public List<Map<String, Object>> executeQuery(String sql, String userId, Long datasetId) {
        // 应用行级权限控制
        String secureSQL = querySecurityEnhancer.enhanceQuerySecurity(sql, userId, datasetId);
        
        // 执行增强后的SQL
        return jdbcTemplate.queryForList(secureSQL);
    }
}
```

### 2. 在Controller中使用

```java
@RestController
public class DataController {
    
    @Autowired
    private QuerySecurityEnhancer querySecurityEnhancer;
    
    @PostMapping("/query")
    public ResponseEntity<?> query(@RequestBody QueryRequest request) {
        String userId = getCurrentUserId();
        
        // 验证权限
        if (!querySecurityEnhancer.validateQueryPermission(userId, request.getDatasetId())) {
            return ResponseEntity.status(403).body("无访问权限");
        }
        
        // 执行安全查询
        String secureSQL = querySecurityEnhancer.enhanceQuerySecurity(
            request.getSql(), userId, request.getDatasetId());
            
        // ... 执行查询逻辑
    }
}
```

## 📊 权限控制效果

### 原始SQL
```sql
SELECT * FROM sales_data ORDER BY amount DESC
```

### 不同角色的增强结果

#### 销售人员 (user001, 华北区)
```sql
SELECT * FROM sales_data WHERE (region = '华北区') ORDER BY amount DESC
```

#### 财务人员 (user003, 财务部)
```sql
SELECT * FROM sales_data WHERE (status = '已审核' AND department = '财务部') ORDER BY amount DESC
```

#### 高级管理层
```sql
SELECT * FROM sales_data WHERE (region IN ('华北区','华东区','华南区')) ORDER BY amount DESC
```

#### 无权限用户
```sql
SELECT * FROM sales_data WHERE 1=0 ORDER BY amount DESC
```

## 🔒 安全特性

### 1. 防SQL注入
- 使用参数化查询
- 过滤危险关键字
- 验证SQL语法

### 2. 权限验证
- 用户角色验证
- 数据集访问权限检查
- 异常情况拒绝访问

### 3. 审计日志
- 记录权限应用情况
- 记录查询增强过程
- 异常情况告警

## 📈 扩展性

### 1. 数据库存储
当前使用内存配置，可扩展为数据库存储：

```sql
-- 行级权限规则表
CREATE TABLE row_level_permissions (
    id BIGINT PRIMARY KEY,
    role_name VARCHAR(100),
    dataset_id BIGINT,
    rule_type VARCHAR(50),
    column_name VARCHAR(100),
    sql_condition TEXT,
    allowed_values JSON,
    enabled BOOLEAN DEFAULT TRUE
);

-- 用户上下文表
CREATE TABLE user_contexts (
    id BIGINT PRIMARY KEY,
    user_id VARCHAR(100) UNIQUE,
    department VARCHAR(100),
    region VARCHAR(100),
    manager_id VARCHAR(100),
    extended_attributes JSON
);
```

### 2. 缓存优化
- Redis缓存用户角色
- 缓存权限规则
- 缓存用户上下文

### 3. 动态配置
- 支持运行时修改权限规则
- 支持权限规则版本管理
- 支持A/B测试

## 🧪 测试用例

### 1. 单元测试
```java
@Test
public void testRowLevelSecurity() {
    String originalSql = "SELECT * FROM sales_data";
    String userId = "user001";
    Long datasetId = 1L;
    
    String result = querySecurityEnhancer.enhanceQuerySecurity(originalSql, userId, datasetId);
    
    assertThat(result).contains("WHERE");
    assertThat(result).contains("region = '华北区'");
}
```

### 2. 集成测试
```java
@Test
public void testEndToEndSecurity() {
    // 模拟不同角色用户的查询
    // 验证返回数据的正确性
}
```

## 📝 部署说明

### 1. 配置要求
- Spring Boot 2.x+
- 现有的 UserRoleService 实现
- 数据库连接（可选）

### 2. 启用步骤
1. 添加新的Service类到Spring容器
2. 在查询服务中注入 QuerySecurityEnhancer
3. 配置用户上下文数据源
4. 配置权限规则数据源

### 3. 监控指标
- 权限应用成功率
- 查询增强耗时
- 权限拒绝次数
- 异常情况统计

## 🎯 总结

该行级权限控制方案具有以下优势：

1. **无侵入性**: 基于现有 UserRoleService，无需修改现有代码
2. **灵活配置**: 支持多种权限规则类型
3. **安全可靠**: 多层安全验证，异常时拒绝访问
4. **易于扩展**: 支持数据库存储和缓存优化
5. **性能友好**: SQL级别过滤，减少数据传输

通过该方案，可以实现细粒度的数据访问控制，确保用户只能看到其权限范围内的数据行。
