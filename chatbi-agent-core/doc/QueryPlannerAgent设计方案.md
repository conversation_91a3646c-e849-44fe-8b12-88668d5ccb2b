# **查询规划模块 v2.0 设计文档**

## **1\. 概述**

为了解决 v1.0 中基于规则拼接SQL导致开发复杂、灵活性差的问题，v2.0 方案将转向利用大语言模型（LLM）的强大代码生成能力来动态生成SQL。

此方案通过为LLM提供高度结构化、精确无误的上下文信息（包括数据库方言、表结构、字段含义和用户查询意图），引导其生成安全、准确且高效的SQL查询。这极大地降低了代码实现的复杂度，同时显著提升了系统处理复杂查询的能力。

## **2\. 核心工作流**

QueryPlannerAgent 的工作流将调整为以下步骤：

| 步骤 | 名称 | 描述 | 输出 | 负责人 |
| :---- | :---- | :---- | :---- | :---- |
| **1** | **数据集选择** | (与v1.0相同) 根据NLU意图和用户权限，从候选数据集中选择匹配度最高的 **唯一** 数据集。 | Dataset 对象 | QueryPlannerAgent |
| **2** | **上下文构建** | (与v1.0相同) 将NLU意图中的业务字段（指标、维度、筛选条件）精确映射到选定数据集的技术字段名或表达式上。 | 映射后的字段列表 | QueryPlannerAgent |
| **3** | **SQL生成提示词构建** | **(核心变更)** 使用一个专用的提示词模板 (sql\_generation.txt)，将 **数据库方言**、**数据集的DDL**、**字段业务描述**、**用户问题**、**结构化意图**、**查询范例(可选)** 等信息，动态地、结构化地填入模板，形成最终的Prompt。 | 发给LLM的完整Prompt字符串 | SqlGenerationChain |
| **4** | **调用LLM生成SQL** | 调用大语言模型（如Qwen），并要求它 **只返回** 一个格式化好的SQL查询字符串。 | LLM生成的SQL字符串 | SqlGenerationChain |
| **5** | **SQL验证与清洗** | **(新增安全层)** 对LLM返回的SQL进行基础的语法和安全检查。例如，使用正则表达式禁止DROP, UPDATE, DELETE等危险关键字，并校验SQL中引用的表名和字段名是否都属于第1步中选定的数据集。 | 安全、合规的SQL字符串 | QueryPlannerAgent |
| **6** | **生成最终查询计划** | 将验证后的SQL和整个决策过程的描述（Thought Process）封装到 QueryPlan 对象中，并返回。 | QueryPlan 对象 | QueryPlannerAgent |

## **3\. 关键组件设计 (SqlGenerationChain)**

我们将引入一个新的类 SqlGenerationChain，专门负责第3和第4步。它将是一个独立的、可复用的组件。

### **3.1. 输入**

SqlGenerationChain 接收一个包含所有SQL生成所需上下文的DTO，例如：

public class SqlGenerationContext {  
    private String dbDialect; // "MySQL", "ClickHouse"  
    private String tableDdl; // "CREATE TABLE daily\_sales\_summary ( ... )"  
    private String userQuestion; // "查一下上个月北京的GMV"  
    private StructuredQueryIntent structuredIntent; // NLU的输出  
    private List\<QueryExample\> relevantExamples; // (可选) 从知识库中检索到的相关查询范例  
    private String currentDate; // (新增) "2023-10-27"，用于处理相对时间  
}

### **3.2. 提示词模板 (sql\_generation.txt)**

这是该方案的灵魂。一个好的模板应该像下面这样：

You are an expert {db\_dialect} data analyst. Your task is to generate a SQL query based on the user's question and the provided context.  
You MUST follow these rules:  
1\.  ONLY use the tables and columns provided in the 'Table Schema' section. Do not hallucinate any table or column names.  
2\.  The generated SQL must be valid for the {db\_dialect} dialect.  
3\.  For any relative date queries like 'last month' or 'this week', you must use '{current\_date}' as the anchor date for calculation.  
4\.  Pay close attention to the definition of calculated metrics in the 'Column Business Semantics' section and use their formulas directly in the SQL.  
5\.  You should ONLY return a single, complete SQL query within a \`\`\`sql ... \`\`\` block and nothing else.

\---  
\*\*Current Date (for reference):\*\*  
{current\_date}

\---  
\*\*Table Schema:\*\*  
{table\_ddl}

\---  
\*\*Column Business Semantics:\*\*  
(除了描述外，还需明确指出计算指标的公式)  
Example:  
\- revenue: The total income.  
\- cost: The total cost.  
\- gross\_margin: This is a calculated metric, defined as \`(revenue \- cost) / revenue\`. DO NOT select it directly.  
{column\_semantics}

\---  
\*\*User's Question:\*\*  
{user\_question}

\---  
\*\*(Optional) Relevant Query Examples:\*\*  
{relevant\_examples}

\---  
\*\*Structured Intent (for reference):\*\*  
{structured\_intent\_json}

\---  
Based on all the information above, please generate the SQL query now.

### **3.3. 输出**

SqlGenerationChain 的 execute() 方法将返回一个纯净的SQL字符串。

## **4\. 优势**

* **降低复杂度**: 无需手动编写复杂的SQL拼接逻辑。  
* **提升灵活性**: 可以自然地支持各种复杂的SQL语法，如JOINs, Subqueries, Window Functions等，只需在Prompt中提供正确的信息即可。  
* **增强鲁棒性**: 通过提供精确的上下文（特别是DDL），极大地减少了LLM“幻觉”的概率。  
* **易于维护**: 当需要调整SQL生成逻辑时，通常只需要修改提示词模板 (sql\_generation.txt)，而不需要修改Java代码，迭代速度更快。  
* **安全可控**: 新增的SQL验证步骤确保了即使LLM出错，也不会执行危险或越权的操作。