# ChatBI Agent Core 日志优化说明

## 优化目标

重点保留和添加对提示词问题定位有帮助的日志，简化或去掉其他业务流程日志。

## 优化原则

1. **保留关键日志**：

   - 【提示词输入】- 传递给提示词的参数和上下文
   - 【提示词变量】- 模板变量和业务决策信息
   - 【提示词内容】- 完整的 Prompt 内容（debug 级别）
   - 【提示词输出】- LLM 的响应和提取的 SQL
   - 【NLU 结果】- NLU 的理解结果

2. **简化业务日志**：

   - 去掉冗余的分隔符（========）
   - 简化成功信息，将详细信息降级为 debug
   - 去掉 emoji 装饰符
   - 合并多行日志为单行

3. **优化错误日志**：
   - 保留错误信息但简化格式
   - 去掉多余的错误描述

## 关键日志标记

使用统一的标记来标识与提示词调试相关的日志：

- `【提示词输入】` - 输入给提示词的信息
- `【提示词变量】` - 模板变量
- `【提示词内容】` - 完整 Prompt（debug 级别）
- `【提示词输出】` - LLM 响应
- `【NLU结果】` - NLU 理解结果

## 优化后的效果

### QueryPlannerAgentImpl

```java
// 优化前
log.info("🔧 开始优化后的查询规划: 意图={}, 查询='{}'", ...);
log.info("========== 阶段1: 验证NLU决策结果 ==========");
log.info("✅ NLU决策验证通过:");
log.info("   - 查询类型: {}", intent.getQueryType());
// ... 多行日志

// 优化后
log.debug("开始查询规划: 查询='{}'", ...);
log.info("【提示词输入】NLU决策信息:");
log.info("  查询类型: {}, 需要聚合: {}, 聚合粒度: {}", ...);
```

### SqlGenerationChain

```java
// 优化前
log.info("========== SqlGenerationChain.execute 开始执行优化版SQL生成 ==========");
log.info("📥 SQL生成上下文信息:");
log.info("   - 数据库类型: {}", context.dbDialect);
// ... 多行日志

// 优化后
log.debug("开始SQL生成: 数据库={}, 数据集={}, 问题='{}'", ...);
log.info("【提示词变量】传递给模板的关键业务决策:");
log.info("  query_type: {}, aggregation: {}, granularity: {}", ...);
```

### 日志级别建议

- **INFO 级别**：仅保留对提示词调试有帮助的关键信息
- **DEBUG 级别**：业务流程信息、详细的 Prompt 内容
- **WARN 级别**：SQL 验证警告（如 NLU 要求与 SQL 不匹配）
- **ERROR 级别**：错误信息（简化格式）

## 使用建议

1. **生产环境**：日志级别设为 INFO，只记录关键的提示词相关信息
2. **调试提示词**：日志级别设为 DEBUG，可以看到完整的 Prompt 内容
3. **快速定位**：通过搜索【提示词】相关标记快速定位问题

## 配置示例

```yaml
logging:
  level:
    com.qding.chatbi.agent: INFO # 生产环境
    # com.qding.chatbi.agent: DEBUG  # 调试环境
```

### 六、注意事项

1. **生产环境配置**：在生产环境中，建议将日志级别设置为 INFO，只保留关键的提示词调试信息
2. **性能影响**：减少日志输出对系统性能有积极影响，特别是在高并发场景下
3. **日志存储**：由于保留了关键的提示词内容，需要注意日志存储空间的管理
4. **敏感信息**：在输出用户查询和 LLM 响应时，注意是否包含敏感信息

## 时间澄清规则更新（2025-01-26）

### 更新背景

发现系统在处理澄清回答时，没有正确执行时间澄清规则。例如：

- 用户查询"成本" → AI 澄清城市
- 用户回答"上海，成都" → AI 直接执行查询，没有澄清时间范围
- 生成的 SQL 缺少时间筛选条件

### 更新内容

在 `nlu_function_calling_agent.txt` 提示词中强化了时间澄清规则：

1. **时间澄清最高优先级**：
   - 任何包含指标的查询都必须有时间信息
   - 时间澄清优先级高于其他所有条件
2. **澄清回答场景特殊处理**：
   - 先合并查询上下文
   - 再次检查时间信息
   - 如果仍缺少时间，继续澄清
3. **多轮澄清支持**：
   - 可能需要多次澄清（先城市，后时间）
   - 每次澄清后都要重新检查是否满足所有条件

### 预期效果

- 用户查询"成本" → 澄清城市
- 用户回答"上海，成都" → 继续澄清时间范围
- 用户回答"本月" → 执行查询，SQL 包含时间筛选条件
