## **Chat BI \- LangChain4j Tools详细设计 (chatbi-agent-core)**

**模块路径**: com.qding.chatbi.agent.tool

### **1\. 定位与用途**

tool 包下的类封装了AI Agent与外部服务或内部复杂逻辑交互的功能，并将这些功能以LangChain4j的 @Tool 形式暴露给AI Agent（尤其是基于ReAct等模式的Agent）或Chains。这些Tools使得LLM能够动态地决定何时以及如何使用这些外部能力来完成用户的请求。每个Tool方法都应该有清晰的描述，以便LLM能够理解其用途和参数。

在我们的自定义编排流程中（由ConversationManager控制），这些Tool类作为普通的Spring Bean被注入到需要的服务中，并直接调用其方法。@Tool注解此时主要起到**文档化**和**为未来兼容更自主的LangChain4j Agent**的作用。

### **2\. DatasetTools.java**

* **用途**: 提供与数据集元数据、Schema信息以及权限校验相关的工具方法。  
* **注解**: @Component (使其成为Spring管理的Bean，以便注入依赖)  
* **依赖注入**:  
  * private final MetadataServiceClient metadataServiceClient; (假设这是调用 chatbi-metadata-service 的客户端接口。如果 chatbi-metadata-service 是作为库依赖，则直接注入其服务类，如 DatasetMetadataService 和 PermissionService)。  
  * private final JsonUtil jsonUtil;

* #### **核心方法:**   **2.1. getDatasetSchemaAndPermissions**

  * **@Tool 描述**: "获取指定数据集的详细Schema信息（包括字段名、业务名、数据类型、描述）以及当前用户对该数据集的访问权限。如果用户无权访问，会明确指出。仅当需要了解特定数据集的结构以构建查询时使用。"  
  * **方法签名**:  
    package com.qding.chatbi.agent.tool;

    import com.qding.chatbi.common.dto.DatasetInfoDTO; // 假设从common包获取  
    import com.qding.chatbi.common.dto.ColumnInfoDTO;  
    import com.qding.chatbi.common.exception.PermissionDeniedException;  
    import com.qding.chatbi.common.exception.ResourceNotFoundException;  
    import com.qding.chatbi.agent.integration.MetadataServiceClient; // 假设的客户端接口  
    import dev.langchain4j.agent.tool.Tool;  
    import org.springframework.stereotype.Component;  
    import java.util.List;  
    import java.util.stream.Collectors;

    @Component  
    public class DatasetTools {

        private final MetadataServiceClient metadataServiceClient;  
        // private final JsonUtil jsonUtil; // 如果返回复杂对象并期望LLM处理JSON字符串

        public DatasetTools(MetadataServiceClient metadataServiceClient) {  
            this.metadataServiceClient \= metadataServiceClient;  
            // this.jsonUtil \= jsonUtil;  
        }

        @Tool("获取指定数据集的详细Schema信息（包括字段名、业务名、数据类型、描述）以及当前用户对该数据集的访问权限。如果用户无权访问，会明确指出。datasetIdentifier可以是数据集的ID或业务名称。userRoles是当前用户的角色列表。")  
        public String getDatasetSchemaAndPermissions(String datasetIdentifier, List\<String\> userRoles) {  
            try {  
                // 1\. 调用 MetadataServiceClient 获取数据集的详细信息和权限状态  
                // 这个服务内部会进行权限校验  
                DatasetInfoDTO datasetInfo \= metadataServiceClient.getDatasetDetailsWithAccessCheck(datasetIdentifier, userRoles);

                if (datasetInfo \== null) {  
                    return "错误：未能找到数据集 '" \+ datasetIdentifier \+ "'。";  
                }

                // 2\. 格式化为LLM易于理解的文本  
                StringBuilder sb \= new StringBuilder();  
                sb.append("数据集 '").append(datasetInfo.getDatasetName()).append("' (ID: ").append(datasetInfo.getDatasetId()).append("):\\n");  
                sb.append("  描述: ").append(datasetInfo.getDescription()).append("\\n");  
                sb.append("  字段列表:\\n");  
                if (datasetInfo.getColumns() \== null || datasetInfo.getColumns().isEmpty()) {  
                    sb.append("    \- (该数据集没有配置可查询的字段)\\n");  
                } else {  
                    for (ColumnInfoDTO column : datasetInfo.getColumns()) {  
                        sb.append("    \- 业务名: '").append(column.getColumnName()).append("'\\n");  
                        // 假设 ColumnInfoDTO 已经扩展了包含 technicalNameOrExpression 和 allowedAggregations  
                        // 在 chatbi\_common\_module\_design\_v1.xml 中 ColumnInfoDTO 的 semanticType 已改为 SemanticType 枚举  
                        // 这里需要从 ColumnInfoDTO 获取更多技术细节以构建schema给LLM  
                        sb.append("      技术名/表达式: '").append(column.getTechnicalNameOrExpression()).append("'\\n");   
                        sb.append("      数据类型: ").append(column.getDataType()).append("\\n");  
                        sb.append("      语义类型: ").append(column.getSemanticType() \!= null ? column.getSemanticType().name() : "N/A").append("\\n");  
                        sb.append("      描述: ").append(column.getDescription()).append("\\n");  
                        if (column.getSemanticType() \!= null && column.getSemanticType().name().equalsIgnoreCase("METRIC")   
                            && column.getAllowedAggregations() \!= null && \!column.getAllowedAggregations().isEmpty()){  
                            sb.append("      允许的聚合: ").append(String.join(", ", column.getAllowedAggregations())).append("\\n");  
                        }  
                    }  
                }  
                return sb.toString();

            } catch (PermissionDeniedException e) {  
                return "错误：用户角色 " \+ userRoles \+ " 无权访问数据集 '" \+ datasetIdentifier \+ "'。";  
            } catch (ResourceNotFoundException e) {  
                return "错误：数据集 '" \+ datasetIdentifier \+ "' 未找到。";  
            } catch (Exception e) {  
                // log.error("Error in getDatasetSchemaAndPermissions for {}: {}", datasetIdentifier, e.getMessage(), e);  
                return "错误：获取数据集 '" \+ datasetIdentifier \+ "' 的Schema信息时发生内部错误。";  
            }  
        }  
    }

  * **返回**: 一个描述性的字符串，包含数据集名称、描述、字段列表（字段业务名、技术名/表达式、数据类型、语义类型、描述、允许的聚合）。如果无权访问或未找到，则返回相应的错误信息文本。  
  * **MetadataServiceClient.getDatasetDetailsWithAccessCheck**: 这个假设的方法在其内部会：  
    1. 根据 datasetIdentifier (可能是ID或名称) 查找 QueryableDataset。  
    2. 检查 userRoles 是否有权限访问此数据集 (通过 DatasetAccessPermissions 表)。若无权限，抛出 PermissionDeniedException。  
    3. 若有权限，则加载该数据集的 DatasetColumns 完整信息，并组装成包含这些信息的 DatasetInfoDTO（或者一个更详细的内部DTO，然后在这里转换为文本）。**ColumnInfoDTO (在chatbi-common中定义) 需要扩展以包含 technicalNameOrExpression 和 allowedAggregations 字段，以便此Tool能提供足够信息给LLM。**

### **3\. KnowledgeBaseTools.java**

* **用途**: 提供与知识库（Milvus向量数据库）交互的工具方法，用于检索相似查询示例和业务术语。  
* **注解**: @Component  
* **依赖注入**:  
  * private final KnowledgeBaseClient knowledgeBaseClient; (假设这是调用 chatbi-knowledge-service 的客户端接口，其内部封装了Milvus的调用)。  
  * private final JsonUtil jsonUtil;

* #### **核心方法:**   **3.1. searchSimilarQueries**

  * **@Tool 描述**: "根据用户当前的查询文本，从知识库中检索最相似的N个已验证的查询示例。这些示例可以帮助理解用户意图或构建更准确的查询。userQuery是用户的原始文本，topK是要返回的示例数量。"  
  * **方法签名**:  
    // package com.qding.chatbi.agent.tool;  
    // import com.qding.chatbi.agent.integration.KnowledgeBaseClient;  
    // import com.qding.chatbi.common.dto.QueryExampleDTO; // 假设的DTO  
    // import dev.langchain4j.agent.tool.Tool;  
    // import org.springframework.stereotype.Component;  
    // import java.util.List;  
    // import java.util.Map;  
    // import java.util.stream.Collectors;

    // @Component  
    // public class KnowledgeBaseTools {  
    //     private final KnowledgeBaseClient knowledgeBaseClient;  
    //     public KnowledgeBaseTools(KnowledgeBaseClient knowledgeBaseClient) { this.knowledgeBaseClient \= knowledgeBaseClient; }

        @Tool("根据用户当前的查询文本，从知识库中检索最相似的N个已验证的查询示例。这些示例可以帮助理解用户意图或构建更准确的查询。userQuery是用户的原始文本，topK是要返回的示例数量。")  
        public String searchSimilarQueries(String userQuery, int topK) {  
            try {  
                //  List\<QueryExampleDTO\> examples \= knowledgeBaseClient.findSimilarQueryExamples(userQuery, topK);  
                //  if (examples \== null || examples.isEmpty()) {  
                //      return "未找到相似的查询示例。";  
                //  }  
                //  return examples.stream()  
                //          .map(ex \-\> "问题: " \+ ex.getUserQuestion() \+ "\\n查询表示: " \+ ex.getTargetQueryRepresentation())  
                //          .collect(Collectors.joining("\\n---\\n"));  
                //  模拟返回  
                if ("查北京GMV".equals(userQuery)) {  
                    return "问题: 查询北京地区上个月的总GMV\\n查询表示: {\\"metrics\\":\[\\"GMV\\"\], \\"filters\\":\[{\\"field\\":\\"城市\\",\\"operator\\":\\"=\\",\\"value\\":\\"北京\\"}, {\\"field\\":\\"时间\\",\\"operator\\":\\"=\\",\\"value\\":\\"上个月\\"}\]}";  
                }  
                return "未找到与 '" \+ userQuery \+ "' 相似的查询示例。";  
            } catch (Exception e) {  
                // log.error("Error in searchSimilarQueries for '{}': {}", userQuery, e.getMessage(), e);  
                return "错误：检索相似查询示例时发生内部错误。";  
            }  
        }  
    // }

  * **返回**: 一个多行字符串，每个示例包含用户问题和对应的结构化查询表示或SQL。如果未找到，则返回提示信息。  
  * **KnowledgeBaseClient.findSimilarQueryExamples**: 内部将 userQuery 转换为向量，然后在Milvus中搜索 QueryExamples 表对应的向量，返回最相似的 topK 个结果 (这些结果应包含 user\_question 和 target\_query\_representation 字段)。

  **3.2. searchBusinessTerms**

  * #### **@Tool 描述: "查询指定的业务术语或黑话在知识库中的定义、解释或其对应的标准数据概念。term是要查询的业务术语。"**

  * **方法签名**:  
    // ... (在 KnowledgeBaseTools 类中) ...  
    @Tool("查询指定的业务术语或黑话在知识库中的定义、解释或其对应的标准数据概念。term是要查询的业务术语。")  
    public String searchBusinessTerms(String term) {  
        try {  
            // BusinessTermDTO termDefinition \= knowledgeBaseClient.findBusinessTerm(term);  
            // if (termDefinition \== null) {  
            //     return "术语库中未找到 '" \+ term \+ "' 的相关定义。";  
            // }  
            // return "术语: " \+ term \+ "\\n定义: " \+ termDefinition.getDefinition() \+  
            //        (termDefinition.getMappingInfo() \!= null ? "\\n映射到: " \+ termDefinition.getMappingInfo() : "");  
            // 模拟返回  
            if ("GMV".equalsIgnoreCase(term)) {  
                return "术语: GMV\\n定义: 商品交易总额 (Gross Merchandise Volume)，通常指网站或平台的总销售额。\\n映射到: 指标'总销售额'";  
            }  
            return "术语库中未找到 '" \+ term \+ "' 的相关定义。";  
        } catch (Exception e) {  
            // log.error("Error in searchBusinessTerms for '{}': {}", term, e.getMessage(), e);  
            return "错误：查询业务术语 '" \+ term \+ "' 时发生内部错误。";  
        }  
    }

  * **返回**: 包含术语定义和映射信息的字符串。如果未找到，则返回提示。  
  * **KnowledgeBaseClient.findBusinessTerm**: 内部可能对 term 进行精确或模糊匹配查询 BusinessTerminology 表（获取其 description 和 standard\_reference\_name），或者如果术语也向量化了，则进行向量搜索。

### **4\. DataAccessTools.java**

* **用途**: 提供执行SQL查询并获取结果的工具方法。  
* **注解**: @Component  
* **依赖注入**:  
  * private final DataAccessServiceClient dataAccessServiceClient; (假设这是调用 chatbi-data-access-service 的客户端接口)。  
  * private final JsonUtil jsonUtil;

* #### **核心方法:**   **4.1. executeSqlAndGetResponse**

  * **@Tool 描述**: "执行给定的SQL查询语句到指定的数据源，并返回查询结果。sqlQuery是待执行的SQL语句，databaseSourceId是目标数据源的ID。注意：此工具用于数据查询，不应用于数据修改操作。返回结果是JSON字符串格式的数据表，或者错误信息。"  
  * **方法签名**:  
    // package com.qding.chatbi.agent.tool;  
    // import com.qding.chatbi.common.dto.QueryResult;  
    // import com.qding.chatbi.agent.integration.DataAccessServiceClient;  
    // import com.qding.chatbi.common.util.JsonUtil;  
    // import dev.langchain4j.agent.tool.Tool;  
    // import org.springframework.stereotype.Component;

    // @Component  
    // public class DataAccessTools {  
    //     private final DataAccessServiceClient dataAccessServiceClient;  
    //     private final JsonUtil jsonUtil;  
    //     public DataAccessTools(DataAccessServiceClient dataAccessServiceClient, JsonUtil jsonUtil) {  
    //         this.dataAccessServiceClient \= dataAccessServiceClient;  
    //         this.jsonUtil \= jsonUtil;  
    //     }

        @Tool("执行给定的SQL查询语句到指定的数据源，并返回查询结果。sqlQuery是待执行的SQL语句，databaseSourceId是目标数据源的ID。注意：此工具用于数据查询，不应用于数据修改操作。返回结果是JSON字符串格式的数据表，或者错误信息。")  
        public String executeSqlAndGetResponse(String sqlQuery, String databaseSourceId) {  
            try {  
                // QueryResult result \= dataAccessServiceClient.executeQuery(sqlQuery, databaseSourceId);  
                // return jsonUtil.toJson(result); // 将QueryResult对象序列化为JSON字符串

                // 模拟返回  
                if (sqlQuery.toLowerCase().contains("select count(\*) from sales\_data")) {  
                    QueryResult mockResult \= new QueryResult();  
                    mockResult.setColumnHeaders(List.of("COUNT(\*)"));  
                    mockResult.setRows(List.of(List.of(100L)));  
                    mockResult.setQuerySql(sqlQuery);  
                    return jsonUtil.toJson(mockResult);  
                } else if (sqlQuery.toLowerCase().contains("error\_test\_query")){  
                    QueryResult errorResult \= new QueryResult();  
                    errorResult.setQuerySql(sqlQuery);  
                    errorResult.setErrorMessage("模拟数据库执行错误: 语法无效");  
                    return jsonUtil.toJson(errorResult);  
                }  
                QueryResult mockResult \= new QueryResult();  
                mockResult.setQuerySql(sqlQuery);  
                mockResult.setErrorMessage("模拟执行成功但无数据");  
                return jsonUtil.toJson(mockResult);

            } catch (Exception e) {  
                // log.error("Error in executeSqlAndGetResponse for SQL '{}' on source '{}': {}", sqlQuery, databaseSourceId, e.getMessage(), e);  
                QueryResult errorResult \= new QueryResult();  
                errorResult.setQuerySql(sqlQuery);  
                errorResult.setErrorMessage("执行SQL时发生严重内部错误: " \+ e.getMessage());  
                try {  
                    return jsonUtil.toJson(errorResult);  
                } catch (Exception ex) {  
                    return "{\\"errorMessage\\":\\"执行SQL时发生严重内部错误且结果无法序列化\\"}";  
                }  
            }  
        }  
    // }

  * **返回**: QueryResult 对象的JSON字符串表示。包含列头、数据行、执行的SQL以及可能的错误信息。  
  * **DataAccessServiceClient.executeQuery**: 内部调用 chatbi-data-access-service 的 QueryExecutionService，该服务负责实际的数据库连接和查询执行，并返回一个标准的 QueryResult DTO。  
  * **结果大小限制与摘要**: 如果查询结果集可能非常大，直接返回完整的JSON字符串可能不切实际（会超出LLM上下文、网络传输慢等）。需要考虑：  
    * **chatbi-data-access-service层面进行截断**: 例如，默认只返回前N条记录。  
    * **Tool层面进行摘要**: 如果结果集大，Tool可以返回一个摘要，例如：“查询成功，共返回X条记录。前几条记录是：...。需要查看更多吗？”然后LLM可以决定是否需要进一步的交互或分页。**这会增加Tool的复杂性。**  
    * **初期简化**: 先假设结果集不会过大，直接返回 QueryResult 的JSON。

### **5\. 服务客户端接口 (假设，位于 com.qding.chatbi.agent.integration)**

如果 chatbi-metadata-service, chatbi-knowledge-service, chatbi-data-access-service 是作为独立的微服务部署的，那么 chatbi-agent-core 模块中会需要这些服务的客户端接口和实现 (例如使用Feign Client)。

* **MetadataServiceClient.java**:  
  * DatasetInfoDTO getDatasetDetailsWithAccessCheck(String datasetIdentifier, List\<String\> userRoles);  
* **KnowledgeBaseClient.java**:  
  * List\<QueryExampleDTO\> findSimilarQueryExamples(String userQuery, int topK); // QueryExampleDTO 需要在common中定义  
  * BusinessTermDTO findBusinessTerm(String term); // BusinessTermDTO 需要在common中定义  
* **DataAccessServiceClient.java**:  
  * QueryResult executeQuery(String sql, String databaseSourceId);

如果这些服务是作为库直接被 chatbi-agent-core 依赖，则Tools会直接注入这些库中的Service类。**为了简化初期开发，可以假设这些服务先作为库依赖，Tools直接注入其Service。**

以上是对 LangChain4j Tools 的详细设计。关键在于Tool的描述要清晰，输入输出要明确，并且内部逻辑能够正确调用相应的后端服务并处理结果。

请您审阅这部分内容。