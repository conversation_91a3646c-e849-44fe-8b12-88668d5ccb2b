// =========================================================================
// 模块: chatbi-common
// 职责: 定义所有模块共享的数据传输对象 (DTOs)。
// =========================================================================

package com.qding.chatbi.common.dto;

import com.qding.chatbi.common.enums.ResponseType;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * Agent最终返回给API层的标准响应结构。
 */
@Data
public class AgentResponse {
    private ResponseType responseType;
    private String responseText;
    private QueryResult queryResult;
    private DisplaySuggestion displaySuggestion;
    private List<ClarificationOption> clarificationOptions;
    private String thoughtProcess;
}

/**
 * 可视化展示建议DTO。
 * 由后端生成，供前端消费，以决定如何渲染数据。
 */
@Data
public class DisplaySuggestion {
    /**
     * 建议的图表类型: "table", "line_chart", "bar_chart", "pie_chart", "kpi_card"
     */
    private String chartType;
    /**
     * 建议的图表标题, e.g., "各城市销售额对比"
     */
    private String title;
    /**
     * 图表渲染所需的具体配置项, e.g., {"xAxis": "city", "yAxis": ["sales", "users"]}
     */
    private Map<String, Object> chartOptions;

    public DisplaySuggestion(String chartType, String title, Map<String, Object> chartOptions) {
        this.chartType = chartType;
        this.title = title;
        this.chartOptions = chartOptions;
    }
}


// =========================================================================
// 模块: chatbi-agent-core
// 职责: 实现Agent的核心业务逻辑。
// =========================================================================

package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.agent.dto.StructuredQueryIntent;

/**
 * ResponseGenerator 接口定义
 */
public interface ResponseGenerator {
    AgentResponse generate(QueryResult queryResult, StructuredQueryIntent intent, String datasetId);
}

// -------------------------------------------------------------------------

package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.DisplaySuggestion;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.enums.SemanticType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 可视化建议生成器服务。
 * 采用启发式规则，根据查询结果和元数据推荐最佳图表。
 */
@Service
public class VisualizationSuggester {

    private static final String DEFAULT_CHART_TYPE = "table";

    public DisplaySuggestion suggest(QueryResult result, DatasetInfoDTO datasetInfo) {
        if (result == null || CollectionUtils.isEmpty(result.getRows())) {
            return createDefaultSuggestion("查询结果为空");
        }

        Map<String, ColumnInfoDTO> columnMetadataMap = datasetInfo.getColumns().stream()
                .collect(Collectors.toMap(ColumnInfoDTO::getColumnName, col -> col, (a, b) -> a));

        List<ColumnInfoDTO> dimensions = new ArrayList<>();
        List<ColumnInfoDTO> metrics = new ArrayList<>();

        for (String columnName : result.getColumns()) {
            ColumnInfoDTO columnMeta = columnMetadataMap.get(columnName);
            if (columnMeta != null) {
                if (columnMeta.getSemanticType() == SemanticType.DIMENSION) {
                    dimensions.add(columnMeta);
                } else if (columnMeta.getSemanticType() == SemanticType.METRIC) {
                    metrics.add(columnMeta);
                }
            }
        }

        int dimCount = dimensions.size();
        int metricCount = metrics.size();

        if (dimCount == 0 && metricCount == 1 && result.getRows().size() == 1) {
            return buildKpiCardSuggestion(metrics.get(0));
        }

        if (dimCount == 1 && metricCount >= 1) {
            ColumnInfoDTO dimension = dimensions.get(0);
            if (isTimeType(dimension)) {
                return buildLineChartSuggestion(dimension, metrics);
            } else {
                if (metricCount == 1 && result.getRows().size() > 1 && result.getRows().size() <= 8) {
                    return buildPieChartSuggestion(dimension, metrics.get(0));
                }
                return buildBarChartSuggestion(dimension, metrics);
            }
        }
        
        return createDefaultSuggestion("多维度数据详情");
    }
    
    // ... (private helper methods are the same as in the previous turn)
    private DisplaySuggestion buildKpiCardSuggestion(ColumnInfoDTO metric) {
        return new DisplaySuggestion("kpi_card", metric.getBusinessName(), null);
    }
    private DisplaySuggestion buildLineChartSuggestion(ColumnInfoDTO timeDim, List<ColumnInfoDTO> metrics) {
        String title = String.format("%s随%s的变化趋势", metrics.stream().map(ColumnInfoDTO::getBusinessName).collect(Collectors.joining("、")), timeDim.getBusinessName());
        Map<String, Object> options = Map.of("xAxis", timeDim.getColumnName(), "yAxis", metrics.stream().map(ColumnInfoDTO::getColumnName).collect(Collectors.toList()));
        return new DisplaySuggestion("line_chart", title, options);
    }
    private DisplaySuggestion buildBarChartSuggestion(ColumnInfoDTO dim, List<ColumnInfoDTO> metrics) {
        String title = String.format("按%s分析%s", dim.getBusinessName(), metrics.stream().map(ColumnInfoDTO::getBusinessName).collect(Collectors.joining("、")));
        Map<String, Object> options = Map.of("xAxis", dim.getColumnName(), "yAxis", metrics.stream().map(ColumnInfoDTO::getColumnName).collect(Collectors.toList()));
        return new DisplaySuggestion("bar_chart", title, options);
    }
    private DisplaySuggestion buildPieChartSuggestion(ColumnInfoDTO dim, ColumnInfoDTO metric) {
        String title = String.format("%s的%s分布", dim.getBusinessName(), metric.getBusinessName());
        Map<String, Object> options = Map.of("category", dim.getColumnName(), "value", metric.getColumnName());
        return new DisplaySuggestion("pie_chart", title, options);
    }
    private DisplaySuggestion createDefaultSuggestion(String title) {
        return new DisplaySuggestion(DEFAULT_CHART_TYPE, title, Map.of());
    }
    private boolean isTimeType(ColumnInfoDTO column) {
        String dataType = column.getDataType().toUpperCase();
        return dataType.contains("DATE") || dataType.contains("TIME");
    }
}


// -------------------------------------------------------------------------


package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.dto.StructuredQueryIntent;
import com.qding.chatbi.agent.service.ResponseGenerator;
import com.qding.chatbi.agent.service.VisualizationSuggester;
import com.qding.chatbi.common.dto.*;
import com.qding.chatbi.common.enums.ResponseType;
import com.qding.chatbi.metadata.service.DatasetMetadataService;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.NumberFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ResponseGenerator 的实现类
 */
@Service
@Slf4j
public class ResponseGeneratorImpl implements ResponseGenerator {

    @Autowired
    private VisualizationSuggester visualizationSuggester;

    @Autowired
    private DatasetMetadataService datasetMetadataService;

    @Autowired
    private ChatLanguageModel chatModel;

    @Override
    public AgentResponse generate(QueryResult queryResult, StructuredQueryIntent intent, String datasetId) {
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getRows())) {
            return createEmptyResponse(queryResult);
        }

        DatasetInfoDTO datasetInfo = datasetMetadataService.getDatasetInfoById(datasetId);
        if (datasetInfo == null) {
            log.warn("Cannot find dataset metadata for id: {}, defaulting to table view.", datasetId);
            return createDefaultTableResponse(queryResult);
        }

        DisplaySuggestion suggestion = visualizationSuggester.suggest(queryResult, datasetInfo);
        String summaryText = generateSummary(queryResult, intent, suggestion);

        AgentResponse finalResponse = new AgentResponse();
        finalResponse.setResponseType(ResponseType.DATA);
        finalResponse.setResponseText(summaryText);
        finalResponse.setQueryResult(queryResult);
        finalResponse.setDisplaySuggestion(suggestion);

        return finalResponse;
    }
    
    private String generateSummary(QueryResult queryResult, StructuredQueryIntent intent, DisplaySuggestion suggestion) {
        if ("kpi_card".equals(suggestion.getChartType())) {
            Object value = queryResult.getRows().get(0).get(0);
            String metricName = intent.getMetrics().isEmpty() ? suggestion.getTitle() : intent.getMetrics().get(0);
            return String.format("您好，查询结果如下：%s 是 **%s**。", metricName, formatValue(value));
        }

        // 对于表格和图表，调用LLM生成更自然的摘要
        try {
            PromptTemplate template = PromptTemplate.from(
                "You are a professional data analyst. Based on the user's original question and the following data (in CSV format), please provide a concise, insightful summary in Chinese (within 100 characters).\n" +
                "Focus on the main findings, such as trends, top values, or significant comparisons.\n" +
                "--- User's Original Question: {{user_question}}\n" +
                "--- Data (CSV Format):\n{{data_csv}}\n" +
                "--- Your summary:"
            );

            Map<String, Object> variables = new HashMap<>();
            variables.put("user_question", intent.getOriginalQuery());
            variables.put("data_csv", toCsv(queryResult));
            
            Prompt prompt = template.apply(variables);
            return chatModel.generate(prompt.text());
        } catch (Exception e) {
            log.error("Failed to generate summary with LLM. Falling back to default.", e);
            return "为您找到了相关数据，" + suggestion.getTitle() + "，详情如下：";
        }
    }

    private String toCsv(QueryResult queryResult) {
        StringBuilder csv = new StringBuilder();
        csv.append(String.join(",", queryResult.getColumns())).append("\n");
        for (List<Object> row : queryResult.getRows()) {
            String rowStr = row.stream().map(String::valueOf).collect(Collectors.joining(","));
            csv.append(rowStr).append("\n");
        }
        return csv.toString();
    }
    
    private AgentResponse createEmptyResponse(QueryResult queryResult) {
        AgentResponse response = new AgentResponse();
        response.setResponseType(ResponseType.DATA);
        response.setResponseText("根据您的查询条件，我没有找到相关数据。您可以尝试调整筛选条件或查询范围。");
        response.setDisplaySuggestion(new DisplaySuggestion("table", "无数据", null));
        response.setQueryResult(queryResult != null ? queryResult : new QueryResult(new ArrayList<>(), new ArrayList<>()));
        return response;
    }
    
    private AgentResponse createDefaultTableResponse(QueryResult queryResult) {
        AgentResponse response = new AgentResponse();
        response.setResponseType(ResponseType.DATA);
        response.setResponseText("已为您查询到以下数据：");
        response.setDisplaySuggestion(new DisplaySuggestion("table", "查询结果", null));
        response.setQueryResult(queryResult);
        return response;
    }

    private String formatValue(Object value) {
        if (value instanceof Number) {
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMaximumFractionDigits(2);
            return nf.format(value);
        }
        return value != null ? value.toString() : "";
    }
}

<!-- 
文件: /src/components/DynamicChart.vue
职责: 接收后端返回的响应，动态渲染表格或ECharts图表。
依赖:
1. `vue-echarts` (需要先通过 `npm install echarts vue-echarts` 安装)
2. Ant Design Vue 的 Table 组件 (`a-table`) 用于展示表格
-->
<template>
  <div class="dynamic-chart-container">
    <h3 v-if="suggestion && suggestion.title" class="chart-title">{{ suggestion.title }}</h3>
    
    <!-- ECharts 图表容器 -->
    <v-chart
      v-if="isChartVisible"
      class="chart"
      :option="chartOption"
      autoresize
    />
    
    <!-- Ant Design Vue 表格 -->
    <a-table
      v-if="isTableVisible"
      :columns="tableColumns"
      :data-source="tableDataSource"
      :pagination="false"
      size="small"
    />

    <!-- KPI 卡片 -->
    <div v-if="isKpiCardVisible" class="kpi-card">
        <div class="kpi-value">{{ kpiValue }}</div>
        <div class="kpi-label">{{ suggestion.title }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart, BarChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';

// 按需引入ECharts模块
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
]);

// 定义Props
const props = defineProps({
  queryResult: {
    type: Object,
    required: true,
  },
  suggestion: {
    type: Object,
    required: true,
  },
});

const chartOption = ref({});
const tableColumns = ref([]);
const tableDataSource = ref([]);

const isChartVisible = computed(() => props.suggestion && props.suggestion.chartType && props.suggestion.chartType.endsWith('_chart'));
const isTableVisible = computed(() => props.suggestion && props.suggestion.chartType === 'table');
const isKpiCardVisible = computed(() => props.suggestion && props.suggestion.chartType === 'kpi_card');
const kpiValue = computed(() => {
    if (isKpiCardVisible.value && props.queryResult.rows.length > 0) {
        const rawValue = props.queryResult.rows[0][0];
        if (typeof rawValue === 'number') {
            return rawValue.toLocaleString('en-US', { maximumFractionDigits: 2 });
        }
        return rawValue;
    }
    return '';
});

// 监听props变化，当新的响应传来时，更新图表或表格
watch(
  () => [props.queryResult, props.suggestion],
  ([newResult, newSuggestion]) => {
    if (!newResult || !newSuggestion) return;
    updateVisualization(newResult, newSuggestion);
  },
  { deep: true, immediate: true }
);

function updateVisualization(result, suggestion) {
  switch (suggestion.chartType) {
    case 'line_chart':
      chartOption.value = createLineChartOption(result, suggestion);
      break;
    case 'bar_chart':
      chartOption.value = createBarChartOption(result, suggestion);
      break;
    case 'pie_chart':
      chartOption.value = createPieChartOption(result, suggestion);
      break;
    case 'table':
      prepareTableData(result);
      break;
    default:
      // 默认或无法识别时也显示表格
      prepareTableData(result);
      break;
  }
}

// ------ ECharts Option 适配器 ------

function createLineChartOption(result, suggestion) {
  const { xAxis, yAxis } = suggestion.chartOptions;
  const xAxisIndex = result.columns.indexOf(xAxis);
  const yAxisIndices = yAxis.map(col => result.columns.indexOf(col));

  return {
    tooltip: { trigger: 'axis' },
    legend: { data: yAxis.map(col => result.columns[result.columns.indexOf(col)]) },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: result.rows.map(row => row[xAxisIndex]),
    },
    yAxis: { type: 'value' },
    series: yAxisIndices.map((yIndex, i) => ({
      name: result.columns[yIndex],
      type: 'line',
      smooth: true,
      data: result.rows.map(row => row[yIndex]),
    })),
  };
}

function createBarChartOption(result, suggestion) {
    const { xAxis, yAxis } = suggestion.chartOptions;
    const xAxisIndex = result.columns.indexOf(xAxis);
    const yAxisIndices = yAxis.map(col => result.columns.indexOf(col));

    return {
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        legend: { data: yAxis.map(col => result.columns[result.columns.indexOf(col)]) },
        xAxis: { type: 'category', data: result.rows.map(row => row[xAxisIndex]) },
        yAxis: { type: 'value' },
        series: yAxisIndices.map(yIndex => ({
            name: result.columns[yIndex],
            type: 'bar',
            data: result.rows.map(row => row[yIndex]),
        })),
    };
}

function createPieChartOption(result, suggestion) {
    const { category, value } = suggestion.chartOptions;
    const categoryIndex = result.columns.indexOf(category);
    const valueIndex = result.columns.indexOf(value);

    return {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [
            {
                type: 'pie',
                radius: '50%',
                data: result.rows.map(row => ({
                    value: row[valueIndex],
                    name: row[categoryIndex],
                })),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                    },
                },
            },
        ],
    };
}


// ------ 表格数据适配器 ------

function prepareTableData(result) {
  if (!result || !result.columns) {
    tableColumns.value = [];
    tableDataSource.value = [];
    return;
  }
  tableColumns.value = result.columns.map(col => ({
    title: col,
    dataIndex: col,
    key: col,
  }));
  tableDataSource.value = result.rows.map((row, index) => {
    const rowData = { key: index };
    result.columns.forEach((col, i) => {
      rowData[col] = row[i];
    });
    return rowData;
  });
}
</script>

<style scoped>
.dynamic-chart-container {
  padding: 16px;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
}
.chart-title {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}
.chart {
  height: 300px;
  width: 100%;
}
.kpi-card {
    text-align: center;
    padding: 24px;
}
.kpi-value {
    font-size: 36px;
    font-weight: 700;
    color: #1f2937;
}
.kpi-label {
    font-size: 14px;
    color: #6b7280;
    margin-top: 4px;
}
</style>
