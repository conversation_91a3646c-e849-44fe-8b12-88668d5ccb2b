# NLU Agent Function Calling 架构升级说明

## 🚀 架构升级概述

本次升级将 ChatBI 的自然语言理解 (NLU) 模块从**固定流程的 RAG**升级为**由模型驱动的 Function Calling**，大大提升了系统的智能化程度和灵活性。

## 📊 架构对比

### 🔄 原有架构：固定流程的 RAG

```mermaid
graph TD
    A[用户查询] --> B[NluAgentImpl]
    B --> C[强制调用 searchSimilarQueries]
    B --> D[强制调用 searchBusinessTerms]
    B --> E[分词处理用户查询]
    C --> F[填入 retrieved_examples 占位符]
    D --> G[填入 term_explanations 占位符]
    E --> H[Simple split 分词]
    F --> I[构建完整 Prompt]
    G --> I
    H --> I
    I --> J[发送给 LLM]
    J --> K[返回结构化意图]
```

#### ❌ 原有架构的问题

1. **中文分词缺陷**：使用简单的 `split("\\s+")` 无法处理中文查询
2. **强制性调用**：无论是否需要，都会调用知识库工具
3. **效率低下**：每次都要执行完整的 RAG 流程
4. **缺乏智能性**：AI 无法自主决定何时需要额外信息

### ✨ 新架构：Function Calling 驱动

```mermaid
graph TD
    A[用户查询] --> B[NluAgentImpl]
    B --> C[构建基础上下文]
    C --> D[数据集权限信息]
    C --> E[对话历史]
    D --> F[AiServices Agent]
    E --> F
    F --> G{AI 自主判断}
    G -->|需要相似示例| H[调用 searchSimilarQueries]
    G -->|需要术语解释| I[调用 searchBusinessTerms]
    G -->|信息充足| J[直接生成意图]
    H --> K[基于检索结果生成意图]
    I --> K
    J --> L[返回结构化意图]
    K --> L
```

## 🔧 核心改造内容

### 1. **NluAgent 接口升级**

```java
public interface NluAgent {

    // ... other methods
    
    /**
     * This method is proxied by AiServices.
     * The @SystemMessage annotation loads instructions from a file.
     * The @UserMessage annotation provides the user's query.
     * @V annotations provide variables for the system message template.
     */
    @SystemMessage(fromResource = "/prompts/nlu_function_calling_agent.txt")
    StructuredQueryIntent processWithAi(@UserMessage String userQuery,
                                       @V("userQuery") String userQueryForTemplate,
                                       @V("chatHistory") String chatHistory,
                                       @V("availableDatasets") String availableDatasets);
}
```

### 2. **KnowledgeBaseTools 实现**

```java
@Tool("根据用户查询搜索相似的查询示例。这可以帮助理解用户意图并提供查询建议")
public String searchSimilarQueries(String userQuery, int topK) {
    // 真正调用 KnowledgePersistenceService
    List<QueryExample> results = knowledgeService.searchSimilarExamples(userQuery, topK);
    // ... 格式化返回结果
}

@Tool("搜索业务术语的定义和解释。这可以帮助理解用户查询中的专业术语和业务概念")
public String searchBusinessTerms(String term) {
    // 真正调用 KnowledgePersistenceService
    List<SearchResultDTO> results = knowledgeService.searchSimilarTerms(term, 3, Collections.emptyList());
    // ... 格式化返回结果
}
```

### 3. **NluAgentImpl 简化**

```java
public class NluAgentImpl implements NluAgent {
    
    private final NluAgent aiAgent;

    public NluAgentImpl(...) {
        // Build the AI Agent that supports tool calling
        this.aiAgent = AiServices.builder(NluAgent.class)
                .chatLanguageModel(chatLanguageModel)
                .tools(knowledgeBaseTools)
                .build();
    }

    @Override
    public StructuredQueryIntent understand(String userQuery, ChatMemory chatMemory, UserContextDTO userContext) {
        String formattedHistory = formatChatHistory(chatMemory.messages());
        String availableDatasets = datasetTools.getDatasetSchemaAndPermissions(null, userContext.getUserRoles());
        
        // AI autonomously decides whether to call tools based on the prompt loaded from the file
        return aiAgent.processWithAi(userQuery, userQuery, formattedHistory, availableDatasets);
    }
}
```

## 🎯 架构优势

### 1. **智能化决策**
- ✅ AI 自主判断何时需要检索相似示例
- ✅ AI 自主判断何时需要术语解释
- ✅ 避免不必要的工具调用，提升效率

### 2. **解决中文分词问题**
- ✅ 不再依赖应用层的简单分词
- ✅ AI 模型本身具备强大的中文理解能力
- ✅ 可以理解上下文和语义关系

### 3. **更好的用户体验**
- ✅ 更准确的意图识别
- ✅ 更相关的澄清选项
- ✅ 更快的响应速度（按需调用）

### 4. **代码简化**
- ✅ 移除了复杂的 Prompt 模板变量构建逻辑
- ✅ 移除了手动分词和关键词提取
- ✅ 代码更清晰、更易维护

## 📈 性能提升

### 执行效率对比

| 场景 | 原有架构 | 新架构 | 提升效果 |
|------|---------|--------|----------|
| 简单查询 | 总是执行 2 次工具调用 | 0-1 次工具调用 | **50-100% 提升** |
| 复杂查询 | 总是执行 2 次工具调用 | 按需 1-2 次调用 | **智能化按需** |
| 中文查询 | 分词失败，工具调用无效 | AI 智能理解 | **质量大幅提升** |

### 准确性提升

| 维度 | 原有架构 | 新架构 | 说明 |
|------|---------|--------|------|
| 中文理解 | ❌ 分词失败 | ✅ 原生支持 | AI 模型强大的中文能力 |
| 澄清选项 | ⚠️ 可能包含无效字段 | ✅ 严格基于数据集 | 强化了字段约束 |
| 工具调用 | ❌ 盲目调用 | ✅ 智能决策 | 按需调用，提升相关性 |

## 🛠️ 实施步骤

### 1. **依赖配置**
```xml
<dependency>
    <groupId>com.qding.chatbi</groupId>
    <artifactId>chatbi-knowledge-service</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. **接口升级**
- 在 `NluAgent` 接口中添加 `@UserMessage` 和 `processWithAi` 方法
- 定义清晰的工具调用提示词

### 3. **工具实现**
- 实现真正的 `KnowledgeBaseTools`，连接 `KnowledgePersistenceService`
- 使用 `@Tool` 注解定义工具描述

### 4. **Agent 构建**
- 使用 `AiServices.builder()` 构建支持工具调用的 Agent
- 注册知识库工具

## 🔮 未来扩展

这次架构升级为未来功能扩展奠定了基础：

1. **更多工具集成**：可以轻松添加更多工具（如计算器、日期工具等）
2. **多步推理**：AI 可以进行多步工具调用来解决复杂问题
3. **动态工具选择**：基于上下文动态选择不同的工具组合
4. **链式思考**：支持 Chain-of-Thought 推理模式

## 📝 总结

这次从固定 RAG 到 Function Calling 的架构升级，不仅解决了中文分词的技术问题，更重要的是将系统的智能化程度提升到了新的层次。AI 现在可以自主决定何时需要什么信息，这为构建更智能、更高效的 ChatBI 系统奠定了坚实基础。

**关键成果**：
- ✅ 解决了中文查询的分词问题
- ✅ 实现了 AI 自主工具调用
- ✅ 提升了系统响应效率
- ✅ 简化了代码架构
- ✅ 为未来扩展奠定基础 