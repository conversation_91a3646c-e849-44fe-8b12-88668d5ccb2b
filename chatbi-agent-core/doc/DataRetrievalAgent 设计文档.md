# **DataRetrievalAgent 设计文档**

## **1\. 概述 (Overview)**

DataRetrievalAgent 是 ChatBI Agent 核心流程中的执行单元。它负责将上游 QueryPlannerAgent 生成的逻辑查询计划 (QueryPlan) 转化为对物理数据源的实际查询操作，并取回数据。它是连接 AI 决策与真实数据的桥梁。

## **2\. 核心输入与输出 (Core Input & Output)**

* **输入**: QueryPlan 对象。DataRetrievalAgent 主要关心此对象中的两个字段：  
  * datasetId: 用于定位该查询需要访问哪个数据集，进而找到其背后的物理数据源配置。  
  * sql: 需要被执行的、已经过验证的SQL字符串。  
* **输出**: QueryResult 对象 (定义于 chatbi-common 模块)。这是一个通用的数据结构，用于封装任何SQL查询的结果，其核心包含：  
  * List\<String\> columns: 结果集的列名列表。  
  * List\<List\<Object\>\> rows: 结果集的数据行，每一行是一个Object列表。  
  * String errorMsg: 如果查询出错，则填充错误信息。

## **3\. 核心工作流 (Core Workflow)**

在当前的模块化单体架构下，DataRetrievalAgent 的工作流程将通过**直接调用 chatbi-data-access-service 模块中的服务Bean**来高效地实现。

| 步骤 | 名称 | 描述 | 关键组件/服务 |
| :---- | :---- | :---- | :---- |
| **1** | **准备查询请求** | 接收到 QueryPlan 后，DataRetrievalAgent 需要从 datasetId 中解析出 dataSourceId，并准备调用数据访问服务。 | DatasetTools |
| **2** | **调用数据访问服务** | DataRetrievalAgent (通过 DataAccessTools) **直接注入并调用** chatbi-data-access-service 提供的服务接口（例如DataAccessService.executeQuery(dataSourceId, sql)）。 | DataAccessTools, DataAccessService (Bean) |
| **3** | **接收查询结果** | chatbi-data-access-service 模块内部负责所有数据库交互（获取连接配置、执行、结果集映射、关闭连接），并将最终的 QueryResult DTO 作为Java对象直接返回。 | chatbi-data-access-service |
| **4** | **返回结果** | 将获取到的 QueryResult 对象返回给上层调用者（通常是 ConversationManager）。 | QueryResult DTO |

## **4\. 关键组件: DataAccessTools**

在 chatbi-agent-core 中，DataAccessTools 是与下游数据服务交互的门面 (Facade)。

* **职责**:  
  * 封装对 chatbi-data-access-service 中服务的调用细节。  
  * 通过 @Autowired 注入 DataAccessService 的Bean。  
  * 提供一个简洁的 executeQuery(String dataSourceId, String sql) 方法供 DataRetrievalAgent 使用，内部直接将调用转发给注入的Service Bean。  
* **实现**:  
  // 在 DataAccessTools.java 中  
  @Autowired  
  private DataAccessService dataAccessService; // 来自 data-access-service 模块的Bean

  public QueryResult executeQuery(String dataSourceId, String sql) {  
      // 直接进行方法调用，而非网络请求  
      return dataAccessService.executeQuery(dataSourceId, sql);  
  }

## **5\. 架构决策: "模块化单体"的优势**

将数据查询执行逻辑封装到独立的 chatbi-data-access-service **模块**中，即使目前是以JAR包依赖的方式运行，也具备显著的架构优势：

* **高内聚，低耦合**:  
  * chatbi-agent-core 专注于AI编排和业务逻辑，不关心数据库连接和JDBC的细节。  
  * chatbi-data-access-service 专注于数据源管理和SQL执行，职责单一，易于维护。  
* **明确的模块边界**: 模块间的依赖关系通过Java接口和pom.xml清晰地定义下来。这使得代码结构非常清晰，新成员更容易理解系统。  
* **易于测试**: 可以对 chatbi-data-access-service 模块进行独立的单元测试和集成测试，而无需启动整个复杂的Agent核心。  
* **平滑演进至微服务**: 这种清晰的模块化设计，为未来可能的架构演进铺平了道路。如果将来系统规模扩大，需要将数据访问层独立部署为微服务，我们只需：  
  1. 将 DataAccessService 接口通过 @FeignClient 或类似技术暴露为REST API。  
  2. 修改 DataAccessTools 的实现，从直接调用Bean改为发起HTTP请求。  
  3. 整个过程中，核心的Agent逻辑（chatbi-agent-core）几乎不需要任何改动，实现了架构的平滑迁移。

## **6\. 异常处理 (Error Handling)**

异常处理机制保持不变。DataRetrievalAgent 在调用 DataAccessTools 时，依然需要负责捕获从服务层抛出的异常，并进行统一的封装处理，将技术细节记录到日志，同时向上层返回对用户友好的错误信息。