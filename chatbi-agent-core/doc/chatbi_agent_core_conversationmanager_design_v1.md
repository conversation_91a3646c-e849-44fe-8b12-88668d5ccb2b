## **Chat BI \- Conversation Manager详细设计 (chatbi-agent-core)**

**模块路径**: com.qding.chatbi.agent.service.ConversationManager (接口) 和 com.qding.chatbi.agent.service.impl.ConversationManagerImpl (实现类)

### **1\. 定位与用途**

Conversation Manager (对话管理器) 是 chatbi-agent-core 模块的核心协调组件。它位于 ConversationServiceImpl 之下，负责接收用户请求（或澄清响应），并编排调用 NluAgent、QueryPlannerAgent 和 DataRetrievalAgent 等下游服务，以完成整个对话处理周期。它还负责管理会话记忆 (ChatMemory)，处理澄清逻辑，并最终构造返回给 ConversationServiceImpl 的 AgentResponse。

### **2\. 核心方法 (ConversationManager 接口)**

package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.AgentResponse;  
import com.qding.chatbi.common.dto.UserQueryRequest;  
import com.qding.chatbi.common.dto.UserContextDTO;  
import com.qding.chatbi.agent.dto.ClarificationProcessingRequest; // 假设一个内部DTO用于澄清

public interface ConversationManager {

    /\*\*  
     \* 处理用户的一轮对话请求。  
     \* 这包括首次查询或在澄清流程中用户提供澄清信息后的后续处理。  
     \*  
     \* @param request 用户的查询请求，可能包含原始问题或对澄清的响应。  
     \* @param userContext 用户上下文信息，包含用户ID、角色、会话ID等。  
     \* @return AI Agent的响应。  
     \*/  
    AgentResponse processConversationTurn(UserQueryRequest request, UserContextDTO userContext);

    // (可选) 如果希望澄清流程有更明确的入口，可以保留一个专门的方法。  
    // 但更推荐的做法是将澄清响应也视为一轮对话，由 processConversationTurn 统一处理，  
    // 内部通过 ChatMemory 或请求中的特定标志来识别是否为澄清响应。  
    // AgentResponse processClarification(String sessionId, ClarificationProcessingRequest clarificationRequest, UserContextDTO userContext);  
}

* **说明**: 我们倾向于使用统一的 processConversationTurn 方法来处理所有用户输入，包括首次查询和澄清响应。内部逻辑将依赖 ChatMemory 和可能的 UserQueryRequest.additionalParams (例如，包含 selectedOptionId) 来区分不同阶段。

### **3\. ConversationManagerImpl 实现类**

* **注解**: @Service  
* **依赖注入**:  
  * NluAgent nluAgent;  
  * QueryPlannerAgent queryPlannerAgent;  
  * DataRetrievalAgent dataRetrievalAgent;  
  * ChatMemoryProvider chatMemoryProvider; (来自 com.qding.chatbi.agent.memory)  
  * JsonUtil jsonUtil; (来自 com.qding.chatbi.common.util)  
  * // 其他必要的服务或工具  
* **processConversationTurn 方法内部逻辑步骤**:  
  1. **获取/创建会话Memory**:  
     ChatMemory memory \= chatMemoryProvider.getMemory(userContext.getSessionId());

  2. **记录用户当前输入到Memory**:  
     * 如果这是一个澄清响应（例如，request.getAdditionalParams() 中包含 clarifiedOptionId），那么记录的文本应该是用户选择的澄清选项的文本或一个代表澄清动作的描述。  
     * 否则，记录 request.getQueryText()。

  String userMessageText \= request.getQueryText();  
       // (可选) 如果是澄清响应，可以从 request.getAdditionalParams() 中获取澄清的选项文本  
       // Map\<String, Object\> additionalParams \= request.getAdditionalParams();  
       // if (additionalParams \!= null && additionalParams.containsKey("clarifiedOptionId")) {  
       //     //  假设 optionsTextMap 存储了 optionId 到 optionText 的映射，在上一轮返回澄清时保存  
       //     //  userMessageText \= "用户选择了：" \+ optionsTextMap.get(additionalParams.get("clarifiedOptionId"));  
       // }  
       memory.add(UserMessage.from(userMessageText));

  3. **调用NLU Agent进行理解**:  
     StructuredQueryIntent intent \= nluAgent.understand(request.getQueryText(), memory, userContext);

     * NluAgent 的 understand 方法会接收到包含当前用户输入以及完整历史的 chatMemory。  
  4. **根据NLU意图进行分支处理**:  
     * **Case 1: intent.isNeedsClarification() 为 true**:  
       * AI Agent 需要用户进一步澄清。  
       * 构造 AgentResponse:  
         * responseType \= ResponseType.CLARIFICATION\_NEEDED;  
         * messageToUser \= intent.getClarificationPromptToUser();  
         * clarificationOptions \= intent.getClarificationOptions();  
       * 将AI的澄清问题（intent.getClarificationPromptToUser()）和选项（可选，可以将其元数据或摘要加入）记录到 ChatMemory:  
         //  可以考虑将澄清选项的ID和文本也作为元数据加入AI消息  
         //  Map\<String, String\> clarificationMetadata \= new HashMap\<\>();  
         //  if (intent.getClarificationOptions() \!= null) {  
         //      intent.getClarificationOptions().forEach(opt \-\> clarificationMetadata.put(opt.getOptionId(), opt.getOptionText()));  
         //  }  
         //  memory.add(AiMessage.from(intent.getClarificationPromptToUser(), clarificationMetadata));  
         memory.add(AiMessage.from(intent.getClarificationPromptToUser()));

       * 返回此 AgentResponse。  
     * **Case 2: intent.getIntent() 为 "DATA\_QUERY" 且 intent.isNeedsClarification() 为 false**:  
       * 用户意图是数据查询，且NLU认为信息足够。  
       * **调用Query Planner Agent**:  
         QueryPlan queryPlan \= queryPlannerAgent.planAndGenerateSql(intent, userContext);

       * **检查 queryPlan.isValid()**:  
         * **If true (SQL生成成功)**:  
           * **调用Data Retrieval Agent**:  
             QueryResult queryResult \= dataRetrievalAgent.retrieveData(queryPlan, userContext);

           * 构造成功的 AgentResponse:  
             * responseType \= ResponseType.DATA\_RESULT;  
             * queryResult 设为获取到的结果。  
             * messageToUser: 可以是一段对查询结果的总结性文字（例如，“这是您查询的\[指标\]结果：”），或者如果结果为空，则是“未查询到相关数据。”。如果 queryResult.getErrorMessage() 不为空，则显示错误信息。  
           * 将AI的回复（包括SQL执行成功或失败的信息）记录到 ChatMemory。  
           * 返回 AgentResponse。  
         * **Else (queryPlan.isValid() 为 false)**:  
           * SQL生成失败或Query Planner认为需要澄清（例如，数据集选择不明确）。  
           * 构造 AgentResponse:  
             * responseType \= ResponseType.CLARIFICATION\_NEEDED; (如果错误信息是引导用户澄清) 或 ResponseType.ERROR; (如果是规划阶段的硬错误)。  
             * messageToUser \= queryPlan.getErrorMessage();  
             * (可选) 如果Query Planner能提供澄清选项，也填充到 clarificationOptions。  
           * 将AI的回复记录到 ChatMemory。  
           * 返回 AgentResponse。  
     * **Case 3: intent.getIntent() 为 "GREETING", "ASK\_CAPABILITIES", 等其他非数据查询意图**:  
       * AI Agent 可以直接生成回复，无需数据库查询。  
       * 例如，对于 "GREETING"：messageToUser \= "您好！有什么我可以帮助您查询的吗？"。  
       * 对于 "ASK\_CAPABILITIES"：messageToUser \= "我可以帮助您通过自然语言查询数据，例如您可以问我‘上个月的销售额是多少？’"。  
       * 构造相应的 AgentResponse，responseType 通常为 ACKNOWLEDGEMENT 或自定义的类型。  
       * 将AI的回复记录到 ChatMemory。  
       * 返回 AgentResponse。  
     * **Case 4: intent.getIntent() 为 "UNKNOWN" 或无法处理**:  
       * 构造 AgentResponse:  
         * responseType \= ResponseType.ERROR;  
         * messageToUser \= "抱歉，我暂时无法理解您的请求，请尝试换一种问法。";  
         * errorCode \= ErrorCode.UNKNOWN\_INTENT.getCode();  
       * 将AI的回复记录到 ChatMemory。  
       * 返回 AgentResponse。  
  5. **全局异常处理**:  
     * 在整个 processConversationTurn 方法外部或内部的关键调用点使用 try-catch 块，捕获可能发生的 ChatBiException 或其他运行时异常。  
     * 如果捕获到异常，构造一个包含错误信息的 AgentResponse (type: ERROR)，并确保将错误情况也简要记录到 ChatMemory（如果可能）。

// 示例性的 try-catch 结构  
// try {  
//     // ... 核心处理流程 ...  
// } catch (ChatBiException e) {  
//     log.error("Business exception in ConversationManager: {}", e.getMessage(), e);  
//     AgentResponse errorResponse \= new AgentResponse();  
//     errorResponse.setSessionId(userContext.getSessionId());  
//     errorResponse.setResponseType(ResponseType.ERROR);  
//     errorResponse.setErrorCode(e.getErrorCode() \!= null ? e.getErrorCode() : ErrorCode.INTERNAL\_SERVER\_ERROR.getCode());  
//     errorResponse.setMessageToUser(e.getMessage());  
//     memory.add(AiMessage.from("An error occurred: " \+ e.getMessage()));  
//     return errorResponse;  
// } catch (Exception e) {  
//     log.error("Unexpected exception in ConversationManager: {}", e.getMessage(), e);  
//     AgentResponse errorResponse \= new AgentResponse();  
//     errorResponse.setSessionId(userContext.getSessionId());  
//     errorResponse.setResponseType(ResponseType.ERROR);  
//     errorResponse.setErrorCode(ErrorCode.INTERNAL\_SERVER\_ERROR.getCode());  
//     errorResponse.setMessageToUser("系统内部发生未知错误，请稍后再试。");  
//     memory.add(AiMessage.from("An unexpected system error occurred."));  
//     return errorResponse;  
// }

### **4\. LangChain4j应用**

* **ChatMemory**: ConversationManager 是 ChatMemory 的主要使用者和管理者（通过 ChatMemoryProvider）。每一轮对话的用户输入和AI输出（包括澄清问题）都应被适当地添加到 ChatMemory 中。  
* **Agent/Chain编排**: 虽然这里的 "Agent"（NluAgent, QueryPlannerAgent, DataRetrievalAgent）是我们自定义的Java服务组件，但它们内部可能会使用LangChain4j的 LLMChain, SequentialChain, 或者一个更复杂的LangChain4j Agent (如 ConversationalAgent 或 AiServices 创建的Agent) 来实现其功能。ConversationManager 负责调用这些自定义的Agent/Service组件。  
* 如果采用更纯粹的LangChain4j Agent模式 (例如，使用 AgentExecutor 和一组 Tools)，那么 ConversationManager 的角色可能更像一个高级别的 AgentExecutor 的配置者和调用者。但目前的设计更偏向于将LLM交互封装在各个职责明确的Java服务中。

以上是对 ConversationManager.java 的详细设计。它作为对话流程的中枢，其逻辑的清晰性和健壮性非常重要。

请您审阅这部分内容。