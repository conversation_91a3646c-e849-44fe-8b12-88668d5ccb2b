// =========================================================================
// Module: chatbi-data-access-service
// 模块:   数据访问服务 (负责实际的数据库交互)
// =========================================================================

package com.qding.chatbi.dataaccess.service;

import com.qding.chatbi.common.dto.QueryResult;

/**
 * DataAccessService 接口
 * 定义了数据访问模块对外提供的核心能力。
 * chatbi-agent-core 模块将会注入此接口的实现。
 */
public interface DataAccessService {

    /**
     * 根据指定的数据源ID和SQL语句执行查询。
     *
     * @param dataSourceId 数据源的唯一标识符
     * @param sql          要执行的SQL查询语句
     * @return 查询结果，封装在QueryResult DTO中
     */
    QueryResult executeQuery(String dataSourceId, String sql);
}

// -------------------------------------------------------------------------

package com.qding.chatbi.dataaccess.service.impl;

import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.enums.ErrorCode;
import com.qding.chatbi.dataaccess.service.DataAccessService;
import com.qding.chatbi.metadata.entity.DatabaseSourceConfig;
import com.qding.chatbi.metadata.service.DatasetMetadataService; // 假设从元数据服务获取配置
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * DataAccessService 的实现类
 * 负责动态创建和管理数据源连接，并执行SQL查询。
 */
@Service
@Slf4j
public class DataAccessServiceImpl implements DataAccessService {

    // 注入元数据服务，用于根据ID获取数据源的详细配置
    @Autowired
    private DatasetMetadataService metadataService;

    // 使用ConcurrentHashMap作为线程安全的数据源缓存，避免为每个请求重复创建连接池
    private final ConcurrentMap<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();

    @Override
    public QueryResult executeQuery(String dataSourceId, String sql) {
        log.info("Executing query on dataSourceId: {}", dataSourceId);
        log.debug("SQL: {}", sql);

        try {
            // 1. 获取或创建数据源
            DataSource dataSource = getDataSource(dataSourceId);

            // 2. 使用JdbcTemplate执行查询
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            
            // 3. 映射结果集到QueryResult DTO
            return jdbcTemplate.query(sql, (ResultSetExtractor<QueryResult>) rs -> {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                List<String> columnNames = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(metaData.getColumnLabel(i));
                }

                List<List<Object>> rows = new ArrayList<>();
                while (rs.next()) {
                    List<Object> row = new ArrayList<>();
                    for (int i = 1; i <= columnCount; i++) {
                        row.add(rs.getObject(i));
                    }
                    rows.add(row);
                }

                return new QueryResult(columnNames, rows);
            });

        } catch (DataAccessException e) {
            log.error("Database query failed for dataSourceId: {}. Error: {}", dataSourceId, e.getMessage());
            // 向上抛出自定义异常，方便上层统一捕获和处理
            throw new ChatBiException(ErrorCode.DATABASE_QUERY_ERROR, "数据库查询执行失败: " + e.getMostSpecificCause().getMessage(), e);
        } catch (Exception e) {
            log.error("An unexpected error occurred during query execution for dataSourceId: {}", dataSourceId, e);
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "数据检索时发生未知错误。", e);
        }
    }

    /**
     * 动态获取或创建数据源。
     * 如果缓存中已存在，则直接返回；否则，从元数据服务获取配置并创建一个新的数据源。
     */
    private DataSource getDataSource(String dataSourceId) {
        return dataSourceCache.computeIfAbsent(dataSourceId, id -> {
            log.info("DataSource for id '{}' not found in cache. Creating a new one.", id);
            // 假设metadataService可以根据ID返回数据源配置实体
            DatabaseSourceConfig config = metadataService.getDatabaseSourceConfigById(id);
            if (config == null) {
                throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, "ID为 '" + id + "' 的数据源未找到配置。");
            }
            return createHikariDataSource(config);
        });
    }

    /**
     * 根据配置信息创建一个HikariCP数据源（连接池）。
     */
    private DataSource createHikariDataSource(DatabaseSourceConfig dbConfig) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(dbConfig.getJdbcUrl());
        hikariConfig.setUsername(dbConfig.getUsername());
        hikariConfig.setPassword(dbConfig.getPassword());
        hikariConfig.setDriverClassName(dbConfig.getDriverClassName());
        hikariConfig.setMaximumPoolSize(10); // 可配置
        hikariConfig.setMinimumIdle(2);    // 可配置
        hikariConfig.setConnectionTimeout(30000); // 30秒，可配置
        
        log.info("Created new HikariDataSource for URL: {}", dbConfig.getJdbcUrl());
        return new HikariDataSource(hikariConfig);
    }
}


// =========================================================================
// Module: chatbi-agent-core
// 模块:   Agent核心 (负责AI逻辑编排)
// =========================================================================

package com.qding.chatbi.agent.tool;

import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.dataaccess.service.DataAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * DataAccessTools - Agent核心模块中用于访问数据服务的门面
 * 它的职责是封装对下游`data-access-service`模块的调用细节。
 */
@Component
public class DataAccessTools {

    @Autowired
    private DataAccessService dataAccessService; // 通过Spring注入来自下游模块的Bean

    /**
     * 执行查询的工具方法。
     *
     * @param dataSourceId 数据源ID
     * @param sql          要执行的SQL
     * @return 查询结果
     */
    public QueryResult executeQuery(String dataSourceId, String sql) {
        // 直接进行Java方法调用，将请求转发给DataAccessService
        return dataAccessService.executeQuery(dataSourceId, sql);
    }
}


// -------------------------------------------------------------------------

package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.agent.service.DataRetrievalAgent;
import com.qding.chatbi.agent.tool.DataAccessTools;
import com.qding.chatbi.agent.tool.DatasetTools;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DataRetrievalAgent 的实现类
 * 负责接收QueryPlan，并调用工具类来执行查询。
 */
@Service
@Slf4j
public class DataRetrievalAgentImpl implements DataRetrievalAgent {

    @Autowired
    private DataAccessTools dataAccessTools;

    @Autowired
    private DatasetTools datasetTools; // 用于根据datasetId查找dataSourceId

    @Override
    public QueryResult retrieve(QueryPlan plan) {
        log.info("Executing data retrieval for datasetId: {}", plan.getDatasetId());

        try {
            // 1. 根据数据集ID获取其完整信息，包括它所使用的数据源ID
            DatasetInfoDTO datasetInfo = datasetTools.getDatasetInfoById(plan.getDatasetId());
            if (datasetInfo == null) {
                throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, "无法找到ID为 " + plan.getDatasetId() + " 的数据集信息。");
            }
            String dataSourceId = datasetInfo.getDataSourceId();

            // 2. 调用DataAccessTools执行查询
            QueryResult result = dataAccessTools.executeQuery(dataSourceId, plan.getSql());
            log.info("Data retrieval successful. Fetched {} rows.", result.getRows().size());
            
            return result;

        } catch (ChatBiException e) {
            // 如果下游服务抛出的是已知的ChatBiException，直接重新抛出
            log.error("A known business exception occurred during data retrieval: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            // 对于其它所有未知异常，进行包装
            log.error("An unexpected error occurred in DataRetrievalAgent", e);
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "在数据检索环节发生未知系统错误。", e);
        }
    }
}
