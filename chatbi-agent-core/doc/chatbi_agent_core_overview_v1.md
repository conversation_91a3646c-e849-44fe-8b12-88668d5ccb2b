## **Chat BI \- Agent Core模块概览与结构 (chatbi-agent-core)**

本模块是 Chat BI 服务的核心业务逻辑层，负责处理用户查询的整个生命周期，从自然语言理解到最终查询结果的生成和对话管理。它集成了 LangChain4j 框架来实现与大语言模型 (LLM) 的交互、知识的检索增强以及构建复杂的AI处理流程。

**包名**: com.qding.chatbi.agent

### **1\. 模块概述与职责**

chatbi-agent-core 模块的主要职责包括：

* **实现核心对话接口**: 提供 com.qding.chatbi.api.service.ConversationApi 接口的具体实现。  
* **自然语言理解 (NLU)**: 解析用户输入的自然语言，识别意图、提取实体，并处理领域特定的术语（黑话）。  
* **查询规划与生成**: 基于NLU的结果和可查询的元数据，规划查询步骤并生成目标数据库的SQL语句。  
* **数据获取与整合**: 调用数据访问服务执行SQL，并对结果进行初步处理。  
* **对话管理**: 维护会话状态，处理多轮对话、澄清逻辑等。  
* **知识库集成**: 利用向量数据库（Milvus）中的查询示例和业务术语辅助NLU和查询生成。  
* **元数据与权限集成**: 查询元数据服务获取数据集、字段信息，并根据用户角色过滤数据访问权限。  
* **对话历史持久化**: 将用户与AI的交互消息保存到数据库。  
* **LangChain4j 框架应用**: 利用LangChain4j构建和管理LLM调用、Prompt模板、Chains、Tools和Memory。

详细的组件设计将在各自的子文档中阐述。

### **2\. 包结构 (更新后)**

com.qding.chatbi.agent  
├── config/                 // Spring Boot及LangChain4j相关配置  
├── dto/                    // 模块内部使用的DTO定义 (例如 StructuredQueryIntent, QueryPlan)  
├── entity/                 // JPA实体 (如 ChatMessage)  
├── repository/             // Spring Data JPA仓库 (如 ChatMessageRepository)  
├── service/                // 核心服务接口及实现  
│   ├── impl/               // 服务实现类包  
│   ├── NluAgent.java       // (或 NluService 接口及其实现)  
│   ├── QueryPlannerAgent.java // (或 QueryGenerationService 接口及其实现)  
│   ├── DataRetrievalAgent.java // (或 DataRetrievalService 接口及其实现)  
│   ├── ConversationManager.java // 对话流程管理器  
│   ├── ConversationHistoryService.java // 对话历史服务接口  
│   ├── UserRoleService.java // (新增) 用户角色服务接口  
│   └── ConversationServiceImpl.java // 实现 ConversationApi  
├── chain/                  // LangChain4j Chains 定义  
├── tool/                   // LangChain4j Tools 定义 (用于调用其他服务)  
├── memory/                 // LangChain4j Memory 相关实现与管理  
└── prompt/                 // Prompt模板管理与加载

* **注意**: ChatBiAgentApplication.java 启动类已移至 chatbi-application 模块。

### **3\. 详细组件设计文档链接**

* **配置类设计 (config/)** \- 打开/创建文档  
* **持久化设计 (entity/, repository/, ConversationHistoryService)** \- 打开/创建文档  
* **NLU Agent 设计 (NluAgent.java)** \- 打开文档  
* **Query Planner Agent 设计 (QueryPlannerAgent.java)** \- 打开文档  
* **Data Retrieval Agent 设计 (DataRetrievalAgent.java)** \- 打开文档  
* **Conversation Manager 设计 (ConversationManager.java)** \- 打开文档  
* **Conversation Service 实现设计 (ConversationServiceImpl.java)** \- 打开文档  
* **LangChain4j Tools 设计 (tool/)** \- 打开文档  
* **LangChain4j Memory 设计 (memory/)** \- 打开文档  
* **Prompt 模板管理设计 (prompt/)** \- 打开/创建文档  
* **LangChain4j Chains 设计 (chain/)** \- 打开/创建文档