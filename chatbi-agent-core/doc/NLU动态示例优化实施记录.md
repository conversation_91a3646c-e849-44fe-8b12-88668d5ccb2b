# NLU 动态示例优化实施记录

## 概述

本次优化将 NLU Agent 中硬编码的示例迁移到 query_examples 知识库，实现动态示例管理。通过让 AI 先生成初步意图，然后验证并搜索相似示例，最后优化输出的两阶段处理流程。

## 实施内容

### 1. 新增数据结构

创建了`IntentValidationResult`类，用于验证意图并返回处理建议：

- `action`: 处理动作（REFINE_WITH_EXAMPLES, REFERENCE_AND_ADAPT, CLARIFICATION_NEEDED, PROCEED_WITH_CAUTION）
- `completeness`: 查询完整性评估
- `similarityScore`: 最高相似度分数
- `examples`: 相似示例列表
- `missingElements`: 缺失元素列表

### 2. 新增验证工具

在`KnowledgeBaseTools`中添加了`validateAndEnhanceIntent`方法：

- 评估查询意图的完整性
- 基于 mergedContext 搜索相似示例
- 根据相似度返回不同的处理建议
- 支持时间信息的智能检查

### 3. 优化 Prompt

#### 系统提示词(nlu_system_prompt.txt)

- 增加两阶段处理要求
- 强调必须调用 validateAndEnhanceIntent 工具
- 明确示例参考的优先级

#### 功能调用提示词(nlu_function_calling_agent.txt)

- 详细说明两阶段处理流程
- 根据不同 action 的处理策略
- 提供处理示例

#### 开发者提示词(nlu_developer_prompt.txt)

- 删除硬编码示例
- 保留核心规则说明
- 提示示例将动态提供

#### 示例提示词(nlu_examples_prompt.txt)

- 完全替换为动态示例说明

### 4. 处理流程

```
用户查询
    ↓
AI生成初步StructuredQueryIntent
    ↓
调用validateAndEnhanceIntent验证
    ↓
根据返回的action决定处理方式：
- REFINE_WITH_EXAMPLES → 参考高相似度示例优化
- REFERENCE_AND_ADAPT → 参考中等相似度示例调整
- CLARIFICATION_NEEDED → 生成澄清请求
- PROCEED_WITH_CAUTION → 按理解继续处理
    ↓
输出最终的StructuredQueryIntent
```

## 示例数据准备

需要在 query_examples 表中准备以下类型的示例：

### 1. 澄清类示例

```sql
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes) VALUES
('北京的收入',
'{"originalQuery":"北京的收入","mergedContext":"北京的收入","intent":"CLARIFICATION_NEEDED","queryType":"AGGREGATION_QUERY","aggregationRequired":true,"expectedGranularity":["city"],"entities":{"metrics":["销售额"],"filters":[{"fieldName":"city","operator":"EQUALS","value":"北京"}]},"clarificationPromptToUser":"我了解您想查看北京的收入数据。请问您想查看哪个时间段的数据？"}',
1,
'缺少时间信息的澄清示例');
```

### 2. 完整查询示例

```sql
-- 排名查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes) VALUES
('各城市本月销售额排名前5',
'{"originalQuery":"各城市本月销售额排名前5","mergedContext":"各城市本月销售额排名前5","intent":"DATA_QUERY","queryType":"RANKING_QUERY","aggregationRequired":true,"expectedGranularity":["city"],"limitCount":5,"entities":{"metrics":["销售额"],"dimensions_to_group":["city"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2024-12-01","2024-12-31"]}],"sort_by":[{"fieldName":"销售额","order":"DESC"}]}}',
1,
'完整的排名查询示例');

-- 趋势查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes) VALUES
('最近30天销售额趋势',
'{"originalQuery":"最近30天销售额趋势","mergedContext":"最近30天销售额趋势","intent":"DATA_QUERY","queryType":"TREND_QUERY","aggregationRequired":true,"expectedGranularity":["DATE_FORMAT(date,''%Y-%m-%d'')"],"entities":{"metrics":["销售额"],"dimensions_to_group":["date"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["CURRENT_DATE-30","CURRENT_DATE"]}]}}',
1,
'时间趋势查询示例');
```

### 3. 特殊处理示例

```sql
-- 同比查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes) VALUES
('本月销售额同比',
'{"originalQuery":"本月销售额同比","mergedContext":"本月销售额同比","intent":"DATA_QUERY","queryType":"COMPARISON_QUERY","aggregationRequired":true,"entities":{"metrics":["销售额"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2024-12-01","2024-12-31"]},{"fieldName":"date","operator":"BETWEEN","value":["2023-12-01","2023-12-31"]}]}}',
1,
'同比查询需要两个时间段的数据');

-- 占比查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes) VALUES
('北京销售额占全国比例',
'{"originalQuery":"北京销售额占全国比例","mergedContext":"北京销售额占全国比例","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","entities":{"metrics":["销售额"],"filters":[{"fieldName":"city","operator":"EQUALS","value":"北京"}],"additionalMetrics":["全国销售额总计"]}}',
1,
'占比查询需要计算分子和分母');
```

## 优势

1. **灵活管理**：通过管理后台即可更新示例，无需修改代码
2. **精准匹配**：基于结构化意图搜索，比原始文本更准确
3. **持续优化**：可以根据用户反馈不断添加新示例
4. **个性化**：可以根据用户角色返回不同的示例
5. **可追踪**：每个决策都有明确的依据（相似度分数）

## 注意事项

1. **相似度阈值**：当前设置为 0.85（高）、0.6（中），需要根据实际测试调整
2. **示例质量**：query_examples 中的数据质量直接影响效果
3. **向量搜索**：需要确保 Milvus 服务正常，向量索引更新及时
4. **性能考虑**：两阶段处理会增加一次工具调用，但带来的准确性提升值得

## 后续优化

1. 增加示例使用统计，分析哪些示例最有效
2. 支持示例的 A/B 测试
3. 自动学习用户反馈，优化示例库
4. 支持按查询类型分类管理示例
