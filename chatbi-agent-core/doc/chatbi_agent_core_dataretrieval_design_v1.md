## **Chat BI \- Data Retrieval Agent详细设计 (chatbi-agent-core)**

**模块路径**: com.qding.chatbi.agent.service.DataRetrievalAgent (接口) 和 com.qding.chatbi.agent.service.impl.DataRetrievalAgentImpl (实现类)

### **1\. 定位与用途**

Data Retrieval Agent 是 chatbi-agent-core 模块中负责执行数据库查询并获取数据的组件。它接收由 QueryPlannerAgent 生成的SQL语句和目标数据源信息，通过调用底层的 chatbi-data-access-service (通常通过LangChain4j Tool封装) 来执行查询，并将原始的数据库结果转换为统一的、可供上层使用的 QueryResult DTO 格式。

### **2\. 核心方法 (DataRetrievalAgent 接口)**

package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.QueryResult;  
import com.qding.chatbi.common.dto.UserContextDTO;  
// QueryPlan DTO 假设已在 com.qding.chatbi.agent.dto 包中定义  
import com.qding.chatbi.agent.dto.QueryPlan;

public interface DataRetrievalAgent {  
    /\*\*  
     \* 根据查询计划中的SQL语句和数据源信息，执行查询并检索数据。  
     \*  
     \* @param queryPlan 包含SQL语句、目标数据集ID、目标数据源ID和数据库类型的查询计划。  
     \* @param userContext 用户上下文，可能用于日志记录或进一步的细粒度控制（尽管主要权限已在规划阶段处理）。  
     \* @return 查询结果对象。  
     \* @throws com.qding.chatbi.common.exception.ChatBiException 如果数据检索过程中发生业务异常。  
     \*/  
    QueryResult retrieveData(QueryPlan queryPlan, UserContextDTO userContext);  
}

### **3\. DataRetrievalAgentImpl 实现类**

* **注解**: @Service  
* **依赖注入**:  
  * **DataAccessTools dataAccessTools;** (直接注入Tool Bean)  
  * JsonUtil jsonUtil; (来自 com.qding.chatbi.common.util, 用于解析Tool返回的JSON字符串结果)  
* **retrieveData 方法内部逻辑步骤**:  
  1. **输入校验**:  
     * 检查 queryPlan 是否为 null 或无效 (\!queryPlan.isValid())。  
     * 检查 queryPlan.getSql() 是否为空。  
     * 检查 queryPlan.getTargetDatabaseSourceId() 是否为空。  
     * 如果校验失败，可以直接返回一个包含错误信息的 QueryResult 对象或抛出 InvalidInputException。  
       if (queryPlan \== null || \!queryPlan.isValid() || StringUtil.isBlank(queryPlan.getSql()) || StringUtil.isBlank(queryPlan.getTargetDatabaseSourceId())) {  
           QueryResult errorResult \= new QueryResult();  
           errorResult.setQuerySql(queryPlan \!= null ? queryPlan.getSql() : "N/A");  
           errorResult.setErrorMessage("无效的查询计划或缺少执行SQL的必要信息。");  
           // log.warn("Attempted to retrieve data with invalid query plan: {}", queryPlan);  
           return errorResult;  
       }  
       String sqlToExecute \= queryPlan.getSql();  
       String databaseSourceId \= queryPlan.getTargetDatabaseSourceId();  
       String databaseType \= queryPlan.getDatabaseType(); // 可用于后续结果处理的提示

  2. **调用数据访问服务 (通过Tool)**:  
     * 准备调用 DataAccessTools.executeSqlAndGetResponse Tool 的参数。  
       String rawToolResponse;  
       try {  
           // 直接调用注入的Tool Bean的方法  
           rawToolResponse \= dataAccessTools.executeSqlAndGetResponse(sqlToExecute, databaseSourceId);  
       } catch (Exception e) {  
           // log.error("Error executing SQL query via DataAccessTool for sourceId {}: {}", databaseSourceId, sqlToExecute, e);  
           QueryResult errorResult \= new QueryResult();  
           errorResult.setQuerySql(sqlToExecute);  
           errorResult.setErrorMessage("执行数据库查询时发生错误: " \+ e.getMessage());  
           return errorResult;  
       }

     * **DataAccessTools.executeSqlAndGetResponse Tool的职责**:  
       * 内部依赖 chatbi-data-access-service 的 QueryExecutionService。  
       * QueryExecutionService 负责根据 databaseSourceId 获取数据库连接配置，连接数据库，执行SQL，并返回一个**通用的结果结构**。  
       * executeSqlAndGetResponse Tool 将这个通用结果结构**序列化为JSON字符串**返回。  
  3. **处理和转换Tool的响应**:  
     * **解析JSON响应**: 将 rawToolResponse (JSON字符串) 反序列化为 QueryResult DTO。  
       com.qding.chatbi.common.dto.QueryResult queryResult;  
       try {  
           queryResult \= jsonUtil.fromJson(rawToolResponse, com.qding.chatbi.common.dto.QueryResult.class);  
           if (queryResult.getErrorMessage() \!= null && \!queryResult.getErrorMessage().isEmpty()) {  
                // log.warn("Query execution returned an error: {}", queryResult.getErrorMessage());  
           }  
            queryResult.setQuerySql(sqlToExecute); // 确保SQL被设置

       } catch (Exception e) {  
           // log.error("Error parsing query execution response for SQL \[{}\]: {}", sqlToExecute, e.getMessage(), e);  
           queryResult \= new com.qding.chatbi.common.dto.QueryResult();  
           queryResult.setQuerySql(sqlToExecute);  
           queryResult.setErrorMessage("处理查询结果时发生错误: " \+ e.getMessage());  
       }  
       return queryResult;

  4. **（可选）结果后处理/丰富化**:  
     * 如果需要，可以在这里对 QueryResult 进行一些额外的处理，例如：  
       * 数据脱敏。  
       * 根据 queryPlan.getColumnAliases() 调整列名以匹配业务名称（如果 QueryResult 中的列名是技术名称）。  
       * 生成简单的统计摘要。  
* **依赖**:  
  * DataAccessTools  
  * JsonUtil  
  * com.qding.chatbi.common.dto.QueryResult  
  * com.qding.chatbi.agent.dto.QueryPlan  
  * com.qding.chatbi.common.dto.UserContextDTO  
  * com.qding.chatbi.common.util.StringUtil  
* **返回**: com.qding.chatbi.common.dto.QueryResult DTO。

### **4\. 关于 DataAccessTools.executeSqlAndGetResponse Tool**

* **输入**: String sqlQuery, String databaseSourceId  
* **输出**: QueryResult 对象的JSON字符串表示。  
* **内部实现**:  
  1. 依赖注入 QueryExecutionService (来自 chatbi-data-access-service 模块)。  
  2. 调用 queryExecutionService.executeQuery(sqlQuery, databaseSourceId)。  
  3. QueryExecutionService 应返回一个 QueryResult DTO。  
  4. 此Tool将该 QueryResult DTO 序列化为JSON字符串返回。

以上是对 DataRetrievalAgent 的详细设计。核心在于它如何通过Tool与 chatbi-data-access-service 交互来执行SQL，并如何处理返回的结果。

请您审阅这部分内容。