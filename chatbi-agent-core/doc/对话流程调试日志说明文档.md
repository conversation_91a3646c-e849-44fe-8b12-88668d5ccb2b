# ChatBI 对话流程调试日志说明文档

## 概述

本文档详细说明了为 ChatBI 对话流程相关类和方法新增的详细日志记录功能，旨在方便开发者在调试过程中快速定位问题和分析数据流转。

## 设计原则

### 1. 分层级日志记录
- **INFO级别**: 记录关键流程节点、数据传递摘要、执行结果
- **DEBUG级别**: 记录详细的数据内容、中间状态、样本数据
- **ERROR级别**: 记录异常信息、完整上下文、故障排查信息

### 2. 标准化日志格式
- 使用表情符号前缀快速识别日志类型：
  - 📥 输入数据
  - 📤 输出数据  
  - 🔍 查询/搜索操作
  - ✅ 成功操作
  - ❌ 失败操作
  - 🔄 处理中
  - 📊 统计信息
  - 🎯 重要结果

### 3. 阶段性分隔
- 使用 `==========` 分隔不同的处理阶段
- 便于快速定位特定环节的问题

## 详细改进内容

### 1. ConversationServiceImpl (对话服务门面)

**主要职责**: 作为对话系统的入口，负责用户身份验证、上下文构建、历史记录管理

**新增调试功能**:
- 📝 完整记录接收到的用户请求参数
- 👤 详细记录用户角色获取过程（支持测试模式和正常模式）
- 🏗️ 记录用户上下文构建的每个步骤
- 💾 记录历史消息保存结果
- 📊 记录AI响应的详细统计信息（处理时间、响应类型、查询结果概要）
- 🎯 提供最终响应的完整摘要

**日志示例**:
```
========== ConversationServiceImpl.processUserQuery 开始 ==========
📝 接收到的用户查询请求:
   - 用户ID: user123
   - 查询文本: '上个月北京的GMV是多少'
   - 会话ID: session-456
   - 附加参数: {userRole=admin}
   - 请求时间戳: 1703123456789
```

### 2. ConversationManagerImpl (对话管理器)

**主要职责**: 协调整个对话处理流程，依次调用NLU、规划、检索等代理

**新增调试功能**:
- 🔗 记录聊天记忆的获取和使用情况
- 📤 详细记录传递给各个Agent的参数
- 🧪 记录权限检查和分支处理逻辑
- 📊 记录各个阶段的耗时统计
- 🎭 记录响应生成的详细过程

### 3. NluAgentImpl (自然语言理解代理)

**主要职责**: 理解用户自然语言，转换为结构化意图

**新增调试功能**:
- 📥 记录用户查询和对话历史的详细信息
- 🏗️ 记录Prompt变量构建过程（数据集摘要、相似查询、业务术语等）
- 🤖 记录LLM调用的输入输出和Token使用情况
- 🔄 记录JSON解析过程和异常处理
- 🎯 记录最终结构化意图的完整信息

**日志示例**:
```
========== NluAgent.understand 开始自然语言理解 ==========
📥 接收到的NLU请求:
   - 用户查询: '上个月北京的GMV是多少'
   - 查询长度: 11 字符
   - 对话历史消息数: 3
   - 用户ID: user123
   - 用户角色: [admin]

📋 Prompt变量统计:
   - user_query: 11 字符
   - available_datasets_summary: 1250 字符
   - retrieved_examples: 890 字符
   - term_explanations: 156 字符
```

### 4. QueryPlannerAgentImpl (查询规划代理)

**主要职责**: 选择数据集、生成SQL、验证安全性

**新增调试功能**:
- 📊 记录候选数据集的统计和选择过程
- 🤖 记录SQL生成上下文的构建
- 🔐 记录详细的SQL安全验证过程
- ✅ 记录查询计划的完整信息和耗时

**日志示例**:
```
========== 阶段1: 数据集选择 ==========
📊 候选数据集统计:
   - 候选数据集数量: 3
   - 数据集: 销售总览 (ID: 1, 表: sales_summary, 类型: PostgreSQL)
   - 数据集: 用户行为 (ID: 2, 表: user_behavior, 类型: MySQL)

✅ 选定的最佳数据集:
   - 数据集名称: 销售总览
   - 数据集ID: 1
   - 表名: sales_summary
```

### 5. DataRetrievalAgentImpl (数据检索代理)

**主要职责**: 执行SQL查询，返回数据结果

**新增调试功能**:
- 📥 记录接收到的查询计划的完整信息
- 📞 记录调用DataAccessTools的详细参数
- ✅ 记录查询成功的统计信息（耗时、行数、列数）
- 🔍 记录前几行数据作为样本（debug级别）
- ❌ 记录详细的异常上下文信息

### 6. DataAccessServiceImpl (数据访问服务)

**主要职责**: 管理数据库连接池，执行SQL查询

**新增调试功能**:
- 🔗 记录数据源连接的获取过程
- 💾 记录连接池的创建和缓存情况
- 📊 记录ResultSet处理的详细过程
- 🏗️ 记录QueryResult对象的构建过程
- ❌ 记录数据库异常的完整上下文

**日志示例**:
```
========== DataAccessService.executeQuery 开始执行数据库查询 ==========
📥 接收到的查询请求:
   - 数据源ID: 'db-1'
   - SQL长度: 156 字符
   - SQL内容: SELECT region, SUM(gmv) as total_gmv FROM sales_summary WHERE date_month = '2023-11' AND region = '北京' GROUP BY region

✅ 查询执行成功:
   - 数据源ID: 'db-1'
   - 查询耗时: 245ms
   - 返回列数: 2
   - 返回行数: 1
   - 列名列表: [region, total_gmv]
```

## 调试指南

### 1. 快速定位问题
根据错误日志中的表情符号和阶段分隔符，可以快速定位问题发生在哪个环节：

- **输入验证问题**: 查找 📥 标记的输入数据日志
- **LLM调用问题**: 查找 🤖 标记的LLM相关日志  
- **数据库问题**: 查找 🔗 和 📞 标记的数据访问日志
- **权限问题**: 查找 🔑 标记的权限检查日志

### 2. 性能分析
每个主要组件都记录了详细的耗时信息：
- 总处理时间
- LLM调用时间
- 数据库查询时间
- 各个阶段的分解时间

### 3. 数据流追踪
可以通过会话ID在整个流程中追踪数据的传递：
```bash
# 查找特定会话的所有日志
grep "session-456" application.log

# 查找特定用户的查询处理
grep "用户ID: user123" application.log
```

### 4. 调试级别配置
建议的日志级别配置：
```yaml
logging:
  level:
    com.qding.chatbi.agent.service.impl: INFO    # 主要流程信息
    com.qding.chatbi.dataaccess: INFO           # 数据访问信息
    com.qding.chatbi.agent.service.impl.NluAgentImpl: DEBUG  # NLU详细信息（开发时）
```

## 最佳实践

### 1. 生产环境
- 主要使用INFO级别日志
- 定期清理历史日志文件
- 监控ERROR级别日志的告警

### 2. 开发环境  
- 使用DEBUG级别获取详细信息
- 关注Token使用量和API调用频率
- 使用样本数据验证流程正确性

### 3. 性能优化
- 监控各个阶段的耗时
- 识别性能瓶颈环节
- 优化缓存和连接池配置

## 注意事项

1. **敏感信息保护**: 用户密码、API密钥等敏感信息不会记录到日志中
2. **日志文件大小**: 详细日志可能导致日志文件增大，注意配置日志轮转
3. **性能影响**: DEBUG级别日志会轻微影响性能，生产环境建议使用INFO级别

## 未来改进方向

1. **结构化日志**: 考虑使用JSON格式的结构化日志，便于日志分析工具处理
2. **分布式追踪**: 引入TraceID实现跨服务的请求追踪
3. **指标监控**: 基于日志数据构建业务指标和告警系统
4. **自动分析**: 开发日志分析工具，自动识别常见问题模式 