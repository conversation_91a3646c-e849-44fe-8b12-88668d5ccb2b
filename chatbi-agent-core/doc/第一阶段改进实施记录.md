# 第一阶段改进实施记录

## 改进目标

解决多轮对话中 AI 忘记上一轮信息的问题，特别是在澄清流程中丢失已获取的实体信息。

## 问题分析

### 原始问题

```
用户: "成本"
AI: "您想查询哪个城市的成本？"
用户: "北京 上海"
AI: "您想查看什么时间段的数据？" （此时AI忘记了"成本"这个指标）
```

### 根本原因

1. `ConversationHistorySummarizer` 使用硬编码的正则表达式提取实体
2. 正则表达式不可靠，导致误识别（如把"成本"识别为城市）
3. 每轮都重新解析文本，而不是使用上一轮 AI 已经理解的结构化信息

## 实施方案

### 1. 新增 ConversationContextManager

- **目的**：管理和维护结构化的对话上下文
- **核心功能**：
  - 保存每轮的 `StructuredQueryIntent`
  - 从历史记录中恢复上一轮的意图
  - 提供简单的缓存机制

### 2. 修改 NluAgent 接口

- 添加 `sessionId` 参数，用于获取历史上下文
- 保持向后兼容性

### 3. 优化历史传递机制

- **旧方案**：文本 → 正则提取 → 不可靠的摘要
- **新方案**：StructuredQueryIntent → JSON → 直接使用

### 4. Prompt 优化

- 在 `nlu_developer_prompt.txt` 中添加结构化历史处理指引
- 清理 `nlu_system_prompt.txt` 中的重复内容

## 代码变更

### 新增文件

1. `ConversationContextManager.java` - 上下文管理接口
2. `ConversationContextManagerImpl.java` - 上下文管理实现
3. `ConversationContextManagerTest.java` - 单元测试

### 修改文件

1. `NluAgent.java` - 添加 sessionId 参数
2. `NluAgentImpl.java` - 使用结构化历史
3. `ConversationManagerImpl.java` - 注入 ContextManager 并保存意图
4. `nlu_developer_prompt.txt` - 添加历史处理指引
5. `nlu_system_prompt.txt` - 删除重复内容

## 预期效果

### 改进后的对话流程

```
用户: "成本"
AI: "您想查询什么时间段哪个地区的成本？"
[AI 保存: intent={metrics:["成本"], needsClarification:true}]

用户: "北京 上海"
[AI 获取上一轮: {metrics:["成本"]}]
[AI 合并: {metrics:["成本"], filters:[{city:["北京","上海"]}]}]
AI: "您想查看什么时间段的数据？" （保留了"成本"信息）

用户: "本月"
[AI 获取累积信息并执行查询]
```

## 测试验证

### 单元测试

- `ConversationContextManagerTest` 验证上下文管理功能

### 集成测试场景

1. **多轮澄清**：验证信息累积
2. **会话切换**：验证不同会话的隔离性
3. **异常恢复**：验证缓存失效后的降级处理

## 后续优化建议

1. **性能优化**

   - 使用 Redis 替代内存缓存
   - 实现更智能的缓存淘汰策略

2. **功能增强**

   - 支持上下文回滚
   - 实现更复杂的冲突解决策略

3. **监控指标**
   - 上下文命中率
   - 澄清轮次统计
   - 意图合并成功率

## 风险与缓解

### 风险 1：内存泄漏

- **缓解**：实现简单的 LRU 缓存，限制最大 1000 个会话

### 风险 2：历史数据不兼容

- **缓解**：优雅降级，当无法解析历史时使用原有的文本摘要

### 风险 3：并发问题

- **缓解**：使用 ConcurrentHashMap，后续可改为分布式缓存

## 总结

通过引入结构化的上下文管理，我们解决了多轮对话中信息丢失的核心问题。这个改进不仅提高了系统的可靠性，也为后续的功能扩展奠定了基础。

### 8. 总结

第一阶段改进已经完成了基础架构的搭建，为后续的优化奠定了基础。下一步可以考虑：

- 实现基于 Redis 的分布式缓存
- 优化 Prompt 模板，进一步提高 NLU 准确性
- 添加更多的监控指标和日志

## 问题修复记录

### 历史消息处理问题（2025-01-26）

#### 问题描述

在测试多轮对话时发现，虽然实现了 `ConversationContextManager`，但历史消息处理仍然存在问题：

- 第一次对话："成本" → AI 正确识别指标
- 第二次对话："北京 成都" → AI 正确识别城市，但对话摘要被污染
- 第三次对话："去年下半年" → AI 忘记了之前的"成本"指标，要求重新输入

#### 问题分析

1. **ConversationContextManager 未被正确使用**：

   - `ConversationManagerImpl` 只保存非澄清意图，但所有测试对话都是澄清意图
   - 导致 `ConversationContextManager.getLastQueryIntent()` 总是返回 null

2. **ConversationHistorySummarizer 的严重缺陷**：
   - 使用简单正则 `[\u4e00-\u9fa5]{2,3}` 匹配城市，会把所有 2-3 个字的中文都当成城市
   - 生成的摘要充满错误信息，如：`城市=[成本、已了解、您想查、看成本...]`
   - AI 被这些错误信息误导，影响后续理解

#### 解决方案

1. **修改意图保存逻辑**：

   ```java
   // 保存意图到上下文管理器（包括澄清意图，因为它们也包含已识别的实体）
   if (intent != null && userContext.getSessionId() != null) {
       contextManager.saveQueryIntent(userContext.getSessionId(), intent);
   }
   ```

2. **停用 ConversationHistorySummarizer**：

   - 当没有历史意图时，直接使用原始对话历史
   - 避免使用有缺陷的正则表达式生成错误摘要

3. **改进后的效果**：
   - 每轮对话的意图都会被保存，包括已识别的实体
   - NLU 能够从结构化的历史意图中准确获取上下文
   - 避免了正则表达式造成的信息污染

### ConversationHistorySummarizer 删除记录（2025-01-26）

经过评估，决定删除 `ConversationHistorySummarizer` 类，原因如下：

1. **没有任何代码引用**：

   - 全项目搜索确认只有该文件本身
   - NluAgentImpl 已经移除了对它的依赖
   - 没有测试类使用它

2. **实现有严重缺陷**：

   - 使用简单正则 `[\u4e00-\u9fa5]{2,3}` 会误识别大量非城市文本
   - 生成的摘要会严重污染对话上下文
   - 是导致多轮对话失败的根本原因

3. **已有更好的替代方案**：
   - `ConversationContextManager` 提供了结构化的上下文管理
   - 基于 JSON 的意图保存和恢复更加可靠
   - 避免了文本解析的不确定性

该类已被安全删除，系统现在完全依赖 `ConversationContextManager` 进行历史上下文管理。

### AI 信息完整性判断问题（2025-01-26）

#### 问题描述

在三轮对话测试中，AI 在第三轮错误地判断信息不完整：

- 第一轮："成本" → AI 识别指标，要求时间
- 第二轮："西安，上海，广州" → AI 识别城市，继续要求时间
- 第三轮："去年下半年" → AI 识别了时间，但错误地说"还需要确认具体的指标"

实际上，此时 AI 的 entities 中已经包含了完整信息：

- metrics: ["成本"]
- filters: [城市过滤、时间过滤]

#### 问题分析

NLU 的 prompt 中缺少明确的规则来判断何时信息已经完整，导致 AI 继续要求澄清。

#### 解决方案

在 prompt 中添加明确的信息完整性判断规则：

1. **系统提示词更新**（nlu_system_prompt.txt）：

   - 添加规则 4：当 entities 中已包含必要信息时，返回 DATA_QUERY

2. **开发者提示词更新**（nlu_developer_prompt.txt）：

   - 在默认行为中添加完整性检查规则
   - 在历史上下文处理中强调合并后的完整性检查

3. **关键改进**：
   - 明确定义"信息完整"：有指标、有时间、有必要的维度/过滤
   - 强调合并历史上下文后要重新评估完整性
   - 提供具体示例帮助 AI 理解

### mergedContext 历史信息丢失和过度澄清问题（2025-01-26）

#### 问题描述

在多轮对话测试中发现两个新问题：

1. **mergedContext 丢失历史信息**：

   - 第一轮："收入" → AI 识别为 metrics: ["销售额"]
   - 第二轮："上海，天津" → mergedContext 只有"上海， 天津"，丢失了"收入"
   - 第三轮："去年 7 月以来" → mergedContext 是"上海， 天津 去年 7 月以来"，仍然没有"收入"

2. **过度澄清问题**：
   - 第三轮时 AI 已经有了完整信息（指标、城市、时间）
   - 但仍然返回 CLARIFICATION_NEEDED，询问是否需要按商品品类分组
   - 这是不必要的澄清，用户没有表达这种需求

#### 问题分析

1. **mergedContext 问题**：

   - Prompt 中对 mergedContext 的定义不够明确
   - AI 理解为只包含当前输入，而不是累积的完整查询意图

2. **过度澄清问题**：
   - Prompt 中缺少明确的"停止澄清"规则
   - AI 倾向于主动询问额外的分组维度

#### 解决方案

1. **修改 mergedContext 定义**（nlu_developer_prompt.txt）：

   - 明确说明 mergedContext 应该包含从历史对话中累积的所有信息
   - 提供具体示例说明如何构建完整的查询描述

2. **加强停止澄清规则**：

   - 在默认行为中添加"停止澄清规则"
   - 明确说明：有指标 + 有时间 = 信息完整，应该执行查询
   - 不要主动询问额外的分组维度

3. **系统提示词更新**（nlu_system_prompt.txt）：
   - 在信息完整性检查中加强判断标准
   - 强调不要过度澄清

这些改进应该能够：

- 确保 mergedContext 正确反映完整的查询意图
- 避免不必要的澄清，提升用户体验

### "每个 X 最高值"查询的 SQL 生成问题（2025-01-26）

#### 问题描述

用户查询"去年每个月收入最高的城市分别是"时：

- 生成的 SQL 返回了所有城市在每个月的销售额
- 没有筛选出每个月销售额最高的那一个城市
- 实际显示的结果只有 2024 年 12 月的数据（所有城市）

#### 问题分析

生成的错误 SQL：

```sql
SELECT city, DATE_FORMAT(dt, '%Y-%m') AS date, SUM(sales_amount) AS 销售额
FROM daily_sales_summary
WHERE YEAR(dt) = YEAR('2025-06-26') - 1
GROUP BY city, DATE_FORMAT(dt, '%Y-%m')
ORDER BY date, 销售额 DESC
```

这个 SQL 会返回所有城市在每个月的数据，而不是每个月最高的那一个。

#### 解决方案

在 SQL 生成 prompt 中添加了专门的规则来处理"每个 X 的最高/最低 Y"模式：

1. **SQL 生成模板更新**（sql_generation_mysql.txt）：

   - 添加了规则 3："每个 X 的最高/最低 Y"查询模式的特殊处理
   - 提供了使用窗口函数 ROW_NUMBER()的正确 SQL 模式
   - 包含了错误示例的说明

2. **正确的 SQL 应该是**：

```sql
SELECT month, city, sales_amount
FROM (
  SELECT
    DATE_FORMAT(dt, '%Y-%m') as month,
    city,
    SUM(sales_amount) as sales_amount,
    ROW_NUMBER() OVER (
      PARTITION BY DATE_FORMAT(dt, '%Y-%m')
      ORDER BY SUM(sales_amount) DESC
    ) as rn
  FROM daily_sales_summary
  WHERE YEAR(dt) = 2024
  GROUP BY DATE_FORMAT(dt, '%Y-%m'), city
) ranked
WHERE rn = 1
ORDER BY month
```

这样可以确保只返回每个月销售额最高的城市。

### SQL 验证器对子查询的处理问题（2025-01-26）

#### 问题描述

当生成包含子查询的 SQL 时，SQL 验证器报错：

```
SQL查询的表名 '(' != 期望的表名 'daily_sales_summary'
```

生成的正确 SQL：

```sql
SELECT month, city, sales_amount
FROM (
  SELECT DATE_FORMAT(dt, '%Y-%m') as month, ...
  FROM daily_sales_summary
  ...
) ranked
WHERE rn = 1
```

#### 问题分析

原始的 SQL 验证逻辑过于简单：

1. 通过 `split("\\s+")[0]` 获取 FROM 后的第一个词
2. 当遇到子查询时，获取到的是 `(` 而不是表名
3. 导致验证失败，抛出"未授权的表"错误

#### 解决方案

重写了 `validateSql` 方法（QueryPlannerAgentImpl.java）：

1. 使用正则表达式查找所有 FROM 子句中的表名
2. 忽略子查询别名（如 "ranked", "subquery"）
3. 确保只访问了授权的表
4. 支持复杂的 SQL 结构，包括子查询和窗口函数

这样可以正确验证包含子查询的复杂 SQL 语句。
