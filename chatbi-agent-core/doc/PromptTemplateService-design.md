// ------------------- 接口定义 -------------------
// 文件路径: com/qding/chatbi/agent/service/prompt/PromptTemplateService.java

package com.qding.chatbi.agent.service.prompt;

import dev.langchain4j.model.input.PromptTemplate;

/**
 * Prompt模板服务接口。
 * 负责加载、缓存和提供具名的Prompt模板。
 */
public interface PromptTemplateService {

    /**
     * 根据模板名称获取一个PromptTemplate实例。
     *
     * @param templateName 模板的名称（通常对应于 resources/prompts/ 目录下的文件名，不含后缀）。
     * @return 一个可用的 PromptTemplate 实例。
     * @throws RuntimeException 如果模板文件未找到或加载失败。
     */
    PromptTemplate getPromptTemplate(String templateName);
}


// ------------------- 实现类 -------------------
// 文件路径: com/qding/chatbi/agent/service/prompt/PromptTemplateServiceImpl.java

package com.qding.chatbi.agent.service.prompt.impl;

import com.qding.chatbi.agent.service.prompt.PromptTemplateService;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PromptTemplateService的实现类。
 * 从类路径下的 /prompts 目录加载模板文件，并进行内存缓存。
 */
@Slf4j
@Service
public class PromptTemplateServiceImpl implements PromptTemplateService {

    private final ResourceLoader resourceLoader;
    // 使用 ConcurrentHashMap 作为线程安全的本地缓存
    private final Map<String, PromptTemplate> templateCache = new ConcurrentHashMap<>();

    @Autowired
    public PromptTemplateServiceImpl(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 根据模板名称获取一个PromptTemplate实例。
     * 优先从缓存中获取，如果缓存未命中，则从文件加载并存入缓存。
     *
     * @param templateName 模板的名称。
     * @return 一个可用的 PromptTemplate 实例。
     */
    @Override
    public PromptTemplate getPromptTemplate(String templateName) {
        // 使用computeIfAbsent保证线程安全和原子性操作
        return templateCache.computeIfAbsent(templateName, this::loadTemplateFromFile);
    }

    /**
     * 从类路径下的文件中加载Prompt模板。
     *
     * @param templateName 模板名称。
     * @return PromptTemplate实例。
     */
    private PromptTemplate loadTemplateFromFile(String templateName) {
        try {
            // 构造资源路径，例如 "classpath:prompts/nlu_intent_extraction.txt"
            String resourcePath = "classpath:prompts/" + templateName + ".txt";
            log.info("Loading prompt template from: {}", resourcePath);
            
            Resource resource = resourceLoader.getResource(resourcePath);
            if (!resource.exists()) {
                log.error("Prompt template file not found: {}", resourcePath);
                throw new RuntimeException("Prompt template file not found: " + resourcePath);
            }
            
            // 读取文件内容为字符串
            String templateString = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            
            // 使用LangChain4j的工具类创建PromptTemplate实例
            return PromptTemplate.from(templateString);
            
        } catch (IOException e) {
            log.error("Failed to load prompt template: {}", templateName, e);
            throw new RuntimeException("Failed to load prompt template: " + templateName, e);
        }
    }
}
