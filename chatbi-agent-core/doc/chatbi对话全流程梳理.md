# **端到端数据查询流程梳理与评估**

本文档旨在全面梳理当用户发起一次数据查询时，ChatBI系统内部从接收请求到最终获取数据的完整工作流。同时，本文档将评估当前设计的完整性，并识别出下一步需要完善的关键环节。

## **1\. 完整流程梳理**

整个流程是一个精心编排的责任链，每个组件各司其职，将用户的自然语言问题逐步转化为最终的数据库结果。

### **1.1. 流程概览图 (Sequence Diagram)**

sequenceDiagram  
    participant User as 用户  
    participant API as chatbi-api  
    participant ConvSvc as ConversationServiceImpl  
    participant ConvMgr as ConversationManager  
    participant NLU as NluAgent  
    participant Planner as QueryPlannerAgent  
    participant Retriever as DataRetrievalAgent  
    participant DataAccess as DataAccessService  
    participant DB as 业务数据库

    User-\>\>+API: 发送查询请求 (e.g., "上个月北京的GMV")  
    API-\>\>+ConvSvc: handleQuery(userRequest)  
    ConvSvc-\>\>+ConvMgr: process(userContext, userQuery)  
      
    ConvMgr-\>\>+NLU: understand(userContext, userQuery)  
    Note right of NLU: 1\. 获取用户权限内的数据集\<br/\>2. 检索知识库(术语/范例)\<br/\>3. 调用LLM(nlu\_intent\_extraction)  
    NLU--\>\>-ConvMgr: 返回 StructuredQueryIntent  
      
    ConvMgr-\>\>+Planner: plan(userContext, structuredIntent)  
    Note right of Planner: 1\. 选择最佳数据集\<br/\>2. 构建Prompt(sql\_generation)\<br/\>3. 调用LLM生成SQL\<br/\>4. 验证SQL  
    Planner--\>\>-ConvMgr: 返回 QueryPlan (含SQL)  
      
    ConvMgr-\>\>+Retriever: retrieve(queryPlan)  
    Retriever-\>\>+DataAccess: executeQuery(dataSourceId, sql)  
    DataAccess-\>\>+DB: 执行SQL查询  
    DB--\>\>-DataAccess: 返回结果集  
    DataAccess--\>\>-Retriever: 返回 QueryResult DTO  
    Retriever--\>\>-ConvMgr: 返回 QueryResult DTO  
      
    ConvMgr--\>\>-ConvSvc: 返回 AgentResponse (包含QueryResult)  
    ConvSvc--\>\>-API: 返回给Controller  
    API--\>\>-User: 响应查询结果

### **1.2. 详细步骤拆解**

1. **【接收请求】 chatbi-api 模块**  
   * ConversationController接收到用户的HTTP请求。  
   * 它从请求中解析出用户信息和查询内容，并调用ConversationServiceImpl。  
2. **【流程入口】 ConversationServiceImpl (位于 chatbi-agent-core)**  
   * 作为Agent核心的门面，它负责构建UserContextDTO（包含用户ID、角色等）。  
   * 调用ConversationManager来启动整个处理流程。  
3. **【总协调】 ConversationManager (位于 chatbi-agent-core)**  
   * 这是一个总指挥。它按顺序调用NLU、Planner和Retriever。  
   * **调用NLU**: nluAgent.understand(...)  
   * **获取意图**: 接收NluAgent返回的StructuredQueryIntent对象。  
   * **调用Planner**: queryPlannerAgent.plan(...)  
   * **获取计划**: 接收QueryPlannerAgent返回的QueryPlan对象。  
   * **调用Retriever**: dataRetrievalAgent.retrieve(...)  
   * **获取数据**: 接收DataRetrievalAgent返回的QueryResult对象。  
   * 最后，它将QueryResult封装进一个AgentResponse DTO中，并返回给ConversationServiceImpl。  
4. **【意图理解】 NluAgent (位于 chatbi-agent-core)**  
   * 接收到用户问题和上下文。  
   * **核心任务**: 将自然语言转化为机器可读的结构化意图StructuredQueryIntent。  
   * **实现**: 调用NluExtractionChain，后者会构建一个包含用户问题、历史对话、有权限的数据集和相关知识库的Prompt，调用大模型（LLM）来完成意图提取。  
5. **【查询规划】 QueryPlannerAgent (位于 chatbi-agent-core)**  
   * 接收StructuredQueryIntent。  
   * **核心任务**: 生成一个安全、可执行的QueryPlan，其中包含最终的SQL语句。  
   * 实现:  
     a. 根据意图评分，选择一个最佳的Dataset。  
     b. 调用SqlGenerationChain，后者会构建一个包含DDL、列定义、用户问题等信息的更专业的Prompt。  
     c. LLM返回SQL语句。  
     d. 对返回的SQL进行安全性和有效性验证。  
6. **【数据检索】 DataRetrievalAgent (位于 chatbi-agent-core)**  
   * 接收QueryPlan。  
   * **核心任务**: 执行SQL并返回结果。  
   * 实现:  
     a. 从plan中的datasetId找到对应的dataSourceId。  
     b. 调用注入的DataAccessTools的executeQuery(dataSourceId, sql)方法。  
7. **【数据执行】 DataAccessService (位于 chatbi-data-access-service)**  
   * 接收dataSourceId和sql。  
   * **核心任务**: 管理数据库连接并执行查询。  
   * 实现 (DataAccessServiceImpl):  
     a. 通过dataSourceId在内部缓存中查找或创建一个动态的HikariDataSource连接池。  
     b. 使用JdbcTemplate执行SQL。  
     c. 将ResultSet映射为统一的QueryResult DTO并返回。

## **2\. 当前设计评估**

### **2.1. 已完善的部分**

经过我们之前的讨论，从**用户提问 \-\> 意图理解 \-\> SQL规划 \-\> 数据库执行 \-\> 结果返回**的核心数据链路已经设计得相当清晰和健壮。

* **模块职责清晰**: 每个模块（api, agent-core, data-access-service）的边界和职责都很明确。  
* **Agent内部流程合理**: Agent内部的三个核心阶段（NLU, Plan, Retrieve）分工合理，符合主流Agent设计思想。  
* **安全与隔离**: 数据源的敏感信息被安全地隔离在data-access-service中，上层模块无法触及。  
* **可扩展性好**: “模块化单体”的设计为未来向微服务演进奠定了良好基础。

### **2.2. 待完善的关键部分**

当前流程的终点是ConversationManager从DataRetrievalAgent处获取到了一个原始的QueryResult对象。这只是完成了“查询”，但距离一次良好的人机“对话”还缺少至关重要的一环。

**核心待完善点：结果的解读与呈现 (Result Interpretation and Presentation)**

QueryResult对象包含了原始的表格数据，但系统还不知道：

1. **如何用自然语言向用户汇报结果？** 我们不能直接把一个JSON表格丢给用户，而是应该说：“您好，您要查询的‘上个月北京的GMV’是：5,800万元。”  
2. **查询结果的业务含义是什么？** 系统只拿到了一个数字58000000，它如何知道这代表“GMV”？如何知道单位是“元”？  
3. **应该如何可视化？** 如果返回的是一个二维表格，系统能否智能地判断出最适合的图表类型（例如，时间序列数据适合用折线图，分类对比数据适合用柱状图）？  
4. **查询成功但无数据怎么办？** 如果QueryResult的rows为空，系统应该如何回复？是直接说“没查到”，还是更友好地提示“在您指定的条件下，系统没有找到相关数据。”？

所有这些问题，都需要由我们接下来要设计的 **ResponseGenerator** 组件来解决。它是连接原始数据和最终用户体验的“最后一公里”，其设计的好坏将直接决定产品的智能化程度和用户满意度。

现在，我们对整个流程和当前状态有了清晰的认识。我建议，我们可以正式开始讨论 **ResponseGenerator** 的设计细节了。