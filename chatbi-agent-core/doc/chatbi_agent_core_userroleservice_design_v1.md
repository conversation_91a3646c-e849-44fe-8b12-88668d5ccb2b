## **Chat BI \- UserRoleService详细设计 (chatbi-agent-core)**

**模块路径**: com.qding.chatbi.agent.service.UserRoleService (接口) 及其实现类。

### **1\. 定位与用途**

UserRoleService 是一个核心服务接口，其唯一职责是根据传入的用户ID (userId)，返回该用户所拥有的角色名称列表 (List\<String\>)。

它是我们系统中连接**用户身份**和**权限控制**的桥梁。后续的 QueryPlannerAgent 会利用这个接口返回的角色列表，去 dataset\_access\_permissions 表中查询该用户可以访问哪些数据集，从而实现数据的安全隔离。

### **2\. UserRoleService.java (接口定义)**

此接口定义了获取用户角色的服务契约，非常简洁。

package com.qding.chatbi.agent.service;

import java.util.List;

/\*\*  
 \* 用户角色服务接口。  
 \* 负责根据用户ID获取其所拥有的角色列表。  
 \*/  
public interface UserRoleService {

    /\*\*  
     \* 根据用户ID获取其所拥有的角色列表。  
     \*  
     \* @param userId 用户的唯一标识，不能为空。  
     \* @return 一个包含角色名称字符串的列表，例如 \["Admin", "Developer"\]。  
     \* 如果用户不存在或没有任何角色，应返回一个空列表 (never null)。  
     \*/  
    List\<String\> getRolesForUser(String userId);  
}

### **3\. 实现策略与示例代码**

UserRoleService 的具体实现方式高度依赖于您企业现有的用户和权限管理体系。以下是两种主要的实现策略：

#### **策略一：模拟实现 (用于开发和测试阶段)**

在项目初期或测试环境中，我们可以创建一个基于硬编码逻辑的模拟实现，以便在不依赖外部系统的情况下进行开发和测试。

* **文件路径**: com.qding.chatbi.agent.service.impl.MockUserRoleServiceImpl.java  
* **代码示例**:  
  package com.qding.chatbi.agent.service.impl;

  import com.qding.chatbi.agent.service.UserRoleService;  
  import org.springframework.context.annotation.Profile;  
  import org.springframework.stereotype.Service;

  import java.util.Collections;  
  import java.util.List;  
  import java.util.Map;

  /\*\*  
   \* UserRoleService的模拟实现，用于开发和测试环境。  
   \* 使用 @Profile("\!prod") 注解，表示这个Bean只在非生产环境(non-prod)下被激活。  
   \*/  
  @Service  
  @Profile("\!prod")   
  public class MockUserRoleServiceImpl implements UserRoleService {

      // 硬编码的用户角色映射  
      // Key: userId, Value: List of role names  
      private static final Map\<String, List\<String\>\> USER\_ROLES\_MAP \= Map.of(  
          "admin-user",   List.of("Admin"),           // 管理员用户  
          "dev-user",     List.of("Developer"),        // 开发者用户  
          "employee-user",List.of("Employee"),         // 普通员工用户  
          "test-user-001",List.of("Admin", "Developer") // 一个拥有多个角色的测试用户  
      );

      @Override  
      public List\<String\> getRolesForUser(String userId) {  
          if (userId \== null || userId.trim().isEmpty()) {  
              return Collections.emptyList();  
          }  
          // 根据userId返回预设的角色列表，如果找不到则默认返回 "Employee" 角色  
          return USER\_ROLES\_MAP.getOrDefault(userId, Collections.singletonList("Employee"));  
      }  
  }

* **如何使用**: 当您的Spring Boot应用以非prod的profile（例如 dev 或 test）运行时，Spring容器会自动装配这个MockUserRoleServiceImpl的实例。

#### **策略二：生产环境实现 (连接真实系统)**

在生产环境中，您需要提供一个连接真实用户/权限系统的实现。

* **文件路径**: com.qding.chatbi.agent.service.impl.ProductionUserRoleServiceImpl.java  
* **注解**: @Service @Profile("prod")  
* **实现逻辑 (根据您的系统选择一种)**:  
  * **a) 如果用户角色关系也存储在本项目的数据库中**:  
    * 假设我们新增了一张 user\_role\_assignments 表 (user\_id, role\_id)。  
    * 这个服务会注入对应的JPA Repository (UserRoleAssignmentRepository)，然后通过数据库查询来获取角色列表。  
  * **b) 如果通过API调用外部权限中心**:  
    * 这个服务会注入一个HTTP客户端（如 RestTemplate 或 WebClient）或者一个Feign客户端。  
    * getRolesForUser 方法内部会发起一个API请求到您的权限中心，获取指定userId的角色信息。

  // 伪代码示例  
      // @Service  
      // @Profile("prod")  
      // public class ProductionUserRoleServiceImpl implements UserRoleService {  
      //     private final AuthCenterApiClient authCenterApiClient; // 假设的Feign客户端  
      //  
      //     public ProductionUserRoleServiceImpl(AuthCenterApiClient authCenterApiClient) {  
      //         this.authCenterApiClient \= authCenterApiClient;  
      //     }  
      //  
      //     @Override  
      //     public List\<String\> getRolesForUser(String userId) {  
      //         // 调用外部API，并处理异常  
      //         try {  
      //             return authCenterApiClient.getRoles(userId);  
      //         } catch (Exception e) {  
      //             // log.error(...)  
      //             return Collections.emptyList();  
      //         }  
      //     }  
      // }

  * **c) 如果通过LDAP/Active Directory**:  
    * 这个服务会使用Spring LDAP或Java的JNDI来连接LDAP服务器，并根据用户的DN（Distinguished Name）查询其所属的用户组（memberOf属性），这些用户组就可以映射为我们的角色。

### **总结**

UserRoleService 是一个关键的**适配器接口**，它将我们系统内部的权限需求与外部多样化的用户管理系统解耦。

通过定义清晰的接口 (UserRoleService) 和利用Spring Profiles提供不同的实现 (MockUserRoleServiceImpl 和 ProductionUserRoleServiceImpl)，我们可以保证系统在开发、测试和生产环境中的灵活性和可维护性。