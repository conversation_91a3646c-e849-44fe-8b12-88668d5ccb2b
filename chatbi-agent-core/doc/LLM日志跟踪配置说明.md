# ChatBI LLM日志跟踪配置说明

## 概述

本文档说明了ChatBI项目中实现的LLM请求和响应的详细日志跟踪功能。通过这些日志，开发人员可以快速定位问题，分析模型行为，优化提示词设计。

## 架构设计

### 核心组件

1. **PromptLoggingInterceptor** - 请求日志拦截器
   - 位置：`chatbi-agent-core/src/main/java/com/qding/chatbi/agent/config/PromptLoggingInterceptor.java`
   - 功能：记录发送给LLM的详细请求信息

2. **ResponseLoggingInterceptor** - 响应日志拦截器
   - 位置：`chatbi-agent-core/src/main/java/com/qding/chatbi/agent/config/ResponseLoggingInterceptor.java`
   - 功能：记录从LLM接收的响应信息和性能统计

### 集成的Chain组件

以下Chain组件已经集成了详细的日志跟踪：

- `SqlGenerationChain` - SQL生成链
- `DataAnalysisChain` - 数据分析链  
- `DataVisualizationChain` - 数据可视化链
- `ChartGenerationChain` - 图表生成链
- `NluAgentImpl` - 自然语言理解代理

## 日志格式说明

### 请求日志格式

```
╔══════════════════════════════════════════════════════════════════
║                    LLM Request Start [12ab34cd]                    
╠══════════════════════════════════════════════════════════════════
║ 📋 Request Context: SQL Generation
║ 📏 Prompt Length: 1234 characters
║ 📊 Estimated Tokens: ~308
╠══════════════════════════════════════════════════════════════════
║ 💬 Prompt Content:
║ ┌─────────────────────────────────────────────────────────────
║ │ You are a SQL generation expert...
║ │ [具体的Prompt内容]
║ └─────────────────────────────────────────────────────────────
╚══════════════════════════════════════════════════════════════════
```

### 响应日志格式

```
╔══════════════════════════════════════════════════════════════════
║                 LLM Response Received [56ef78gh]                  
╠══════════════════════════════════════════════════════════════════
║ 🎯 Context: SQL Generation
║ ⏱️ Processing Time: 2340ms
║ 📤 Request Length: 1234 chars
║ 📥 Response Length: 567 chars
║ 📊 Request Tokens (est.): ~308
║ 📊 Response Tokens (est.): ~142
║ 💰 Total Tokens (est.): ~450
║ ⚡ Fast processing time: 2340ms
╠══════════════════════════════════════════════════════════════════
║ 🤖 Response Content:
║ ┌─────────────────────────────────────────────────────────────
║ │ SELECT column1, column2...
║ │ [具体的响应内容]
║ └─────────────────────────────────────────────────────────────
╚══════════════════════════════════════════════════════════════════
```

### 错误日志格式

```
╔══════════════════════════════════════════════════════════════════
║                 LLM Response Error [9ijk12lm]                     
╠══════════════════════════════════════════════════════════════════
║ 🎯 Context: SQL Generation
║ ⏱️ Processing Time: 15000ms
║ 📤 Request Length: 1234 chars
║ ❌ Error Type: TimeoutException
║ 📝 Error Message: Request timeout after 15 seconds
╠══════════════════════════════════════════════════════════════════
║ 📤 Request Content (Error Context): 
║ ┌─────────────────────────────────────────────────────────────
║ │ [导致错误的请求内容]
║ └─────────────────────────────────────────────────────────────
╚══════════════════════════════════════════════════════════════════
```

## 配置选项

### 日志级别配置

在 `application.yml` 中配置详细的日志级别：

```yaml
logging:
  level:
    com.qding.chatbi: DEBUG
    com.qding.chatbi.agent.config: DEBUG
    com.qding.chatbi.agent.chain: DEBUG
    com.qding.chatbi.agent.service.impl: DEBUG
    # 启用LangChain4j的详细日志
    dev.langchain4j: DEBUG
    root: INFO
```

### 内容截断控制

拦截器会智能处理长内容：

- **短内容** (≤2000字符)：完整显示
- **中等内容** (2000-4000字符)：显示前1000字符和后200字符
- **长内容** (>4000字符)：显示前800字符和后300字符

## 性能监控功能

### 处理时间分析

系统会自动分析每次LLM调用的处理时间：

- ⚡ **快速**：< 5秒
- 🐌 **中等**：5-10秒  
- ⚠️ **慢速**：> 10秒（会特别标注）

### Token使用统计

每次调用都会估算Token使用量：

- 输入Token估算：字符数 ÷ 4
- 输出Token估算：字符数 ÷ 4
- 总Token使用量统计

## 调试指南

### 如何快速定位问题

1. **搜索请求ID**：每个请求都有8位随机ID，便于在日志中追踪
2. **查看Context标识**：根据业务场景快速筛选相关日志
3. **关注错误日志**：重点查看带有`❌`标识的错误信息
4. **分析处理时间**：关注慢查询和超时情况

### 常见问题排查

#### SQL生成问题
搜索日志关键词：`SQL Generation`，查看Prompt内容和生成的SQL

#### 数据分析问题  
搜索日志关键词：`Data Analysis`，检查输入数据和分析结果

#### NLU理解问题
搜索日志关键词：`NLU`或`Function Calling`，分析意图理解过程

## 最佳实践

### 开发环境
- 保持 `DEBUG` 级别的日志
- 定期清理日志文件，避免磁盘空间不足
- 使用日志搜索工具快速定位问题

### 生产环境注意事项
- 考虑调整为 `INFO` 级别，减少日志量
- 注意敏感信息的处理（虽然当前不考虑安全问题）
- 监控日志文件大小和磁盘使用情况

### 性能调优建议
- 通过Token使用统计优化Prompt长度
- 根据处理时间分析调整模型配置
- 监控错误率和成功率趋势

## 扩展功能

### 未来可以添加的功能

1. **日志持久化**：将日志存储到数据库
2. **指标收集**：集成Prometheus等监控系统
3. **实时分析**：构建实时日志分析仪表板
4. **智能告警**：基于错误率和性能指标的自动告警

## 技术实现细节

### 依赖注入方式
所有Chain组件通过Spring的构造器注入方式获取日志拦截器实例：

```java
@Autowired
public SqlGenerationChain(ChatLanguageModel chatModel, 
                         PromptTemplateService promptTemplateService, 
                         ObjectMapper objectMapper,
                         PromptLoggingInterceptor promptLoggingInterceptor,
                         ResponseLoggingInterceptor responseLoggingInterceptor) {
    // ...
}
```

### 错误处理机制
每个LLM调用都包装在try-catch块中，确保错误信息被正确记录：

```java
try {
    response = chatModel.generate(prompt.text());
    responseLoggingInterceptor.logLLMResponse(prompt.text(), response, processingTime, context);
} catch (Exception e) {
    responseLoggingInterceptor.logErrorResponse(prompt.text(), e, processingTime, context);
    throw new RuntimeException("处理失败: " + e.getMessage(), e);
}
```

## 结论

通过实施这套完整的LLM日志跟踪系统，ChatBI项目具备了强大的调试和问题定位能力。开发人员可以快速理解模型行为，优化提示词设计，提升整体系统的稳定性和性能。 