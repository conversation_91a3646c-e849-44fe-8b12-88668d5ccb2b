# **在 LangChain4j 中实现全局LLM Prompt日志记录的最佳实践**

## **1\. 概述**

在开发基于大型语言模型（LLM）的应用时，一个核心的调试需求是能够清晰地看到每次调用LLM时，我们究竟向它发送了什么内容（即 "Prompt"）。这对于理解模型行为、优化提示词以及排查问题至关重要。

### **1.1. 面临的问题**

直接在每个调用LLM的业务代码处手动添加日志，会造成代码冗余、耦合度高，且容易遗漏。特别是当使用像 AiServices 这样高度封装的工具时，我们几乎没有直接的切入点来添加日志逻辑。

### **1.2. 解决方案**

LangChain4j 框架提供了一个强大且优雅的解决方案：**拦截器 (Interceptors)**。通过实现框架提供的拦截器接口，我们可以在不修改任何业务代码的前提下，创建一个全局的“钩子”，自动捕获所有发往LLM的请求，从而实现集中式的日志记录。

本文档将详细介绍如何使用 ChatModelRequestInterceptor 来实现这一目标。

## **2\. 核心原理：LangChain4j 拦截器机制**

LangChain4j 的拦截器机制遵循经典的设计模式——**“责任链模式”** 或 \*\*“AOP（面向切面编程）”\*\*思想。

当您构建一个 ChatLanguageModel 实例时，可以为其注册一个或多个拦截器。当您通过这个模型实例与LLM交互时（无论是直接调用 .generate() 还是通过 AiServices 间接调用），整个流程会像这样：

graph TD  
    A\[业务代码发起调用\] \--\> B{ChatLanguageModel 实例};  
    B \--\> C(执行请求拦截器\<br\>ChatModelRequestInterceptor);  
    C \--\> D\[向 LLM API 发送请求\];  
    D \--\> E(执行响应拦截器\<br\>ChatModelResponseInterceptor);  
    E \--\> F\[业务代码接收结果\];

    subgraph LangChain4j 内部  
        B  
        C  
        D  
        E  
    end

    style C fill:\#d4edda,stroke:\#155724  
    style E fill:\#fff3cd,stroke:\#856404

我们的目标就是在 **步骤C** 中加入日志逻辑。ChatModelRequestInterceptor 接口提供了一个 onBeforeSend 方法，该方法会在请求被实际发送到LLM的API端点之前被调用，并接收完整的请求对象 ChatModelRequest 作为参数。这正是我们记录Prompt的完美时机。

## **3\. 实现步骤**

实现全局Prompt日志记录仅需两步：创建一个拦截器类，并将其注册到ChatLanguageModel的Bean中。

### **步骤一：创建日志拦截器 PromptLoggingInterceptor.java**

首先，我们需要创建一个实现了 ChatModelRequestInterceptor 接口的 Spring 组件。这个类将负责具体的日志打印逻辑。

**代码实现 (PromptLoggingInterceptor.java)**:

package com.qding.chatbi.agent.config;

import dev.langchain4j.data.message.ChatMessage;  
import dev.langchain4j.model.interceptor.ChatModelRequestInterceptor;  
import dev.langchain4j.model.protocol.ChatModelRequest;  
import lombok.extern.slf4j.Slf4j;  
import org.springframework.stereotype.Component;

import java.util.List;

/\*\*  
 \* LLM 请求拦截器，用于在发送给大模型前记录完整的 Prompt 内容。  
 \* This interceptor logs the complete prompt content before it is sent to the Large Language Model.  
 \*/  
@Slf4j  
@Component  
public class PromptLoggingInterceptor implements ChatModelRequestInterceptor {

    @Override  
    public ChatModelRequest onBeforeSend(ChatModelRequest request) {  
        log.info("==================== LLM Prompt Request Start \====================");

        // 获取并打印所有消息。  
        // 对于现代LLM，一个"Prompt"实际上是一个消息列表。  
        // 记录所有消息对于理解完整上下文至关重要。  
        List\<ChatMessage\> messages \= request.messages();  
        for (int i \= 0; i \< messages.size(); i++) {  
            ChatMessage message \= messages.get(i);  
            // 打印每条消息的类型和详细内容，便于调试。  
            log.info("\[Message {}\] Type: {}, Content:\\n---\\n{}\\n---",  
                    i,  
                    message.type(),  
                    message.text());  
        }

        // 如果您的模型支持 Function Calling/Tools，这里也可以打印工具的定义。  
        // 这对于调试Agent行为非常有帮助。  
        if (request.toolSpecifications() \!= null && \!request.toolSpecifications().isEmpty()) {  
            log.info("\[Tools Definitions\]\\n---\\n{}\\n---", request.toolSpecifications());  
        }

        log.info("==================== LLM Prompt Request End \======================");

        // 必须原样返回请求对象，否则请求将不会被发送。  
        return request;  
    }  
}

### **步骤二：在Spring配置中注册拦截器 LangChainConfig.java**

创建好拦截器后，我们需要告诉 LangChain4j 去使用它。最佳实践是在定义 ChatLanguageModel 的Spring @Bean 时，通过其构建器（Builder）注入。

**修改 config/LangChainConfig.java**:

package com.qding.chatbi.agent.config;

import dev.langchain4j.model.chat.ChatLanguageModel;  
import dev.langchain4j.model.openai.OpenAiChatModel;  
import org.springframework.beans.factory.annotation.Autowired;  
import org.springframework.context.annotation.Bean;  
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration  
public class LangChainConfig {

    // 1\. 通过 @Autowired 注入我们创建的日志拦截器Bean  
    @Autowired  
    private PromptLoggingInterceptor promptLoggingInterceptor;

    @Bean  
    public ChatLanguageModel chatLanguageModel() {  
        // 在这里配置您的大模型，例如 OpenAI, ZhipuAI, Google Gemini 等  
        return OpenAiChatModel.builder()  
                .apiKey("YOUR\_API\_KEY") // 强烈建议从安全的配置源读取  
                .modelName("gpt-4-turbo") // 根据需要选择模型  
                .timeout(Duration.ofSeconds(60))  
                  
                // 2\. 关键步骤：在这里通过 .addRequestInterceptor() 添加请求拦截器  
                // 可以添加多个拦截器，它们会按照添加的顺序依次执行  
                .addRequestInterceptor(promptLoggingInterceptor)  
                  
                // 提示：您也可以用 .addResponseInterceptor() 添加响应拦截器来记录模型的返回内容  
                  
                .logRequests(true) // LangChain4j内置的简要日志，建议开启  
                .logResponses(true)  
                .build();  
    }  
      
    // ... 项目中其他的Bean定义  
}

## **4\. 方案优势与注意事项**

### **4.1. 优势**

* **全局覆盖 (Global Coverage)**：一次配置，项目中所有通过 chatLanguageModel Bean 发起的LLM调用都会被自动记录，无需担心遗漏。  
* **低侵入性 (Non-Invasive)**：完全无需修改任何现有的业务逻辑代码（如 NluAgentImpl、SqlGenerationChain 等）。这遵循了“开闭原则”，使得代码整洁、解耦且易于维护。  
* **职责单一 (Single Responsibility)**：日志记录的逻辑被完全封装在 PromptLoggingInterceptor 中，职责非常清晰。未来如果需要将日志输出到文件或消息队列，也只需修改这一个地方。  
* **框架原生支持 (Framework-Native)**：这是LangChain4j官方设计的标准扩展方式，比自己实现AOP切面等方式更稳定、更简单、更符合框架的设计思想。

### **4.2. 注意事项**

* **响应拦截**：同理，您可以通过实现 ChatModelResponseInterceptor 接口来拦截模型的响应，记录模型的输出、Token用量等信息。  
* **性能影响**：对于大多数应用，简单的日志记录对性能的影响可以忽略不计。但如果日志内容非常庞大，或需要进行复杂的序列化操作，请注意其可能带来的微小延迟。  
* **信息安全**：Prompt中可能包含敏感信息。在生产环境中，请确保您的日志系统符合公司的安全和合规要求，避免敏感数据泄露。