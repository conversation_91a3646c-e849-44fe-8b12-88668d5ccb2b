# NLU 提示词优化实施计划

## 一、实施目标

解决三个核心问题：

1. **时间维度遗漏**：确保所有包含指标的查询都有时间范围
2. **历史消息混乱**：简化澄清回答的处理逻辑
3. **提示词复杂度**：减少 60% 的内容，提高执行准确性

## 二、分阶段实施

### 第一阶段：小范围测试（1 周）

#### 2.1 准备工作

```java
// 在 NluAgentImpl 中添加配置开关
@Value("${chatbi.nlu.prompt.version:v1}")
private String promptVersion;

private String selectPromptTemplate() {
    return "v2".equals(promptVersion)
        ? "nlu_function_calling_agent_v2.txt"
        : "nlu_function_calling_agent.txt";
}
```

#### 2.2 测试用例

| 场景     | 输入序列                   | 预期行为       |
| -------- | -------------------------- | -------------- |
| 时间澄清 | "成本" → "上海"            | 必须澄清时间   |
| 完整查询 | "上海本月成本"             | 直接执行       |
| 多轮澄清 | "销售额" → "北京" → "去年" | 正确合并上下文 |

### 第二阶段：A/B 测试（2 周）

#### 2.2 监控指标

```java
// 添加监控指标
@Component
public class NluMetrics {
    // 时间维度遗漏率
    private final Counter timeDimensionMissing = Counter.builder("nlu.time.missing")
        .description("查询缺少时间维度的次数")
        .register(meterRegistry);

    // 平均澄清轮次
    private final Summary clarificationRounds = Summary.builder("nlu.clarification.rounds")
        .description("获得完整信息需要的澄清轮次")
        .register(meterRegistry);
}
```

#### 2.3 对比测试

- 50% 流量使用 v1 版本
- 50% 流量使用 v2 版本
- 收集两周数据进行对比

### 第三阶段：全量发布（1 周）

#### 3.1 发布检查清单

- [ ] 时间维度遗漏率 < 5%
- [ ] 平均澄清轮次 < 3
- [ ] 响应时间提升 > 20%
- [ ] 用户满意度无下降

#### 3.2 回滚方案

```yaml
# application.yml 快速切换
chatbi:
  nlu:
    prompt:
      version: v1 # 快速回滚到 v1
```

## 三、配套优化

### 3.1 日志优化（已完成）

- 保留提示词调试关键日志
- 简化业务流程日志
- 统一日志标记格式

### 3.2 前端提示优化

```javascript
// 在前端添加时间提示
const timeHints = {
  noTime: "提示：查询指标数据时需要指定时间范围，如'本月'、'去年'等",
  examples: ["本月销售额", "去年Q4成本", "最近7天订单数"],
};
```

### 3.3 默认时间策略（可选）

```java
// 可配置的默认时间策略
public class DefaultTimeStrategy {
    public String getDefaultTime(String queryType) {
        switch (queryType) {
            case "RANKING_QUERY":
            case "AGGREGATION_QUERY":
                return "本月";
            case "DETAIL_QUERY":
                return "最近7天";
            case "TREND_QUERY":
                return null; // 趋势必须明确时间
        }
    }
}
```

## 四、风险评估

### 4.1 潜在风险

1. **过度澄清**：可能导致用户体验下降
2. **语义理解偏差**：简化后可能丢失某些边缘情况
3. **兼容性问题**：与现有系统的集成

### 4.2 缓解措施

1. **智能默认值**：对常见场景提供合理默认
2. **用户教育**：在 UI 层面提示完整查询格式
3. **渐进式发布**：分阶段推进，及时收集反馈

## 五、成功标准

### 5.1 量化指标

- **时间维度覆盖率**：> 95%
- **首次查询成功率**：> 70%
- **Token 消耗**：减少 40%
- **响应延迟**：减少 30%

### 5.2 质量指标

- 用户反馈积极
- 开发团队认可简化后的维护性
- 系统稳定性无影响

## 六、时间线

```mermaid
gantt
    title NLU 提示词优化时间线
    dateFormat YYYY-MM-DD
    section 准备阶段
    创建 v2 提示词    :done, 2025-01-26, 1d
    添加配置开关      :2025-01-27, 2d
    准备测试用例      :2025-01-29, 2d
    section 测试阶段
    小范围测试        :2025-01-31, 7d
    A/B 测试         :2025-02-07, 14d
    section 发布阶段
    数据分析         :2025-02-21, 3d
    全量发布         :2025-02-24, 2d
    监控观察         :2025-02-26, 5d
```

## 七、关键决策点

1. **2025-02-07**：根据小范围测试结果决定是否继续
2. **2025-02-21**：根据 A/B 测试数据决定是否全量发布
3. **2025-03-03**：根据全量发布效果决定是否需要进一步优化

## 八、总结

通过简化 NLU 提示词，我们期望：

1. 彻底解决时间维度遗漏问题
2. 提升系统响应速度和准确性
3. 降低维护成本和复杂度

成功的关键在于：

- 充分的测试覆盖
- 渐进式的发布策略
- 持续的监控和优化
