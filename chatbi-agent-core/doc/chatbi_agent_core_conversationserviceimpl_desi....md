## **Chat BI \- Conversation Service实现详细设计 (chatbi-agent-core)**

模块路径: com.qding.chatbi.agent.service.impl.ConversationServiceImpl (实现类)  
实现接口: com.qding.chatbi.api.service.ConversationApi

### **1\. 定位与用途**

ConversationServiceImpl 是 chatbi-agent-core 模块中暴露给 chatbi-api 模块的核心服务实现。它负责接收来自 Controller 层的用户请求，进行初步的上下文构建（如 UserContextDTO），然后委托给 ConversationManager 进行核心的对话处理。同时，它还负责协调 ConversationHistoryService 来持久化用户与AI的交互消息，并对可能发生的异常进行统一处理和封装，最终返回 AgentResponse 给API层。

### **2\. ConversationServiceImpl 实现类**

* **注解**: @Service (Spring组件)  
* **依赖注入**:  
  * private final ConversationManager conversationManager;  
  * private final ConversationHistoryService conversationHistoryService;  
  * private final UserRoleService userRoleService; (假设这是一个用于获取用户角色的服务接口，需要定义或引入。如果角色信息直接在 UserQueryRequest 或其他方式中提供，则此依赖可能不需要。)  
  * private final JsonUtil jsonUtil; (来自 com.qding.chatbi.common.util)  
  * private static final Logger log \= LoggerFactory.getLogger(ConversationServiceImpl.class);  
* **构造方法**:  
  @Autowired  
  public ConversationServiceImpl(ConversationManager conversationManager,  
                                 ConversationHistoryService conversationHistoryService,  
                                 UserRoleService userRoleService, // 或其他获取角色的方式  
                                 JsonUtil jsonUtil) {  
      this.conversationManager \= conversationManager;  
      this.conversationHistoryService \= conversationHistoryService;  
      this.userRoleService \= userRoleService;  
      this.jsonUtil \= jsonUtil;  
  }

* #### **核心方法实现:**   **2.1. AgentResponse processUserQuery(UserQueryRequest request)**

  * **职责**: 处理用户的主要查询请求。  
  * **实现逻辑**:  
    1. **参数校验**: 检查 request 对象及其关键字段 (如 userId, queryText) 是否为空。如果为空，可以抛出 InvalidInputException 或返回一个错误 AgentResponse。  
       if (request \== null || StringUtil.isBlank(request.getQueryText()) || StringUtil.isBlank(request.getUserId())) {  
           log.warn("Received invalid UserQueryRequest: {}", request);  
           return createErrorResponse(request \!= null ? request.getSessionId() : null,   
                                    ErrorCode.INVALID\_INPUT, "用户ID和查询内容不能为空。");  
       }

    2. **构建用户上下文 (UserContextDTO)**:  
       * 获取或生成 sessionId。  
       * 调用 userRoleService.getRolesForUser(request.getUserId()) 获取用户角色列表。  
       * 从 request.getAdditionalParams() 或 HttpServletRequest (如果Controller传递了) 中提取客户端信息。

  String sessionId \= StringUtil.isBlank(request.getSessionId()) ?   
                            UUID.randomUUID().toString() : request.getSessionId();  
         request.setSessionId(sessionId); //确保request中的sessionId被更新或设置

         List\<String\> userRoles;  
         try {  
             userRoles \= userRoleService.getRolesForUser(request.getUserId());  
             if (userRoles \== null) { // 或者 userRoles.isEmpty() 根据业务逻辑判断  
                 log.warn("No roles found for user: {}", request.getUserId());  
                 // 根据业务需求，可能返回权限不足或允许匿名/默认角色继续  
                 userRoles \= Collections.emptyList(); // 或者抛出异常  
             }  
         } catch (Exception e) {  
             log.error("Error fetching roles for user {}: {}", request.getUserId(), e.getMessage(), e);  
             return createErrorResponse(sessionId, ErrorCode.INTERNAL\_SERVER\_ERROR, "获取用户角色信息失败。");  
         }

         UserContextDTO userContext \= new UserContextDTO(  
             request.getUserId(),  
             userRoles,  
             sessionId,  
             extractClientInfoFromRequest(request) // 辅助方法  
         );

    3. **记录用户消息到历史**:  
       try {  
           conversationHistoryService.saveUserMessage(request, userContext);  
       } catch (Exception e) {  
           log.error("Failed to save user message for session {}: {}", sessionId, e.getMessage(), e);  
           //  根据策略，可以选择继续处理请求，或者返回错误  
       }

    4. **调用 ConversationManager 处理对话**:  
       AgentResponse agentResponse;  
       long startTime \= System.currentTimeMillis();  
       String llmModelUsed \= null; // 初始化  
       Map\<String, Object\> diagnosticInfo \= null;

       try {  
           agentResponse \= conversationManager.processConversationTurn(request, userContext);  
           if (agentResponse.getDiagnosticInfo() \!= null) {  
               diagnosticInfo \= agentResponse.getDiagnosticInfo();  
               Object model \= diagnosticInfo.get("llmModel"); // 假设 ConversationManager 会填充这个  
               if (model instanceof String) {  
                   llmModelUsed \= (String) model;  
               }  
           }  
       } catch (ChatBiException e) {  
           log.error("ChatBiException during conversation turn for session {}: {}", sessionId, e.getMessage(), e);  
           agentResponse \= createErrorResponse(sessionId, e.getErrorCode(), e.getMessage());  
       } catch (Exception e) {  
           log.error("Unexpected exception during conversation turn for session {}: {}", sessionId, e.getMessage(), e);  
           agentResponse \= createErrorResponse(sessionId, ErrorCode.INTERNAL\_SERVER\_ERROR, "抱歉，处理您的请求时发生内部错误。");  
       }  
       long processingTimeMs \= System.currentTimeMillis() \- startTime;

       * **确保 agentResponse 的 sessionId 被正确设置**: ConversationManager 返回的 AgentResponse 应包含正确的 sessionId。  
    5. **记录AI响应到历史**:  
       try {  
           // 将诊断信息（如果存在）合并到要保存的结构化数据中，或者单独处理  
           if (diagnosticInfo \!= null && agentResponse.getStructuredResponseData() \== null) {  
                // agentResponse.setStructuredResponseData(jsonUtil.toJson(diagnosticInfo)); // 仅作示例  
           }  
           conversationHistoryService.saveAgentResponse(agentResponse, request, userContext, llmModelUsed, processingTimeMs);  
       } catch (Exception e) {  
           log.error("Failed to save agent response for session {}: {}", sessionId, e.getMessage(), e);  
           //  通常不应因历史记录失败而影响给用户的返回  
       }

    6. **返回 AgentResponse**:  
       return agentResponse;

  **2.2. AgentResponse processClarificationResponse(String sessionId, String optionId, Map\<String, Object\> payload)**

  * #### **职责: 处理用户对AI澄清问题的响应。**

  * **前提**: 此方法的存在取决于 ConversationApi 接口的设计。如之前讨论，更推荐将澄清响应也通过 processUserQuery 统一处理，在 UserQueryRequest 中携带澄清相关信息（如 selectedOptionId，optionPayload 放入 additionalParams），由 ConversationManager 内部识别和处理。  
  * **如果独立实现此方法**:  
    1. **参数校验**: sessionId, optionId 不应为空。  
    2. **获取用户上下文 (UserContextDTO)**:  
       * 需要一种方式从 sessionId 获取 userId（例如，从会话存储或最近的聊天记录中查询）。然后根据 userId 获取角色。**这增加了复杂性。**  
       * 或者，要求客户端在调用此接口时也传递 userId。  
    3. **构造一个内部表示用户澄清动作的请求**: 例如，可以创建一个新的 UserQueryRequest，其中 queryText 可以描述用户的选择 (e.g., "用户选择了选项: " \+ optionId), additionalParams 包含 optionId 和 payload。  
    4. **记录用户的澄清动作到历史**: 调用 ConversationHistoryService。  
    5. **调用 ConversationManager 的特定方法处理澄清** (例如，conversationManager.processClarificationTurn(internalClarificationRequest, userContext))，或者复用 processConversationTurn 但 ConversationManager 内部需要有逻辑识别这是澄清响应。  
    6. **记录AI对澄清的后续响应到历史**。  
    7. **返回 AgentResponse**。  
    8. **异常处理**。  
  * **当前建议回顾**: 鉴于简化API接口的考虑，**推荐不单独实现此方法**，而是让客户端将澄清选择（如 optionId 和 payload）作为 UserQueryRequest 的一部分（例如放在 additionalParams），通过统一的 /query 端点提交。ConversationManager 内部通过 ChatMemory 和这些参数来识别和处理澄清流程。

  **2.3. 辅助方法**

  * #### **private AgentResponse createErrorResponse(String sessionId, ErrorCode errorCode, String message):**     **private AgentResponse createErrorResponse(String sessionId, ErrorCode errorCode, String message) {**         **AgentResponse response \= new AgentResponse();**         **response.setSessionId(sessionId); // 确保sessionId被设置**         **response.setResponseType(ResponseType.ERROR);**         **response.setErrorCode(errorCode.getCode());**         **response.setMessageToUser(message \!= null ? message : errorCode.getDescription());**         **return response;**     **}** 

  * **private Map\<String, String\> extractClientInfoFromRequest(UserQueryRequest request)**:  
    private Map\<String, String\> extractClientInfoFromRequest(UserQueryRequest request) {  
        Map\<String, String\> clientInfo \= new HashMap\<\>();  
        if (request.getAdditionalParams() \!= null) {  
            // 假设 additionalParams 中有 "clientIp", "userAgent" 等键  
            Object ip \= request.getAdditionalParams().get("clientIp");  
            if (ip instanceof String) {  
                clientInfo.put("clientIp", (String) ip);  
            }  
            Object ua \= request.getAdditionalParams().get("userAgent");  
            if (ua instanceof String) {  
                clientInfo.put("userAgent", (String) ua);  
            }  
        }  
        return clientInfo;  
    }

### **3\. UserRoleService (假设的依赖)**

* **用途**: 提供根据 userId 获取用户角色列表的功能。  
* **接口示例 (com.qding.chatbi.agent.service.UserRoleService)**:  
  package com.qding.chatbi.agent.service;

  import java.util.List;

  public interface UserRoleService {  
      List\<String\> getRolesForUser(String userId);  
  }

* **实现**: 这个服务的具体实现可能依赖于企业现有的用户和权限管理系统。它可以是一个独立的微服务客户端，或者直接查询相关的数据库表。在本模块中，我们只需要其接口定义和注入。

以上是对 ConversationServiceImpl.java 的详细设计。它清楚地展示了如何将API层的请求与核心的 ConversationManager 连接起来，并处理了上下文构建、历史记录和异常封装。

请您审阅。