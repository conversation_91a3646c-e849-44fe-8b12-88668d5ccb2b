## **Chat BI \- Query Planner Agent详细设计 (chatbi-agent-core)**

**模块路径**: com.qding.chatbi.agent.service.QueryPlannerAgent (接口) 和 com.qding.chatbi.agent.service.impl.QueryPlannerAgentImpl (实现类)

### **1\. 定位与用途**

Query Planner Agent 是 chatbi-agent-core 模块中的关键组件，它承接自然语言理解 (NLU) 阶段的输出（即 StructuredQueryIntent），负责将其转化为一个具体的、可执行的查询计划 (QueryPlan)。这个计划的核心是生成的SQL语句，以及执行该SQL所需的目标数据源信息。此组件需要与元数据服务紧密协作，以获取表结构、字段定义、权限等信息，并确保生成的SQL在语义上正确且符合用户权限。

### **2\. com.qding.chatbi.agent.dto.QueryPlan.java (内部DTO)**

在设计 QueryPlannerAgent 之前，我们先定义其输出对象 QueryPlan。

* **用途**: 封装查询规划的结果，包括生成的SQL、目标数据源信息以及有效性状态。  
* **包**: com.qding.chatbi.agent.dto  
* **属性**:  
  * private String sql; // 生成的SQL语句  
  * private Long targetDatasetId; // 目标数据集ID (来自 QueryableDatasets 表)  
  * private String targetDatabaseSourceId; // 目标数据源ID (来自 QueryableDatasets \-\> DatabaseSourceConfigs)  
  * private String databaseType; // 目标数据库类型 (例如 "MYSQL", "HIVE", 来自 DatabaseSourceConfigs)，用于SQL方言适配和后续执行  
  * private boolean isValid; // 指示当前规划/SQL是否有效且可执行  
  * private String errorMessage; // 如果 isValid 为false，则包含具体的错误信息或需要澄清的提示  
  * private List\<String\> warnings; // (可选) 查询规划过程中产生的警告信息（例如，某些次要的请求实体无法满足，但不影响主查询）  
  * private Map\<String, String\> columnAliases; // (可选) 存储生成的SQL中，查询列的别名与原始业务名称的映射，方便后续结果展示。例如 {"总销售额": "sum\_sales\_amount"}  
* **构造方法, Getters, Setters**。

### **3\. 核心方法 (QueryPlannerAgent 接口)**

package com.qding.chatbi.agent.service;

import com.qding.chatbi.agent.dto.QueryPlan;  
import com.qding.chatbi.agent.dto.StructuredQueryIntent;  
import com.qding.chatbi.common.dto.UserContextDTO;

public interface QueryPlannerAgent {  
    /\*\*  
     \* 根据结构化查询意图和用户上下文，规划并生成SQL查询。  
     \*  
     \* @param intent 结构化的查询意图，由NluAgent生成。  
     \* @param userContext 用户上下文，包含角色信息用于权限校验。  
     \* @return 查询计划对象，包含SQL语句、目标数据源等信息。  
     \* @throws com.qding.chatbi.common.exception.ChatBiException 如果规划过程中发生无法恢复的业务异常。  
     \*/  
    QueryPlan planAndGenerateSql(StructuredQueryIntent intent, UserContextDTO userContext);  
}

### **4\. QueryPlannerAgentImpl 实现类**

* **注解**: @Service  
* **依赖注入 (已修正)**:  
  * ChatLanguageModel chatLanguageModel; (可选, 主要用于Text-to-SQL的备用方案或复杂的字段映射逻辑)  
  * PromptTemplateService promptTemplateService; (可选, 与 chatLanguageModel 配套使用)  
  * **DatasetTools datasetTools;** (直接注入Tool Bean)  
  * **KnowledgeBaseTools knowledgeBaseTools;** (直接注入Tool Bean)  
  * JsonUtil jsonUtil;  
  * SqlBuilderService sqlBuilderService; (新增的辅助服务，专门负责SQL语句的构建，下面会讨论)  
* **planAndGenerateSql 方法内部逻辑步骤**:  
  1. **输入校验**:  
     * 检查 intent 是否为 null，intent.getIntent() 是否为 "DATA\_QUERY"。如果不是，则返回一个无效的 QueryPlan，并附带错误信息 "无效的查询意图" 或 "非数据查询意图，无需生成SQL"。  
     * 检查 intent.getEntities() 是否为空或缺少核心查询要素（如指标或维度）。  
  2. **确定目标数据集 (targetDatasetId)**:  
     * **a. 获取NLU提示**: 从 intent.getTargetDatasetHint() 获取NLU初步判断的数据集业务名称。  
     * **b. 获取用户有权限的数据集列表及元数据摘要**:  
       * 直接调用 datasetTools.getDatasetSchemaAndPermissions(null, userContext.getUserRoles()) 获取用户所有可访问的数据集摘要。  
     * **c. 选择最佳数据集**:  
       * 如果 nluDatasetHint 存在且有效，优先验证该数据集。  
       * 如果没有提示，则根据 intent.getEntities() 中的指标、维度业务名，与所有候选数据集的字段进行匹配度评分，选择最佳匹配的数据集。  
       * **权限校验**: 确保最终选定的 targetDatasetId 是用户有权访问的 (此步骤在获取元数据时已隐含)。  
       * **歧义处理**: 如果无法唯一确定一个数据集，应返回一个 QueryPlan 表明需要澄清。  
  3. **获取选定数据集的完整元数据**:  
     * 再次调用 datasetTools.getDatasetSchemaAndPermissions(selectedDatasetIdentifier, userContext.getUserRoles()) 获取该数据集的完整字段列表和基础信息。  
  4. **字段映射与校验 (核心逻辑)**:  
     * 为 StructuredQueryIntent 中的每个业务实体（指标、维度、过滤器字段名）找到其在选定数据集中对应的 DatasetColumn 元数据（通过匹配 column\_name 或 synonyms）。  
     * 记录映射后的技术细节：technical\_name\_or\_expression, data\_type 等。  
     * 如果任何关键实体无法映射，则可能需要返回澄清或错误。  
  5. **SQL构建 (委托给 SqlBuilderService)**:  
     * 调用 sqlBuilderService.buildSql(mappedIntent, datasetDetails, databaseType)。  
  6. **(可选) 基于LLM的SQL生成/优化 (备用方案)**:  
     * 如果规则化SQL构建失败，可以尝试使用LLM直接生成SQL。  
     * 准备 sql\_generation.txt Prompt，填充变量。  
     * 调用 chatLanguageModel.generate(...)。  
     * **安全警告**: LLM生成的SQL必须经过严格的校验和审查。  
  7. **构造并返回 QueryPlan**。

### **5\. com.qding.chatbi.agent.service.SqlBuilderService.java (新增辅助服务)**

* **定位**: @Service 类，被 QueryPlannerAgentImpl 依赖。  
* **用途**: 封装规则化的SQL语句构建逻辑，使其与 QueryPlannerAgentImpl 的主流程解耦。  
* **核心方法**:  
  // package com.qding.chatbi.agent.service;  
  // import com.qding.chatbi.agent.dto.internal.MappedQueryIntent;  
  // import com.qding.chatbi.agent.dto.internal.DatasetTechnicalDetails;  
  // import com.qding.chatbi.common.enums.DatabaseType;

  // public interface SqlBuilderService {  
  //    String buildSql(MappedQueryIntent mappedIntent, DatasetTechnicalDetails datasetDetails, String databaseType);  
  // }  
  public String buildSql(MappedQueryIntent mappedIntent, DatasetTechnicalDetails datasetDetails, String databaseType) {  
      // 1\. 构建FROM子句  
      // 2\. 构建SELECT子句 (处理聚合函数和别名)  
      // 3\. 构建WHERE子句 (处理操作符和值的格式化)  
      // 4\. 构建GROUP BY子句  
      // 5\. 构建ORDER BY子句  
      // 6\. 构建LIMIT子句  
      // 7\. 组合所有子句，并考虑数据库方言  
      // 返回构建好的SQL字符串。  
  }

* **MappedQueryIntent 和 DatasetTechnicalDetails**: 这两个是传递给 SqlBuilderService 的内部DTO，包含了所有必要的技术信息。

以上是对 QueryPlannerAgent.java 的详细设计。它负责将NLU的输出转化为SQL查询计划，是连接理解与执行的关键桥梁。

请您审阅这部分内容。