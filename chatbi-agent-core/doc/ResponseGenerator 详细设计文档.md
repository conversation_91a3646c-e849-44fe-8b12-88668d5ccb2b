# **ResponseGenerator 详细设计文档**

## **1\. 模块定位与职责**

ResponseGenerator 是 ChatBI Agent 核心流程的最后一步。在 DataRetrievalAgent 成功获取数据后，ResponseGenerator 负责对这些数据进行解读、包装和丰富，生成最终面向用户的友好响应。

**核心职责**:

1. **数据解读 (Interpretation)**: 分析 QueryResult 的结构和内容，理解其业务含义。  
2. **自然语言生成 (Natural Language Generation)**: 将数据结果用通俗易懂的自然语言进行总结和汇报。  
3. **可视化建议 (Visualization Suggestion)**: 根据数据特征，智能推荐最合适的前端图表渲染类型。  
4. **最终应答封装 (Response Assembly)**: 将自然语言摘要、原始数据、可视化建议以及其他必要信息，统一封装到 AgentResponse DTO 中。

## **2\. 核心输入与输出**

* **输入**:  
  * QueryResult: 从 DataRetrievalAgent 获取的原始查询结果。  
  * StructuredQueryIntent: 从 NluAgent 阶段传来，用于理解用户原始问题和意图。  
  * QueryPlan: (可选) 从 QueryPlannerAgent 传来，可用于获取执行的SQL等调试信息。  
* **输出**: AgentResponse (定义于 chatbi-common 模块)。我们将对该DTO进行扩展，以承载更丰富的信息：  
  // File: chatbi-common/src/main/java/com/qding/chatbi/common/dto/AgentResponse.java  
  public class AgentResponse {  
      private ResponseType responseType; // e.g., DATA, CLARIFICATION, ERROR  
      private String responseText; // \[核心产出\] 对结果的自然语言摘要  
      private QueryResult queryResult; // \[核心产出\] 原始的、未修改的查询结果数据  
      private DisplaySuggestion displaySuggestion; // \[核心产出\] 前端展示建议  
      private List\<ClarificationOption\> clarificationOptions;  
      private String thoughtProcess; // 从QueryPlan中继承的思考过程  
  }

  // 新增一个DTO用于承载可视化建议  
  public class DisplaySuggestion {  
      private String chartType; // "table", "line\_chart", "bar\_chart", "pie\_chart", "kpi\_card"  
      private String title; // 图表的建议标题, e.g., "各城市销售额对比"  
      private Map\<String, String\> chartOptions; // e.g., {"xAxis": "city", "yAxis": "sales"}  
  }

## **3\. 核心工作流**

ResponseGenerator 将采用**规则与AI混合**的策略来处理结果。

| 步骤 | 名称 | 描述 | 实现方式 |
| :---- | :---- | :---- | :---- |
| **1** | **结果预处理** | 检查 QueryResult 是否有效，是否为空。 | 规则判断 |
| **2** | **处理特殊情况** | 如果查询成功但返回0行数据，或查询失败，直接生成固定的友好提示语。 | 规则判断 |
| **3** | **分析数据结构** | 根据 QueryResult 的行数和列数，判断结果类型（单值、单行、多行表格）。 | 规则判断 |
| **4** | **生成自然语言摘要** | 根据数据结构，生成对结果的文字描述。 | **混合模式**: \- **单值**: 规则化生成。 \- **表格**: 调用LLM生成。 |
| **5** | **生成可视化建议** | 根据列的语义类型（维度/指标）和数量，生成图表建议。 | **启发式规则 (Heuristics)** |
| **6** | **组装最终响应** | 将步骤4和5的产出以及原始数据，一同装入 AgentResponse DTO。 | 对象组装 |

## **4\. 关键实现细节**

### **4.1. 自然语言摘要生成 (responseText)**

* **Case 1: 查询成功，但无数据 (queryResult.getRows().isEmpty())**  
  * **策略**: 返回固定话术。  
  * **responseText**: "根据您的查询条件，我没有找到相关数据。您可以尝试调整筛选条件或查询范围。"  
* **Case 2: 单值结果 (1行1列)**  
  * **场景**: 用户问 “上个月北京的GMV是多少？”  
  * **策略**: 使用规则，结合 StructuredQueryIntent 中的信息，格式化输出。  
  * **responseText**: "您好，查询结果如下：上个月北京的GMV是 **5,800.00万元**。" (数字需要进行格式化)  
* **Case 3: 表格结果 (多行/多列)**  
  * **场景**: 用户问 “查询各城市的销售额和用户数”  
  * **策略**: **调用大语言模型 (LLM)**。因为表格数据的解读是开放性的，LLM能从中发现趋势、最大值/最小值等，给出比固定规则更深刻的洞察。  
  * **Prompt (data\_interpretation.txt) 设计**:  
    You are a professional data analyst. Based on the user's original question and the following data (in CSV format), please provide a concise, insightful summary in Chinese.  
    Your summary should be easy to understand for a non-technical person.  
    Focus on the main findings, such as trends, top values, or significant comparisons.

    \---  
    User's Original Question:  
    {user\_question}

    \---  
    Data (CSV Format):  
    {data\_csv}

    \---  
    Please provide your summary now.

  * **responseText**: LLM可能会返回：“根据您的查询，销售额最高的城市是上海，达到了1.2亿元。北京和广州紧随其后。具体数据见下表。”

### **4.2. 可视化建议生成 (displaySuggestion)**

这里采用**启发式规则**，因为它的成本低、效果稳定。

1. **获取列的语义类型**: 我们需要知道 QueryResult 中的每一列是“维度(Dimension)”还是“指标(Metric)”。这个信息可以从 QueryPlannerAgent 阶段选择的那个 Dataset 的列定义中获取。  
2. **应用规则**:  
   * **KPI卡片 (kpi\_card)**:  
     * 当结果为 **单值** (1行1列) 时。  
   * **折线图 (line\_chart)**:  
     * 当有 **1个时间维度** \+ **1个或多个指标** 时。 (e.g., "最近30天每天的销售额")  
   * **柱状图/条形图 (bar\_chart)**:  
     * 当有 **1个分类维度** \+ **1个或多个指标** 时。 (e.g., "各城市的销售额")  
   * **饼图 (pie\_chart)**:  
     * 当有 **1个分类维度** (且分类项通常不多于6个) \+ **1个指标**，且指标代表整体的构成部分时。(e.g., "各产品线销售额占比")  
   * **表格 (table)**:  
     * 当有 **2个以上维度** 时，或不满足以上任何图表规则时。这是最通用的默认选项。

## **5\. 在 ConversationManager 中的集成**

ConversationManager 的 process 方法将被更新，在获取到 QueryResult 后，增加调用 ResponseGenerator 的步骤：

// 在 ConversationManager.java 的 process 方法中

// ... (已有代码)  
QueryResult queryResult \= dataRetrievalAgent.retrieve(queryPlan);

// 新增步骤: 调用 ResponseGenerator  
AgentResponse finalResponse \= responseGenerator.generate(queryResult, structuredIntent);

// 将思考过程补充到最终响应中  
finalResponse.setThoughtProcess(queryPlan.getThoughtProcess());

return finalResponse;

通过以上设计，ResponseGenerator 成为了连接数据和用户的关键枢纽，它将原始数据转化为有温度、有洞察、易于消费的智能应答，极大地提升了产品的用户体验。