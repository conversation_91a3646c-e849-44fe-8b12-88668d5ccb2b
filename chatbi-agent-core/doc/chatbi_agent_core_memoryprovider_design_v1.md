## **Chat BI \- Chat Memory Provider详细设计 (chatbi-agent-core)**

模块路径: com.qding.chatbi.agent.memory.ChatMemoryProvider (实现类)  
(可能也需要一个接口 ChatMemoryProvider，实现类为 ChatMemoryProviderImpl，但为简化，先直接设计实现类)

### **1\. 定位与用途**

ChatMemoryProvider 是 chatbi-agent-core 模块中负责提供和管理会话记忆 (ChatMemory) 实例的组件。LangChain4j 中的 ChatMemory 用于存储对话历史，为大语言模型 (LLM) 提供必要的上下文，以便理解多轮对话和用户意图。此 Provider 的核心职责是为给定的会话ID (sessionId) 提供一个配置好的 ChatMemory 实例，并可选地从持久化存储（如 ChatMessages 表）中预加载近期的对话历史到该内存实例中。

### **2\. ChatMemoryProvider.java 实现类**

* **注解**: @Component (或 @Service)  
* **依赖注入**:  
  * private final ConversationHistoryService conversationHistoryService; (用于从数据库加载历史消息)  
  * @Value("${chatbi.agent.memory.max-messages:20}") private int maxMemoryMessages; (从 application.yml 读取配置，设置内存中保留的最大消息数量)  
  * @Value("${chatbi.agent.memory.cache.max-size:1000}") private long memoryCacheMaxSize; (LRU缓存的最大条目数)  
  * @Value("${chatbi.agent.memory.cache.expire-after-access-minutes:60}") private long memoryCacheExpireAfterAccessMinutes; (LRU缓存条目在最后一次访问后多久过期)  
  * private final com.github.benmanes.caffeine.cache.Cache\<String, ChatMemory\> sessionMemoryCache; (使用Caffeine作为LRU本地缓存)  
* **构造方法**:  
  import dev.langchain4j.memory.ChatMemory;  
  import dev.langchain4j.memory.chat.MessageWindowChatMemory;  
  import dev.langchain4j.data.message.ChatMessage; // LangChain4j's ChatMemory  
  import dev.langchain4j.data.message.UserMessage;  
  import dev.langchain4j.data.message.AiMessage;  
  import com.qding.chatbi.agent.entity.ChatMessage\_AgentEntity; // 我们的JPA实体  
  import com.qding.chatbi.agent.service.ConversationHistoryService;  
  import com.github.benmanes.caffeine.cache.Caffeine;  
  import org.springframework.beans.factory.annotation.Autowired;  
  import org.springframework.beans.factory.annotation.Value;  
  import org.springframework.stereotype.Component;  
  import java.util.List;  
  import java.util.concurrent.TimeUnit;  
  // import java.util.concurrent.ConcurrentHashMap; // 不再直接使用  
  // import java.util.Map; // 不再直接使用  
  // import java.util.stream.Collectors; // 如果转换逻辑复杂可能需要

  @Component  
  public class ChatMemoryProvider {

      private final ConversationHistoryService conversationHistoryService;  
      private final int maxMemoryMessages;  
      private final com.github.benmanes.caffeine.cache.Cache\<String, ChatMemory\> sessionMemoryCache;

      @Autowired  
      public ChatMemoryProvider(ConversationHistoryService conversationHistoryService,  
                                @Value("${chatbi.agent.memory.max-messages:20}") int maxMemoryMessages,  
                                @Value("${chatbi.agent.memory.cache.max-size:1000}") long memoryCacheMaxSize,  
                                @Value("${chatbi.agent.memory.cache.expire-after-access-minutes:60}") long memoryCacheExpireAfterAccessMinutes) {  
          this.conversationHistoryService \= conversationHistoryService;  
          this.maxMemoryMessages \= maxMemoryMessages;  
          this.sessionMemoryCache \= Caffeine.newBuilder()  
                  .maximumSize(memoryCacheMaxSize)  
                  .expireAfterAccess(memoryCacheExpireAfterAccessMinutes, TimeUnit.MINUTES)  
                  .build();  
      }

      // ... getMemory 方法 ...  
  }

  * **注意**: JPA实体 ChatMessage 在此假设为 ChatMessage\_AgentEntity 以避免与LangChain4j的 ChatMessage 冲突。

* #### **核心方法:**   **public ChatMemory getMemory(String sessionId)**

  * **职责**: 根据会话ID获取或创建一个 ChatMemory 实例。优先从Caffeine缓存中获取；如果缓存未命中，则创建一个新的实例，从数据库预加载最近的对话历史，然后将其放入缓存并返回。  
  * **实现逻辑**:  
    1. **参数校验**: 确保 sessionId 不为空。  
       if (sessionId \== null || sessionId.trim().isEmpty()) {  
           throw new IllegalArgumentException("Session ID cannot be null or empty.");  
       }

    2. 从Caffeine缓存获取:  
       get 方法的第二个参数是一个 mappingFunction，它会在缓存未命中时被调用来创建值。  
       return sessionMemoryCache.get(sessionId, this::createAndLoadMemoryForSession);

    3. private ChatMemory createAndLoadMemoryForSession(String sessionId) 辅助方法:  
       此方法在缓存未命中时被调用。  
       * **创建 ChatMemory 实例**:  
         // log.debug("Cache miss for sessionId: {}. Creating new ChatMemory instance.", sessionId);  
         MessageWindowChatMemory memory \= MessageWindowChatMemory.builder()  
                 .id(sessionId) // 设置会话ID  
                 .maxMessages(maxMemoryMessages) // 设置内存中保留的最大消息数  
                 .build();

         * **选择 ChatMemory 实现**:  
           * MessageWindowChatMemory: 保留最近的N条消息，简单有效。  
           * TokenBufferChatMemory: 根据Token数量限制内存大小，更精确控制上下文长度，但计算Token可能稍复杂。  
           * 对于BI场景，MessageWindowChatMemory 仍然是一个不错的起点。  
       * **预加载历史消息**:  
         // log.debug("Loading chat history for sessionId: {} with maxMessages: {}", sessionId, maxMemoryMessages);  
         List\<com.qding.chatbi.agent.entity.ChatMessage\_AgentEntity\> persistedHistory \=  
                 conversationHistoryService.getHistoryBySessionId(sessionId, maxMemoryMessages);

         if (persistedHistory \!= null && \!persistedHistory.isEmpty()) {  
             // log.debug("Found {} persisted messages for sessionId: {}.", persistedHistory.size(), sessionId);  
             // LangChain4j 的 MessageWindowChatMemory 在添加消息时会自动处理窗口大小  
             // 所以我们可以按顺序添加，它会保留最新的 maxMemoryMessages 条  
             // JPA返回的是升序，所以直接添加即可。  
             for (com.qding.chatbi.agent.entity.ChatMessage\_AgentEntity dbMessage : persistedHistory) {  
                 if ("USER".equalsIgnoreCase(dbMessage.getSenderType())) {  
                     memory.add(UserMessage.from(dbMessage.getMessageText()));  
                 } else if ("AI\_AGENT".equalsIgnoreCase(dbMessage.getSenderType())) {  
                     // AiMessage 可以包含 ToolExecutionRequest/Response，但我们ChatMessage表目前只存文本  
                     // 如果需要更丰富的 AiMessage 类型，ChatMessage 表和转换逻辑需要扩展  
                     memory.add(AiMessage.from(dbMessage.getMessageText()));  
                 }  
             }  
         } else {  
             // log.debug("No persisted chat history found for sessionId: {}.", sessionId);  
         }  
         // log.info("ChatMemory instance created and loaded for sessionId: {}", sessionId);  
         return memory;

       * **缓存策略 (已由Caffeine处理)**:  
         * Caffeine 提供了基于LRU (Least Recently Used), LFU (Least Frequently Used), 或基于时间的驱逐策略。我们这里配置了 maximumSize 和 expireAfterAccess。  
         * **优点**:  
           * **有效管理内存**: 自动驱逐不常用的会话记忆，防止内存无限增长。  
           * **高性能**: Caffeine 是一个高性能的本地缓存库。  
         * **缺点**:  
           * **仍然是本地缓存**: 在多实例部署时，会话记忆不共享。如果用户请求被路由到不同实例，会创建新的会话记忆（除非 sessionId 保持不变且历史被重新加载）。  
         * **对于分布式环境的进一步考虑**:  
           * 如果需要跨实例共享会话记忆，或者需要更持久的、独立于应用实例生命周期的记忆，应考虑使用 LangChain4j 的 ChatMemoryStore 接口，并为其提供基于 Redis、数据库或其他分布式存储的实现。在这种情况下，ChatMemoryProvider 的角色会更多地偏向于从 ChatMemoryStore 加载和构建 ChatMemory 对象，而不是自己维护一个本地缓存。  
           * 不过，对于“LRU Cache的本地实现”这一初始目标，Caffeine 是一个非常好的选择。  
* **其他考虑**:  
  * **线程安全**: Caffeine 本身是线程安全的。computeIfAbsent 模式 (Caffeine的 cache.get(key, mappingFunction)) 确保了对同一个key的加载操作是原子的。  
  * **日志**: 在关键步骤（如缓存未命中、加载历史）添加日志，有助于调试和监控。

### **4\. application.yml 配置示例 (新增缓存相关配置)**

chatbi:  
  agent:  
    memory:  
      max-messages: 20 \# ChatMemory中保留的最大消息数 (用于MessageWindowChatMemory内部)  
      cache:  
        max-size: 1000 \# LRU缓存的最大会话条目数  
        expire-after-access-minutes: 60 \# 会话缓存在最后一次访问后60分钟过期

* **Maven/Gradle 依赖 (需要添加 Caffeine)**:  
  \<\!-- For Maven \--\>  
  \<dependency\>  
      \<groupId\>com.github.ben-manes.caffeine\</groupId\>  
      \<artifactId\>caffeine\</artifactId\>  
      \<version\>3.1.8\</version\> \<\!-- 使用最新稳定版 \--\>  
  \</dependency\>  
  \`\`\`gradle  
  // For Gradle  
  implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8' // 使用最新稳定版

以上是对 ChatMemoryProvider.java 使用本地LRU Cache (Caffeine) 方案的优化设计。这个方案比简单的 ConcurrentHashMap 更能有效地管理内存。

请您审阅。