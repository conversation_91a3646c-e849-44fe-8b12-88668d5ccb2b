# NLU 提示词优化方案

## 一、问题分析

### 1.1 当前提示词的主要问题

#### 复杂度过高

- **321 行**的超长提示词，包含 4 个主要步骤，每个步骤下有多个子步骤
- 逻辑嵌套深度达到 3-4 层，AI 难以准确跟踪执行路径
- 大量重复内容（如时间检查规则在不同位置出现 3 次）

#### 历史消息处理混乱

- 历史引用规则过于复杂，包含太多"如果...那么..."的条件判断
- AI 难以准确判断何时应该引用历史消息
- 澄清回答的识别规则不够直观

#### 时间维度检查被边缘化

- 虽然标注为"最高优先级"，但实际放在第 4 步
- 被大量其他规则和文字淹没
- 导致 AI 经常忽略时间维度检查

### 1.2 实际执行中的问题

```
用户: "成本"
AI: "您想查询哪个城市的成本？"
用户: "上海，成都"
AI: 直接执行查询，生成 SQL 缺少时间筛选
```

**问题**：AI 没有继续澄清时间范围，导致可能查询全量数据。

## 二、优化方案

### 2.1 设计原则

1. **简洁性**：减少步骤数量，使用更直接的逻辑
2. **优先级明确**：时间检查作为第一优先级，而不是埋在后面
3. **流程清晰**：使用简单的决策树，减少嵌套
4. **示例驱动**：通过具体示例说明处理流程

### 2.2 新版提示词结构

```
总行数：约 130 行（减少 60%）
主要步骤：5 个（从 4 个复杂步骤简化）
嵌套深度：最多 2 层（从 3-4 层减少）
```

## 三、核心改进

### 3.1 时间维度检查前置

**旧版本**：时间检查在第 4 步，容易被忽略

```
步骤1：智能上下文分析
步骤2：分析查询意图
步骤3：选择目标数据集
步骤4：验证完整性并决定最终意图（包含时间检查）
```

**新版本**：时间检查在第 2 步，作为首要检查项

```
第一步：识别澄清场景
第二步：检查时间信息（如果缺少，立即澄清）
第三步：识别查询类型
第四步：提取实体
第五步：生成响应
```

### 3.2 简化历史消息处理

**旧版本**：复杂的历史引用规则

```
- 强制引用历史消息的情况...
- 澄清回答识别规则...
- 历史引用规则...
- 业务实体验证...
```

**新版本**：简单直接的判断

```
如果最近 AI 消息是 CLARIFICATION_NEEDED：
  - 合并原始查询 + 当前回答
否则：
  - 使用当前查询
```

### 3.3 使用伪代码而非自然语言

**旧版本**：大段自然语言描述

```
如果最近AI消息包含澄清选项，且当前用户查询内容与澄清选项相关
（如用户说"北京"，AI之前问过城市；用户说"销售额"，AI之前问过指标），
则视为澄清回答...
```

**新版本**：简洁的伪代码

```
如果最近 AI 消息是 CLARIFICATION_NEEDED：
  - 找到澄清前的原始查询
  - 将原始查询 + 当前回答合并为 mergedContext
```

## 四、预期效果

### 4.1 正确的多轮澄清流程

```mermaid
graph TD
    A[用户: 成本] --> B[AI: 您想查询什么时间段哪个地区的成本？]
    B --> C[用户: 上海，成都]
    C --> D[AI: 您想查看什么时间段的数据？]
    D --> E[用户: 本月]
    E --> F[执行查询: 上海和成都本月的成本]
```

### 4.2 关键改进指标

| 指标         | 旧版本       | 新版本       | 改进       |
| ------------ | ------------ | ------------ | ---------- |
| 提示词长度   | 321 行       | 130 行       | -60%       |
| 逻辑步骤     | 4 个复杂步骤 | 5 个简单步骤 | 更清晰     |
| 时间检查位置 | 第 4 步      | 第 2 步      | 优先级提升 |
| 嵌套深度     | 3-4 层       | 最多 2 层    | -50%       |

## 五、实施建议

### 5.1 测试场景

1. **基础场景**：单一指标查询

   - 输入："成本"
   - 预期：澄清时间和地区

2. **多轮澄清**：逐步补充信息

   - 第一轮："成本" → 澄清
   - 第二轮："上海" → 继续澄清时间
   - 第三轮："本月" → 执行查询

3. **完整查询**：一次性提供所有信息
   - 输入："上海本月的成本"
   - 预期：直接执行查询

### 5.2 回退方案

保留原有提示词文件，新版本作为 v2 进行 A/B 测试：

- `nlu_function_calling_agent.txt`（原版本）
- `nlu_function_calling_agent_v2.txt`（新版本）

### 5.3 监控指标

1. **时间维度遗漏率**：统计生成的 SQL 中缺少时间筛选的比例
2. **澄清轮次**：平均需要几轮澄清才能获得完整信息
3. **响应时间**：简化后的提示词是否提升了响应速度

## 六、总结

新版提示词通过以下方式解决了核心问题：

1. **时间维度优先**：将时间检查提前到第二步，确保不会被遗漏
2. **逻辑简化**：减少嵌套和条件判断，使 AI 更容易理解和执行
3. **示例驱动**：通过具体示例说明处理流程，减少歧义

这种简化不仅能提高 AI 的执行准确性，还能减少 token 消耗，提升系统性能。
