-- Query Examples 示例数据
-- 用于NLU动态示例优化

-- 1. 澄清类示例（缺少必要信息）

-- 缺少时间信息
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('北京的收入',
'{"intent":"CLARIFICATION_NEEDED", "response":"我了解您想查看北京的收入数据。请问您想查看哪个时间段的数据？比如本月、上个月还是特定时间范围？", "entities":{"metrics":["收入"],"dimensions_to_group":[],"filters":[{"fieldName":"city","operator":"EQUALS","value":"北京"}]}}',
1,
'缺少时间信息的澄清示例',
'Easy',
'verified'),

('各城市销售额',
'{"intent":"CLARIFICATION_NEEDED", "response":"我了解您想查看各城市的销售额。请问您想查看哪个时间段的数据？", "entities":{"metrics":["销售额"],"dimensions_to_group":["city"]}}',
1,
'聚合查询缺少时间',
'Easy',
'verified'),

-- 缺少指标
('本月的数据',
'{"intent":"CLARIFICATION_NEEDED", "response":"我了解您想查看本月的数据。请问您想查看哪个指标？比如销售额、订单数还是其他？", "entities":{"filters":[{"fieldName":"日期","operator":"BETWEEN","value":["2024-12-01","2024-12-31"]}]}}',
1,
'缺少指标的澄清示例',
'Easy',
'verified');

-- 2. 完整查询示例

-- 排名查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('各城市本月销售额排名前5',
'{"intent":"DATA_QUERY", "queryType":"RANKING_QUERY", "entities":{"metrics":["销售额"],"dimensions_to_group":["city"],"filters":[{"fieldName":"日期","operator":"BETWEEN","value":["2024-12-01","2024-12-31"]}],"sort_by":[{"fieldName":"销售额","order":"DESC"}],"limit":5}}',
1,
'完整的排名查询示例',
'Medium',
'verified'),

('销售额最高的10个产品',
'{"originalQuery":"销售额最高的10个产品","mergedContext":"销售额最高的10个产品","intent":"DATA_QUERY","queryType":"RANKING_QUERY","aggregationRequired":true,"expectedGranularity":["product_name"],"targetDatasetId":1,"datasetMatchConfidence":"HIGH","suggestedAggregationFunction":"SUM","limitCount":10,"entities":{"metrics":["销售额"],"dimensions_to_group":["product_name"],"sort_by":[{"fieldName":"销售额","order":"DESC"}]}}',
1,
'产品排名查询（默认为本月）',
'Medium',
'verified');

-- 趋势查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('最近30天销售额趋势',
'{"originalQuery":"最近30天销售额趋势","mergedContext":"最近30天销售额趋势","intent":"DATA_QUERY","queryType":"TREND_QUERY","aggregationRequired":true,"expectedGranularity":["date"],"granularityDescription":"按日期汇总","targetDatasetId":1,"datasetMatchConfidence":"HIGH","suggestedAggregationFunction":"SUM","entities":{"metrics":["销售额"],"dimensions_to_group":["date"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["CURRENT_DATE-30","CURRENT_DATE"]}],"sort_by":[{"fieldName":"date","order":"ASC"}]}}',
1,
'日趋势查询示例',
'Medium',
'verified'),

('今年每月销售额变化',
'{"originalQuery":"今年每月销售额变化","mergedContext":"今年每月销售额变化","intent":"DATA_QUERY","queryType":"TREND_QUERY","aggregationRequired":true,"expectedGranularity":["DATE_FORMAT(date,\'%Y-%m\')"],"granularityDescription":"按月份汇总","targetDatasetId":1,"datasetMatchConfidence":"HIGH","suggestedAggregationFunction":"SUM","entities":{"metrics":["销售额"],"dimensions_to_group":["month"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2024-01-01","2024-12-31"]}],"sort_by":[{"fieldName":"month","order":"ASC"}]}}',
1,
'月度趋势查询示例',
'Medium',
'verified');

-- 聚合查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('北京上海本月的销售额',
'{"originalQuery":"北京上海本月的销售额","mergedContext":"北京上海本月的销售额","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","aggregationRequired":true,"expectedGranularity":["city"],"targetDatasetId":1,"datasetMatchConfidence":"HIGH","suggestedAggregationFunction":"SUM","entities":{"metrics":["销售额"],"dimensions_to_group":["city"],"filters":[{"fieldName":"city","operator":"IN","value":["北京","上海"]},{"fieldName":"date","operator":"BETWEEN","value":["2024-12-01","2024-12-31"]}]}}',
1,
'多城市聚合查询',
'Easy',
'verified'),

('各品类去年的总销售额',
'{"originalQuery":"各品类去年的总销售额","mergedContext":"各品类去年的总销售额","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","aggregationRequired":true,"expectedGranularity":["category"],"targetDatasetId":1,"datasetMatchConfidence":"HIGH","suggestedAggregationFunction":"SUM","entities":{"metrics":["销售额"],"dimensions_to_group":["category"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2023-01-01","2023-12-31"]}]}}',
1,
'按品类聚合的年度数据',
'Medium',
'verified');

-- 明细查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('列出昨天的所有订单',
'{"originalQuery":"列出昨天的所有订单","mergedContext":"列出昨天的所有订单","intent":"DATA_QUERY","queryType":"DETAIL_QUERY","aggregationRequired":false,"targetDatasetId":2,"datasetMatchConfidence":"HIGH","entities":{"metrics":["订单数"],"filters":[{"fieldName":"date","operator":"EQUALS","value":"YESTERDAY"}]}}',
2,
'明细查询示例',
'Easy',
'verified');

-- 3. 特殊处理示例

-- 同比查询
INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('本月销售额同比增长',
'{"originalQuery":"本月销售额同比增长","mergedContext":"本月销售额同比增长","intent":"DATA_QUERY","queryType":"COMPARISON_QUERY","aggregationRequired":true,"targetDatasetId":1,"datasetMatchConfidence":"HIGH","entities":{"metrics":["销售额"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2024-12-01","2024-12-31"],"label":"本月"},{"fieldName":"date","operator":"BETWEEN","value":["2023-12-01","2023-12-31"],"label":"去年同月"}]}}',
1,
'同比查询需要两个时间段的数据',
'Hard',
'verified'),

-- 环比查询
('上月销售额环比',
'{"originalQuery":"上月销售额环比","mergedContext":"上月销售额环比","intent":"DATA_QUERY","queryType":"COMPARISON_QUERY","aggregationRequired":true,"targetDatasetId":1,"entities":{"metrics":["销售额"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2024-11-01","2024-11-30"],"label":"上月"},{"fieldName":"date","operator":"BETWEEN","value":["2024-10-01","2024-10-31"],"label":"上上月"}]}}',
1,
'环比查询示例',
'Hard',
'verified'),

-- 占比查询
('北京销售额占全国比例',
'{"originalQuery":"北京销售额占全国比例","mergedContext":"北京销售额占全国比例","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","aggregationRequired":true,"targetDatasetId":1,"datasetMatchConfidence":"HIGH","entities":{"metrics":["销售额"],"dimensions_to_group":["city"],"filters":[{"fieldName":"city","operator":"EQUALS","value":"北京"}],"additionalCalculations":{"type":"percentage","numerator":"北京销售额","denominator":"全国销售额"}}}',
1,
'占比查询需要计算分子和分母',
'Hard',
'verified'),

-- 累计查询
('本年累计销售额',
'{"originalQuery":"本年累计销售额","mergedContext":"本年累计销售额","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","aggregationRequired":false,"targetDatasetId":1,"datasetMatchConfidence":"HIGH","suggestedAggregationFunction":"SUM","entities":{"metrics":["销售额"],"filters":[{"fieldName":"date","operator":"BETWEEN","value":["2024-01-01","CURRENT_DATE"]}]}}',
1,
'年累计(YTD)查询',
'Medium',
'verified'),

-- 分组内排名
('每个月销售额最高的城市',
'{"originalQuery":"每个月销售额最高的城市","mergedContext":"每个月销售额最高的城市","intent":"DATA_QUERY","queryType":"RANKING_QUERY","aggregationRequired":true,"expectedGranularity":["month","city"],"targetDatasetId":1,"datasetMatchConfidence":"HIGH","limitCount":100,"entities":{"metrics":["销售额"],"dimensions_to_group":["month","city"],"sort_by":[{"fieldName":"销售额","order":"DESC"}]},"specialProcessing":"GROUP_RANKING"}',
1,
'分组内排名查询，需要特殊处理',
'Hard',
'verified'),

-- 多指标查询
('销售额和订单数的对比',
'{"originalQuery":"销售额和订单数的对比","mergedContext":"销售额和订单数的对比","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","aggregationRequired":false,"targetDatasetId":1,"datasetMatchConfidence":"HIGH","entities":{"metrics":["销售额","订单数"]}}',
1,
'多指标并列查询',
'Medium',
'verified');

-- 4. 多轮对话示例

INSERT INTO query_examples (user_question, target_query_representation, target_dataset_id, notes, difficulty_level, status) VALUES
('去年7月以来',
'{"originalQuery":"去年7月以来","mergedContext":"去年7月以来上海天津的收入","intent":"DATA_QUERY","queryType":"AGGREGATION_QUERY","aggregationRequired":true,"expectedGranularity":["city"],"targetDatasetId":1,"datasetMatchConfidence":"HIGH","entities":{"metrics":["销售额"],"dimensions_to_group":["city"],"filters":[{"fieldName":"city","operator":"IN","value":["上海","天津"]},{"fieldName":"date","operator":"BETWEEN","value":["2023-07-01","CURRENT_DATE"]}]}}',
1,
'多轮对话中的时间补充（假设之前已有城市和指标信息）',
'Medium',
'verified'); 