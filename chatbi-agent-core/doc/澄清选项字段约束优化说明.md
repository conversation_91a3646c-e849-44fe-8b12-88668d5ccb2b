# 澄清选项字段约束优化说明

## 问题背景

在 ChatBI 系统中，当用户查询信息不完整时，AI 会生成澄清选项来帮助用户完善查询。但发现了一个问题：AI 在生成澄清选项时，可能会基于常识而非实际数据集字段来建议选项。

### 具体问题案例

用户查询："公司今年每个月的利润和利润率是多少？"

AI 错误地生成了澄清选项：
```
"使用销售额 (sales_amount) 减去成本来计算利润，并用利润除以销售额计算利润率。"
```

**问题**：数据集中只有 `sales_amount` 字段，没有 `cost` 字段，这个澄清选项是无效的。如果用户选择了这个选项，后续生成 SQL 时会因为找不到 `cost` 字段而出错。

## 根本原因分析

1. **数据集信息传递**：系统通过 `datasetTools.getDatasetSchemaAndPermissions()` 方法获取并传递数据集信息给 AI
2. **约束不足**：原始 prompt 中对字段约束的描述不够强烈，AI 可能忽略这些约束
3. **常识推理**：AI 基于商业常识推断出"利润 = 销售额 - 成本"，但没有验证数据集中是否真的存在这些字段

## 解决方案

### 1. 强化 NLU Prompt 约束

在 `chatbi-agent-core/src/main/resources/prompts/nlu_intent_extraction.txt` 中加强了约束：

#### 原始约束：
```
- When generating clarification options, they MUST be specific and derived from the "Available datasets for querying".
```

#### 优化后的约束：
```
- **CRITICAL CONSTRAINT**: When generating clarification options, they MUST be strictly based ONLY on the fields (columns) listed in the "Available datasets for querying" section. DO NOT suggest any metrics, dimensions, or calculations that are not explicitly defined in the provided dataset schemas.
- **FIELD VALIDATION**: Before suggesting any field name, calculation, or derived metric in clarification options, verify that ALL required fields exist in the dataset.
- **FORBIDDEN SUGGESTIONS**: Never suggest common business metrics like "利润/profit", "利润率/profit margin", "成本/cost", "毛利/gross profit" unless these exact fields exist in the dataset schema.
- **VALIDATION STEP**: Before generating each clarification option, mentally check: "Does this exact field name appear in the provided dataset schema?" If not, do not include it.
```

### 2. 强化澄清策略指导

在 `SmartClarificationStrategy.generateGuidanceText()` 方法中加强了对 AI 的指导：

#### 原始指导：
```java
guidance.append("Use the schema of the '").append(targetDataset.getDatasetName()).append("' dataset to create specific and helpful options. ");
```

#### 优化后的指导：
```java
guidance.append("CRITICAL CONSTRAINTS: ");
guidance.append("1) Use ONLY the exact field names from the dataset schema provided below. ");
guidance.append("2) DO NOT suggest any fields, metrics, or calculations that are not explicitly listed in the dataset. ");
guidance.append("3) DO NOT suggest common business metrics like 'profit', 'profit margin', 'cost' unless these exact field names exist in the dataset. ");
guidance.append("4) Before suggesting any clarification option, verify that all referenced field names are present in the dataset schema. ");

// 列出具体可用字段
guidance.append("Available fields in this dataset: ");
List<String> fieldNames = targetDataset.getColumns().stream()
        .map(col -> col.getColumnName())
        .collect(Collectors.toList());
guidance.append(String.join(", ", fieldNames)).append(". ");
```

## 优化效果

### 1. 严格字段验证
- AI 现在会在生成每个澄清选项前验证字段是否存在
- 禁止基于常识推断不存在的字段

### 2. 明确可用字段列表
- 在澄清引导中明确列出数据集中的所有可用字段
- AI 只能基于这些字段生成澄清选项

### 3. 防止常见错误
- 明确禁止建议常见但可能不存在的业务指标（如利润、成本等）
- 除非这些字段确实存在于数据集中

### 4. 更好的用户体验
- 用户看到的澄清选项都是可执行的
- 避免了选择无效选项导致的 SQL 错误

## 预期改进

修改后，对于用户查询"公司今年每个月的利润和利润率是多少？"，系统现在会：

1. 检查数据集中是否存在 `profit`、`profit_margin`、`cost` 等字段
2. 如果不存在，不会建议基于这些字段的澄清选项
3. 只会基于实际存在的字段（如 `sales_amount`、`order_count` 等）生成澄清选项
4. 可能会建议用户查询现有的指标，或者提示用户需要的字段不存在

## 技术细节

- **修改文件**：
  - `chatbi-agent-core/src/main/resources/prompts/nlu_intent_extraction.txt`
  - `chatbi-agent-core/src/main/java/com/qding/chatbi/agent/service/impl/SmartClarificationStrategy.java`

- **核心原理**：通过在 prompt 中加强约束和在代码中明确指导，确保 AI 严格基于数据集 schema 生成澄清选项

- **兼容性**：此修改向后兼容，不会影响现有功能，只是让澄清选项更加准确

## 测试建议

建议测试以下场景：
1. 用户查询包含数据集中不存在的字段
2. 用户查询需要多个字段计算的指标（如利润率）
3. 用户查询模糊的业务术语
4. 确保系统只基于实际存在的字段生成澄清选项 