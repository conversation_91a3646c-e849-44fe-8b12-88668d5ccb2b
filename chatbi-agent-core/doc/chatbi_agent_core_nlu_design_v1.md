## **Chat BI \- NLU Agent详细设计 (chatbi-agent-core)**

**模块路径**: com.qding.chatbi.agent.service.NluAgent (接口) 和 com.qding.chatbi.agent.service.impl.NluAgentImpl (实现类)

### **1\. 定位与用途**

自然语言理解 (NLU) Agent 是 chatbi-agent-core 模块中的核心组件之一。它负责接收用户的原始查询文本和相关的对话上下文，通过调用大语言模型 (LLM) 并结合知识库信息（如查询示例、业务术语），将其解析为结构化的查询意图 (Structured Query Intent)。这个结构化的输出将作为后续查询规划与生成步骤的输入。

### **2\. 核心方法 (NluAgent 接口)**

package com.qding.chatbi.agent.service;

import com.qding.chatbi.agent.dto.StructuredQueryIntent; // 定义在 agent.dto 包下  
import com.qding.chatbi.common.dto.UserContextDTO;  
import dev.langchain4j.memory.ChatMemory;

public interface NluAgent {  
    /\*\*  
     \* 理解用户查询并提取意图和实体。  
     \*  
     \* @param userQuery 用户的原始查询文本。  
     \* @param chatMemory 当前会话的聊天记录。  
     \* @param userContext 包含用户角色等上下文信息，用于获取权限内的数据集摘要。  
     \* @return 结构化的查询意图对象。  
     \* @throws com.qding.chatbi.common.exception.ChatBiException 如果NLU处理过程中发生业务异常。  
     \*/  
    StructuredQueryIntent understand(String userQuery, ChatMemory chatMemory, UserContextDTO userContext);  
}

### **3\. NluAgentImpl 实现类**

* **注解**: @Service  
* **依赖注入 (已修正)**:  
  * ChatLanguageModel chatLanguageModel; (from LangChainConfig)  
  * PromptTemplateService promptTemplateService; (from com.qding.chatbi.agent.prompt)  
  * **DatasetTools datasetTools;** (直接注入Tool Bean)  
  * **KnowledgeBaseTools knowledgeBaseTools;** (直接注入Tool Bean)  
  * JsonUtil jsonUtil; (from com.qding.chatbi.common.util)  
* **understand 方法内部逻辑步骤 (已修正)**:  
  1. **预处理用户输入 (userQuery)**: 例如，去除多余空格。  
  2. **准备Prompt构建所需的变量**:  
     * chat\_history: 格式化 chatMemory.messages() 为字符串。  
     * available\_datasets\_summary:  
       * **直接调用Tool方法**:  
         String datasetSummary \= datasetTools.getDatasetSchemaAndPermissions(  
             null, // 传入null或一个通用标识，表示获取所有可访问数据集的摘要  
             userContext.getUserRoles()  
         );

     * retrieved\_examples (可选, RAG):  
       * **直接调用Tool方法**:  
         String similarQueries \= knowledgeBaseTools.searchSimilarQueries(userQuery, 3);

     * term\_explanations (可选, RAG):  
       * 对 userQuery 进行简单的关键词提取。  
       * **直接调用Tool方法**:  
         String termExplanation \= knowledgeBaseTools.searchBusinessTerms(keyword);

  3. **构建Prompt**:  
     * 获取 nlu\_intent\_extraction Prompt模板。  
     * 创建变量Map并填充，包括直接调用Tool返回的字符串。  
     * Prompt prompt \= nluPromptTemplate.apply(variables);  
  4. **调用LLM**: AiMessage aiMessage \= chatLanguageModel.generate(prompt.toMessages());  
  5. **解析LLM输出**:  
     * 将LLM返回的JSON字符串反序列化为 StructuredQueryIntent 对象。  
     * 进行健壮性处理和校验。  
  6. **返回解析后的 StructuredQueryIntent 对象**。

### **4\. com.qding.chatbi.agent.dto.StructuredQueryIntent.java (内部DTO)**

* **用途**: 承载NLU模块的结构化输出。此类应定义在 com.qding.chatbi.agent.dto 包下。  
* **属性**:  
  * private String intent; // 例如: "DATA\_QUERY", "CLARIFICATION\_NEEDED", "GREETING", "ASK\_CAPABILITIES", "UNKNOWN"  
  * private Map\<String, Object\> entities; // 存储提取的实体  
  * private boolean needsClarification;  
  * private String clarificationPromptToUser; // 当 needsClarification 为true时，向用户提出的问题  
  * private List\<com.qding.chatbi.common.dto.ClarificationOption\> clarificationOptions; // 当需要澄清时，AI生成的澄清选项  
  * private String targetDatasetHint; // (可选) NLU初步判断用户可能想查询的数据集名称  
* **entities Map 结构示例**:  
  * "metrics": List\<String\>  
  * "dimensions\_to\_group": List\<String\>  
  * "filters": List\<FilterCondition\>  
    * FilterCondition (内部record或class): String fieldName, String operator, Object value, Object value2  
  * "time\_range\_text": String  
  * "sort\_by": List\<SortCondition\>  
    * SortCondition (内部record或class): String fieldName, String order  
  * "limit": Integer  
* **构造方法, Getters, Setters**。

### **5\. Prompt 模板 (nlu\_intent\_extraction.txt)**

You are an expert in understanding user queries for a Business Intelligence system.  
Your goal is to parse the "Current User Question" into a structured JSON output.  
Consider the "Chat History" for context.  
Refer to "Available datasets for querying" to understand what data can be queried.  
"Retrieved similar query examples" and "Retrieved business term explanations" are provided for additional context if available.

Chat History (last N turns):  
{{chat\_history}}

Available datasets for querying (user has permission to access these):  
{{available\_datasets\_summary}}

{{\#if retrieved\_examples}}  
Retrieved similar query examples:  
\- {{retrieved\_examples}}  
{{/if}}

{{\#if term\_explanations}}  
Retrieved business term explanations:  
\- {{term\_explanations}}  
{{/if}}

Current User Question: {{user\_query}}

Output your analysis strictly as a JSON object with the following structure:  
{  
  "intent": "...", // Possible intents: DATA\_QUERY, CLARIFICATION\_NEEDED, GREETING, UNKNOWN  
  "entities": { // Populate ONLY if intent is DATA\_QUERY or if entities can be partially extracted for a CLARIFICATION\_NEEDED intent.  
    "metrics": \["metric\_name\_1", "metric\_name\_2"\], // Business names of metrics requested.  
    "dimensions\_to\_group": \["dimension\_name\_1"\], // Business names of dimensions to group by.  
    "filters": \[ // List of filter conditions.  
      {"fieldName": "business\_field\_name\_1", "operator": "EQUALS", "value": "filter\_value\_1"},  
      {"fieldName": "date\_field", "operator": "BETWEEN", "value": "YYYY-MM-DD", "value2\_for\_between": "YYYY-MM-DD"}  
      // Supported operators: EQUALS, NOT\_EQUALS, GREATER\_THAN, GREATER\_THAN\_OR\_EQUAL\_TO, LESS\_THAN, LESS\_THAN\_OR\_EQUAL\_TO, BETWEEN, CONTAINS, STARTS\_WITH, ENDS\_WITH, IN, NOT\_IN  
    \],  
    "time\_range\_text": "original time phrase like 'last week' or 'this month'", // Original text if a time range is mentioned.  
    "sort\_by": \[{"fieldName": "business\_metric\_or\_dimension\_name", "order": "ASC|DESC"}\],  
    "limit": 10 // Integer, if mentioned by user.  
  },  
  "needsClarification": true\_or\_false, // Set to true if critical information is missing or query is ambiguous.  
  "clarificationPromptToUser": "If needsClarification is true, formulate a clear question to ask the user to resolve ambiguity or get missing info.",  
  "clarificationOptions": \[ // If needsClarification is true, provide 2-3 concise options for the user to choose from.  
    {"optionId": "auto\_generated\_option\_id\_1", "optionText": "Description of option 1"},  
    {"optionId": "auto\_generated\_option\_id\_2", "optionText": "Description of option 2"}  
  \],  
  "targetDatasetHint": "If you can confidently identify a single target dataset from 'Available datasets' that fits the query, mention its business name here. Otherwise, leave null."  
}  
Ensure the JSON output is valid and directly parsable. All string values in JSON must be enclosed in double quotes.  
If the user's intent is not clear or critical information for a DATA\_QUERY is missing (e.g., what specific metrics to query, or ambiguity in terms that cannot be resolved by available datasets), set "intent" to "CLARIFICATION\_NEEDED" and provide "clarificationPromptToUser" and "clarificationOptions".  
If intent is DATA\_QUERY but some common implicit assumptions can be made (e.g., user asks for 'sales in Beijing' and 'sales' usually means 'total sales amount' from 'SalesSummary' dataset), you can infer and fill entities.  
JSON:  
