// File: chatbi-agent-core/src/main/java/com/qding/chatbi/agent/chain/SqlGenerationChain.java
package com.qding.chatbi.agent.chain;

import com.qding.chatbi.agent.dto.StructuredQueryIntent;
import com.qding.chatbi.agent.service.prompt.PromptTemplateService;
import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职责: 专门负责根据结构化上下文生成SQL查询。
 * 这是对LLM的第二次调用，专注于Text-to-SQL任务。
 */
@Component
@Slf4j
public class SqlGenerationChain {

    private final ChatLanguageModel chatModel;
    private final PromptTemplate sqlPromptTemplate;

    @Autowired
    public SqlGenerationChain(ChatLanguageModel chatModel, PromptTemplateService promptTemplateService) {
        this.chatModel = chatModel;
        // 从资源文件中加载SQL生成专用的提示词模板
        this.sqlPromptTemplate = promptTemplateService.getPromptTemplate("sql_generation");
    }

    @Builder
    public static class SqlGenerationContext {
        String dbDialect;
        DatasetInfoDTO dataset;
        String userQuestion;
        StructuredQueryIntent structuredIntent;
    }

    /**
     * 执行SQL生成链
     * @param context 包含生成SQL所需的所有上下文信息
     * @return LLM生成的SQL字符串
     */
    public String execute(SqlGenerationContext context) {
        log.info("Executing SQL Generation Chain for dataset: {}", context.dataset.getDatasetName());

        // 1. 构建模板所需的变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("db_dialect", context.dbDialect);
        variables.put("user_question", context.userQuestion);
        variables.put("structured_intent_json", context.structuredIntent.toJson()); // 假设有toJson方法
        variables.put("table_ddl", generateDdl(context.dataset));
        variables.put("column_semantics", generateColumnSemantics(context.dataset));

        // 2. 应用模板
        Prompt prompt = sqlPromptTemplate.apply(variables);
        log.debug("SQL Generation Prompt: \n{}", prompt.text());

        // 3. 调用LLM并获取响应
        String response = chatModel.generate(prompt.text());
        log.info("LLM generated SQL response: {}", response);

        // 4. 清洗和提取SQL
        return extractSqlFromResponse(response);
    }

    /**
     * 根据数据集信息动态生成DDL (CREATE TABLE) 语句，为LLM提供表结构。
     */
    private String generateDdl(DatasetInfoDTO dataset) {
        StringBuilder ddl = new StringBuilder();
        ddl.append("CREATE TABLE ").append(dataset.getDatasetName()).append(" (\n");
        for (ColumnInfoDTO column : dataset.getColumns()) {
            ddl.append("  ")
               .append(column.getColumnName())
               .append(" ")
               .append(column.getDataType()) // e.g., VARCHAR, BIGINT, DATETIME
               .append(" COMMENT '")
               .append(column.getBusinessName()) // 将业务名放在注释里
               .append("',\n");
        }
        ddl.delete(ddl.length() - 2, ddl.length()); // 移除最后一个逗号和换行符
        ddl.append("\n);");
        return ddl.toString();
    }

    /**
     * 生成列的业务语义描述，帮助LLM更好地理解每个字段的含义。
     */
    private String generateColumnSemantics(DatasetInfoDTO dataset) {
        return dataset.getColumns().stream()
            .map(col -> String.format("- %s: %s", col.getColumnName(), col.getDescription()))
            .collect(Collectors.joining("\n"));
    }

    /**
     * 从LLM的完整响应中提取出 ```sql ... ``` 代码块中的SQL。
     */
    private String extractSqlFromResponse(String response) {
        // 这是一个简化的实现，实际应用中需要更健壮的正则表达式
        if (response.contains("```sql")) {
            int start = response.indexOf("```sql") + 6;
            int end = response.indexOf("```", start);
            return response.substring(start, end).trim();
        }
        // 如果没有找到代码块，可能LLM直接返回了SQL
        return response.trim();
    }
}


// File: chatbi-agent-core/src/main/java/com/qding/chatbi/agent/service/impl/QueryPlannerAgentImpl.java
package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.chain.SqlGenerationChain;
import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.agent.dto.StructuredQueryIntent;
import com.qding.chatbi.agent.service.QueryPlannerAgent;
import com.qding.chatbi.agent.tool.DatasetTools;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.UserContextDTO;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Service
@Slf4j
public class QueryPlannerAgentImpl implements QueryPlannerAgent {

    @Autowired
    private DatasetTools datasetTools;

    @Autowired
    private SqlGenerationChain sqlGenerationChain;

    // 定义一个简单的正则表达式来屏蔽危险的SQL关键字
    private static final Pattern DANGEROUS_SQL_PATTERN = Pattern.compile(
        "\\b(DROP|DELETE|UPDATE|INSERT|TRUNCATE|ALTER|CREATE|EXEC|EXECUTE)\\b",
        Pattern.CASE_INSENSITIVE
    );

    @Override
    public QueryPlan plan(UserContextDTO userContext, StructuredQueryIntent intent) {
        long startTime = System.currentTimeMillis();
        StringBuilder thoughtProcess = new StringBuilder("Starting query planning...\n");

        // 1. 数据集选择 (逻辑不变)
        List<DatasetInfoDTO> candidateDatasets = datasetTools.getDatasetInfoByUserRoles(userContext.getRoles());
        DatasetInfoDTO bestDataset = findBestDataset(intent, candidateDatasets, thoughtProcess);

        if (bestDataset == null) {
            throw new ChatBiException(ErrorCode.DATASET_NOT_FOUND, "Could not find a suitable dataset for the query.");
        }
        log.info("Best dataset selected: {}", bestDataset.getDatasetName());
        thoughtProcess.append(String.format("Step 1: Selected dataset '%s' based on query intent.\n", bestDataset.getDatasetName()));


        // 2. 核心变更: 调用SqlGenerationChain来生成SQL
        SqlGenerationChain.SqlGenerationContext context = SqlGenerationChain.SqlGenerationContext.builder()
                .dbDialect(bestDataset.getDbDialect()) // 假设DatasetInfoDTO中包含数据库方言信息
                .dataset(bestDataset)
                .userQuestion(intent.getOriginalQuery())
                .structuredIntent(intent)
                .build();
        
        String generatedSql = sqlGenerationChain.execute(context);
        thoughtProcess.append("Step 2: Generated SQL using LLM with contextual prompt.\n");
        thoughtProcess.append("Generated SQL (pre-validation): ").append(generatedSql).append("\n");


        // 3. SQL 验证与清洗 (新增安全层)
        validateSql(generatedSql, bestDataset);
        thoughtProcess.append("Step 3: SQL validation passed. The query is safe to execute.\n");


        // 4. 构建最终QueryPlan (逻辑不变)
        QueryPlan plan = new QueryPlan();
        plan.setDatasetId(bestDataset.getId());
        plan.setSql(generatedSql);
        plan.setThoughtProcess(thoughtProcess.toString());

        log.info("Query planning completed in {} ms", System.currentTimeMillis() - startTime);
        return plan;
    }

    private DatasetInfoDTO findBestDataset(StructuredQueryIntent intent, List<DatasetInfoDTO> candidates, StringBuilder thoughtProcess) {
        // v1.0中的数据集评分和选择逻辑在这里实现
        // ... (此处省略具体实现, 逻辑与之前讨论的相同)
        if (candidates.isEmpty()) return null; // 简化处理
        return candidates.get(0);
    }
    
    /**
     * 对LLM生成的SQL进行安全验证
     * @param sql LLM生成的SQL
     * @param dataset 选定的数据集
     */
    private void validateSql(String sql, DatasetInfoDTO dataset) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new ChatBiException(ErrorCode.INVALID_INPUT, "LLM returned an empty SQL query.");
        }

        // 安全检查: 禁止危险关键字
        if (DANGEROUS_SQL_PATTERN.matcher(sql).find()) {
            log.error("Dangerous SQL keyword detected in LLM output: {}", sql);
            throw new ChatBiException(ErrorCode.PERMISSION_DENIED, "Generated query contains potentially harmful operations.");
        }

        // 越权检查: 确保查询的表名是指定的表名或子查询别名
        // 这是一个简化的检查，实际可能需要更复杂的SQL解析库（如JSqlParser）
        String fromClause = sql.substring(sql.toLowerCase().indexOf("from") + 4).trim();
        String mainTable = fromClause.split("\\s+")[0];
        
        if (!mainTable.equalsIgnoreCase(dataset.getDatasetName()) && !mainTable.startsWith("(")) {
            log.error("SQL is querying a different table '{}' than the selected one '{}'", mainTable, dataset.getDatasetName());
            throw new ChatBiException(ErrorCode.PERMISSION_DENIED, "Generated query attempts to access an unauthorized table.");
        }

        // (可选) 还可以校验所有引用的字段是否都存在于dataset.getColumns()中
    }
}
