# LLM返回模板变量问题调试指南

## 🚨 问题描述

在ChatBI系统的SQL生成过程中，发现LLM直接返回了模板变量 `{generated_sql_query}` 而不是实际的SQL查询语句。

### 问题现象

```
LLM原始响应: ```sql
{generated_sql_query}
```
```

### 错误表现
- LLM返回的SQL包含未替换的模板变量
- SQL验证失败，提示"缺少FROM子句"
- 系统无法执行查询

## 🔍 问题分析

### 可能的根本原因

1. **Prompt模板变量替换失败**
   - `PromptTemplate.apply(variables)` 方法未正确工作
   - 变量映射不匹配模板中的占位符

2. **模板文件问题**
   - SQL生成模板文件可能包含错误的变量名
   - 模板格式不正确

3. **LLM理解问题**
   - LLM可能误解了任务要求
   - Prompt指令不够清晰

## 🛠️ 调试步骤

### 步骤1: 检查变量映射
在 `SqlGenerationChain.java` 中添加详细的变量调试日志：

```java
// 🔍 调试：记录所有变量
log.info("🔍 调试 - 传递给模板的变量:");
for (Map.Entry<String, Object> entry : variables.entrySet()) {
    String value = entry.getValue() != null ? entry.getValue().toString() : "null";
    if (value.length() > 100) {
        value = value.substring(0, 100) + "... (截断)";
    }
    log.info("   - {}: {}", entry.getKey(), value);
}
```

### 步骤2: 检查完整Prompt内容
```java
// 🔍 调试：输出完整的Prompt内容
log.info("🔍 调试 - 完整的Prompt内容:");
log.info("{}", prompt.text());
```

### 步骤3: 验证模板文件
检查 `sql_generation.txt` 模板文件：
- 确认所有变量占位符格式正确
- 验证没有包含 `{generated_sql_query}` 这样的变量

## 🎯 预期解决方案

### 解决方案1: 修复模板变量替换
如果发现变量替换失败，需要：
1. 检查变量名是否与模板中的占位符完全匹配
2. 确认 `PromptTemplate` 的实现是否正确
3. 验证变量值是否为null或空

### 解决方案2: 优化Prompt指令
如果LLM理解有误，需要：
1. 在模板中明确指出不要返回模板变量
2. 强化SQL生成的具体要求
3. 提供更清晰的示例

### 解决方案3: 添加响应验证
在SQL提取过程中：
1. 检测并拒绝包含模板变量的响应
2. 提供错误处理和重试机制
3. 记录详细的错误信息

## 📋 检查清单

- [ ] 变量映射是否正确
- [ ] 模板文件是否包含错误变量
- [ ] Prompt是否正确应用变量
- [ ] LLM响应是否包含实际SQL
- [ ] 错误处理是否完善

## 🔄 测试验证

### 测试用例
- 用户查询: "今年上半年的销售收入是多少？"
- 预期SQL: 包含实际的SELECT语句和FROM子句
- 实际结果: 应该不包含任何模板变量

### 验证点
1. 变量替换日志显示所有变量都有有效值
2. 生成的Prompt不包含未替换的占位符
3. LLM返回的是实际可执行的SQL语句
4. SQL验证通过，包含FROM子句

## 📝 修复记录

### 已实施的修复
1. ✅ 增强了日志记录，显示变量映射详情
2. ✅ 添加了完整Prompt内容输出
3. ✅ 增加了SQL响应的详细检查

### 待实施的修复
- [ ] 根据调试日志结果确定具体问题
- [ ] 实施相应的修复方案
- [ ] 添加自动化测试用例

## 🚀 后续优化

1. **增强错误处理**: 当检测到模板变量时自动重试
2. **改进Prompt设计**: 使用更明确的指令避免混淆
3. **添加响应验证**: 在SQL提取前进行格式检查
4. **优化调试工具**: 提供更好的问题诊断能力

---
*本文档将根据调试结果持续更新* 