package com.qding.chatbi.agent.service;

import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.dto.UserContextDTO;

/**
 * DataRetrievalAgent 接口
 * 负责接收一个确定的QueryPlan，并执行它以从物理数据源检索数据。
 */
public interface DataRetrievalAgent {

    /**
     * 根据给定的查询计划执行数据检索。
     *
     * @param plan 包含了要执行的SQL和目标数据集ID的查询计划。
     * @param userContext 当前的用户上下文。
     * @return 封装了查询结果的QueryResult对象。
     */
    QueryResult retrieve(QueryPlan plan, UserContextDTO userContext);
}
