package com.qding.chatbi.agent.service;

import java.util.List;
import java.util.Map;

/**
 * 行级权限服务接口
 * 负责生成基于用户角色的数据行级过滤条件
 */
public interface RowLevelPermissionService {

    /**
     * 为指定用户和数据集生成行级权限过滤条件
     *
     * @param userId 用户ID
     * @param datasetId 数据集ID
     * @param baseTableName 基础表名
     * @return SQL WHERE 条件字符串，如果没有权限限制则返回空字符串
     */
    String generateRowLevelFilter(String userId, Long datasetId, String baseTableName);

    /**
     * 为指定角色和数据集生成行级权限过滤条件
     *
     * @param roleNames 角色名称列表
     * @param datasetId 数据集ID
     * @param baseTableName 基础表名
     * @param userContext 用户上下文信息
     * @return SQL WHERE 条件字符串
     */
    String generateRowLevelFilterByRoles(List<String> roleNames, Long datasetId, 
                                       String baseTableName, Map<String, Object> userContext);

    /**
     * 检查用户是否对指定数据集有行级权限限制
     *
     * @param userId 用户ID
     * @param datasetId 数据集ID
     * @return true表示有行级权限限制，false表示无限制
     */
    boolean hasRowLevelRestriction(String userId, Long datasetId);

    /**
     * 获取用户上下文信息
     *
     * @param userId 用户ID
     * @return 用户上下文信息Map
     */
    Map<String, Object> getUserContext(String userId);

    /**
     * 验证SQL过滤条件的安全性
     *
     * @param sqlCondition SQL条件
     * @return true表示安全，false表示不安全
     */
    boolean validateSqlCondition(String sqlCondition);
}
