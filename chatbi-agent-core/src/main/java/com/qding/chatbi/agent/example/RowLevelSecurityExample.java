package com.qding.chatbi.agent.example;

import com.qding.chatbi.agent.service.QuerySecurityEnhancer;
import com.qding.chatbi.agent.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 行级权限控制使用示例
 * 展示如何在现有查询流程中集成行级权限控制
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RowLevelSecurityExample {

    private final QuerySecurityEnhancer querySecurityEnhancer;
    private final UserRoleService userRoleService;

    /**
     * 示例：在查询执行前应用行级权限
     */
    public String executeSecureQuery(String originalSql, String userId, Long datasetId) {
        log.info("执行安全查询 - 用户: {}, 数据集: {}", userId, datasetId);

        try {
            // 1. 验证用户权限
            if (!querySecurityEnhancer.validateQueryPermission(userId, datasetId)) {
                log.warn("用户 {} 没有访问数据集 {} 的权限", userId, datasetId);
                return null;
            }

            // 2. 获取用户角色（用于日志记录）
            List<String> userRoles = userRoleService.getUserRoles(userId);
            log.debug("用户 {} 的角色: {}", userId, userRoles);

            // 3. 应用行级权限控制
            String secureSQL = querySecurityEnhancer.enhanceQuerySecurity(originalSql, userId, datasetId);

            // 4. 执行增强后的SQL（这里只是示例，实际执行需要调用数据库服务）
            log.info("执行安全SQL: {}", secureSQL);
            
            return secureSQL;

        } catch (Exception e) {
            log.error("执行安全查询失败", e);
            return null;
        }
    }

    /**
     * 示例场景演示
     */
    public void demonstrateRowLevelSecurity() {
        log.info("=== 行级权限控制演示 ===");

        String originalSql = "SELECT * FROM sales_data ORDER BY amount DESC";
        Long datasetId = 1L;

        // 场景1：销售人员查询（只能看自己区域的数据）
        log.info("\n--- 场景1：销售人员查询 ---");
        String salesResult = executeSecureQuery(originalSql, "user001", datasetId);
        log.info("销售人员查询结果SQL: {}", salesResult);

        // 场景2：区域经理查询（可以看自己区域的数据）
        log.info("\n--- 场景2：区域经理查询 ---");
        String managerResult = executeSecureQuery(originalSql, "user002", datasetId);
        log.info("区域经理查询结果SQL: {}", managerResult);

        // 场景3：财务人员查询（只能看已审核的数据）
        log.info("\n--- 场景3：财务人员查询 ---");
        String financeResult = executeSecureQuery(originalSql, "user003", datasetId);
        log.info("财务人员查询结果SQL: {}", financeResult);

        // 场景4：高级管理层查询（可以看多个区域的数据）
        log.info("\n--- 场景4：高级管理层查询 ---");
        String seniorResult = executeSecureQuery(originalSql, "user004", datasetId);
        log.info("高级管理层查询结果SQL: {}", seniorResult);
    }

    /**
     * 权限检查示例
     */
    public void demonstratePermissionCheck() {
        log.info("=== 权限检查演示 ===");

        String[] userIds = {"user001", "user002", "user003", "user004", "user999"};
        
        for (String userId : userIds) {
            try {
                List<String> roles = userRoleService.getUserRoles(userId);
                boolean hasRestriction = querySecurityEnhancer.needsRowLevelSecurity(userId);
                
                log.info("用户: {} | 角色: {} | 有行级限制: {}", 
                    userId, roles, hasRestriction);
                    
            } catch (Exception e) {
                log.warn("检查用户 {} 权限失败: {}", userId, e.getMessage());
            }
        }
    }
}
