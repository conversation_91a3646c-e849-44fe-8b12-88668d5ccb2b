package com.qding.chatbi.agent.util;

import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.ColumnMetadata;
import com.qding.chatbi.common.enums.DataType;
import com.qding.chatbi.common.enums.SemanticType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Utility class for handling column name mappings and resolving ambiguities.
 */
@Slf4j
public class ColumnMappingUtil {

    // Regex to parse a column expression, capturing the expression itself and an optional alias.
    // Handles "expr as alias", "expr AS alias", "expr alias"
    private static final Pattern ALIAS_PATTERN = Pattern.compile(
            "^(.*?)(?:\\s+(?:as|AS)?\\s+[`\"]?(\\w+)[`\"]?)?$", Pattern.CASE_INSENSITIVE);


    /**
     * 将SQL查询的列映射到其元数据。
     *
     * @param sql             生成的SQL查询语句
     * @param allColumns      数据集中所有可用列的元数据
     * @return 映射后的元数据列表，顺序与SELECT子句中的列一致
     */
    public static List<ColumnMetadata> mapColumnsToMetadata(String sql, List<ColumnInfoDTO> allColumns) {
        if (sql == null || sql.isEmpty() || CollectionUtils.isEmpty(allColumns)) {
            log.warn("SQL或列元数据为空，无法进行映射。");
            return Collections.emptyList();
        }

        List<String> selectedColumnsExpressions = extractSelectedColumns(sql);
        if (CollectionUtils.isEmpty(selectedColumnsExpressions)) {
            log.warn("无法从SQL中提取出任何列: {}", sql);
            return Collections.emptyList();
        }

        // 创建一个从技术名称到元数据的映射，以便快速查找
        Map<String, ColumnInfoDTO> technicalNameToMetadata = allColumns.stream()
                .filter(c -> c.getTechnicalNameOrExpression() != null && !c.getTechnicalNameOrExpression().isEmpty())
                .collect(Collectors.toMap(ColumnInfoDTO::getTechnicalNameOrExpression, Function.identity(), (a, b) -> a));

        List<ColumnMetadata> result = new ArrayList<>();
        log.info("开始将 {} 个SQL查询列映射到元数据...", selectedColumnsExpressions.size());

        for (String colExpression : selectedColumnsExpressions) {
            Matcher matcher = ALIAS_PATTERN.matcher(colExpression);
            String expressionPart = colExpression;
            String alias = null;

            if (matcher.matches()) {
                expressionPart = matcher.group(1).trim();
                alias = matcher.group(2); // 可能为null
            }
            
            // 如果没有显式别名，使用表达式的最后部分作为别名
            if (alias == null || alias.trim().isEmpty()) {
                alias = expressionPart;
                // 如果是复杂表达式（如函数调用），尝试提取有意义的部分
                if (alias.contains("(") && alias.contains(")")) {
                    // 对于函数调用，使用整个表达式作为别名，但去除空格
                    alias = alias.replaceAll("\\s+", "_").toLowerCase();
                }
            }
            
            // 清理别名：移除引号和表前缀
            if (alias.contains(".")) {
                alias = alias.substring(alias.lastIndexOf('.') + 1);
            }
            alias = alias.replace("`", "").replace("\"", "").replace("'", "");

            ColumnInfoDTO foundMetadata = findOriginalColumn(expressionPart, technicalNameToMetadata);

            if (foundMetadata != null) {
                log.debug("成功映射: SQL表达式 '{}' -> 元数据列 '{}' (别名: '{}')", colExpression, foundMetadata.getColumnName(), alias);
                DataType dataType = safeConvertDataType(foundMetadata.getDataType(), foundMetadata.getColumnName());
                result.add(new ColumnMetadata(alias, foundMetadata.getColumnName(), foundMetadata.getSemanticType(), dataType));
            } else {
                log.warn("无法为SQL列 '{}' (别名: '{}') 找到元数据，将使用默认值", expressionPart, alias);
                // 如果找不到元数据，friendlyName就用别名代替
                result.add(new ColumnMetadata(alias, alias, SemanticType.METRIC, DataType.STRING));
            }
        }
        log.info("映射完成，成功映射 {} 个列。", result.stream().filter(c -> c.getSemanticType() != null).count());
        return result;
    }

     /**
     * 在表达式中查找其引用的原始列元数据。
     */
    private static ColumnInfoDTO findOriginalColumn(String expressionPart, Map<String, ColumnInfoDTO> technicalNameToMetadata) {
        // 优化：优先匹配更长的技术名称，避免 "user_id" 错误地匹配到 "id"
        List<String> sortedTechnicalNames = technicalNameToMetadata.keySet().stream()
                .sorted((s1, s2) -> Integer.compare(s2.length(), s1.length()))
                .collect(Collectors.toList());

        for (String technicalName : sortedTechnicalNames) {
            // 使用单词边界 `\b` 来确保匹配到的是完整的列名，而不是一个子字符串
            Pattern pattern = Pattern.compile("\\b" + Pattern.quote(technicalName) + "\\b", Pattern.CASE_INSENSITIVE);
            if (pattern.matcher(expressionPart).find()) {
                return technicalNameToMetadata.get(technicalName);
            }
        }
        log.debug("在表达式 '{}' 中未找到任何已知的技术列名。", expressionPart);
        return null;
    }

    /**
     * 安全地将字符串类型转换为DataType枚举。
     * @param typeStr 数据类型字符串
     * @param columnName 列名，用于日志记录
     * @return DataType枚举，如果转换失败则返回STRING
     */
    private static DataType safeConvertDataType(String typeStr, String columnName) {
        if (typeStr == null || typeStr.trim().isEmpty()) {
            log.warn("列 '{}' 的数据类型为空，默认为STRING。", columnName);
            return DataType.STRING;
        }
        try {
            return DataType.valueOf(typeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("无法识别列 '{}' 的数据类型 '{}'。默认为STRING。", columnName, typeStr);
            return DataType.STRING;
        }
    }

    /**
     * 从SQL语句中提取SELECT子句中的列名和别名。
     *
     * @param sql SQL查询
     * @return 列名列表
     */
    private static List<String> extractSelectedColumns(String sql) {
        try {
            // 使用正则表达式来匹配SELECT和FROM，支持多行和任意空白字符
            Pattern selectPattern = Pattern.compile("SELECT\\s+(.*?)\\s+FROM", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = selectPattern.matcher(sql);
            
            if (!matcher.find()) {
                log.warn("无法在SQL中找到SELECT...FROM模式: {}", sql);
                return Collections.emptyList();
            }
            
            String columnsStr = matcher.group(1).trim();
            log.debug("提取的列字符串: {}", columnsStr);
            
            // 按逗号分割，但要忽略括号内的逗号
            String[] cols = columnsStr.split(",(?![^()]*\\))");
            List<String> columnList = new ArrayList<>();
            for (String col : cols) {
                String trimmedCol = col.trim();
                if (!trimmedCol.isEmpty()) {
                    columnList.add(trimmedCol);
                }
            }
            
            log.debug("成功提取 {} 个列: {}", columnList.size(), columnList);
            return columnList;
        } catch (Exception e) {
            log.error("Failed to extract columns from SQL: {}", sql, e);
            return Collections.emptyList();
        }
    }
} 