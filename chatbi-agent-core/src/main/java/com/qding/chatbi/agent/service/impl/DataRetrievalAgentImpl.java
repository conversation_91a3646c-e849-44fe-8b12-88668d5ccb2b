package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.agent.service.DataRetrievalAgent;
import com.qding.chatbi.agent.tool.DataAccessTools;
import com.qding.chatbi.agent.tool.DatasetTools;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.dto.UserContextDTO;
import com.qding.chatbi.common.enums.ErrorCode;
import com.qding.chatbi.common.exception.ChatBiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据检索代理实现类
 * 
 * 职责：
 * 1. 接收QueryPlan对象，包含已生成的SQL和目标数据集信息
 * 2. 调用DataAccessTools执行SQL查询
 * 3. 返回QueryResult对象给上层调用者
 * 
 * 数据流转：
 * QueryPlan -> DataAccessService -> Database -> QueryResult
 * 
 * 调试说明：
 * - 记录接收到的QueryPlan的完整信息
 * - 记录调用DataAccessTools的参数
 * - 记录查询结果的详细统计信息
 * - 所有异常都记录完整的上下文信息
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
@Service
@Slf4j
public class DataRetrievalAgentImpl implements DataRetrievalAgent {

    @Autowired
    private DataAccessTools dataAccessTools;

    @Autowired
    private DatasetTools datasetTools;

    /**
     * 执行数据检索操作
     * 
     * @param plan        查询计划，包含SQL语句和目标数据源信息
     * @param userContext 用户上下文，用于权限检查和日志记录
     * @return QueryResult 查询结果对象
     * @throws ChatBiException 当查询失败时抛出业务异常
     */
    @Override
    public QueryResult retrieve(QueryPlan plan, UserContextDTO userContext) {
        log.info("========== DataRetrievalAgent.retrieve 开始执行数据检索 ==========");
        log.info("📥 接收到的查询计划 (QueryPlan) 详情:");
        log.info("   - 目标数据集ID: {}", plan != null ? plan.getTargetDatasetId() : "NULL");
        log.info("   - 目标数据库源ID: {}", plan != null ? plan.getTargetDatabaseSourceId() : "NULL");
        log.info("   - 数据库类型: {}", plan != null ? plan.getDatabaseType() : "NULL");
        log.info("   - SQL查询: {}", plan != null ? plan.getSql() : "NULL");
        log.info("   - 计划是否有效: {}", plan != null ? plan.isValid() : "NULL");
        log.info("   - 思考过程长度: {} 字符",
                plan != null && plan.getThoughtProcess() != null ? plan.getThoughtProcess().length() : 0);

        log.info("👤 用户上下文 (UserContext) 详情:");
        log.info("   - 用户ID: {}", userContext != null ? userContext.getUserId() : "NULL");
        log.info("   - 用户角色: {}", userContext != null ? userContext.getUserRole() : "NULL");
        log.info("   - 会话ID: {}", userContext != null ? userContext.getSessionId() : "NULL");

        // 🔍 输入验证
        if (plan == null || !plan.isValid() || plan.getSql() == null) {
            log.error("❌ 接收到无效的QueryPlan:");
            log.error("   - plan == null: {}", plan == null);
            if (plan != null) {
                log.error("   - plan.isValid(): {}", plan.isValid());
                log.error("   - plan.getSql() == null: {}", plan.getSql() == null);
                log.error("   - plan详情: {}", plan);
            }
            throw new ChatBiException(ErrorCode.INVALID_INPUT, "接收到无效的查询计划，无法执行数据检索。");
        }

        long startTime = System.currentTimeMillis();

        try {
            // 🔗 获取数据源ID
            String dataSourceId = plan.getTargetDatabaseSourceId();
            if (dataSourceId == null) {
                log.error("❌ QueryPlan中缺少数据源ID信息:");
                log.error("   - 目标数据集ID: {}", plan.getTargetDatasetId());
                log.error("   - 这通常表示QueryPlannerAgent未正确填充数据源信息");
                throw new ChatBiException(ErrorCode.INVALID_INPUT, "查询计划缺少必要的数据源信息。");
            }

            log.info("🔗 准备执行数据库查询:");
            log.info("   - 数据源ID: {}", dataSourceId);
            log.info("   - SQL长度: {} 字符", plan.getSql().length());
            log.info("   - SQL内容: {}", plan.getSql().replace("\n", " ").replaceAll("\\s+", " "));

            // 📞 调用DataAccessTools执行查询
            log.info("📞 调用DataAccessTools.executeQuery...");
            QueryResult result = dataAccessTools.executeQuery(dataSourceId, plan.getSql());

            long queryTime = System.currentTimeMillis() - startTime;

            // ✅ 查询成功，记录结果统计信息
            log.info("✅ 数据检索成功完成:");
            log.info("   - 查询耗时: {}ms", queryTime);
            log.info("   - 返回列数: {}", result.getColumnHeaders() != null ? result.getColumnHeaders().size() : 0);
            log.info("   - 返回行数: {}", result.getRows() != null ? result.getRows().size() : 0);
            log.info("   - 总行数统计: {}", result.getTotalRows());

            if (result.getColumnHeaders() != null && !result.getColumnHeaders().isEmpty()) {
                log.info("   - 列名列表: {}", result.getColumnHeaders());
            }

            // 🔍 记录前几行数据作为样本（用于调试）
            if (result.getRows() != null && !result.getRows().isEmpty()) {
                int sampleSize = Math.min(3, result.getRows().size()); // 最多显示前3行
                log.debug("📋 查询结果样本数据 (前{}行):", sampleSize);
                for (int i = 0; i < sampleSize; i++) {
                    log.debug("   - 第{}行: {}", i + 1, result.getRows().get(i));
                }
                if (result.getRows().size() > sampleSize) {
                    log.debug("   - ... 还有{}行数据未显示", result.getRows().size() - sampleSize);
                }
            }

            // 📋 将执行过的SQL回填到结果中，便于追踪
            result.setQuerySql(plan.getSql());

            // 🚀 传递列的元数据
            result.setColumnMetadata(plan.getColumnMetadata());
            log.info("✅ 已将列元数据传递到QueryResult中。");

            log.info("========== DataRetrievalAgent.retrieve 执行完成 ==========");
            return result;

        } catch (ChatBiException e) {
            // 📊 已知的业务异常，添加上下文信息后重新抛出
            long failedTime = System.currentTimeMillis() - startTime;
            log.error("❌ 数据检索过程中发生已知业务异常:");
            log.error("   - 异常发生时间: {}ms 后", failedTime);
            log.error("   - 异常类型: {}", e.getClass().getSimpleName());
            log.error("   - 错误代码: {}", e.getErrorCode());
            log.error("   - 错误消息: {}", e.getMessage());
            log.error("   - 目标数据集ID: {}", plan.getTargetDatasetId());
            log.error("   - 用户ID: {}", userContext != null ? userContext.getUserId() : "未知");
            throw e;
        } catch (Exception e) {
            // 📊 未知异常，包装为业务异常
            long failedTime = System.currentTimeMillis() - startTime;
            log.error("❌ 数据检索过程中发生未知系统异常:");
            log.error("   - 异常发生时间: {}ms 后", failedTime);
            log.error("   - 异常类型: {}", e.getClass().getSimpleName());
            log.error("   - 异常消息: {}", e.getMessage());
            log.error("   - 目标数据集ID: {}", plan.getTargetDatasetId());
            log.error("   - 目标数据源ID: {}", plan.getTargetDatabaseSourceId());
            log.error("   - 执行的SQL: {}", plan.getSql());
            log.error("   - 用户ID: {}", userContext != null ? userContext.getUserId() : "未知");
            log.error("   - 会话ID: {}", userContext != null ? userContext.getSessionId() : "未知");
            log.error("   - 完整异常堆栈:", e);

            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR,
                    "在数据检索环节发生未知系统错误。", e);
        }
    }
}
