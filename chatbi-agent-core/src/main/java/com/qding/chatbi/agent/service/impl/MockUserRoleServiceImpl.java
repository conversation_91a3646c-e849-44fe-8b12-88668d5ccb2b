package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.service.UserRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * UserRoleService的模拟实现，用于开发和测试环境。
 * 使用 @Profile("!prod") 注解，表示这个Bean只在非生产环境(non-prod)下被激活。
 */
@Service
@Profile("!prod")
public class MockUserRoleServiceImpl implements UserRoleService {

    private static final Logger log = LoggerFactory.getLogger(MockUserRoleServiceImpl.class);

    // 硬编码的用户角色映射
    // Key: userId, Value: List of role names
    private static final Map<String, List<String>> USER_ROLES_MAP = Map.of(
        "admin-user",   List.of("Admin"),           // 管理员用户
        "dev-user",     List.of("Developer"),        // 开发者用户
        "employee-user",List.of("Employee"),         // 普通员工用户
        "test-user-001",List.of("Admin", "Developer") // 一个拥有多个角色的测试用户
    );

    @Override
    public List<String> getRolesForUser(String userId) {
        log.info("========== MockUserRoleServiceImpl.getRolesForUser 开始 ==========");
        log.info("正在为用户 '{}' 获取角色信息...", userId);
        
        if (userId == null || userId.trim().isEmpty()) {
            log.warn("用户ID为空或空白，返回空角色列表");
            log.info("========== MockUserRoleServiceImpl.getRolesForUser 完成 ==========");
            return Collections.emptyList();
        }
        
        log.info("在预设的用户角色映射中查找用户: {}", userId);
        log.info("可用的用户映射: {}", USER_ROLES_MAP.keySet());
        
        // 根据userId返回预设的角色列表，如果找不到则默认返回 "Employee" 角色
        List<String> userRoles = USER_ROLES_MAP.getOrDefault(userId, Collections.singletonList("Employee"));
        
        if (USER_ROLES_MAP.containsKey(userId)) {
            log.info("在预设映射中找到用户 '{}' 的角色: {}", userId, userRoles);
        } else {
            log.info("用户 '{}' 不在预设映射中，使用默认角色: {}", userId, userRoles);
        }
        
        log.info("========== MockUserRoleServiceImpl.getRolesForUser 完成 ==========");
        return userRoles;
    }
} 