package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.StructuredQueryIntent;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

/**
 * NLU Tool Agent 接口
 * 用于 AiServices 的 Function Calling 架构
 */
public interface NluToolAgent {

    /**
     * 处理用户查询，返回结构化查询意图。
     * 核心逻辑由 System Message Provider 提供的 nlu_system_prompt_template.txt 驱动。
     * 下方的 @UserMessage 主要用于满足 LangChain4j 框架对用户消息注解的强制要求。
     */
    @UserMessage("用户：{{userQuery}}")
    StructuredQueryIntent process(
            @V("userQuery") String userQuery,
            @V("lastStructuredIntent") String lastStructuredIntent,
            @V("recentRawConversation") String recentRawConversation,
            @V("userRole") String userRole,
            @V("currentTime") String currentTime,
            @V("availableDatasetsSchema") String availableDatasetsSchema);
}