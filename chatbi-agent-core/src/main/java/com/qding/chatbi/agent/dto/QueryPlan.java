package com.qding.chatbi.agent.dto;

import java.util.ArrayList;
import java.util.HashMap; // Added
import java.util.List;
import java.util.Map; // Added
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

import com.qding.chatbi.common.dto.ColumnMetadata;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class QueryPlan {

    private String sql;
    private Long targetDatasetId;
    private String targetDatabaseSourceId;
    private String databaseType;
    private List<ColumnMetadata> columnMetadata;
    private boolean isValid;
    private String errorMessage;
    private String thoughtProcess;
    private List<String> warnings = new ArrayList<>();
    private Map<String, String> columnAliases = new HashMap<>(); // Added
    private String originalQuery;
    private List<String> sqls;
    private String reason;
    private boolean executable;
}
