package com.qding.chatbi.agent.tool;

import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.dataaccess.service.DataAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * DataAccessTools - Agent核心模块中用于访问数据服务的门面
 * 它的职责是封装对下游`data-access-service`模块的调用细节。
 */
@Component
@Slf4j
public class DataAccessTools {

    @Autowired
    private DataAccessService dataAccessService;

    /**
     * 执行查询的工具方法。
     *
     * @param dataSourceId 数据源ID
     * @param sql          要执行的SQL
     * @return 查询结果
     */
    public QueryResult executeQuery(String dataSourceId, String sql) {
        Objects.requireNonNull(dataSourceId, "dataSourceId must not be null");
        Objects.requireNonNull(sql, "SQL query must not be null");

        log.info("🔍 执行查询: 数据源={}", dataSourceId);
        log.debug("🔍 详细SQL内容:");
        log.debug("   - 完整SQL:\n{}", sql);
        log.debug("   - SQL（单行）: {}", sql.replace("\n", " ").replaceAll("\\s+", " "));

        long startTime = System.currentTimeMillis();

        try {
            QueryResult result = dataAccessService.executeQuery(dataSourceId, sql);

            if (result.getErrorMessage() != null) {
                log.warn("查询执行失败: {}", result.getErrorMessage());
            }

            return result;
        } catch (Exception e) {
            log.error("❌ 查询执行异常: 数据源={}, 错误={}", dataSourceId, e.getMessage(), e);
            throw new RuntimeException("Error executing SQL query: " + e.getMessage(), e);
        }
    }
}
