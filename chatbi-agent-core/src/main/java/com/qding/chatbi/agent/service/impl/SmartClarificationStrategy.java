package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.agent.service.ClarificationStrategy;
import com.qding.chatbi.agent.tool.DatasetTools;
import com.qding.chatbi.common.dto.ClarificationOption;
import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.UserContextDTO;
import com.qding.chatbi.common.enums.SemanticType;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.memory.ChatMemory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.List;

/**
 * 智能澄清策略实现
 *
 * 核心职责变更：
 * 不再直接生成澄清选项，而是分析缺失的查询组件（指标、维度、时间等），
 * 为NLU Prompt提供"澄清引导"，由LLM负责生成最终的澄清内容。
 *
 */
@Slf4j
@Service
public class SmartClarificationStrategy implements ClarificationStrategy {

    private final DatasetTools datasetTools;

    public SmartClarificationStrategy(DatasetTools datasetTools) {
        this.datasetTools = datasetTools;
        log.info("SmartClarificationStrategy 初始化完成");
    }

    /**
     * 生成关于如何进行澄清的引导性文本。
     * 这段文本将注入到NLU prompt中，以指导LLM生成更智能的澄清选项。
     *
     * @param partialIntent 部分解析的用户意图
     * @param userContext   用户上下文
     * @return 用于指导LLM的文本，或在不需要指导时返回空Optional
     */
    @Override
    public Optional<String> generateClarificationGuidance(
            StructuredQueryIntent partialIntent,
            UserContextDTO userContext) {

        log.info("========== 开始生成澄清引导 ==========");
        log.info("部分意图: {}", partialIntent.getIntent());

        // 1. 获取可访问的数据集
        String userRole = userContext != null ? userContext.getUserRole() : null;
        List<DatasetInfoDTO> accessibleDatasets = datasetTools.getCandidateDatasets(userRole);
        if (accessibleDatasets == null || accessibleDatasets.isEmpty()) {
            log.warn("用户 {} 没有任何可访问的数据集，无法生成澄清选项。", userContext.getUserId());
            return Optional.empty();
        }

        // 2. 分析缺失的查询组件
        Set<String> missingComponents = analyzeMissingComponents(partialIntent, accessibleDatasets);
        if (missingComponents.isEmpty()) {
            log.info("查询意图完整，无需澄清引导。");
            return Optional.empty();
        }

        // 3. 生成澄清引导文本
        String guidance = generateGuidanceText(missingComponents, accessibleDatasets, userContext);

        log.info("✅ 生成澄清引导完成");
        return Optional.of(guidance);
    }

    /**
     * 【已废弃】此方法不再生成选项，逻辑已转移到LLM。
     * 为保持接口兼容性，返回空列表。
     */
    @Override
    @Deprecated
    public List<ClarificationOption> generateClarificationOptions(
            StructuredQueryIntent partialIntent,
            ChatMemory chatMemory,
            UserContextDTO userContext) {
        log.warn("generateClarificationOptions 已废弃，不应再被调用。请更新调用逻辑至LLM生成澄清。");
        // 返回空列表，因为澄清选项的生成已移交给LLM
        return Collections.emptyList();
    }

    @Override
    public StructuredQueryIntent processClarificationResponse(
            String selectedOptionId,
            Map<String, Object> payload,
            StructuredQueryIntent currentIntent,
            ChatMemory chatMemory,
            UserContextDTO userContext) {

        log.info("========== 处理澄清响应 ==========");
        log.info("选择的选项ID: {}", selectedOptionId);
        log.info("载荷数据: {}", payload);

        // 根据选项ID和载荷更新意图
        StructuredQueryIntent updatedIntent = new StructuredQueryIntent();
        updatedIntent.setIntent("DATA_QUERY");
        updatedIntent.setInternal_context(currentIntent.getInternal_context());

        // 复制现有实体
        Map<String, Object> entities = new HashMap<>();
        if (currentIntent.getEntities() != null) {
            entities.putAll(currentIntent.getEntities());
        }

        // 根据澄清选项更新实体
        if (payload != null) {
            if (payload.containsKey("metrics")) {
                entities.put("metrics", payload.get("metrics"));
            }
            if (payload.containsKey("time_range")) {
                entities.put("time_range_text", payload.get("time_range"));
                entities.put("filters", generateTimeFilters((String) payload.get("time_range")));
            }
            if (payload.containsKey("dimensions")) {
                entities.put("dimensions_to_group", payload.get("dimensions"));
            }
            if (payload.containsKey("sort_by")) {
                entities.put("sort_by", payload.get("sort_by"));
            }
        }

        updatedIntent.setEntities(entities);

        // 检查是否还需要进一步澄清
        boolean needsMore = needsFurtherClarification(updatedIntent, userContext);
        if (needsMore) {
            updatedIntent.setIntent("CLARIFICATION_NEEDED");
            // 生成下一轮澄清
            List<ClarificationOption> nextOptions = generateClarificationOptions(updatedIntent, chatMemory,
                    userContext);
            updatedIntent.setClarificationOptions(nextOptions);

            Set<String> stillMissing = analyzeMissingDimensions(updatedIntent, userContext);
            updatedIntent.setResponse(generateClarificationPrompt(new ArrayList<>(stillMissing), userContext));
        } else {
            updatedIntent.setIntent("DATA_QUERY");
        }

        if (currentIntent.getInternal_context() != null) {
            updatedIntent.setInternal_context(currentIntent.getInternal_context());
        }

        log.info("澄清处理完成，仍需澄清: {}", needsMore);
        return updatedIntent;
    }

    @Override
    public boolean needsFurtherClarification(StructuredQueryIntent intent, UserContextDTO userContext) {
        // 检查意图是否需要进一步澄清
        if (intent == null || intent.getEntities() == null) {
            return true;
        }

        // 检查是否缺少关键组件
        boolean hasMetrics = intent.getEntities().containsKey("metrics") &&
                !((List<?>) intent.getEntities().getOrDefault("metrics", Collections.emptyList())).isEmpty();

        return !hasMetrics; // 如果没有指标，则需要澄清
    }

    @Override
    public String generateClarificationPrompt(List<String> missingDimensions, UserContextDTO userContext) {
        if (missingDimensions.isEmpty()) {
            return "请提供更多信息以帮助我理解您的查询。";
        }

        if (missingDimensions.contains("metrics")) {
            return "您想查询哪个具体的指标？比如营业收入、销售额还是利润？";
        }

        if (missingDimensions.contains("time_range")) {
            return "您想查询哪个时间范围的数据？";
        }

        return "请选择您想要的查询方式：";
    }

    /**
     * 分析缺失的维度
     */
    private Set<String> analyzeMissingDimensions(StructuredQueryIntent intent, UserContextDTO userContext) {
        Set<String> missing = new HashSet<>();
        Map<String, Object> entities = intent.getEntities();

        // 检查指标
        if (entities == null || !entities.containsKey("metrics") ||
                ((List<?>) entities.getOrDefault("metrics", Collections.emptyList())).isEmpty()) {
            missing.add("metrics");
        }

        // 检查时间范围
        if (entities == null || !entities.containsKey("filters")) {
            boolean hasTimeFilter = false;
            if (entities != null && entities.containsKey("filters")) {
                List<Map<String, Object>> filters = (List<Map<String, Object>>) entities.get("filters");
                for (Map<String, Object> filter : filters) {
                    // This is a simplistic check. A better check would be to look up the column's
                    // semantic type.
                    if (filter.get("fieldName").toString().contains("日期")
                            || filter.get("fieldName").toString().contains("时间")) {
                        hasTimeFilter = true;
                        break;
                    }
                }
            }
            if (!hasTimeFilter) {
                missing.add("time_range");
            }
        }

        return missing;
    }

    /**
     * 从对话历史中提取用户偏好
     */
    private Map<String, String> extractUserPreferences(ChatMemory chatMemory) {
        Map<String, String> preferences = new HashMap<>();

        if (chatMemory == null)
            return preferences;

        List<ChatMessage> messages = chatMemory.messages();

        // 分析最近的查询，提取常用的时间范围、维度等
        for (ChatMessage message : messages) {
            String content = message.text().toLowerCase();

            // 提取时间偏好
            if (content.contains("本月") || content.contains("这个月")) {
                preferences.put("preferred_time", "本月");
            } else if (content.contains("上月") || content.contains("上个月")) {
                preferences.put("preferred_time", "上月");
            } else if (content.contains("本季度")) {
                preferences.put("preferred_time", "本季度");
            } else if (content.contains("今年")) {
                preferences.put("preferred_time", "今年");
            }
        }

        return preferences;
    }

    /**
     * 创建澄清选项
     */
    private ClarificationOption createClarificationOption(String optionId, String optionText,
            Map<String, Object> payload) {
        ClarificationOption option = new ClarificationOption();
        option.setOptionId(optionId);
        option.setOptionText(optionText);
        option.setPayload(payload);
        return option;
    }

    /**
     * 根据时间范围文本生成过滤条件
     */
    private List<Map<String, Object>> generateTimeFilters(String timeRangeText) {
        List<Map<String, Object>> filters = new ArrayList<>();
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Map<String, Object> filter = new HashMap<>();
        filter.put("fieldName", "日期");
        filter.put("operator", "BETWEEN");

        switch (timeRangeText) {
            case "本月":
                filter.put("value", now.withDayOfMonth(1).format(formatter));
                filter.put("value2_for_between", now.format(formatter));
                break;
            case "上月":
                LocalDate lastMonth = now.minusMonths(1);
                filter.put("value", lastMonth.withDayOfMonth(1).format(formatter));
                filter.put("value2_for_between", lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()).format(formatter));
                break;
            case "本季度":
                int currentQuarter = (now.getMonthValue() - 1) / 3;
                LocalDate quarterStart = now.withMonth(currentQuarter * 3 + 1).withDayOfMonth(1);
                filter.put("value", quarterStart.format(formatter));
                filter.put("value2_for_between", now.format(formatter));
                break;
            case "今年":
                filter.put("value", now.withDayOfYear(1).format(formatter));
                filter.put("value2_for_between", now.format(formatter));
                break;
            case "最近7天":
                filter.put("value", now.minusDays(6).format(formatter));
                filter.put("value2_for_between", now.format(formatter));
                break;
            case "最近30天":
                filter.put("value", now.minusDays(29).format(formatter));
                filter.put("value2_for_between", now.format(formatter));
                break;
            default:
                // 默认为本月
                filter.put("value", now.withDayOfMonth(1).format(formatter));
                filter.put("value2_for_between", now.format(formatter));
        }

        filters.add(filter);
        return filters;
    }

    private DatasetInfoDTO determineTargetDataset(StructuredQueryIntent partialIntent,
            List<DatasetInfoDTO> accessibleDatasets) {
        if (partialIntent.getTargetDatasetHint() != null) {
            for (DatasetInfoDTO ds : accessibleDatasets) {
                if (ds.getDatasetName().equalsIgnoreCase(partialIntent.getTargetDatasetHint())) {
                    return ds;
                }
            }
        }
        return accessibleDatasets.get(0); // Fallback to the first one
    }

    private List<ColumnInfoDTO> getColumnsByType(DatasetInfoDTO dataset, SemanticType type) {
        if (dataset == null || dataset.getColumns() == null) {
            return Collections.emptyList();
        }
        return dataset.getColumns().stream()
                .filter(c -> c.getSemanticType() == type)
                .collect(Collectors.toList());
    }

    private List<ClarificationOption> generateDynamicOptions(
            StructuredQueryIntent partialIntent,
            Set<String> missingDimensions,
            List<ColumnInfoDTO> availableMetrics,
            List<ColumnInfoDTO> availableDimensions) {

        List<ClarificationOption> options = new ArrayList<>();
        Map<String, Object> entities = partialIntent.getEntities() != null ? partialIntent.getEntities()
                : new HashMap<>();
        List<String> currentMetrics = (List<String>) entities.getOrDefault("metrics", Collections.emptyList());

        // 优先级1：如果缺少指标，则生成澄清指标的选项
        if (missingDimensions.contains("metrics")) {
            log.info("场景：指标缺失。生成指标澄清选项。");
            for (ColumnInfoDTO metric : availableMetrics) {
                // 推荐组合：指标 + 默认时间
                options.add(createClarificationOption(
                        "metric_time_" + metric.getColumnId(),
                        "查询本月" + metric.getColumnName(),
                        Map.of("metrics", List.of(metric.getColumnName()), "time_range", "本月")));
                // 推荐组合：指标 + 首个维度
                if (!availableDimensions.isEmpty()) {
                    ColumnInfoDTO firstDim = availableDimensions.get(0);
                    options.add(createClarificationOption(
                            "metric_dim_" + metric.getColumnId(),
                            "按" + firstDim.getColumnName() + "查看" + metric.getColumnName(),
                            Map.of("metrics", List.of(metric.getColumnName()), "dimensions",
                                    List.of(firstDim.getColumnName()))));
                }
            }
        }
        // 优先级2：指标已存在，但缺少维度或时间
        else if (!currentMetrics.isEmpty()
                && (missingDimensions.contains("dimensions_to_group") || missingDimensions.contains("time_range"))) {
            log.info("场景：指标存在，但缺少维度或时间。");
            String metricName = currentMetrics.get(0);

            // 生成按维度分析的选项
            for (ColumnInfoDTO dim : availableDimensions) {
                options.add(createClarificationOption(
                        "dim_" + dim.getColumnId(),
                        "按" + dim.getColumnName() + "分析" + metricName,
                        Map.of("metrics", List.of(metricName), "dimensions", List.of(dim.getColumnName()))));
            }
            // 生成按时间分析的选项
            options.add(createClarificationOption("time_month_total", "查看本月" + metricName + "总和",
                    Map.of("metrics", List.of(metricName), "time_range", "本月")));
            options.add(createClarificationOption("time_quarter_total", "查看本季度" + metricName + "总和",
                    Map.of("metrics", List.of(metricName), "time_range", "本季度")));

        }

        // 去重并返回
        return new ArrayList<>(new LinkedHashSet<>(options));
    }

    /**
     * 添加唯一选项，避免重复的optionId
     */
    private void addUniqueOptions(List<ClarificationOption> targetList,
            List<ClarificationOption> newOptions,
            Set<String> addedOptionIds) {
        for (ClarificationOption option : newOptions) {
            if (!addedOptionIds.contains(option.getOptionId())) {
                targetList.add(option);
                addedOptionIds.add(option.getOptionId());
            }
        }
    }

    private boolean isMissing(Object entity) {
        if (entity == null)
            return true;
        if (entity instanceof Collection) {
            return ((Collection<?>) entity).isEmpty();
        }
        return false;
    }

    // 辅助方法
    private Set<String> analyzeMissingComponents(StructuredQueryIntent partialIntent,
            List<DatasetInfoDTO> accessibleDatasets) {
        Set<String> missingComponents = new HashSet<>();

        // 添加空值检查，避免空指针异常
        Map<String, Object> entities = partialIntent.getEntities();
        if (entities == null) {
            // 如果 entities 为 null，说明所有组件都缺失
            missingComponents.add("指标(metrics)");
            missingComponents.add("维度(dimensions)");
            missingComponents.add("时间范围(time_range)");
            return missingComponents;
        }

        if (isMissing(entities.get("metrics"))) {
            missingComponents.add("指标(metrics)");
        }
        if (isMissing(entities.get("dimensions_to_group")) && isMissing(entities.get("filters"))) {
            missingComponents.add("维度(dimensions)");
        }
        if (entities.get("time_range_text") == null) {
            missingComponents.add("时间范围(time_range)");
        }

        return missingComponents;
    }

    private String generateGuidanceText(Set<String> missingComponents, List<DatasetInfoDTO> accessibleDatasets,
            UserContextDTO userContext) {
        DatasetInfoDTO targetDataset = accessibleDatasets.get(0);

        StringBuilder guidance = new StringBuilder();
        guidance.append(
                "The user's query is incomplete. Please generate clarification options to help them complete it. ");
        guidance.append("Key missing information includes: ").append(String.join(", ", missingComponents)).append(". ");

        // 加强字段约束说明
        guidance.append("CRITICAL CONSTRAINTS: ");
        guidance.append("1) Use ONLY the exact field names from the '").append(targetDataset.getDatasetName())
                .append("' dataset schema provided below. ");
        guidance.append(
                "2) DO NOT suggest any fields, metrics, or calculations that are not explicitly listed in the dataset. ");
        guidance.append(
                "3) DO NOT suggest common business metrics like 'profit', 'profit margin', 'cost' unless these exact field names exist in the dataset. ");
        guidance.append(
                "4) Before suggesting any clarification option, verify that all referenced field names are present in the dataset schema. ");

        // 列出具体可用字段
        if (targetDataset.getColumns() != null && !targetDataset.getColumns().isEmpty()) {
            guidance.append("Available fields in this dataset: ");
            List<String> fieldNames = targetDataset.getColumns().stream()
                    .map(col -> col.getColumnName())
                    .collect(Collectors.toList());
            guidance.append(String.join(", ", fieldNames)).append(". ");
        }

        return guidance.toString();
    }

    private List<ClarificationOption> generateDimensionOptions(StructuredQueryIntent intent,
            List<String> availableDimensions) {
        List<ClarificationOption> options = new ArrayList<>();
        String originalQuery = (intent.getInternal_context() != null) ? intent.getInternal_context().getOriginalQuery()
                : "";

        for (String dim : availableDimensions) {
            options.add(new ClarificationOption("按" + dim + "分析", "按" + dim + "分析" + originalQuery, null));
        }
        return options;
    }
}