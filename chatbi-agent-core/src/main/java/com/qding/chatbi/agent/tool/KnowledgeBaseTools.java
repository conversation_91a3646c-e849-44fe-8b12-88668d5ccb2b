package com.qding.chatbi.agent.tool;

import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import com.qding.chatbi.knowledge.dto.SearchResultDTO;
import com.qding.chatbi.metadata.entity.QueryExample;
import com.qding.chatbi.metadata.entity.BusinessTerminology;
import com.qding.chatbi.metadata.entity.DatasetColumn;
import com.qding.chatbi.metadata.repository.DatasetColumnRepository;
import com.qding.chatbi.metadata.service.DatasetMetadataService;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
// import com.qding.chatbi.agent.dto.IntentValidationResult; // 已在新设计中移除
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

/**
 * 知识库工具类 - 提供 RAG 功能
 * 通过 Function Calling 方式由 AI 自主调用
 */
@Component
public class KnowledgeBaseTools {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseTools.class);

    private final KnowledgePersistenceService knowledgeService;
    private final DatasetMetadataService datasetMetadataService;
    private final DatasetColumnRepository datasetColumnRepository;

    @Autowired
    public KnowledgeBaseTools(KnowledgePersistenceService knowledgeService,
            DatasetMetadataService datasetMetadataService,
            DatasetColumnRepository datasetColumnRepository) {
        this.knowledgeService = knowledgeService;
        this.datasetMetadataService = datasetMetadataService;
        this.datasetColumnRepository = datasetColumnRepository;
        log.info("KnowledgeBaseTools 初始化完成，已连接 Knowledge Service, DatasetMetadataService, and DatasetColumnRepository");
    }

    @Tool("根据用户查询搜索相似的查询示例。这可以帮助理解用户意图并提供查询建议。mergedContext是AI结合上下文初步理解的当前最准确的用户意图,userRole是用户的角色，用于过滤用户可访问的数据集，topK是返回的示例数量")
    public String searchSimilarQueries(@P("mergedContext") String userQuery, @P("userRole") String userRole,
            @P("topK") int topK) {
        long startTime = System.currentTimeMillis();
        log.info("🔍 [V2] 开始查询示例搜索: '{}', 用户角色: '{}'", userQuery, userRole);

        if (!StringUtils.hasText(userQuery)) {
            String errorMessage = "错误：searchSimilarQueries 收到无效参数 - userQuery 为空或null";
            log.error("   ❌ {}", errorMessage);
            return errorMessage;
        }

        try {
            // 1. 向量库语义搜索，获取候选项
            List<QueryExample> initialResults = knowledgeService.searchSimilarExamples(userQuery, 5);
            log.info("   📊 向量搜索返回 {} 条结果", initialResults.size());

            if (CollectionUtils.isEmpty(initialResults)) {
                log.info("   ❌ 向量库中未找到与 '{}' 相关的查询示例", userQuery);
                String response = String.format("未找到与 '%s' 相似的查询示例。", userQuery);
                log.info("   📤 [V2] 返回: {} (耗时: {}ms)", response, System.currentTimeMillis() - startTime);
                return response;
            }

            // 2. 获取用户角色可访问的数据集ID
            List<DatasetInfoDTO> accessibleDatasets = datasetMetadataService.getDatasetInfoByUserRole(userRole);
            Set<Long> accessibleDatasetIds = accessibleDatasets.stream().map(DatasetInfoDTO::getDatasetId).collect(Collectors.toSet());
            log.debug("   🔑 角色 '{}' 可访问的数据集IDs: {}", userRole, accessibleDatasetIds);

            // 3. 按权限过滤查询示例
            List<QueryExample> finalResults = initialResults.stream().filter(example -> {
                        // 检查示例的目标数据集是否在用户可访问范围内
                        if (example.getTargetDatasetId() == null) {
                            log.debug("   ⚠️ 跳过无目标数据集的示例: {}", example.getUserQuestion());
                            return false;
                        }
                        boolean hasAccess = accessibleDatasetIds.contains(example.getTargetDatasetId());
                        if (!hasAccess) {
                            log.debug("   🚫 用户角色 '{}' 无权限访问数据集 {}: {}", userRole, example.getTargetDatasetId(), example.getUserQuestion());
                        }
                        return hasAccess;
                    })
                    .limit(topK).collect(Collectors.toList());

            log.info("   ✅ 权限过滤后剩余 {} 条结果", finalResults.size());

            if (finalResults.isEmpty()) {
                String response = String.format("未找到与 '%s' 相关的查询示例，或您没有访问相关数据集的权限。", userQuery);
                log.info("   📤 [V2] 返回: {} (耗时: {}ms)", response, System.currentTimeMillis() - startTime);
                return response;
            }

            // 4. 格式化输出结果
            String formattedResults = finalResults.stream()
                    .map(example -> String.format("示例查询: %s\n对应SQL: %s\n目标数据集ID: %s",
                            example.getUserQuestion(), example.getTargetQueryRepresentation(),
                            example.getTargetDatasetId()))
                    .collect(Collectors.joining("\n---\n"));

            log.info("   📤 [V2] 返回 {} 条查询示例 (耗时: {}ms)", finalResults.size(), System.currentTimeMillis() - startTime);
            return formattedResults;

        } catch (Exception e) {
            log.error("🔧 [Function Call] searchSimilarQueries 异常 (耗时: {}ms): {}", System.currentTimeMillis() - startTime, e.getMessage(), e);
            String errorResponse = "搜索相似查询示例时发生错误: " + e.getMessage();
            log.info("   📤 返回错误响应: {}", errorResponse);
            return errorResponse;
        }
    }

    /*
     * 
     * 【新增方法】智能查询搜索 - 支持 mergedContext 和原始查询的智能选择
     * 这是对原有searchSimilarQueries方法的增强版本
     * 
     * @Tool("智能搜索相似查询示例，支持上下文感知。如果提供了mergedContext，将优先使用；否则使用原始查询")
     * public String searchSimilarQueriesEnhanced(@P("originalQuery") String
     * originalQuery,
     * 
     * @P("mergedContext") String mergedContext,
     * 
     * @P("topK") int topK) {
     * long startTime = System.currentTimeMillis();
     * 
     * // 【核心优化】智能选择查询文本
     * String queryText = selectOptimalQueryText(originalQuery, mergedContext);
     * 
     * log.info("🔍 [Enhanced] 智能搜索相似查询");
     * log.info("   📝 原始查询: '{}'", originalQuery);
     * log.info("   🧠 合并上下文: '{}'", mergedContext);
     * log.info("   ✅ 选择的查询文本: '{}' (topK={})", queryText, topK);
     * 
     * try {
     * log.info("   🔍 调用向量搜索服务: '{}'", queryText);
     * List<QueryExample> results =
     * knowledgeService.searchSimilarExamples(queryText, topK);
     * log.info("   📊 向量搜索返回 {} 条结果", results.size());
     * 
     * if (CollectionUtils.isEmpty(results)) {
     * log.info("   ❌ 未找到与查询 '{}' 相似的示例", queryText);
     * String response = "未找到相似的查询示例。";
     * log.info("🔧 [Function Call] searchSimilarQueriesEnhanced 完成 (耗时: {}ms)",
     * System.currentTimeMillis() - startTime);
     * log.info("   📤 返回: {}", response);
     * return response;
     * }
     * 
     * log.info("   ✅ 找到 {} 条相似示例", results.size());
     * 
     * String formattedResults = results.stream()
     * .map(example -> String.format("示例查询: %s\n对应SQL: %s\n目标数据集ID: %s\n查询类型: %s",
     * example.getUserQuestion(),
     * example.getTargetQueryRepresentation(),
     * example.getTargetDatasetId(),
     * example.getDifficultyLevel() != null ? example.getDifficultyLevel() : "未分类"))
     * .collect(Collectors.joining("\n---\n"));
     * 
     * log.info("🔧 [Function Call] searchSimilarQueriesEnhanced 完成 (耗时: {}ms)",
     * System.currentTimeMillis() - startTime);
     * return formattedResults;
     * 
     * } catch (Exception e) {
     * log.error("🔧 [Function Call] searchSimilarQueriesEnhanced 异常 (耗时: {}ms): {}"
     * ,
     * System.currentTimeMillis() - startTime, e.getMessage(), e);
     * String errorResponse = "搜索相似查询示例时发生错误: " + e.getMessage();
     * log.info("   📤 返回错误响应: {}", errorResponse);
     * return errorResponse;
     * }
     * }
     * 
     * 
     * private String selectOptimalQueryText(String originalQuery, String
     * mergedContext) {
     * // 1. 如果 mergedContext 存在且有意义，优先使用
     * if (StringUtils.hasText(mergedContext) && mergedContext.trim().length() > 3)
     * {
     * // 检查 mergedContext 是否比原始查询更丰富
     * if (mergedContext.length() > originalQuery.length() * 1.2) {
     * log.debug("   🧠 选择mergedContext：内容更丰富 (原始: {}字符, 合并: {}字符)",
     * originalQuery.length(), mergedContext.length());
     * return mergedContext.trim();
     * }
     * 
     * // 检查 mergedContext 是否包含更多的结构化信息
     * if (containsStructuredInfo(mergedContext) &&
     * !containsStructuredInfo(originalQuery)) {
     * log.debug("   🧠 选择mergedContext：包含结构化信息");
     * return mergedContext.trim();
     * }
     * }
     * 
     * // 2. 如果 mergedContext 不理想，但仍然存在，进行混合处理
     * if (StringUtils.hasText(mergedContext)) {
     * // 创建混合查询文本
     * String hybridQuery = createHybridQuery(originalQuery, mergedContext);
     * if (!hybridQuery.equals(originalQuery)) {
     * log.debug("   🔄 选择混合查询：结合原始查询和上下文");
     * return hybridQuery;
     * }
     * }
     * 
     * // 3. 默认使用原始查询
     * log.debug("   📝 选择原始查询：上下文不足或不适用");
     * return originalQuery.trim();
     * }
     */

    /**
     * 检查文本是否包含结构化信息（如指标、维度、数据集名称等）
     * 
     * private boolean containsStructuredInfo(String text) {
     * String lowerText = text.toLowerCase();
     * // 检查是否包含数据分析相关的关键词
     * String[] structuredKeywords = {
     * "指标", "维度", "数据集", "metric", "dimension", "dataset",
     * "聚合", "分组", "排序", "过滤", "sum", "count", "avg", "group by", "order by"
     * };
     * 
     * for (String keyword : structuredKeywords) {
     * if (lowerText.contains(keyword)) {
     * return true;
     * }
     * }
     * return false;
     * }
     */

    /**
     * 创建混合查询文本，结合原始查询和有用的上下文信息
     * 
     * private String createHybridQuery(String originalQuery, String mergedContext)
     * {
     * // 如果上下文太长，进行智能截取
     * if (mergedContext.length() > originalQuery.length() * 3) {
     * // 提取关键信息
     * String keyInfo = extractKeyInformation(mergedContext);
     * if (StringUtils.hasText(keyInfo) && !keyInfo.equals(originalQuery)) {
     * return originalQuery + " " + keyInfo;
     * }
     * }
     * 
     * // 如果上下文合理，直接结合
     * if (mergedContext.length() <= originalQuery.length() * 2) {
     * return originalQuery + " " + mergedContext;
     * }
     * 
     * return originalQuery;
     * }
     */

    /**
     * 从较长的上下文中提取关键信息
     * 
     * private String extractKeyInformation(String context) {
     * // 提取包含关键词的句子或短语
     * String[] sentences = context.split("[。！？;\\n]");
     * StringBuilder keyInfo = new StringBuilder();
     * 
     * for (String sentence : sentences) {
     * if (containsStructuredInfo(sentence.trim()) && sentence.trim().length() < 50)
     * {
     * if (keyInfo.length() > 0)
     * keyInfo.append(" ");
     * keyInfo.append(sentence.trim());
     * 
     * // 限制输出长度
     * if (keyInfo.length() > 100)
     * break;
     * }
     * }
     * 
     * return keyInfo.toString();
     * }
     */

    @Tool("搜索业务术语的定义和解释。这可以帮助理解用户查询中的专业术语和业务概念。参数term是要查询的术语JSON数组，必须是非空字符串。userRole是用户的角色，用于过滤用户可访问的数据集，topK是返回的术语数量")
    public String searchBusinessTerms(@P("term") List<String> terms, @P("userRole") String userRole,
            @P("topK") int topK) {
        long startTime = System.currentTimeMillis();
        log.info("🔍 [V2] 开始业务术语搜索: '{}', 用户角色: '{}'", terms, userRole);

        if (CollectionUtils.isEmpty(terms)) {
            String errorMessage = "查询术语的条件不能为空";
            log.error("   ❌ {}", errorMessage);
            return "{\"error\": \"" + errorMessage + "\"}";
        }

        try {
            // 存储所有术语的搜索结果
            Map<String, List<BusinessTerminology>> allTermResults = new HashMap<>();

            // 2. 获取用户角色可访问的数据集ID
            List<DatasetInfoDTO> accessibleDatasets = datasetMetadataService
                    .getDatasetInfoByUserRole(userRole);
            Set<Long> accessibleDatasetIds = accessibleDatasets.stream()
                    .map(DatasetInfoDTO::getDatasetId)
                    .collect(Collectors.toSet());
            log.debug("   🔑 角色 '{}' 可访问的数据集IDs: {}", userRole, accessibleDatasetIds);

            // 3. 为每个术语进行搜索
            for (String singleTerm : terms) {
                log.info("   🔍 搜索术语: '{}'", singleTerm);

                // 3.1 向量库语义搜索，获取候选项
                List<SearchResultDTO> initialResults = knowledgeService.searchSimilarTerms(singleTerm, 5,
                        Collections.emptyList());

                if (CollectionUtils.isEmpty(initialResults)) {
                    log.info("   ❌ 向量库中未找到与 '{}' 相关的业务术语", singleTerm);
                    allTermResults.put(singleTerm, new ArrayList<>());
                    continue;
                }

                // 3.2 按权限过滤业务术语
                List<BusinessTerminology> allFoundTerms = initialResults.stream()
                        .map(SearchResultDTO::getTerm)
                        .collect(Collectors.toList());

                List<BusinessTerminology> finalResults = new ArrayList<>();
                List<BusinessTerminology> termsToCheckPermission = new ArrayList<>();

                // 3.3 分类处理：Custom类型直接通过，其他类型待检
                for (BusinessTerminology bt : allFoundTerms) {
                    if ("Custom".equalsIgnoreCase(bt.getStandardReferenceType())) {
                        finalResults.add(bt);
                    } else if (StringUtils.hasText(bt.getStandardReferenceId())) {
                        termsToCheckPermission.add(bt);
                    }
                }

                // 3.4 批量检查权限
                if (!termsToCheckPermission.isEmpty()) {
                    List<Long> columnIds = termsToCheckPermission.stream()
                            .map(bt -> Long.parseLong(bt.getStandardReferenceId()))
                            .distinct()
                            .collect(Collectors.toList());

                    List<DatasetColumn> columns = datasetColumnRepository.findAllById(columnIds);
                    Map<Long, DatasetColumn> columnMap = columns.stream()
                            .collect(Collectors.toMap(DatasetColumn::getId, Function.identity()));

                    for (BusinessTerminology bt : termsToCheckPermission) {
                        Long columnId = Long.parseLong(bt.getStandardReferenceId());
                        DatasetColumn column = columnMap.get(columnId);
                        if (column != null && column.getDataset() != null
                                && accessibleDatasetIds.contains(column.getDataset().getId())) {
                            finalResults.add(bt); // 有权限，加入最终结果
                        }
                    }
                }

                allTermResults.put(singleTerm, finalResults);
            }

            // 4. 构建JSON格式返回结果
            ObjectMapper resultMapper = new ObjectMapper();
            ObjectNode resultJson = resultMapper.createObjectNode();

            for (Map.Entry<String, List<BusinessTerminology>> entry : allTermResults.entrySet()) {
                String termKey = entry.getKey();
                List<BusinessTerminology> termResults = entry.getValue();

                if (CollectionUtils.isEmpty(termResults)) {
                    resultJson.set(termKey, resultMapper.createArrayNode());
                } else {
                    ArrayNode definitionsArray = resultMapper.createArrayNode();
                    for (BusinessTerminology bt : termResults) {
                        ObjectNode definitionNode = resultMapper.createObjectNode();
                        definitionNode.put("term", bt.getBusinessTerm());
                        definitionNode.put("definition", bt.getContextDescription());
                        definitionNode.put("mappingType", bt.getStandardReferenceType());
                        definitionNode.put("mappingTarget", bt.getStandardReferenceName());
                        definitionsArray.add(definitionNode);
                    }
                    resultJson.set(termKey, definitionsArray);
                }
            }

            String jsonResult = resultMapper.writeValueAsString(resultJson);
            log.info("   ✅ [V2] 搜索完成，返回 {} 个术语的搜索结果 (耗时: {}ms)", terms.size(),
                    System.currentTimeMillis() - startTime);

            // 输出查到的内容详情
            log.info("   📋 搜索结果详情:");
            for (Map.Entry<String, List<BusinessTerminology>> entry : allTermResults.entrySet()) {
                String termKey = entry.getKey();
                List<BusinessTerminology> termResults = entry.getValue();
                if (!CollectionUtils.isEmpty(termResults)) {
                    log.info("     🔍 术语 '{}': 找到 {} 条定义", termKey, termResults.size());
                    for (BusinessTerminology bt : termResults) {
                        log.info("       • {} -> {} ({})", bt.getBusinessTerm(),
                                bt.getStandardReferenceName(), bt.getContextDescription());
                    }
                } else {
                    log.info("     ❌ 术语 '{}': 未找到定义", termKey);
                }
            }

            return jsonResult;

        } catch (Exception e) {
            log.error("🔧 [V2] [Function Call] searchBusinessTerms 异常 (耗时: {}ms): {}",
                    System.currentTimeMillis() - startTime, e.getMessage(), e);
            String errorResponse = "{\"error\": \"搜索业务术语时发生错误: " + e.getMessage() + "\"}";
            log.info("   📤 返回错误响应: {}", errorResponse);
            return errorResponse;
        }
    }

    @Tool("澄清筛选条件中的模糊值。例如，将'一线城市'转换为['北京', '上海', '广州', '深圳']。")
    public String clarifyFilterValues(@P("ambiguousValues") List<String> ambiguousValues) {
        long startTime = System.currentTimeMillis();
        log.info("🔍 [Clarify] 开始澄清模糊筛选值: '{}'", ambiguousValues);

        if (CollectionUtils.isEmpty(ambiguousValues)) {
            String errorMessage = "需要澄清的筛选值列表不能为空";
            log.error("   ❌ {}", errorMessage);
            return "{\"error\": \"" + errorMessage + "\"}";
        }

        try {
            ObjectMapper resultMapper = new ObjectMapper();
            ObjectNode resultJson = resultMapper.createObjectNode();

            for (String ambiguousValue : ambiguousValues) {
                log.info("   🔍 正在澄清: '{}'", ambiguousValue);

                // 调用知识库搜索
                List<SearchResultDTO> searchResults = knowledgeService.searchSimilarTerms(ambiguousValue, 5,
                        Collections.emptyList());

                if (CollectionUtils.isEmpty(searchResults)) {
                    log.info("   ❌ 未找到 '{}' 对应的澄清定义", ambiguousValue);
                    resultJson.set(ambiguousValue, resultMapper.createArrayNode());
                    continue;
                }

                // 优先使用Custom类型的术语，因为它们更适合用于澄清筛选值
                List<BusinessTerminology> foundTerms = searchResults.stream()
                        .map(SearchResultDTO::getTerm)
                        .collect(Collectors.toList());

                // 分类处理：优先使用Custom类型，如果没有则使用其他类型
                BusinessTerminology selectedTerm = foundTerms.stream()
                        .filter(term -> "Custom".equalsIgnoreCase(term.getStandardReferenceType()))
                        .findFirst()
                        .orElse(foundTerms.get(0)); // 如果没有Custom类型，使用第一个

                log.info("   🎯 选择术语: '{}', 类型: '{}', 映射目标: '{}'",
                        selectedTerm.getBusinessTerm(), selectedTerm.getStandardReferenceType(),
                        selectedTerm.getStandardReferenceName());

                String mappingTarget = selectedTerm.getStandardReferenceName();

                // 如果没有mappingTarget，使用contextDescription
                if (!StringUtils.hasText(mappingTarget)) {
                    mappingTarget = selectedTerm.getContextDescription();
                    log.info("   📝 使用术语描述作为澄清: '{}'", mappingTarget);
                }

                if (!StringUtils.hasText(mappingTarget)) {
                    log.warn("   ⚠️ 术语 '{}' 没有可用的映射目标或描述", ambiguousValue);
                    resultJson.set(ambiguousValue, resultMapper.createArrayNode());
                    continue;
                }

                // 🔧 修复：使用找到的标准术语名称作为键，而不是用户输入的模糊值
                String standardTermName = selectedTerm.getBusinessTerm();

                try {
                    // 尝试将mappingTarget解析为JSON
                    JsonNode mappedValueNode = resultMapper.readTree(mappingTarget);
                    resultJson.set(standardTermName, mappedValueNode);
                    log.info("   ✅ 成功澄清 '{}' -> '{}' -> {}", ambiguousValue, standardTermName,
                            mappedValueNode.toString());
                } catch (Exception jsonException) {
                    // 如果解析失败，则将其作为普通字符串处理
                    log.info("   📝 '{}' -> '{}' 的映射目标 '{}' 不是JSON格式，作为文本处理", ambiguousValue, standardTermName,
                            mappingTarget);
                    resultJson.put(standardTermName, mappingTarget);
                    log.info("   ✅ 成功澄清 '{}' -> '{}' -> '{}'", ambiguousValue, standardTermName, mappingTarget);
                }
            }

            String jsonResult = resultMapper.writeValueAsString(resultJson);
            log.info("   ✅ [Clarify] 澄清完成 (耗时: {}ms)，结果: {}", System.currentTimeMillis() - startTime, jsonResult);
            return jsonResult;

        } catch (Exception e) {
            log.error("🔧 [Clarify] [Function Call] clarifyFilterValues 异常 (耗时: {}ms): {}",
                    System.currentTimeMillis() - startTime, e.getMessage(), e);
            return "{\"error\": \"澄清筛选值时发生错误: " + e.getMessage() + "\"}";
        }
    }

    /*
     * @Tool("检查用户意图是否完整，返回处理建议")
     * public IntentValidationResult validateAndEnhanceIntent(@P("intent")
     * StructuredQueryIntent intent) {
     * long startTime = System.currentTimeMillis();
     * log.info("🔍 验证查询意图: {}", intent.getMergedContext());
     * 
     * IntentValidationResult result = new IntentValidationResult();
     * 
     * try {
     * // 1. 评估意图完整性
     * IntentValidationResult.IntentCompleteness completeness =
     * evaluateCompleteness(intent);
     * result.setCompleteness(completeness);
     * 
     * // 2. 如果查询不完整，直接返回澄清建议
     * if (!completeness.isComplete()) {
     * result.setAction("CLARIFICATION_NEEDED");
     * result.setMissingElements(completeness.getMissingElements());
     * result.setMessage("查询缺少必要信息：" + String.join(", ",
     * completeness.getMissingElements()));
     * log.info("   ⚠️ 查询不完整，需要澄清: {}", result.getMessage());
     * return result;
     * }
     * 
     * // 3. 基于完整的意图搜索相似示例
     * if (completeness.isSearchable()) {
     * // 【核心优化】使用智能查询文本选择
     * String searchQuery = selectOptimalQueryText(intent.getOriginalQuery(),
     * intent.getMergedContext());
     * log.info("   🔍 使用智能选择的查询文本进行搜索: '{}'", searchQuery);
     * 
     * log.info("   🔍 调用向量搜索服务: '{}'", searchQuery);
     * List<QueryExample> examples =
     * knowledgeService.searchSimilarExamples(searchQuery, 5);
     * log.info("   📊 向量搜索返回 {} 条结果", examples.size());
     * 
     * if (!examples.isEmpty()) {
     * // 假设第一个结果的相似度最高
     * float topSimilarity = 0.8f; // TODO: 从实际搜索结果获取相似度分数
     * result.setSimilarityScore(topSimilarity);
     * 
     * // 转换为DTO
     * List<IntentValidationResult.QueryExampleDTO> exampleDTOs = examples.stream()
     * .limit(3)
     * .map(this::convertToDTO)
     * .collect(Collectors.toList());
     * result.setExamples(exampleDTOs);
     * 
     * // 根据相似度决定处理策略
     * if (topSimilarity > 0.85) {
     * result.setAction("REFINE_WITH_EXAMPLES");
     * result.setMessage("找到高度相似的示例，请参考这些示例优化输出");
     * log.info("   ✅ 找到高相似度示例 (相似度: {})", topSimilarity);
     * } else if (topSimilarity > 0.6) {
     * result.setAction("REFERENCE_AND_ADAPT");
     * result.setMessage("找到部分相似示例，请参考但注意根据实际查询调整");
     * log.info("   ℹ️ 找到中等相似度示例 (相似度: {})", topSimilarity);
     * } else {
     * result.setAction("PROCEED_WITH_CAUTION");
     * result.setMessage("相似度较低，请根据理解生成结果");
     * log.info("   ⚠️ 示例相似度较低 (相似度: {})", topSimilarity);
     * }
     * } else {
     * result.setAction("PROCEED_WITH_CAUTION");
     * result.setMessage("未找到相似示例，请根据理解生成结果");
     * log.info("   ❌ 未找到相似示例");
     * }
     * } else {
     * result.setAction("PROCEED_WITH_CAUTION");
     * result.setMessage("查询过于简短或不具体，无法搜索示例");
     * log.info("   ⚠️ 查询不适合搜索示例");
     * }
     * 
     * log.info("🔧 [Function Call] validateAndEnhanceIntent 完成 (耗时: {}ms)",
     * System.currentTimeMillis() - startTime);
     * return result;
     * 
     * } catch (Exception e) {
     * log.error("🔧 [Function Call] validateAndEnhanceIntent 异常 (耗时: {}ms): {}",
     * System.currentTimeMillis() - startTime, e.getMessage(), e);
     * result.setAction("PROCEED_WITH_CAUTION");
     * result.setMessage("验证过程出现错误，请继续处理：" + e.getMessage());
     * return result;
     * }
     * }
     */
    // 注意：evaluateCompleteness 和 convertToDTO 方法已移除，因为 IntentValidationResult
    // 在新设计中已被去掉
}
