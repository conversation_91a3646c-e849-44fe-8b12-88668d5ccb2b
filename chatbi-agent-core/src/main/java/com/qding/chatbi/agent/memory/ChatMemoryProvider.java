package com.qding.chatbi.agent.memory;

import com.qding.chatbi.metadata.entity.ChatMessage;
import com.qding.chatbi.agent.service.ConversationHistoryService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.TokenWindowChatMemory;
import dev.langchain4j.model.openai.OpenAiTokenizer;
import dev.langchain4j.store.memory.chat.InMemoryChatMemoryStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ChatMemoryProvider {

    private final ConversationHistoryService conversationHistoryService;
    private final Cache<String, ChatMemory> cache;

    @Autowired
    public ChatMemoryProvider(ConversationHistoryService conversationHistoryService) {
        this.conversationHistoryService = conversationHistoryService;
        this.cache = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterAccess(60, TimeUnit.MINUTES)
                .build();
        log.info("ChatMemoryProvider initialized with Caffeine cache (maxSize: 10000, expireAfterAccess: 60 min)");
    }

    public ChatMemory getMemory(String sessionId) {
        return cache.get(sessionId, this::loadMemoryForSession);
    }

    private ChatMemory loadMemoryForSession(String sessionId) {
        log.info("Creating new chat memory for sessionId: {}", sessionId);
        ChatMemory memory = TokenWindowChatMemory.builder()
                .maxTokens(3000, new OpenAiTokenizer("gpt-3.5-turbo"))
                .chatMemoryStore(new InMemoryChatMemoryStore())
                .build();

        // Pre-load historical messages
        int maxMemoryMessages = 20; // Example: load last 20 messages
        List<ChatMessage> history = conversationHistoryService.getHistoryBySessionId(sessionId, maxMemoryMessages);
        log.debug("Found {} messages in history for sessionId: {}", history.size(), sessionId);

        // 添加详细的历史消息日志
        log.info("🔍 从数据库加载的历史消息详情 (会话ID: {}),数据库返回消息数量:{}", sessionId, history.size());

        /**
         * 0722 删除无用代码
        if (history != null && !history.isEmpty()) {
            for (int i = 0; i < history.size(); i++) {
                ChatMessage msg = history.get(i);
                String preview = msg.getMessageText().length() > 100 ? msg.getMessageText().substring(0, 100) + "..." : msg.getMessageText();
                log.debug("   - 第{}条数据库消息 [{}] (时间: {}): {}", i + 1, msg.getSenderType(), msg.getMessageTimestamp(), preview);
            }
        } else {
            log.debug("   - 无数据库历史消息");
        }
         **/

        for (ChatMessage msg : history) {
            if ("USER".equalsIgnoreCase(msg.getSenderType())) {
                memory.add(UserMessage.from(msg.getMessageText()));
                log.debug("   ✅ 添加用户消息到内存: {}", msg.getMessageText().substring(0, Math.min(50, msg.getMessageText().length())));
            } else if ("AGENT".equalsIgnoreCase(msg.getSenderType()) || "AI_AGENT".equalsIgnoreCase(msg.getSenderType())) {

                String messageContent = msg.getMessageText();
                // 关键修复：将结构化数据（如澄清选项）一并加载到内存中，以提供完整的上下文。
                // 如果没有这一步，AI将无法“记住”它之前提供了哪些具体的澄清选项，从而无法理解用户的后续回答。
                String structuredData = msg.getStructuredResponseData();
                if (structuredData != null && !structuredData.trim().isEmpty() && !"null".equalsIgnoreCase(structuredData.trim())) {
                    // 将消息文本和结构化数据合并，形成AI的完整回复，这样历史记录才不会丢失关键信息。
                    String fullMessage = messageContent + "\n\n[补充上下文]\n" + structuredData;
                    memory.add(AiMessage.from(fullMessage));
                    log.debug("   ✅ 添加AI消息到内存 (含结构化数据): {}", fullMessage.substring(0, Math.min(150, fullMessage.length())));
                } else {
                    memory.add(AiMessage.from(messageContent));
                    log.debug("   ✅ 添加AI消息到内存 (纯文本): {}", messageContent.substring(0, Math.min(100, messageContent.length())));
                }
            }
        }
        log.info("Chat memory created and preloaded for sessionId: {}. Current memory size: {}", sessionId, memory.messages().size());
        return memory;
    }
}
