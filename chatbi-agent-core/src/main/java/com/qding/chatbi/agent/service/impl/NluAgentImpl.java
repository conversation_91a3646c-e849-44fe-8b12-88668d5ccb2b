package com.qding.chatbi.agent.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.agent.service.ConversationContextManager;
import com.qding.chatbi.agent.service.NluAgent;
import com.qding.chatbi.agent.service.NluToolAgent;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * NLU智能体实现类
 * 重构为使用 AiServices + Function Calling 架构
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NluAgentImpl implements NluAgent {

    private final NluToolAgent nluToolAgent;
    private final ObjectMapper objectMapper;
    private final ConversationContextManager contextManager;

    @Override
    public StructuredQueryIntent understand(String userQuery, String sessionId, String chatHistory,
            List<DatasetInfoDTO> availableDatasets, String userRole) {
        log.debug("开始NLU理解: '{}', sessionId: {}", userQuery, sessionId);
        String availableDatasetsSchema = "[]";
        try {
            availableDatasetsSchema = objectMapper.writeValueAsString(availableDatasets);
        } catch (JsonProcessingException e) {
            log.error("序列化可用数据集时出错: {}", e.getMessage());
        }

        String currentTime = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);

        // 获取上一轮的结构化意图
        StructuredQueryIntent lastIntent = null;
        if (sessionId != null) {
            lastIntent = contextManager.getLastQueryIntent(sessionId);
        }
        String lastStructuredIntent = "";
        if (lastIntent != null) {
            try {
                lastStructuredIntent = objectMapper.writeValueAsString(lastIntent);
            } catch (JsonProcessingException e) {
                log.error("序列化上一轮意图时出错: {}", e.getMessage());
            }
        }

        try {
            log.info("上一轮意图:{}",lastStructuredIntent);
            StructuredQueryIntent intent = nluToolAgent.process(userQuery, lastStructuredIntent,
                     chatHistory, // recentRawConversation
                     userRole, currentTime, availableDatasetsSchema);
            log.info("【NLU结果】意图: {}, 查询类型: {}, 需要聚合: {}", intent.getIntent(), intent.getQueryType(), intent.isAggregationRequired());
            return intent;
        } catch (Exception e) {
            log.error("NLU理解失败: {}", e.getMessage());
            throw new RuntimeException("NLU处理失败", e);
        }
    }

    /**
     * 从聊天历史中提取会话ID
     * TODO: 这是一个临时方案，应该通过更好的方式传递sessionId
     */
    private String extractSessionId(String chatHistory) {
        // 暂时返回null，需要在架构上改进
        return null;
    }
}