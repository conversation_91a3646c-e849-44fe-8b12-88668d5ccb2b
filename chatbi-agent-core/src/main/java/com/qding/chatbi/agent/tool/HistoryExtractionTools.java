package com.qding.chatbi.agent.tool;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 提供 LLM Function Calling 用的工具，用于从 conversationSummary 中抽取实体。
 * 当前实现仅把模型传入的 JSON 字符串直接回传；
 * 如果模型传入原文，也会原样返回，便于后续 LLM 继续解析。
 * 后期可替换为正则 / 词典解析 + 校验。
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HistoryExtractionTools {

    private final ObjectMapper objectMapper;

    // 暂用普通方法，后续接入 Function Calling 框架时再加注解
    public String extractEntitiesFromHistory(String summaryRaw) {
        log.debug("extract_entities_from_history called with: {}", summaryRaw);
        // 直接返回，让后续 LLM 使用；未来可在此解析/校验
        return summaryRaw;
    }
}