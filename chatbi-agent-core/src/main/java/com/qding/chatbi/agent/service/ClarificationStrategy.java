package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.ClarificationOption;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.common.dto.UserContextDTO;
import dev.langchain4j.memory.ChatMemory;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 澄清策略接口
 * 
 * 负责根据用户查询的不完整程度和上下文信息，
 * 智能生成层次化的澄清选项，逐步引导用户明确查询意图。
 * 
 * 核心设计理念：
 * 1. 层次化澄清：从粗粒度到细粒度逐步澄清
 * 2. 上下文感知：基于对话历史和用户偏好优化澄清选项
 * 3. 智能组合：将多个缺失维度合并到单个澄清选项中
 * 4. 动态适应：根据数据集特征动态调整澄清策略
 * 
 * <AUTHOR> Team
 */
public interface ClarificationStrategy {
    
    /**
     * 【已废弃】生成澄清选项列表。此逻辑已移至LLM，通过prompt指导生成。
     * @param partialIntent 部分解析的意图
     * @param chatMemory 对话历史
     * @param userContext 用户上下文
     * @return 澄清选项列表
     */
    @Deprecated
    List<ClarificationOption> generateClarificationOptions(StructuredQueryIntent partialIntent, ChatMemory chatMemory, UserContextDTO userContext);
    
    /**
     * 生成用于指导LLM进行澄清的文本。
     * @param partialIntent 部分解析的用户意图
     * @param userContext 用户上下文
     * @return 一个包含引导文本的Optional，如果不需要引导则为空
     */
    Optional<String> generateClarificationGuidance(StructuredQueryIntent partialIntent, UserContextDTO userContext);
    
    /**
     * 处理用户的澄清响应，更新查询意图
     * 
     * @param selectedOptionId 用户选择的澄清选项ID
     * @param payload 澄清选项的载荷数据
     * @param currentIntent 当前的查询意图
     * @param chatMemory 对话历史记忆
     * @param userContext 用户上下文
     * @return 更新后的查询意图
     */
    StructuredQueryIntent processClarificationResponse(
            String selectedOptionId,
            Map<String, Object> payload,
            StructuredQueryIntent currentIntent,
            ChatMemory chatMemory,
            UserContextDTO userContext);
    
    /**
     * 检查查询意图是否仍需要进一步澄清
     * 
     * @param intent 查询意图
     * @param userContext 用户上下文
     * @return true如果仍需澄清，false如果已足够明确
     */
    boolean needsFurtherClarification(StructuredQueryIntent intent, UserContextDTO userContext);
    
    /**
     * 获取澄清提示文本
     * 
     * @param missingDimensions 缺失的维度列表
     * @param userContext 用户上下文
     * @return 澄清提示文本
     */
    String generateClarificationPrompt(List<String> missingDimensions, UserContextDTO userContext);
} 