package com.qding.chatbi.agent.chain;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.agent.service.prompt.PromptTemplateService;
import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.enums.DatabaseType;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Qualifier;

import jakarta.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * SQL生成链组件
 * 
 * 职责: 专门负责根据结构化上下文生成SQL查询。
 * 这是对LLM的第二次调用，专注于Text-to-SQL任务。
 * 
 * 重构后的架构特点:
 * - 使用数据库专用的提示词模板文件，便于维护和调优
 * - 支持 MySQL 和 ClickHouse 数据库类型
 * - 模板缓存机制，提升性能
 * - 清晰的错误处理和日志记录
 * 
 * 支持的数据库:
 * - MySQL: 使用 sql_generation_mysql.txt 模板
 * - ClickHouse: 使用 sql_generation_clickhouse.txt 模板
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
@Component
@Slf4j
public class SqlGenerationChain {

    private final ChatLanguageModel chatModel;
    private final PromptTemplateService promptTemplateService;
    private final ObjectMapper objectMapper;

    // 支持的数据库类型模板缓存
    private final Map<DatabaseType, PromptTemplate> templateCache = new ConcurrentHashMap<>();

    @Autowired
    public SqlGenerationChain(@Qualifier("qwenChatModelForFunctionCalling") ChatLanguageModel chatModel,
            PromptTemplateService promptTemplateService,
            ObjectMapper objectMapper) {
        this.chatModel = chatModel;
        this.promptTemplateService = promptTemplateService;
        this.objectMapper = objectMapper;
        log.debug("SqlGenerationChain初始化完成");
    }

    @PostConstruct
    private void initTemplates() {
        // 加载支持的数据库专用模板
        loadDatabaseTemplate(DatabaseType.MYSQL, "sql_generation_mysql");
        loadDatabaseTemplate(DatabaseType.CLICKHOUSE, "sql_generation_clickhouse");

        log.info("SQL生成模板初始化完成，支持的数据库类型: {}", templateCache.keySet());
    }

    private void loadDatabaseTemplate(DatabaseType dbType, String templateName) {
        try {
            PromptTemplate template = promptTemplateService.getPromptTemplate(templateName);
            templateCache.put(dbType, template);
            log.debug("{}模板加载成功", dbType);
        } catch (Exception e) {
            log.error("{}模板加载失败: {}", dbType, e.getMessage());
            throw new RuntimeException("关键模板加载失败: " + templateName, e);
        }
    }

    @Builder
    public static class SqlGenerationContext {
        String dbDialect;
        DatasetInfoDTO dataset;
        String userQuestion;
        StructuredQueryIntent structuredIntent;
        String currentDate;
        // (可选) 从知识库中检索到的相关查询范例
        List<String> relevantExamples;
    }

    /**
     * 执行SQL生成链
     * 
     * 优化后的特点：
     * - 直接使用NLU提供的聚合策略决策
     * - 根据queryType和expectedGranularity生成对应的SQL
     * - 减少对LLM的业务逻辑判断依赖
     * 
     * @param context 包含生成SQL所需的所有上下文信息
     * @return LLM生成的SQL字符串
     */
    public String execute(SqlGenerationContext context) {
        log.debug("开始SQL生成: 数据库={}, 数据集={}, 问题='{}'", context.dbDialect, context.dataset.getDatasetName(), context.userQuestion);

        // 🏗️ 1. 构建模板所需的变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("db_dialect", context.dbDialect);
        variables.put("user_question", context.userQuestion);
        variables.put("table_ddl", generateDdl(context.dataset));
        variables.put("column_semantics", generateColumnSemantics(context.dataset));
        variables.put("current_date", context.currentDate);

        // 新增：NLU业务决策信息 (增加null值防御)
        variables.put("query_type",
                context.structuredIntent.getQueryType() != null ? context.structuredIntent.getQueryType() : "");
        variables.put("aggregation_required", context.structuredIntent.isAggregationRequired());

        List<String> granularityList = context.structuredIntent.getExpectedGranularity();
        String granularityStr = (granularityList != null && !granularityList.isEmpty())
                ? String.join(", ", granularityList)
                : "";
        variables.put("expected_granularity", granularityStr);
        variables.put("granularity_description",
                context.structuredIntent.getGranularityDescription() != null
                        ? context.structuredIntent.getGranularityDescription()
                        : "");

        try {
            variables.put("structured_intent_json",
                    objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(context.structuredIntent));
        } catch (JsonProcessingException e) {
            log.error("结构化意图JSON序列化失败: {}", e.getMessage());
            variables.put("structured_intent_json", "Error: could not serialize intent to JSON.");
        }

        if (context.relevantExamples != null && !context.relevantExamples.isEmpty()) {
            String examples = context.relevantExamples.stream().collect(Collectors.joining("\n---\n"));
            variables.put("relevant_examples", examples);
        } else {
            variables.put("relevant_examples", "No relevant examples found.");
        }

        // 📤 2. 根据数据库类型选择模板并生成Prompt

        // 选择对应的数据库模板
        DatabaseType dbType;
        try {
            dbType = DatabaseType.valueOf(context.dbDialect.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.error("不支持的数据库类型: {}", context.dbDialect);
            throw new RuntimeException("不支持的数据库类型: " + context.dbDialect +
                    ". 支持的类型: " + templateCache.keySet());
        }

        PromptTemplate selectedTemplate = templateCache.get(dbType);
        if (selectedTemplate == null) {
            log.error("未找到数据库类型 {} 的专用模板", dbType);
            throw new RuntimeException("未找到数据库类型 " + dbType + " 的专用模板");
        }

        // 🎯 保留：业务决策变量对提示词调试很重要
        log.info("【提示词变量】传递给模板的关键业务决策:");
        log.info("  query_type: {}, aggregation: {}, granularity: {}",
                variables.get("query_type"), variables.get("aggregation_required"),
                variables.get("expected_granularity"));

        Prompt prompt = selectedTemplate.apply(variables);

        // 🎯 保留：完整Prompt对提示词调试很重要
        log.debug("【提示词内容】Prompt长度: {} 字符", prompt.text().length());
        log.debug("【提示词内容】完整Prompt:\n{}", prompt.text());

        // 🤖 3. 调用LLM并获取响应
        long startTime = System.currentTimeMillis();
        String response = chatModel.generate(prompt.text());
        long llmTime = System.currentTimeMillis() - startTime;

        // 🎯 保留：LLM响应对提示词调试很重要
        log.info("【提示词输出】LLM响应 - 耗时: {}ms, 长度: {} 字符", llmTime, response.length());
        log.debug("【提示词输出】LLM原始响应: {}", response);

        // 🔄 4. 清洗和提取SQL
        String extractedSql = extractSqlFromResponse(response);

        // 新增：验证SQL是否符合NLU的业务决策
        validateSqlAgainstNluDecisions(extractedSql, context.structuredIntent);

        return extractedSql;
    }

    /**
     * 新增方法：验证生成的SQL是否符合NLU的业务决策
     */
    private void validateSqlAgainstNluDecisions(String sql, StructuredQueryIntent intent) {
        if (sql == null || sql.trim().isEmpty()) {
            log.error("生成的SQL为空");
            return;
        }

        String sqlLower = sql.toLowerCase();

        // 验证1：聚合需求验证
        if (intent.isAggregationRequired()) {
            boolean hasGroupBy = sqlLower.contains("group by");
            if (!hasGroupBy) {
                log.warn("NLU要求聚合但SQL中缺少GROUP BY子句");
            }

            // 验证聚合粒度
            if (intent.getExpectedGranularity() != null && !intent.getExpectedGranularity().isEmpty()) {
                for (String granularity : intent.getExpectedGranularity()) {
                    boolean containsGranularity = sqlLower.contains(granularity.toLowerCase());
                    if (!containsGranularity) {
                        log.warn("期望聚合字段 '{}' 不在SQL中", granularity);
                    }
                }
            }
        } else {
            boolean hasGroupBy = sqlLower.contains("group by");
            if (hasGroupBy) {
                log.warn("NLU不要求聚合但SQL中包含了GROUP BY子句");
            }
        }

        // 验证2：查询类型验证
        if ("RANKING_QUERY".equals(intent.getQueryType())) {
            boolean hasOrderBy = sqlLower.contains("order by");
            if (!hasOrderBy) {
                log.warn("排名查询但SQL中缺少ORDER BY子句");
            }
        }
    }

    /**
     * 根据数据集信息动态生成DDL (CREATE TABLE) 语句，为LLM提供表结构。
     * 只包含非计算字段。
     * 
     * 优化:
     * - 在COMMENT中明确指出业务名称，格式为 'business_name: [业务名]', 以避免LLM混淆
     */
    private String generateDdl(DatasetInfoDTO dataset) {
        StringBuilder markdown = new StringBuilder();
        markdown.append("## 表名: `").append(dataset.getTableName()).append("`\n\n");

        markdown.append("| 指标名称 | 数据库表列名 | 数据类型 | 语义类型 | 是否计算字段 |\n");
        markdown.append("|---------|-------------|----------|----------|----------|\n");

        String tableRows = dataset.getColumns().stream()
                // .filter(col -> !isCalculatedField(col))
                .map(col -> String.format("| %s | `%s` | %s | %s | %s |",
                        col.getColumnName(), // 指标名称
                        col.getTechnicalNameOrExpression(), // 数据库表列名
                        col.getDataType(), // 数据类型
                        col.getSemanticType() != null ? col.getSemanticType() : "-", // 语义类型
                        col.getIsComputed() != null ? col.getIsComputed() : "-")) // 是否计算字段
                .collect(Collectors.joining("\n"));

        markdown.append(tableRows);
        markdown.append("\n");

        return markdown.toString();
    }

    /**
     * 生成列的业务语义描述，供LLM参考。
     * 只包含业务名称和描述，并且会指出计算指标的公式。
     * 
     * 优化:
     * - 明确列出物理名、业务名和描述/公式，格式为 `- column_name: `physical_name` (business_name:
     * [业务名]): [描述或公式]`
     */
    private String generateColumnSemantics(DatasetInfoDTO dataset) {
        return dataset.getColumns().stream()
                .map(col -> {
                    String formattedString;
                    if (isCalculatedField(col)) {
                        // 🔥 CRITICAL FIX: 对于计算字段，直接提供完整的SQL表达式，不要让LLM使用业务名称
                        String definition = String.format(
                                "This is a calculated metric '%s', use the expression `%s` in your SQL. DO NOT use the business name '%s' directly.",
                                col.getColumnName(), // business name for reference
                                col.getTechnicalNameOrExpression(), // actual SQL expression to use
                                col.getColumnName()); // business name to avoid
                        formattedString = String.format(
                                "- business_name: `%s` -> use_expression: `%s` (calculated): %s",
                                col.getColumnName(), // business name
                                col.getTechnicalNameOrExpression(), // SQL expression
                                definition);
                    } else {
                        formattedString = String.format("- column_name: `%s` (business_name: %s): %s",
                                col.getTechnicalNameOrExpression(), // physical name
                                col.getColumnName(), // business name
                                col.getDescription());
                    }
                    return formattedString;
                })
                .collect(Collectors.joining("\n"));
    }

    /**
     * 检查一个字段是否为计算字段。
     *
     * @param column 要检查的字段
     * @return 如果是计算字段则返回 true
     */
    private boolean isCalculatedField(ColumnInfoDTO column) {
        return column.getIsComputed() != null && column.getIsComputed();
    }

    /**
     * 从LLM的完整响应中提取出 ```sql ... ``` 代码块中的SQL。
     */
    private String extractSqlFromResponse(String response) {
        final String startTag = "```sql";
        final String endTag = "```";

        int startIndex = response.indexOf(startTag);
        if (startIndex != -1) {
            int endIndex = response.indexOf(endTag, startIndex + startTag.length());
            if (endIndex != -1) {
                String extractedSql = response.substring(startIndex + startTag.length(), endIndex).trim();
                log.info("【提示词输出】提取的SQL: {}", extractedSql.replace("\n", " ").replaceAll("\\s+", " "));
                return extractedSql;
            } else {
                log.warn("找到SQL开始标记但未找到结束标记");
            }
        } else {
            log.warn("未找到SQL代码块标记 '```sql'");
        }

        // 未找到SQL代码块，清理并返回原始响应
        String cleanedResponse = response.replace("`", "").trim();

        // 如果清理后的响应是空的或包含模板字符串，返回错误提示
        if (cleanedResponse.isEmpty() || cleanedResponse.contains("{") || cleanedResponse.contains("}")) {
            log.error("LLM返回了无效的SQL内容: {}", response);
            return "-- ERROR: LLM failed to generate valid SQL";
        }

        log.info("【提示词输出】提取的SQL(无代码块): {}", cleanedResponse.replace("\n", " ").replaceAll("\\s+", " "));
        return cleanedResponse;
    }
}
