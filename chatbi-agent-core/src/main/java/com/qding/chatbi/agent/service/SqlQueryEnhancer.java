package com.qding.chatbi.agent.service;

import com.qding.chatbi.agent.service.RowLevelPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * SQL查询增强器
 * 负责在SQL查询中注入行级权限过滤条件
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SqlQueryEnhancer {

    private final RowLevelPermissionService rowLevelPermissionService;

    /**
     * 为SQL查询添加行级权限过滤条件
     *
     * @param originalSql 原始SQL
     * @param userId 用户ID
     * @param datasetId 数据集ID
     * @return 增强后的SQL
     */
    public String enhanceQueryWithRowLevelPermission(String originalSql, String userId, Long datasetId) {
        try {
            // 检查是否需要行级权限控制
            if (!rowLevelPermissionService.hasRowLevelRestriction(userId, datasetId)) {
                log.debug("用户 {} 对数据集 {} 无行级权限限制", userId, datasetId);
                return originalSql;
            }

            // 解析SQL
            Statement statement = CCJSqlParserUtil.parse(originalSql);
            
            if (!(statement instanceof Select)) {
                log.warn("只支持SELECT语句的行级权限控制");
                return originalSql;
            }

            Select select = (Select) statement;
            SelectBody selectBody = select.getSelectBody();

            // 处理不同类型的SELECT语句
            if (selectBody instanceof PlainSelect) {
                enhancePlainSelect((PlainSelect) selectBody, userId, datasetId);
            } else if (selectBody instanceof SetOperationList) {
                enhanceSetOperationList((SetOperationList) selectBody, userId, datasetId);
            }

            return select.toString();

        } catch (JSQLParserException e) {
            log.error("解析SQL失败，返回原始SQL: {}", originalSql, e);
            return originalSql;
        } catch (Exception e) {
            log.error("增强SQL查询失败，返回原始SQL: {}", originalSql, e);
            return originalSql;
        }
    }

    /**
     * 增强普通SELECT语句
     */
    private void enhancePlainSelect(PlainSelect plainSelect, String userId, Long datasetId) {
        FromItem fromItem = plainSelect.getFromItem();
        
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            String tableName = table.getName();
            
            // 生成行级权限过滤条件
            String rowLevelFilter = rowLevelPermissionService
                .generateRowLevelFilter(userId, datasetId, tableName);
            
            if (StringUtils.hasText(rowLevelFilter)) {
                addWhereCondition(plainSelect, rowLevelFilter);
                log.debug("为表 {} 添加行级权限过滤条件: {}", tableName, rowLevelFilter);
            }
        }
        
        // 处理JOIN中的表
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table) {
                    Table joinTable = (Table) rightItem;
                    String joinTableName = joinTable.getName();
                    
                    String rowLevelFilter = rowLevelPermissionService
                        .generateRowLevelFilter(userId, datasetId, joinTableName);
                    
                    if (StringUtils.hasText(rowLevelFilter)) {
                        // 将行级权限条件添加到JOIN条件中
                        addJoinCondition(join, rowLevelFilter);
                        log.debug("为JOIN表 {} 添加行级权限过滤条件: {}", joinTableName, rowLevelFilter);
                    }
                }
            }
        }
    }

    /**
     * 增强UNION等复合SELECT语句
     */
    private void enhanceSetOperationList(SetOperationList setOperationList, String userId, Long datasetId) {
        List<SelectBody> selectBodies = setOperationList.getSelects();
        
        for (SelectBody selectBody : selectBodies) {
            if (selectBody instanceof PlainSelect) {
                enhancePlainSelect((PlainSelect) selectBody, userId, datasetId);
            }
        }
    }

    /**
     * 添加WHERE条件
     */
    private void addWhereCondition(PlainSelect plainSelect, String condition) {
        try {
            Expression newCondition = CCJSqlParserUtil.parseCondExpression(condition);
            Expression existingWhere = plainSelect.getWhere();
            
            if (existingWhere == null) {
                plainSelect.setWhere(newCondition);
            } else {
                AndExpression andExpression = new AndExpression(existingWhere, newCondition);
                plainSelect.setWhere(andExpression);
            }
        } catch (JSQLParserException e) {
            log.error("解析WHERE条件失败: {}", condition, e);
        }
    }

    /**
     * 添加JOIN条件
     */
    private void addJoinCondition(Join join, String condition) {
        try {
            Expression newCondition = CCJSqlParserUtil.parseCondExpression(condition);
            Expression existingOnExpression = join.getOnExpression();
            
            if (existingOnExpression == null) {
                join.setOnExpression(newCondition);
            } else {
                AndExpression andExpression = new AndExpression(existingOnExpression, newCondition);
                join.setOnExpression(andExpression);
            }
        } catch (JSQLParserException e) {
            log.error("解析JOIN条件失败: {}", condition, e);
        }
    }

    /**
     * 验证增强后的SQL是否安全
     */
    public boolean validateEnhancedSql(String sql) {
        try {
            CCJSqlParserUtil.parse(sql);
            return true;
        } catch (JSQLParserException e) {
            log.error("增强后的SQL语法错误: {}", sql, e);
            return false;
        }
    }
}
