package com.qding.chatbi.agent.interceptor;

import com.qding.chatbi.agent.service.RowLevelPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 行级权限拦截器
 * 在SQL执行前注入行级权限过滤条件
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RowLevelPermissionInterceptor {

    private final RowLevelPermissionService rowLevelPermissionService;

    /**
     * 拦截并增强SQL查询
     *
     * @param originalSql 原始SQL
     * @param userId 用户ID
     * @param datasetId 数据集ID
     * @param tableName 主表名
     * @return 增强后的SQL
     */
    public String interceptAndEnhanceQuery(String originalSql, String userId, Long datasetId, String tableName) {
        try {
            log.debug("开始为用户 {} 增强SQL查询，数据集ID: {}", userId, datasetId);

            // 检查是否需要行级权限控制
            if (!rowLevelPermissionService.hasRowLevelRestriction(userId, datasetId)) {
                log.debug("用户 {} 对数据集 {} 无行级权限限制", userId, datasetId);
                return originalSql;
            }

            // 生成行级权限过滤条件
            String rowLevelFilter = rowLevelPermissionService.generateRowLevelFilter(userId, datasetId, tableName);

            if (!StringUtils.hasText(rowLevelFilter)) {
                log.debug("用户 {} 对数据集 {} 生成的行级权限过滤条件为空", userId, datasetId);
                return originalSql;
            }

            // 增强SQL
            String enhancedSql = enhanceSqlWithRowLevelFilter(originalSql, rowLevelFilter);
            
            log.info("SQL查询已增强行级权限过滤 - 用户: {}, 数据集: {}, 过滤条件: {}", 
                    userId, datasetId, rowLevelFilter);
            log.debug("原始SQL: {}", originalSql);
            log.debug("增强后SQL: {}", enhancedSql);

            return enhancedSql;

        } catch (Exception e) {
            log.error("增强SQL查询失败，返回原始SQL - 用户: {}, 数据集: {}", userId, datasetId, e);
            return originalSql;
        }
    }

    /**
     * 在SQL中注入行级权限过滤条件
     */
    private String enhanceSqlWithRowLevelFilter(String originalSql, String rowLevelFilter) {
        // 简单的SQL增强逻辑
        String sql = originalSql.trim();
        
        // 检查是否已有WHERE子句
        String upperSql = sql.toUpperCase();
        int whereIndex = upperSql.indexOf(" WHERE ");
        int groupByIndex = upperSql.indexOf(" GROUP BY ");
        int orderByIndex = upperSql.indexOf(" ORDER BY ");
        int limitIndex = upperSql.indexOf(" LIMIT ");
        
        if (whereIndex != -1) {
            // 已有WHERE子句，使用AND连接
            String beforeWhere = sql.substring(0, whereIndex + 7); // 包含" WHERE "
            String afterWhere = sql.substring(whereIndex + 7);
            return beforeWhere + "(" + rowLevelFilter + ") AND (" + afterWhere + ")";
        } else {
            // 没有WHERE子句，添加WHERE
            int insertIndex = sql.length();
            
            // 找到插入位置（在GROUP BY, ORDER BY, LIMIT之前）
            if (groupByIndex != -1) {
                insertIndex = Math.min(insertIndex, groupByIndex);
            }
            if (orderByIndex != -1) {
                insertIndex = Math.min(insertIndex, orderByIndex);
            }
            if (limitIndex != -1) {
                insertIndex = Math.min(insertIndex, limitIndex);
            }
            
            String beforeInsert = sql.substring(0, insertIndex).trim();
            String afterInsert = sql.substring(insertIndex);
            
            return beforeInsert + " WHERE " + rowLevelFilter + " " + afterInsert;
        }
    }

    /**
     * 验证用户对数据集的访问权限
     */
    public boolean validateDatasetAccess(String userId, Long datasetId) {
        try {
            // 这里可以添加额外的数据集访问验证逻辑
            return true;
        } catch (Exception e) {
            log.error("验证用户 {} 对数据集 {} 的访问权限失败", userId, datasetId, e);
            return false;
        }
    }

    /**
     * 记录查询审计日志
     */
    public void auditQuery(String userId, Long datasetId, String originalSql, String enhancedSql, boolean hasRowLevelFilter) {
        try {
            log.info("查询审计 - 用户: {}, 数据集: {}, 是否应用行级权限: {}", 
                    userId, datasetId, hasRowLevelFilter);
            
            // 这里可以将审计信息写入数据库或日志系统
            // 例如：auditService.recordQueryAudit(userId, datasetId, originalSql, enhancedSql, hasRowLevelFilter);
            
        } catch (Exception e) {
            log.error("记录查询审计日志失败", e);
        }
    }
}
