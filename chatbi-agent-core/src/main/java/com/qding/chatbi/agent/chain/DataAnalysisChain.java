package com.qding.chatbi.agent.chain;

import com.qding.chatbi.agent.service.prompt.PromptTemplateService;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.util.JsonUtil;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据分析链服务
 * 负责分析查询结果并生成数据洞察和解读
 */
@Service
@Slf4j
public class DataAnalysisChain {

    private final ChatLanguageModel chatLanguageModel;
    private final PromptTemplateService promptTemplateService;

    @Autowired
    public DataAnalysisChain(@Qualifier("qwenChatModelForFunctionCalling") ChatLanguageModel chatLanguageModel,
            PromptTemplateService promptTemplateService) {
        this.chatLanguageModel = chatLanguageModel;
        this.promptTemplateService = promptTemplateService;
        log.info("DataAnalysisChain初始化完成，使用ChatLanguageModel: {}", chatLanguageModel.getClass().getSimpleName());
    }

    /**
     * 生成数据分析洞察
     * 
     * @param queryResult 查询结果
     * @param userQuery   用户查询
     * @return 数据分析洞察文本
     */
    public String generateDataInsights(QueryResult queryResult, String userQuery) {
        if (queryResult == null || queryResult.getRows() == null || queryResult.getRows().isEmpty()) {
            return "暂无数据可供分析。";
        }

        try {
            // 数据预处理和统计
            Map<String, Object> dataAnalysis = analyzeQueryResult(queryResult);

            // 构建AI提示
            PromptTemplate template = promptTemplateService.getPromptTemplate("data_analysis");
            Map<String, Object> variables = new HashMap<>();
            variables.put("user_query", userQuery);
            variables.put("row_count", queryResult.getRows().size());
            variables.put("column_count", queryResult.getColumnHeaders().size());
            variables.put("columns", String.join(", ", queryResult.getColumnHeaders()));
            variables.put("data_summary", JsonUtil.toJson(dataAnalysis));
            variables.put("sample_data", getSampleDataJson(queryResult, 5));

            Prompt prompt = template.apply(variables);

            log.info("发送数据分析请求到AI模型");
            String aiResponse = chatLanguageModel.generate(prompt.text());

            log.info("AI数据分析响应长度: {} 字符", aiResponse.length());
            return aiResponse.trim();

        } catch (Exception e) {
            log.error("生成数据洞察时发生错误", e);
            return generateFallbackAnalysis(queryResult, userQuery);
        }
    }

    /**
     * 分析查询结果，生成统计信息
     */
    private Map<String, Object> analyzeQueryResult(QueryResult queryResult) {
        Map<String, Object> analysis = new HashMap<>();

        List<String> headers = queryResult.getColumnHeaders();
        List<List<Object>> rows = queryResult.getRows();

        // 基本统计
        analysis.put("total_rows", rows.size());
        analysis.put("total_columns", headers.size());

        // 按列分析
        Map<String, Object> columnAnalysis = new HashMap<>();
        for (int i = 0; i < headers.size(); i++) {
            final int columnIndex = i;
            String columnName = headers.get(i);
            List<Object> columnValues = rows.stream()
                    .map(row -> columnIndex < row.size() ? row.get(columnIndex) : null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            Map<String, Object> columnStats = analyzeColumn(columnValues);
            columnAnalysis.put(columnName, columnStats);
        }
        analysis.put("column_analysis", columnAnalysis);

        return analysis;
    }

    /**
     * 分析单个列的数据
     */
    private Map<String, Object> analyzeColumn(List<Object> values) {
        Map<String, Object> stats = new HashMap<>();

        if (values.isEmpty()) {
            stats.put("type", "empty");
            return stats;
        }

        // 检查数据类型
        boolean allNumbers = values.stream().allMatch(v -> v instanceof Number);
        if (allNumbers) {
            List<Double> numbers = values.stream()
                    .map(v -> ((Number) v).doubleValue())
                    .collect(Collectors.toList());

            stats.put("type", "numeric");
            stats.put("min", Collections.min(numbers));
            stats.put("max", Collections.max(numbers));
            stats.put("sum", numbers.stream().mapToDouble(Double::doubleValue).sum());
            stats.put("avg", numbers.stream().mapToDouble(Double::doubleValue).average().orElse(0.0));
            stats.put("count", numbers.size());
        } else {
            stats.put("type", "text");
            stats.put("unique_count", values.stream().distinct().count());
            stats.put("total_count", values.size());

            // 频次统计（前5个）
            Map<Object, Long> frequency = values.stream()
                    .collect(Collectors.groupingBy(v -> v, Collectors.counting()));
            List<Map.Entry<Object, Long>> topEntries = frequency.entrySet().stream()
                    .sorted(Map.Entry.<Object, Long>comparingByValue().reversed())
                    .limit(5)
                    .collect(Collectors.toList());
            stats.put("top_values", topEntries);
        }

        return stats;
    }

    /**
     * 获取示例数据的JSON字符串
     */
    private String getSampleDataJson(QueryResult queryResult, int sampleSize) {
        try {
            Map<String, Object> sampleData = new HashMap<>();
            sampleData.put("headers", queryResult.getColumnHeaders());

            List<List<Object>> sampleRows = queryResult.getRows().stream()
                    .limit(sampleSize)
                    .collect(Collectors.toList());
            sampleData.put("rows", sampleRows);

            return JsonUtil.toJson(sampleData);
        } catch (Exception e) {
            log.error("生成示例数据JSON时出错", e);
            return "{}";
        }
    }

    /**
     * 生成备用分析（当AI分析失败时）
     */
    private String generateFallbackAnalysis(QueryResult queryResult, String userQuery) {
        StringBuilder analysis = new StringBuilder();

        int rowCount = queryResult.getRows().size();
        int columnCount = queryResult.getColumnHeaders().size();

        analysis.append(String.format("📊 **数据概览**\n"));
        analysis.append(String.format("- 查询到 %d 条记录，包含 %d 个字段\n", rowCount, columnCount));
        analysis.append(String.format("- 字段包括：%s\n\n", String.join("、", queryResult.getColumnHeaders())));

        // 简单的数值分析
        for (int i = 0; i < queryResult.getColumnHeaders().size(); i++) {
            String columnName = queryResult.getColumnHeaders().get(i);
            final int columnIndex = i;

            List<Object> columnValues = queryResult.getRows().stream()
                    .map(row -> columnIndex < row.size() ? row.get(columnIndex) : null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (columnValues.stream().allMatch(v -> v instanceof Number)) {
                List<Double> numbers = columnValues.stream()
                        .map(v -> ((Number) v).doubleValue())
                        .collect(Collectors.toList());

                double sum = numbers.stream().mapToDouble(Double::doubleValue).sum();
                double avg = numbers.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double max = Collections.max(numbers);
                double min = Collections.min(numbers);

                analysis.append(String.format("📈 **%s 数据分析**\n", columnName));
                analysis.append(String.format("- 总计：%,.0f\n", sum));
                analysis.append(String.format("- 平均值：%,.2f\n", avg));
                analysis.append(String.format("- 最大值：%,.0f，最小值：%,.0f\n\n", max, min));
            }
        }

        return analysis.toString();
    }
}