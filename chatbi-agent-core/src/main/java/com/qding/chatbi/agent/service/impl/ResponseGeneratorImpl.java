package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.chain.DataVisualizationChain;
import com.qding.chatbi.agent.chain.DataAnalysisChain;
import com.qding.chatbi.agent.service.ResponseGenerator;
import com.qding.chatbi.common.dto.*;
import com.qding.chatbi.common.enums.ResponseType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 响应生成器实现
 * 基于查询结果和用户意图生成智能响应
 */
@Service
@Slf4j
public class ResponseGeneratorImpl implements ResponseGenerator {

    private final DataVisualizationChain dataVisualizationChain;
    private final DataAnalysisChain dataAnalysisChain;

    @Autowired
    public ResponseGeneratorImpl(DataVisualizationChain dataVisualizationChain, DataAnalysisChain dataAnalysisChain) {
        this.dataVisualizationChain = dataVisualizationChain;
        this.dataAnalysisChain = dataAnalysisChain;
        log.info("ResponseGeneratorImpl初始化完成，使用统一的数据可视化服务");
    }

    @Override
    public AgentResponse generate(QueryResult queryResult, StructuredQueryIntent intent) {
        log.info("========== ResponseGenerator开始生成智能响应 ==========");

        if (queryResult == null || queryResult.getRows() == null || queryResult.getRows().isEmpty()) {
            log.info("查询结果为空，生成无数据响应");
            return createNoDataResponse();
        }

        try {
            // 使用mergedContext作为标题生成的主要依据，如果没有则使用originalQuery
            String userQueryForTitle = generateTitle(intent);

            DataVisualizationConfig vizConfig = dataVisualizationChain.generateVisualizationConfig(
                    queryResult, userQueryForTitle);

            if (!vizConfig.isSuccess()) {
                log.error("数据可视化配置生成失败: {}", vizConfig.getErrorMessage());
                return createErrorResponse(vizConfig.getErrorMessage(), intent);
            }

            DisplaySuggestion suggestion = createDisplaySuggestion(vizConfig);
            String responseMessage = generateResponseMessage(queryResult, vizConfig, intent);

            AgentResponse response = new AgentResponse();
            response.setResponseType(ResponseType.DATA);

            if (queryResult.getRows().size() > 1) {
                String[] parts = responseMessage.split("\n\n", 2);
                response.setSummary(parts[0]);
                if (parts.length > 1) {
                    response.setMessageToUser(parts[1]);
                }
            } else {
                response.setMessageToUser(responseMessage);
            }

            response.setQueryResult(queryResult);
            response.setDisplaySuggestion(suggestion);

            log.info("✅ 智能响应生成完成:");
            log.info("   - 展示策略: {}", vizConfig.getDisplayStrategy());
            log.info("   - 总结: {}", response.getSummary());
            log.info("   - 详细消息: {}", response.getMessageToUser());
            log.info("   - 数据行数: {}", queryResult.getRows().size());

            return response;

        } catch (Exception e) {
            log.error("生成智能响应时发生错误", e);
            return createErrorResponse("生成响应失败: " + e.getMessage(), intent);
        }
    }

    private DisplaySuggestion createDisplaySuggestion(DataVisualizationConfig vizConfig) {
        DisplaySuggestion suggestion = new DisplaySuggestion(
                vizConfig.getDisplayStrategy(),
                vizConfig.getTitle(),
                vizConfig.getTableConfigJson(),
                vizConfig.getChartConfigJson());
        suggestion.setDataVisualizationConfig(vizConfig);
        return suggestion;
    }

    private String generateResponseMessage(QueryResult queryResult, DataVisualizationConfig vizConfig,
            StructuredQueryIntent intent) {
        int rowCount = queryResult.getRows().size();

        if (rowCount == 1) {
            List<String> headers = queryResult.getColumnHeaders();
            List<Object> values = queryResult.getRows().get(0);
            List<String> dimensionParts = new ArrayList<>();
            List<String> metricParts = new ArrayList<>();

            List<String> metricNamesFromIntent = (List<String>) intent.getEntities().get("metrics");
            if (metricNamesFromIntent == null) {
                metricNamesFromIntent = new ArrayList<>();
            }

            // 检查是否有null值
            boolean hasNullValue = false;
            for (Object value : values) {
                if (value == null) {
                    hasNullValue = true;
                    break;
                }
            }

            // 如果有null值，返回友好的消息
            if (hasNullValue) {
                List<String> timeRanges = (List<String>) intent.getEntities().get("timeRanges");
                if (timeRanges != null && !timeRanges.isEmpty()) {
                    String timeRangeText = mapTimeRangeToText(timeRanges.get(0));
                    if (!timeRangeText.isEmpty()) {
                        return String.format("关于您查询的\"%s\"，暂无相关数据。", timeRangeText);
                    }
                }
                return "根据您的查询条件，暂无相关数据。";
            }

            for (int i = 0; i < headers.size(); i++) {
                String header = headers.get(i);
                Object value = values.get(i);
                if (metricNamesFromIntent.contains(header)) {
                    metricParts.add(String.format("%s为%s", header, formatValue(value)));
                } else {
                    dimensionParts.add(formatValue(value));
                }
            }

            if (metricParts.isEmpty() && !dimensionParts.isEmpty()) {
                StringBuilder sb = new StringBuilder("查询结果: ");
                for (int i = 0; i < headers.size(); i++) {
                    sb.append(String.format("%s = %s", headers.get(i), formatValue(values.get(i))));
                    if (i < headers.size() - 1) {
                        sb.append(", ");
                    }
                }
                return sb.toString();
            }

            StringBuilder sentence = new StringBuilder();
            if (!dimensionParts.isEmpty()) {
                sentence.append(String.join(" ", dimensionParts)).append("的");
            }
            sentence.append(String.join("，", metricParts));

            String baseSentence = sentence.toString();

            List<String> timeRanges = (List<String>) intent.getEntities().get("timeRanges");
            if (timeRanges != null && !timeRanges.isEmpty()) {
                String timeRangeText = mapTimeRangeToText(timeRanges.get(0));
                if (!timeRangeText.isEmpty()) {
                    return String.format("关于您查询的\"%s\"，%s。", timeRangeText, baseSentence);
                }
            }
            return baseSentence + "。";
        } else {
            try {
                String summary = String.format("好的，已为您查到%d条相关记录。", rowCount);
                String userQuery = intent.getInternal_context() != null
                        ? intent.getInternal_context().getOriginalQuery()
                        : "";
                String dataInsights = dataAnalysisChain.generateDataInsights(queryResult, userQuery);
                if (dataInsights != null && !dataInsights.trim().isEmpty()) {
                    return summary + "\n\n" + dataInsights;
                } else {
                    return summary;
                }
            } catch (Exception e) {
                log.error("生成数据洞察失败，使用默认消息", e);
                return String.format("已为您查询到 %d 条数据。", rowCount);
            }
        }
    }

    private AgentResponse createNoDataResponse() {
        AgentResponse response = new AgentResponse();
        response.setResponseType(ResponseType.NO_DATA);
        response.setMessageToUser("根据您的查询条件，我没有找到相关数据。您可以尝试调整筛选条件或查询范围。");
        DisplaySuggestion suggestion = new DisplaySuggestion();
        suggestion.setDisplayStrategy("table_only");
        suggestion.setTitle("无数据");
        response.setDisplaySuggestion(suggestion);
        return response;
    }

    private AgentResponse createErrorResponse(String errorMessage, StructuredQueryIntent intent) {
        AgentResponse response = new AgentResponse();
        response.setResponseType(ResponseType.ERROR);
        response.setMessageToUser("处理您的查询时遇到了问题：" + errorMessage);
        DisplaySuggestion suggestion = new DisplaySuggestion();
        suggestion.setDisplayStrategy("table_only");
        suggestion.setTitle("错误");
        response.setDisplaySuggestion(suggestion);
        if (intent != null && intent.getInternal_context() != null) {
            // response.setOriginalQuery(intent.getInternal_context().getOriginalQuery());
            // // 暂时注释掉，在新设计中不需要
        }
        return response;
    }

    private String formatValue(Object value) {
        if (value == null)
            return "null";
        if (value instanceof Number) {
            Number num = (Number) value;
            if (num.doubleValue() == num.longValue()) {
                return String.format("%,d", num.longValue());
            } else {
                return String.format("%,.2f", num.doubleValue());
            }
        }
        return value.toString();
    }

    private String mapTimeRangeToText(String timeRange) {
        if (timeRange == null)
            return "";
        switch (timeRange) {
            case "last_year":
                return "去年";
            case "this_year":
                return "今年";
            case "last_month":
                return "上个月";
            case "this_month":
                return "本月";
            case "last_7_days":
                return "过去7天";
            case "last_30_days":
                return "过去30天";
            case "past_year":
            case "past_12_months":
                return "过去一年";
            case "recent_year":
                return "最近一年";
            default:
                return "";
        }
    }

    private String generateTitle(StructuredQueryIntent intent) {
        // 简单的标题生成逻辑，可以根据需要进行扩展
        if (intent == null) {
            return "查询结果";
        }
        if (intent.getInternal_context() != null
                && StringUtils.hasText(intent.getInternal_context().getMergedContext())) {
            return intent.getInternal_context().getMergedContext();
        }
        if (intent.getInternal_context() != null
                && StringUtils.hasText(intent.getInternal_context().getOriginalQuery())) {
            return intent.getInternal_context().getOriginalQuery();
        }
        return "查询结果";
    }

    private String generateSuggestion(StructuredQueryIntent intent) {
        // 示例：如果这是一个聚合查询，建议进行下钻
        if ("AGGREGATION_QUERY".equals(intent.getQueryType())) {
            // ... a more complex logic to get drillable dimension
            return "您可以尝试按'产品类别'进行下钻分析。";
        }

        return "您可以问我\"按城市查看销售额\"或\"上个月的订单数趋势\"。";
    }
}