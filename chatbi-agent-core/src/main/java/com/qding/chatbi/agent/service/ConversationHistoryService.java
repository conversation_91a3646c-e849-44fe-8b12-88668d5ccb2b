package com.qding.chatbi.agent.service;

import com.qding.chatbi.metadata.entity.ChatMessage;
import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.UserContextDTO;
import com.qding.chatbi.common.dto.UserQueryRequest;

import java.util.List;

public interface ConversationHistoryService {

    ChatMessage saveUserMessage(UserQueryRequest request, UserContextDTO userContext);

    ChatMessage saveAgentResponse(AgentResponse response, UserQueryRequest originalRequest, UserContextDTO userContext,
            String llmModelUsed, long processingTimeMs);

    List<ChatMessage> getHistoryBySessionId(String sessionId, int limit);
}
