package com.qding.chatbi.agent.config;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.listener.ChatModelErrorContext;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.chat.listener.ChatModelRequestContext;
import dev.langchain4j.model.chat.listener.ChatModelResponseContext;
import dev.langchain4j.model.dashscope.QwenChatModel;
import dev.langchain4j.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;
import dev.langchain4j.store.embedding.milvus.MilvusEmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * LangChain4j 配置类
 */
@Configuration
public class LangChainConfig {

    @Value("${langchain4j.dashscope.chat-model.api-key}")
    private String dashscopeApiKey;

    @Value("${langchain4j.dashscope.chat-model.model-name}")
    private String dashscopeModelName;

    @Value("${langchain4j.dashscope.chat-model.temperature}")
    private Double dashscopeTemperature;

    @Value("${langchain4j.dashscope.embedding-model.model-name}")
    private String embeddingModelName;

    @Value("${langchain4j.embedding-store.milvus.host}")
    private String milvusHost;

    @Value("${langchain4j.embedding-store.milvus.port}")
    private Integer milvusPort;

    @Value("${langchain4j.embedding-store.milvus.username:}")
    private String milvusUsername;
    @Value("${langchain4j.embedding-store.milvus.password:}")
    private String milvusPassword;

    @Value("${langchain4j.embedding-store.milvus.collection-name}")
    private String milvusCollectionName;

    @Value("${langchain4j.embedding-store.milvus.dimension}")
    private Integer milvusDimension;

    /**
     * 配置 ChatModelListener 以记录 LLM 交互日志
     */
    @Bean
    public ChatModelListener loggingChatModelListener() {
        return new ChatModelListener() {
            private final Logger log = LoggerFactory.getLogger(ChatModelListener.class);

            @Override
            public void onRequest(ChatModelRequestContext requestContext) {
                log.info("[LLM请求] 消息数量: {}", requestContext.request().messages().size());

                log.info("--- 完整Prompt ---");
                requestContext.request().messages().forEach(message -> {
                    if (message instanceof AiMessage && ((AiMessage) message).hasToolExecutionRequests()) {
                        log.debug("[{}] {}", message.type(), ((AiMessage) message).toolExecutionRequests());
                    } else {
                        log.debug("[{}] {}", message.type(), message.text());
                    }
                });
                log.info("--- 完整Prompt结束 ---");
            }

            @Override
            public void onResponse(ChatModelResponseContext responseContext) {
                log.info("--- LLM完整响应 ---");
                if (responseContext.response() != null && responseContext.response().aiMessage() != null) {
                    AiMessage aiMessage = responseContext.response().aiMessage();

                    boolean hasText = aiMessage.text() != null && !aiMessage.text().isEmpty();
                    boolean hasTools = aiMessage.hasToolExecutionRequests();

                    if (hasText) {
                        log.info("[AI回复] {}", aiMessage.text());
                    }

                    if (hasTools) {
                        log.info("[工具调用] {}", aiMessage.toolExecutionRequests());
                    }

                    if (!hasText && !hasTools) {
                        log.info("[LLM响应] 响应内容为空 (无文本或工具调用)");
                    }

                } else {
                    log.info("[LLM响应] 响应内容为空 (aiMessage为null)");
                }

                // 记录 token 使用情况（如果可用）
                if (responseContext.response() != null && responseContext.response().tokenUsage() != null) {
                    log.info("[Token使用情况] 输入: {}, 输出: {}, 总计: {}", responseContext.response().tokenUsage().inputTokenCount(),
                            responseContext.response().tokenUsage().outputTokenCount(), responseContext.response().tokenUsage().totalTokenCount());
                }
                log.info("--- LLM完整响应结束 ---");
            }

            @Override
            public void onError(ChatModelErrorContext errorContext) {
                log.error("[LLM错误] 错误信息: {}", errorContext.error().getMessage(), errorContext.error());
            }
        };
    }

    @Bean
    public QwenChatModel qwenChatModel(ChatModelListener loggingChatModelListener) {
        return QwenChatModel.builder()
                .apiKey(dashscopeApiKey)
                .modelName(dashscopeModelName)
                .temperature(dashscopeTemperature.floatValue())
                .listeners(List.of(loggingChatModelListener))
                .build();
    }

    @Bean
    @Primary
    public QwenChatModel qwenChatModelForFunctionCalling(ChatModelListener loggingChatModelListener) {
        return QwenChatModel.builder()
                .apiKey(dashscopeApiKey)
                .modelName(dashscopeModelName)
                .temperature(dashscopeTemperature.floatValue())
                .listeners(List.of(loggingChatModelListener))
                .build();
    }

    @Bean
    public EmbeddingModel embeddingModel() {
        return QwenEmbeddingModel.builder()
                .apiKey(dashscopeApiKey)
                .modelName(embeddingModelName)
                .build();
    }

    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        return MilvusEmbeddingStore.builder()
                .host(milvusHost)
                .port(milvusPort)
                .username(milvusUsername)
                .password(milvusPassword)
                .collectionName(milvusCollectionName)
                .dimension(milvusDimension)
                .build();
    }

    @Bean
    public EmbeddingStoreIngestor embeddingStoreIngestor(EmbeddingStore<TextSegment> embeddingStore,
            EmbeddingModel embeddingModel) {
        return EmbeddingStoreIngestor.builder()
                .embeddingStore(embeddingStore)
                .embeddingModel(embeddingModel)
                .build();
    }
}
