package com.qding.chatbi.agent.service.impl;

import com.google.common.collect.Maps;
import com.qding.chatbi.agent.service.ConversationHistoryService;
import com.qding.chatbi.agent.service.ConversationManager;
import com.qding.chatbi.agent.service.UserRoleService;
import com.qding.chatbi.api.service.ConversationApi;
import com.qding.chatbi.common.dto.*;
import com.qding.chatbi.common.enums.ErrorCode;
import com.qding.chatbi.common.enums.MessageType;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import com.qding.chatbi.common.util.StringUtil;
import com.qding.chatbi.metadata.entity.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ChatBI对话服务实现类
 * 
 * 职责：
 * 1. 作为对话系统的门面服务，接收HTTP请求
 * 2. 负责用户身份验证和角色权限管理
 * 3. 构建用户上下文UserContextDTO
 * 4. 调用ConversationManager进行实际的对话处理
 * 5. 保存对话历史记录
 * 
 * 调试说明：
 * - 使用 ========== 分隔不同阶段的日志，便于快速定位
 * - 在每个关键数据传递点记录详细信息
 * - 所有异常都会记录完整的堆栈信息
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
@Service
@Slf4j
public class ConversationServiceImpl implements ConversationApi {

    private final ConversationManager conversationManager;
    private final ConversationHistoryService conversationHistoryService;
    private final UserRoleService userRoleService;

    @Autowired
    public ConversationServiceImpl(ConversationManager conversationManager,
            ConversationHistoryService conversationHistoryService,
            UserRoleService userRoleService) {
        this.conversationManager = conversationManager;
        this.conversationHistoryService = conversationHistoryService;
        this.userRoleService = userRoleService;
        log.info(
                "ConversationServiceImpl初始化完成，已注入依赖: ConversationManager={}, ConversationHistoryService={}, UserRoleService={}",
                conversationManager.getClass().getSimpleName(),
                conversationHistoryService.getClass().getSimpleName(),
                userRoleService.getClass().getSimpleName());
    }

    /**
     * 处理用户查询请求的主入口方法
     * 
     * 数据流转说明：
     * UserQueryRequest -> UserContextDTO -> ConversationManager -> AgentResponse
     * 
     * @param request 用户查询请求，包含用户ID、查询文本、会话ID等信息
     * @return AgentResponse 包含AI响应结果的对象
     * @throws ChatBiException 业务异常
     */
    @Override
    public AgentResponse processUserQuery(UserQueryRequest request) throws ChatBiException {
        log.info("🔄 处理用户查询: '{}' (用户: {}, 会话: {})", request.getQueryText(), request.getUserId(), request.getSessionId());

        // 🔍 输入验证
        if (request == null || StringUtil.isBlank(request.getUserId()) || StringUtil.isBlank(request.getQueryText())) {
            log.warn("❌ 收到无效的UserQueryRequest。必须包含userId和queryText。请求详情: {}", request);
            throw new ChatBiException(ErrorCode.INVALID_INPUT, "无效的请求：用户ID和查询内容不能为空。");
        }

        // 🏷️ 生成或使用现有的会话ID
        String sessionId = StringUtil.isBlank(request.getSessionId()) ? UUID.randomUUID().toString() : request.getSessionId();
        request.setSessionId(sessionId);

        // 👤 获取用户角色

        List<String> userRoles;

        // 首先检查 additionalParams 中是否有用户角色（用于测试目的）
        String roleFromParams = null;
        if (request.getAdditionalParams() != null && request.getAdditionalParams().containsKey("userRole")) {
            Object roleObj = request.getAdditionalParams().get("userRole");
            if (roleObj instanceof String) {
                roleFromParams = (String) roleObj;
            }
        }

        if (roleFromParams != null && !roleFromParams.trim().isEmpty()) {
            // 使用请求参数中的角色（测试模式）
            userRoles = Collections.singletonList(roleFromParams.trim());
        } else {
            // 使用 UserRoleService 获取角色（正常模式）
            try {
                userRoles = userRoleService.getRolesForUser(request.getUserId());
                if (userRoles == null || userRoles.isEmpty()) {
                    log.warn("⚠️ 用户 {} 未获取到任何角色", request.getUserId());
                    userRoles = Collections.emptyList();
                }
            } catch (Exception e) {
                log.error("❌ 获取用户角色失败，用户ID: {}, 错误: {}", request.getUserId(), e.getMessage(), e);
                throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "获取用户角色信息失败。");
            }
        }

        // 🏗️ 构建用户上下文
        // 由于用户只有一个角色，从角色列表中取第一个角色
        String singleUserRole = userRoles.isEmpty() ? null : userRoles.get(0);
        UserContextDTO userContext = new UserContextDTO(request.getUserId(), singleUserRole, sessionId, Maps.newHashMap());

        try {
            // 💾 保存用户消息到历史记录
            conversationHistoryService.saveUserMessage(request, userContext);
        } catch (Exception e) {
            log.error("❌ 保存用户消息失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            // 继续处理，不因为历史记录保存失败而中断
        }

        // 🤖 调用ConversationManager处理对话
        AgentResponse agentResponse;
        long startTime = System.currentTimeMillis();
        String llmModelUsed = null;
        try {
            agentResponse = conversationManager.processConversationTurn(request, userContext);
            long processingTime = System.currentTimeMillis() - startTime;
            log.info("✅ ConversationManager处理完成: {}ms, 响应类型: {}", processingTime, agentResponse.getResponseType());
            // 📊 记录诊断信息中的LLM模型
            if (agentResponse.getDiagnosticInfo() != null) {
                Object model = agentResponse.getDiagnosticInfo().get("llmModel");
                if (model instanceof String) {
                    llmModelUsed = (String) model;
                }
            }

            // 📈 记录查询结果概要信息
            if (agentResponse.getQueryResult() != null) {
                QueryResult result = agentResponse.getQueryResult();
                if (result.getErrorMessage() != null) {
                    log.warn("查询执行错误: {}", result.getErrorMessage());
                }
            }

        } catch (ChatBiException e) {
            log.error("❌ ConversationManager处理时发生已知业务异常，会话 {}: {}", sessionId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("❌ ConversationManager处理时发生未知异常，会话 {}: {}", sessionId, e.getMessage(), e);
            throw new ChatBiException(ErrorCode.INTERNAL_SERVER_ERROR, "抱歉，系统内部发生未知错误。");
        }

        long processingTimeMs = System.currentTimeMillis() - startTime;
        agentResponse.setSessionId(sessionId);

        // 💾 **修复：在处理完成后、保存AI响应之前，保存用户消息**
        try {
            conversationHistoryService.saveUserMessage(request, userContext);
        } catch (Exception e) {
            log.error("❌ 保存用户消息失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            // 继续处理，不因为历史记录保存失败而中断
        }

        // 💾 保存AI响应到历史记录
        try {
            conversationHistoryService.saveAgentResponse(agentResponse, request, userContext, llmModelUsed, processingTimeMs);
        } catch (Exception e) {
            log.error("❌ 保存AI响应失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            // 继续处理，不因为历史记录保存失败而中断
        }

        log.info("🎯 查询处理完成: {}ms, 响应类型: {}", processingTimeMs, agentResponse.getResponseType());

        return agentResponse;
    }

    /**
     * 处理用户的澄清响应
     * 
     * @param sessionId 会话ID
     * @param optionId  用户选择的澄清选项ID
     * @param payload   附加数据载荷
     * @return AgentResponse AI的后续响应
     * @throws ChatBiException 业务异常
     */
    @Override
    public AgentResponse processClarificationResponse(String sessionId, String optionId, Map<String, Object> payload)
            throws ChatBiException {
        log.info("========== 处理澄清响应 ==========");
        log.info("📝 澄清响应详情:");
        log.info("   - 会话ID: {}", sessionId);
        log.info("   - 选择的选项ID: {}", optionId);
        log.info("   - 载荷数据: {}", payload);

        String userId = (String) payload.getOrDefault("userId", "default-clarification-user");
        log.info("🔍 从载荷中提取的用户ID: {}", userId);

        UserQueryRequest clarificationRequest = new UserQueryRequest();
        clarificationRequest.setUserId(userId);
        clarificationRequest.setSessionId(sessionId);
        clarificationRequest.setQueryText("用户澄清，选择: " + optionId);

        Map<String, Object> additionalParams = new HashMap<>();
        additionalParams.put("clarifiedOptionId", optionId);
        additionalParams.putAll(payload);
        clarificationRequest.setAdditionalParams(additionalParams);

        log.info("🔄 构建的澄清请求，即将调用processUserQuery进行处理");
        return processUserQuery(clarificationRequest);
    }

    /**
     * 获取会话详情
     * 
     * @param sessionId 会话ID
     * @return SessionInfoDTO 会话信息
     * @throws ChatBiException 业务异常
     */
    @Override
    public SessionInfoDTO getSessionDetails(String sessionId) throws ChatBiException {
        log.info("========== 获取会话详情 ==========");
        log.info("🔍 查询会话ID: {}", sessionId);

        if (StringUtil.isBlank(sessionId)) {
            log.warn("❌ 会话ID为空");
            throw new ChatBiException(ErrorCode.INVALID_INPUT, "会话ID不能为空。");
        }

        List<ChatMessage> history = conversationHistoryService.getHistoryBySessionId(sessionId, Integer.MAX_VALUE);
        if (history == null || history.isEmpty()) {
            log.warn("❌ 会话不存在或无历史记录，会话ID: {}", sessionId);
            throw new ResourceNotFoundException("会话", sessionId);
        }

        log.info("✅ 找到会话历史记录，消息数量: {}", history.size());

        SessionInfoDTO sessionInfo = new SessionInfoDTO();
        sessionInfo.setSessionId(sessionId);
        sessionInfo.setUserId(history.get(0).getUserId());
        sessionInfo.setStartTime(history.get(0).getMessageTimestamp());
        sessionInfo.setLastActivityTime(history.get(history.size() - 1).getMessageTimestamp());
        sessionInfo.setMessageCount(history.size());

        List<ChatMessageDTO> messageDTOs = history.stream()
                .map(this::convertChatMessageEntityToDTO)
                .collect(Collectors.toList());
        sessionInfo.setMessages(messageDTOs);

        return sessionInfo;
    }

    private ChatMessageDTO convertChatMessageEntityToDTO(ChatMessage entity) {
        ChatMessageDTO dto = new ChatMessageDTO();
        dto.setId(entity.getId().toString());

        if ("USER".equalsIgnoreCase(entity.getSenderType())) {
            dto.setSender(ChatMessageDTO.Sender.USER);
        } else {
            dto.setSender(ChatMessageDTO.Sender.BOT);
        }

        dto.setContent(entity.getMessageText());

        try {
            if (entity.getResponseType() != null) {
                dto.setMessageType(MessageType.valueOf(entity.getResponseType()));
            }
        } catch (IllegalArgumentException e) {
            // Log or handle the case where responseType string doesn't match any enum
            // constant
            dto.setMessageType(MessageType.TEXT); // Fallback to a default
        }

        dto.setCreateTime(entity.getCreatedAt().getTime());
        // Note: RawData conversion logic would be needed if it were used.
        return dto;
    }
}