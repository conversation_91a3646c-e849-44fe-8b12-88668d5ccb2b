package com.qding.chatbi.agent.config;

import com.qding.chatbi.agent.service.NluToolAgent;
import com.qding.chatbi.agent.tool.KnowledgeBaseTools;
import com.qding.chatbi.agent.tool.DataAccessTools;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.service.AiServices;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * NLU Agent 配置类
 * 采用自定义资源加载模式，避免LangChain4j的fromResource问题
 */
@Slf4j
@Configuration
public class NluAgentConfig {

    @Bean
    public String nluSystemPromptTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource("prompts/nlu_system_prompt_template.txt");
            String template = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            log.info("✅ 成功加载NLU系统提示词模板，长度: {}", template.length());
            return template;
        } catch (IOException e) {
            log.error("❌ 加载NLU系统提示词模板失败", e);
            throw new RuntimeException("无法加载NLU系统提示词模板", e);
        }
    }

    @Bean
    public NluToolAgent nluToolAgent(ChatLanguageModel qwenChatModelForFunctionCalling,
            KnowledgeBaseTools knowledgeBaseTools,
            DataAccessTools dataAccessTools,
            String nluSystemPromptTemplate) {

        log.info("🔧 初始化NLU Agent - 使用自定义提示词加载模式");
        log.info("   ✅ 支持运行时模板变量注入");
        log.info("   ✅ 模块化提示词架构");
        log.info("   ✅ Function Calling工具集成");
        log.info("   ✅ 自定义资源加载器");

        return AiServices.builder(NluToolAgent.class)
                .chatLanguageModel(qwenChatModelForFunctionCalling)
                .tools(knowledgeBaseTools, dataAccessTools)
                .systemMessageProvider(chatMemoryId -> nluSystemPromptTemplate)
                .build();
    }
}