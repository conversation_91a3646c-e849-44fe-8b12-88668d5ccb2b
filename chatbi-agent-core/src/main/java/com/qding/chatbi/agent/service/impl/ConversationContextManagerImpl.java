package com.qding.chatbi.agent.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.agent.service.ConversationContextManager;
import com.qding.chatbi.agent.service.ConversationHistoryService;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.metadata.entity.ChatMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对话上下文管理器实现
 * 使用内存缓存 + 数据库持久化的方式管理上下文
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ConversationContextManagerImpl implements ConversationContextManager {

    private final ConversationHistoryService historyService;
    private final ObjectMapper objectMapper;

    // 临时存储最近的意图，避免频繁查询数据库
    private final ConcurrentHashMap<String, StructuredQueryIntent> recentIntents = new ConcurrentHashMap<>();

    @Override
    @Cacheable(value = "queryIntents", key = "#sessionId")
    public StructuredQueryIntent getLastQueryIntent(String sessionId) {
        // 先从缓存查找
        StructuredQueryIntent cachedIntent = recentIntents.get(sessionId);
        if (cachedIntent != null) {
            log.debug("从缓存获取到最近的查询意图: {}", cachedIntent.getIntent());
            return cachedIntent;
        }

        // 从数据库查找最近的AI响应
        List<ChatMessage> history = historyService.getHistoryBySessionId(sessionId, 10);

        for (ChatMessage message : history) {
            if ("AI_AGENT".equals(message.getSenderType()) && message.getStructuredResponseData() != null && !message.getStructuredResponseData().isEmpty()) {

                try {
                    // 尝试解析structured_response_data中的intent
                    String structuredData = message.getStructuredResponseData();

                    // 检查是否包含intent信息
                    if (structuredData.contains("intent") && structuredData.contains("entities")) {
                        StructuredQueryIntent intent = objectMapper.readValue(structuredData, StructuredQueryIntent.class);

                        // 只返回非澄清的意图
                        if (!"CLARIFICATION_NEEDED".equals(intent.getIntent())) {
                            log.info("从历史记录中找到最近的查询意图: {}", intent.getIntent());
                            return intent;
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析历史意图失败: {}", e.getMessage());
                }
            }
        }

        log.info("未找到历史查询意图，会话ID: {}", sessionId);
        return null;
    }

    @Override
    @CacheEvict(value = "queryIntents", key = "#sessionId")
    public void saveQueryIntent(String sessionId, StructuredQueryIntent intent) {
        if (intent == null || sessionId == null) {
            return;
        }

        // 保存到内存缓存
        recentIntents.put(sessionId, intent);

        // 限制缓存大小，避免内存泄漏
        if (recentIntents.size() > 1000) {
            // 简单的LRU策略：删除最早的条目
            String oldestKey = recentIntents.keySet().iterator().next();
            recentIntents.remove(oldestKey);
        }

        log.info("保存查询意图到缓存: sessionId={}, intent={}", sessionId, intent.getIntent());
    }

    @Override
    public List<ChatMessage> getRecentHistory(String sessionId, int limit) {
        return historyService.getHistoryBySessionId(sessionId, limit);
    }

    @Override
    @CacheEvict(value = "queryIntents", key = "#sessionId")
    public void clearContext(String sessionId) {
        recentIntents.remove(sessionId);
        log.info("清理会话上下文: {}", sessionId);
    }
}