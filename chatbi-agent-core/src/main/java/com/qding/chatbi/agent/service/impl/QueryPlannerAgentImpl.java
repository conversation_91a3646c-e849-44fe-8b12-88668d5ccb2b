package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.chain.SqlGenerationChain;
import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.common.dto.StructuredQueryIntent.InternalContext;
import com.qding.chatbi.agent.service.QueryPlannerAgent;
import com.qding.chatbi.agent.tool.DatasetTools;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.UserContextDTO;
import com.qding.chatbi.common.dto.ColumnMetadata;
import com.qding.chatbi.common.enums.ErrorCode;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.agent.util.ColumnMappingUtil;
import com.qding.chatbi.agent.util.StructuredIntentValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Map;
import java.util.HashMap;

// Placeholder for PromptTemplateService if not defined elsewhere
// interface PromptTemplateService { String getPrompt(String key, Map<String, Object> variables); }

// Placeholder for JsonUtil if not defined elsewhere
// class JsonUtil { public static <T> T fromJson(String json, Class<T> clazz) { return null; } public static String toJson(Object obj) { return ""; }}

// Placeholder for ChatLanguageModel if not defined elsewhere
// interface ChatLanguageModel { String generate(String prompt); }

// Placeholder for DatasetTools
// class DatasetTools { public String findRelevantDataset(String query) { return "{\"datasetId\": 1, \"databaseSourceId\": \"db1\", \"type\": \"POSTGRESQL\"}"; } }

/**
 * 查询规划代理实现类
 * 
 * 职责：
 * 1. 接收StructuredQueryIntent（结构化查询意图）
 * 2. 选择最合适的数据集
 * 3. 调用SqlGenerationChain生成SQL查询语句
 * 4. 验证生成的SQL的安全性和正确性
 * 5. 返回包含SQL和数据源信息的QueryPlan对象
 * 
 * 数据流转：
 * StructuredQueryIntent -> 数据集选择 -> SQL生成 -> SQL验证 -> QueryPlan
 * 
 * 调试说明：
 * - 记录接收到的意图信息和用户上下文
 * - 记录数据集选择过程和结果
 * - 记录SQL生成的输入参数和输出结果
 * - 记录SQL验证的详细过程
 * - 所有异常都会记录完整的上下文信息
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
@Service
@Slf4j
public class QueryPlannerAgentImpl implements QueryPlannerAgent {

    @Autowired
    private DatasetTools datasetTools;

    @Autowired
    private SqlGenerationChain sqlGenerationChain;

    // 定义一个正则表达式来屏蔽危险的SQL关键字 (增加了对分号和注释的考虑)
    private static final Pattern DANGEROUS_SQL_PATTERN = Pattern.compile(
            "--|/\\*|\\*/|(DROP|DELETE|UPDATE|INSERT|TRUNCATE|ALTER|CREATE|EXEC|EXECUTE)\\b",
            Pattern.CASE_INSENSITIVE);

    // Dependencies as per the design document
    // private final DatasetTools datasetTools;

    // Optional dependencies for more advanced, LLM-based planning
    // private final ChatLanguageModel chatLanguageModel;
    // private final PromptTemplateService promptTemplateService;

    public QueryPlannerAgentImpl(DatasetTools datasetTools) {
        this.datasetTools = datasetTools;
        log.debug("QueryPlannerAgentImpl初始化完成");
    }

    /**
     * 执行查询规划和SQL生成
     * 
     * 优化后的职责：
     * 1. 验证NLU的决策结果
     * 2. 获取数据集详细信息（基于NLU确定的ID）
     * 3. 权限二次验证
     * 4. 调用SQL生成（直接使用NLU的业务决策）
     * 5. 基础安全验证
     * 
     * @param intent      结构化查询意图（已包含所有业务决策）
     * @param userContext 用户上下文，包含用户ID和权限信息
     * @return QueryPlan 包含SQL和执行信息的查询计划
     * @throws ChatBiException 当规划失败时抛出业务异常
     */
    @Override
    public QueryPlan planAndGenerateSql(StructuredQueryIntent intent, UserContextDTO userContext) {
        log.debug("开始查询规划: 查询='{}'", intent != null && intent.getInternal_context() != null ? intent.getInternal_context().getOriginalQuery() : "NULL");

        long startTime = System.currentTimeMillis();
        StringBuilder thoughtProcess = new StringBuilder("Starting optimized query planning v3.0...\n");

        // ========== 阶段1: 验证NLU决策结果 ==========
        thoughtProcess.append("Step 1: Validating NLU decisions...\n");

        if (intent == null || !"DATA_QUERY".equals(intent.getIntent())) {
            log.error("接收到非数据查询意图: {}", intent != null ? intent.getIntent() : "NULL");
            throw new ChatBiException(ErrorCode.INVALID_INPUT, "非数据查询意图，无需生成SQL");
        }

        // 若缺少时间过滤等关键信息 -> 继续澄清
        /*
         * if (!StructuredIntentValidator.validate(intent)) {
         * throw new ChatBiException(ErrorCode.INVALID_INPUT, "查询缺少时间范围或关键信息，需要澄清");
         * }
         */

        if (intent.getTargetDatasetId() == null) {
            log.error("NLU未确定目标数据集，置信度: {}", intent.getDatasetMatchConfidence());
            throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, "NLU未能确定目标数据集，无法生成查询计划");
        }

        // 🎯 保留：NLU决策信息对提示词调试很重要
        log.info("【提示词输入】NLU决策信息:");
        log.info("  查询类型: {}, 需要聚合: {}, 聚合粒度: {}", intent.getQueryType(), intent.isAggregationRequired(), intent.getExpectedGranularity());

        thoughtProcess.append(String.format(
                "Step 1 PASSED: NLU provided clear decisions - queryType=%s, aggregation=%s, targetDataset=%d\n",
                intent.getQueryType(), intent.isAggregationRequired(), intent.getTargetDatasetId()));

        // ========== 阶段2: 获取数据集详细信息 ==========
        thoughtProcess.append("Step 2: Retrieving dataset details based on NLU selection...\n");

        DatasetInfoDTO bestDataset;
        try {
            // 通过用户权限获取所有候选数据集，然后找到NLU指定的数据集
            String userRole = userContext != null ? userContext.getUserRole() : null;
            List<DatasetInfoDTO> candidateDatasets = datasetTools.getCandidateDatasets(userRole);

            // 从候选数据集中找到NLU指定的数据集ID
            bestDataset = candidateDatasets.stream()
                    .filter(dataset -> dataset.getDatasetId().equals(intent.getTargetDatasetId()))
                    .findFirst()
                    .orElse(null);

            if (bestDataset == null) {
                log.error("无法获取数据集ID: {}，候选数量: {}", intent.getTargetDatasetId(), candidateDatasets.size());
                throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, "指定的数据集不存在或无权访问");
            }
        } catch (Exception e) {
            log.error("获取数据集信息异常: {}", e.getMessage());
            throw new ChatBiException(ErrorCode.RESOURCE_NOT_FOUND, "无法获取指定数据集的详细信息: " + e.getMessage());
        }

        log.debug("数据集: {}, 表: {}, 数据库: {}", bestDataset.getDatasetName(), bestDataset.getTableName(), bestDataset.getDatabaseType());

        thoughtProcess.append(String.format("Step 2 PASSED: Retrieved dataset '%s' (ID: %d) successfully.\n",
                bestDataset.getDatasetName(), bestDataset.getDatasetId()));

        // ========== 阶段3: 权限二次验证 ==========
        thoughtProcess.append("Step 3: Performing secondary permission validation...\n");

        String userRole = userContext != null ? userContext.getUserRole() : null;
        if (!hasDatasetPermission(bestDataset, userRole)) {
            log.error("权限验证失败: 用户角色={}, 数据集ID={}", userRole, bestDataset.getDatasetId());
            throw new ChatBiException(ErrorCode.PERMISSION_DENIED, "当前用户无权访问指定数据集");
        }

        thoughtProcess.append("Step 3 PASSED: User has permission to access the dataset.\n");

        // ========== 阶段4: SQL生成（直接使用NLU决策） ==========
        thoughtProcess.append("Step 4: Generating SQL using NLU business decisions...\n");
        String databaseType = bestDataset.getDatabaseType() != null ? bestDataset.getDatabaseType().toString()
                : "MYSQL";

        // 🛡️ 防护措施：确保userQuestion不为null
        String userQuestion = (intent.getInternal_context() != null
                && intent.getInternal_context().getMergedContext() != null)
                        ? intent.getInternal_context().getMergedContext()
                        : "用户：";

        SqlGenerationChain.SqlGenerationContext context = SqlGenerationChain.SqlGenerationContext.builder()
                .dbDialect(databaseType)
                .dataset(bestDataset)
                .userQuestion(userQuestion)
                .structuredIntent(intent) // 包含了NLU的所有业务决策
                .currentDate(java.time.Instant.now().toString()) // 显式传递当前日期, 格式为 ISO 8601 UTC
                .relevantExamples(Collections.emptyList()) // TODO: 从知识库中检索相关查询范例
                .build();

        // 🎯 保留：SQL生成上下文对提示词调试很重要
        log.info("【提示词输入】SQL生成上下文:");
        log.info("  数据库: {}, 表: {}, 用户问题: '{}'", databaseType, bestDataset.getTableName(), userQuestion);
        log.info("  查询类型: {}, 需要聚合: {}, 聚合粒度: {}", intent.getQueryType(), intent.isAggregationRequired(), intent.getExpectedGranularity());

        String generatedSql = sqlGenerationChain.execute(context);
        thoughtProcess.append("Step 4 PASSED: SQL generated successfully using NLU business decisions.\n");

        // 🎯 保留：LLM生成的SQL对提示词调试很重要
        log.info("【提示词输出】LLM生成的SQL:");
        log.info("  {}", generatedSql != null ? generatedSql.replace("\n", " ").replaceAll("\\s+", " ") : "NULL");

        // ========== 阶段5: 基础安全验证 ==========
        thoughtProcess.append("Step 5: Performing basic SQL security validation...\n");

        try {
            validateSql(generatedSql, bestDataset);
            thoughtProcess.append("Step 5 PASSED: SQL validation passed. The query is safe to execute.\n");
        } catch (Exception e) {
            log.error("SQL验证失败: {}, SQL: {}", e.getMessage(), generatedSql);
            throw e; // 重新抛出异常
        }

        // ========== 阶段6: 构建查询计划 ==========
        QueryPlan plan = new QueryPlan();
        plan.setTargetDatasetId(bestDataset.getDatasetId());
        plan.setSql(generatedSql);
        plan.setThoughtProcess(thoughtProcess.toString());
        plan.setTargetDatabaseSourceId(String.valueOf(bestDataset.getDatabaseSourceId()));
        plan.setDatabaseType(bestDataset.getDatabaseType());
        plan.setValid(true);

        // ========== 阶段7: 映射列元数据 ==========
        List<ColumnMetadata> columnMetadata = ColumnMappingUtil.mapColumnsToMetadata(generatedSql,
                bestDataset.getColumns());
        plan.setColumnMetadata(columnMetadata);

        long totalTime = System.currentTimeMillis() - startTime;
        log.debug("查询规划完成，耗时: {}ms", totalTime);

        plan.setOriginalQuery(
                intent.getInternal_context() != null ? intent.getInternal_context().getOriginalQuery() : "未知原始查询");

        return plan;
    }

    /**
     * 简化的权限检查方法
     * （原来的复杂数据集选择逻辑已移除）
     */
    private boolean hasDatasetPermission(DatasetInfoDTO dataset, String userRole) {
        // 简化的权限检查逻辑
        // 实际实现应该调用权限服务进行验证
        return dataset != null && userRole != null;
    }

    /**
     * 对LLM生成的SQL进行安全和基础的正确性验证
     * 
     * @param sql     LLM生成的SQL语句
     * @param dataset 选定的数据集信息
     * @throws ChatBiException 当SQL验证失败时抛出
     */
    private void validateSql(String sql, DatasetInfoDTO dataset) {
        // 验证1: 空值检查
        if (!StringUtils.hasText(sql)) {
            log.error("LLM返回了空的SQL查询");
            throw new ChatBiException(ErrorCode.LLM_ERROR, "LLM返回了空的SQL查询。");
        }

        // 验证2: 安全检查 - 禁止危险关键字、注释和多语句
        Matcher matcher = DANGEROUS_SQL_PATTERN.matcher(sql);
        if (matcher.find()) {
            String detected = matcher.group(1) != null ? matcher.group(1) : matcher.group();
            log.error("检测到危险的SQL关键字: '{}', SQL: {}", detected, sql);
            throw new ChatBiException(ErrorCode.PERMISSION_DENIED,
                    "生成的查询包含潜在危险操作: " + detected);
        }

        // 验证3: 权限检查 - 确保查询只访问了授权的表
        // 使用正则表达式查找所有的FROM子句（包括子查询中的）
        String tableName = dataset.getTableName().toLowerCase();
        String sqlLower = sql.toLowerCase();

        // 检查SQL中是否包含目标表名
        // 这个简单的检查确保SQL至少引用了正确的表
        if (!sqlLower.contains(tableName)) {
            log.error("SQL中未找到期望的表名 '{}': {}", dataset.getTableName(), sql);
            throw new ChatBiException(ErrorCode.PERMISSION_DENIED,
                    "生成的查询未访问指定的数据表。");
        }

        // 使用正则表达式查找所有FROM子句中的表名
        Pattern tablePattern = Pattern.compile("from\\s+`?([a-zA-Z0-9_]+)`?", Pattern.CASE_INSENSITIVE);
        Matcher tableMatcher = tablePattern.matcher(sql);

        boolean foundValidTable = false;
        while (tableMatcher.find()) {
            String foundTable = tableMatcher.group(1);
            if (foundTable.equalsIgnoreCase(dataset.getTableName())) {
                foundValidTable = true;
            } else if (!foundTable.equalsIgnoreCase("ranked") && !foundTable.equalsIgnoreCase("subquery")) {
                // 如果发现了其他表名（不是子查询别名），则报错
                log.error("SQL查询了未授权的表 '{}', 期望的表名: '{}'", foundTable, dataset.getTableName());
                throw new ChatBiException(ErrorCode.PERMISSION_DENIED,
                        "生成的查询试图访问未授权的表: " + foundTable);
            }
        }

        if (!foundValidTable) {
            log.error("SQL中未找到有效的FROM子句引用表 '{}'", dataset.getTableName());
            throw new ChatBiException(ErrorCode.SQL_GENERATION_ERROR,
                    "生成的查询结构无效。");
        }
    }
}
