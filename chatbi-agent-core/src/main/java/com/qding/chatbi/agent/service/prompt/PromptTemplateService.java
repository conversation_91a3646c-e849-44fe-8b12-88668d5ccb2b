package com.qding.chatbi.agent.service.prompt;

import dev.langchain4j.model.input.PromptTemplate;

/**
 * Prompt模板服务接口。
 * 负责加载、缓存和提供具名的Prompt模板。
 */
public interface PromptTemplateService {

    /**
     * 根据模板名称获取一个PromptTemplate实例。
     *
     * @param templateName 模板的名称（通常对应于 resources/prompts/ 目录下的文件名，不含后缀）。
     * @return 一个可用的 PromptTemplate 实例。
     * @throws RuntimeException 如果模板文件未找到或加载失败。
     */
    PromptTemplate getPromptTemplate(String templateName);
} 