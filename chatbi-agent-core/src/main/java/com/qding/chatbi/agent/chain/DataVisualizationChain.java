package com.qding.chatbi.agent.chain;

import com.qding.chatbi.agent.service.prompt.PromptTemplateService;
import com.qding.chatbi.common.dto.DataVisualizationConfig;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.util.JsonUtil;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据可视化链服务
 * 统一处理表格和图表的生成，通过一次AI调用完成
 */
@Service
@Slf4j
public class DataVisualizationChain {

    private final ChatLanguageModel chatLanguageModel;
    private final PromptTemplateService promptTemplateService;

    @Autowired
    public DataVisualizationChain(@Qualifier("qwenChatModelForFunctionCalling") ChatLanguageModel chatLanguageModel,
            PromptTemplateService promptTemplateService) {
        this.chatLanguageModel = chatLanguageModel;
        this.promptTemplateService = promptTemplateService;
        log.info("DataVisualizationChain初始化完成，使用ChatLanguageModel: {}", chatLanguageModel.getClass().getSimpleName());
    }

    /**
     * 生成数据可视化配置（表格+图表）
     * 
     * @param queryResult 查询结果
     * @param userQuery   用户查询
     * @return 数据可视化配置
     */
    public DataVisualizationConfig generateVisualizationConfig(QueryResult queryResult, String userQuery) {
        if (queryResult == null || queryResult.getRows() == null || queryResult.getRows().isEmpty()) {
            log.info("查询结果为空，返回空配置。");
            return createEmptyConfig();
        }

        // 检查是否为单值结果
        if (queryResult.getRows().size() == 1 && queryResult.getRows().get(0).size() == 1) {
            log.info("检测到单值结果，使用纯文本展示策略");
            return createTextOnlyConfig(userQuery);
        }

        try {
            log.info("开始生成数据可视化配置...");
            log.info("用户查询: {}", userQuery);
            log.info("数据行数: {}, 列数: {}", queryResult.getRows().size(), queryResult.getColumnHeaders().size());

            // 构建提示词变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("user_query", userQuery);
            variables.put("query_result_json", JsonUtil.toJson(queryResult));
            variables.put("row_count", queryResult.getRows().size());
            variables.put("column_count", queryResult.getColumnHeaders().size());
            variables.put("columns", queryResult.getColumnHeaders());

            // 分析数据类型
            String dataTypeAnalysis = analyzeDataTypes(queryResult);
            variables.put("data_type_analysis", dataTypeAnalysis);

            // 获取提示词模板并生成配置
            PromptTemplate promptTemplate = promptTemplateService.getPromptTemplate("data_visualization");
            Prompt prompt = promptTemplate.apply(variables);
            String aiResponse = chatLanguageModel.generate(prompt.text());

            log.info("AI成功生成数据可视化配置");
            log.debug("AI响应: {}", aiResponse);

            // 清理并解析AI响应
            String cleanedResponse = cleanJsonResponse(aiResponse);
            log.debug("清理后的响应: {}", cleanedResponse);

            // 解析AI生成的配置
            return parseAiResponse(cleanedResponse, userQuery);

        } catch (Exception e) {
            log.error("生成数据可视化配置时发生错误", e);
            return createErrorConfig("生成配置失败: " + e.getMessage());
        }
    }

    /**
     * 分析数据类型分布
     */
    private String analyzeDataTypes(QueryResult queryResult) {
        if (queryResult.getColumnMetadata() == null || queryResult.getColumnMetadata().isEmpty()) {
            return "无列元数据";
        }

        Map<String, Integer> typeCount = new HashMap<>();
        queryResult.getColumnMetadata().forEach(meta -> {
            String type = meta.getDataType().toString();
            typeCount.put(type, typeCount.getOrDefault(type, 0) + 1);
        });

        return JsonUtil.toJson(typeCount);
    }

    /**
     * 清理AI返回的JSON响应
     */
    private String cleanJsonResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return response;
        }

        String cleaned = response.trim();

        // 去除markdown代码块标记
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7).trim();
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3).trim();
        }

        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3).trim();
        }

        // 移除JavaScript函数（这是JSON解析失败的主要原因）
        cleaned = removeJavaScriptFunctions(cleaned);

        return cleaned;
    }

    /**
     * 移除JSON中的JavaScript函数
     */
    private String removeJavaScriptFunctions(String json) {
        // 移除包含function关键字的行
        String[] lines = json.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inFunction = false;

        for (String line : lines) {
            String trimmedLine = line.trim();

            // 检测函数开始
            if (trimmedLine.contains("function") && trimmedLine.contains("(")) {
                inFunction = true;
                // 跳过这一行，但保留逗号（如果有的话）
                if (result.length() > 0) {
                    // 移除最后的逗号，避免JSON语法错误
                    String lastLine = result.toString().trim();
                    if (lastLine.endsWith(",")) {
                        result.setLength(result.length() - 1);
                    }
                }
                continue;
            }

            // 检测函数结束
            if (inFunction && trimmedLine.contains("}")) {
                inFunction = false;
                continue;
            }

            // 如果不在函数内，保留这一行
            if (!inFunction) {
                result.append(line).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 解析AI响应为数据可视化配置
     */
    private DataVisualizationConfig parseAiResponse(String aiResponse, String userQuery) {
        try {
            // 尝试解析为Map
            Map<String, Object> responseMap = JsonUtil.fromJson(aiResponse, Map.class);

            // 检查解析结果是否为空
            if (responseMap == null) {
                log.warn("AI响应解析结果为空，使用默认配置");
                return createDefaultConfig(userQuery);
            }

            DataVisualizationConfig config = new DataVisualizationConfig();
            config.setSuccess(true);
            config.setDisplayStrategy((String) responseMap.get("displayStrategy"));
            config.setTitle((String) responseMap.get("title"));

            // 提取表格配置
            Object tableConfig = responseMap.get("tableConfig");
            if (tableConfig != null) {
                config.setTableConfigJson(JsonUtil.toJson(tableConfig));
            }

            // 提取图表配置
            Object chartConfig = responseMap.get("chartConfig");
            if (chartConfig != null) {
                config.setChartConfigJson(JsonUtil.toJson(chartConfig));
            }

            // 如果没有明确的展示策略，根据配置推断
            if (config.getDisplayStrategy() == null) {
                if (config.getTableConfigJson() != null && config.getChartConfigJson() != null) {
                    config.setDisplayStrategy("both");
                } else if (config.getChartConfigJson() != null) {
                    config.setDisplayStrategy("chart_only");
                } else {
                    config.setDisplayStrategy("table_only");
                }
            }

            // 如果没有标题，使用默认标题
            if (config.getTitle() == null) {
                config.setTitle("查询结果");
            }

            log.info("成功解析数据可视化配置: displayStrategy={}, hasTable={}, hasChart={}",
                    config.getDisplayStrategy(),
                    config.getTableConfigJson() != null,
                    config.getChartConfigJson() != null);

            return config;

        } catch (Exception e) {
            log.error("解析AI响应失败", e);
            return createErrorConfig("解析AI响应失败: " + e.getMessage());
        }
    }

    /**
     * 创建空配置（数据为空时）
     */
    private DataVisualizationConfig createEmptyConfig() {
        DataVisualizationConfig config = new DataVisualizationConfig();
        config.setSuccess(true);
        config.setDisplayStrategy("table_only");
        config.setTitle("无数据");
        return config;
    }

    /**
     * 创建纯文本配置（单值结果时）
     */
    private DataVisualizationConfig createTextOnlyConfig(String userQuery) {
        DataVisualizationConfig config = new DataVisualizationConfig();
        config.setSuccess(true);
        config.setDisplayStrategy("text_only");
        config.setTitle("查询结果");

        log.info("使用纯文本配置: 仅显示文本回答，不显示表格和图表");
        return config;
    }

    /**
     * 创建默认配置（AI响应解析失败时）
     */
    private DataVisualizationConfig createDefaultConfig(String userQuery) {
        DataVisualizationConfig config = new DataVisualizationConfig();
        config.setSuccess(true);
        config.setDisplayStrategy("table_only");
        config.setTitle("查询结果");

        // 创建基本的表格配置
        Map<String, Object> tableConfig = new HashMap<>();
        tableConfig.put("tableType", "standard");
        tableConfig.put("showTotals", Map.of("row", false, "column", false));
        config.setTableConfigJson(JsonUtil.toJson(tableConfig));

        log.info("使用默认配置: 仅显示标准表格");
        return config;
    }

    /**
     * 创建错误配置
     */
    private DataVisualizationConfig createErrorConfig(String errorMessage) {
        DataVisualizationConfig config = new DataVisualizationConfig();
        config.setSuccess(false);
        config.setErrorMessage(errorMessage);
        config.setDisplayStrategy("table_only");
        config.setTitle("数据展示");
        return config;
    }
}