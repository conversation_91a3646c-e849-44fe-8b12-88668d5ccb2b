package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.metadata.entity.ChatMessage;
import com.qding.chatbi.metadata.repository.ChatMessageRepository;
import com.qding.chatbi.agent.service.ConversationHistoryService;
import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.UserContextDTO;
import com.qding.chatbi.common.dto.UserQueryRequest;
import com.qding.chatbi.common.enums.ResponseType;
import com.qding.chatbi.common.util.JsonUtil;
import com.qding.chatbi.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * ConversationHistoryService的实现类。
 * 负责对话历史的持久化和检索。
 */
@Service
@Transactional
public class ConversationHistoryServiceImpl implements ConversationHistoryService {

    private static final Logger log = LoggerFactory.getLogger(ConversationHistoryServiceImpl.class);

    private final ChatMessageRepository chatMessageRepository;

    @Autowired
    public ConversationHistoryServiceImpl(ChatMessageRepository chatMessageRepository) {
        this.chatMessageRepository = chatMessageRepository;
    }

    /**
     * 保存用户的消息到数据库。
     * 
     * @param request     用户的查询请求。
     * @param userContext 用户的上下文信息。
     * @return 已保存的ChatMessage实体。
     */
    @Override
    public ChatMessage saveUserMessage(UserQueryRequest request, UserContextDTO userContext) {

        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setSessionId(userContext.getSessionId());
        chatMessage.setUserId(userContext.getUserId());
        chatMessage.setSenderType("USER");
        chatMessage.setMessageText(request.getQueryText());
        chatMessage.setMessageTimestamp(new Date());
        try {
            ChatMessage savedMessage = chatMessageRepository.save(chatMessage);
            log.debug("用户消息保存成功，消息ID: {}", savedMessage.getId());
            return savedMessage;
        } catch (Exception e) {
            log.error("保存用户消息失败: {}", e.getMessage(), e);
            log.error("失败时的用户上下文: 用户ID={}, 会话ID={}, 角色={}", userContext.getUserId(), userContext.getSessionId(), userContext.getUserRole());
            throw e;
        }
    }

    /**
     * 保存AI Agent的响应到数据库。
     * 
     * @param response         AI的响应。
     * @param originalRequest  用户的原始请求。
     * @param userContext      用户的上下文信息。
     * @param llmModelUsed     使用的LLM模型名称。
     * @param processingTimeMs 处理耗时。
     * @return 已保存的ChatMessage实体。
     */
    @Override
    public ChatMessage saveAgentResponse(AgentResponse response, UserQueryRequest originalRequest,
            UserContextDTO userContext, String llmModelUsed, long processingTimeMs) {
        log.info("========== ConversationHistoryServiceImpl.saveAgentResponse 开始 ==========");
        log.info("保存AI Agent响应到数据库");
        log.info("AI响应详情:");
        log.info("  - 响应类型: {}", response.getResponseType());
        log.info("  - 消息内容: {}", response.getMessageToUser());
        log.info("  - 会话ID: {}", response.getSessionId());
        log.info("  - 错误码: {}", response.getErrorCode());

        log.info("原始用户请求详情:");
        log.info("  - 用户ID: {}", originalRequest.getUserId());
        log.info("  - 查询文本: {}", originalRequest.getQueryText());
        log.info("  - 会话ID: {}", originalRequest.getSessionId());

        log.info("用户上下文详情:");
        log.info("  - 用户ID: {}", userContext.getUserId());
        log.info("  - 用户角色: {}", userContext.getUserRole());
        log.info("  - 会话ID: {}", userContext.getSessionId());
        log.info("  - 客户端信息: {}", userContext.getClientInfo());

        log.info("处理信息:");
        log.info("  - 使用的LLM模型: {}", llmModelUsed);
        log.info("  - 处理耗时: {}ms", processingTimeMs);

        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setSessionId(userContext.getSessionId());
        chatMessage.setUserId(userContext.getUserId());
        chatMessage.setSenderType("AI_AGENT");
        chatMessage.setMessageText(response.getMessageToUser());
        chatMessage.setMessageTimestamp(new Date());
        chatMessage.setResponseType(response.getResponseType() != null ? response.getResponseType().name() : null);
        chatMessage.setErrorCode(response.getErrorCode());
        chatMessage.setLlmModelUsed(llmModelUsed);
        chatMessage.setProcessingDurationMs((int) processingTimeMs);

        // --- 核心逻辑：序列化结构化数据 ---
        log.info("========== 序列化结构化响应数据 ==========");
        String structuredDataJson = null;
        if (response.getResponseType() == ResponseType.DATA && response.getQueryResult() != null) {
            log.info("响应包含查询结果，进行序列化");
            structuredDataJson = JsonUtil.toJson(response.getQueryResult());
            log.info("查询结果序列化完成，JSON长度: {} 字符", structuredDataJson.length());
        } else if (response.getResponseType() == ResponseType.CLARIFICATION_NEEDED
                && response.getClarificationOptions() != null) {
            log.info("响应包含澄清选项，进行序列化");
            structuredDataJson = JsonUtil.toJson(response.getClarificationOptions());
            log.info("澄清选项序列化完成，JSON长度: {} 字符", structuredDataJson.length());
        } else {
            log.info("响应不包含需要序列化的结构化数据");
        }

        // 只有当序列化后的JSON字符串不为空白且不为"null"时才设置到实体中
        if (StringUtil.isNotBlank(structuredDataJson) && !"null".equalsIgnoreCase(structuredDataJson.trim())) {
            chatMessage.setStructuredResponseData(structuredDataJson);
            log.info("结构化响应数据已设置到ChatMessage实体");
        } else {
            chatMessage.setStructuredResponseData(null); // 明确设置为Java null，以便JPA正确插入SQL NULL
            log.info("结构化响应数据为空，设置为null");
        }

        log.info("构建的ChatMessage实体:");
        log.info("  - 会话ID: {}", chatMessage.getSessionId());
        log.info("  - 用户ID: {}", chatMessage.getUserId());
        log.info("  - 发送者类型: {}", chatMessage.getSenderType());
        log.info("  - 消息文本: {}", chatMessage.getMessageText());
        log.info("  - 响应类型: {}", chatMessage.getResponseType());
        log.info("  - 错误码: {}", chatMessage.getErrorCode());
        log.info("  - LLM模型: {}", chatMessage.getLlmModelUsed());
        log.info("  - 处理耗时: {}ms", chatMessage.getProcessingDurationMs());
        log.info("  - 结构化数据长度: {} 字符",
                chatMessage.getStructuredResponseData() != null ? chatMessage.getStructuredResponseData().length() : 0);

        try {
            ChatMessage savedMessage = chatMessageRepository.save(chatMessage);
            log.info("AI响应保存成功，消息ID: {}", savedMessage.getId());
            log.info("========== ConversationHistoryServiceImpl.saveAgentResponse 完成 ==========");
            return savedMessage;
        } catch (Exception e) {
            log.error("保存AI响应失败: {}", e.getMessage(), e);
            log.error("失败时的用户上下文: 用户ID={}, 会话ID={}, 角色={}",
                    userContext.getUserId(), userContext.getSessionId(), userContext.getUserRole());
            throw e;
        }
    }

    /**
     * 根据会话ID获取最近的N条聊天记录。
     * 
     * @param sessionId 会话ID。
     * @param limit     要获取的记录数量。
     * @return ChatMessage实体列表，按时间升序排列（对话的自然顺序）。
     */
    @Override
    public List<ChatMessage> getHistoryBySessionId(String sessionId, int limit) {
        // 核心逻辑：按时间倒序获取最近的N条记录
        // 这是正确的顺序，因为LLM在处理提示时，更关注最新的（即离当前问题最近的）上下文
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "messageTimestamp"));
        List<ChatMessage> recentMessages = chatMessageRepository.findBySessionId(sessionId, pageable).getContent();

        // 直接返回按时间降序排列的列表，无需反转
        return new java.util.ArrayList<>(recentMessages);
    }
}
