package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.StructuredQueryIntent;

import java.util.List;

/**
 * NLU (Natural Language Understanding) Agent
 * 
 * 负责理解用户的自然语言查询，并将其转换为结构化的查询意图。
 * 这是对话系统的第一步，为后续的查询计划和执行提供基础。
 */
public interface NluAgent {

    /**
     * 理解用户的查询意图
     * 
     * @param userQuery         用户的自然语言查询
     * @param sessionId         会话ID，用于获取历史上下文
     * @param chatHistory       对话历史（文本形式，用于兼容）
     * @param availableDatasets 当前用户可访问的数据集列表
     * @param userRole          用户角色
     * @return 结构化的查询意图
     */
    StructuredQueryIntent understand(String userQuery, String sessionId, String chatHistory,
                                     List<DatasetInfoDTO> availableDatasets, String userRole);

    /**
     * 兼容旧接口
     */
    default StructuredQueryIntent understand(String userQuery, String chatHistory,
            List<DatasetInfoDTO> availableDatasets, String userRole) {
        return understand(userQuery, null, chatHistory, availableDatasets, userRole);
    }
}
