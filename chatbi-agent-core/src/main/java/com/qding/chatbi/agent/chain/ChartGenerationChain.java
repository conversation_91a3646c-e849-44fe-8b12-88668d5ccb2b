package com.qding.chatbi.agent.chain;

import com.qding.chatbi.agent.service.prompt.PromptTemplateService;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.util.JsonUtil;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class ChartGenerationChain {

    private final ChatLanguageModel chatLanguageModel;
    private final PromptTemplateService promptTemplateService;

    @Autowired
    public ChartGenerationChain(@Qualifier("qwenChatModelForFunctionCalling") ChatLanguageModel chatLanguageModel,
            PromptTemplateService promptTemplateService) {
        this.chatLanguageModel = chatLanguageModel;
        this.promptTemplateService = promptTemplateService;
        log.info("ChartGenerationChain初始化完成，使用ChatLanguageModel: {}", chatLanguageModel.getClass().getSimpleName());
    }

    public Optional<String> generateChartConfig(QueryResult queryResult, String userQuery) {
        if (queryResult == null || queryResult.getRows() == null || queryResult.getRows().isEmpty()) {
            log.info("查询结果为空，不生成图表配置。");
            return Optional.empty();
        }

        // 单值结果不生成图表
        if (queryResult.getRows().size() == 1 && queryResult.getRows().get(0).size() == 1) {
            log.info("查询结果为单值，不生成图表配置。");
            return Optional.empty();
        }

        try {
            log.info("开始为查询生成图表配置...");
            PromptTemplate promptTemplate = promptTemplateService.getPromptTemplate("chart_generation");
            Map<String, Object> variables = new HashMap<>();
            variables.put("user_query", userQuery);
            variables.put("query_result_json", JsonUtil.toJson(queryResult));

            Prompt prompt = promptTemplate.apply(variables);
            String chartConfigJson = chatLanguageModel.generate(prompt.text());

            log.info("AI成功生成图表配置JSON");
            log.debug("图表配置: {}", chartConfigJson);

            // 清理AI返回的JSON字符串，去除markdown代码块标记
            String cleanedJson = cleanJsonResponse(chartConfigJson);
            log.debug("清理后的图表配置: {}", cleanedJson);

            return Optional.of(cleanedJson);

        } catch (Exception e) {
            log.error("生成图表配置时发生错误", e);
            return Optional.empty();
        }
    }

    /**
     * 清理AI返回的JSON响应，去除markdown代码块标记
     */
    private String cleanJsonResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return response;
        }

        String cleaned = response.trim();

        // 去除开头的 ```json 或 ```
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7).trim();
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3).trim();
        }

        // 去除结尾的 ```
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3).trim();
        }

        return cleaned;
    }
}