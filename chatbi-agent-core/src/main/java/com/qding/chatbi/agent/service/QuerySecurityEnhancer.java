package com.qding.chatbi.agent.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 查询安全增强器
 * 在查询执行前应用行级权限控制
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuerySecurityEnhancer {

    private final RowLevelSecurityManager rowLevelSecurityManager;

    /**
     * 增强查询安全性
     * 在现有的查询流程中调用此方法来应用行级权限
     *
     * @param originalSql 原始SQL查询
     * @param userId 用户ID
     * @param datasetId 数据集ID
     * @return 增强后的SQL查询
     */
    public String enhanceQuerySecurity(String originalSql, String userId, Long datasetId) {
        log.debug("开始增强查询安全性 - 用户: {}, 数据集: {}", userId, datasetId);
        
        try {
            // 应用行级权限控制
            String enhancedSql = rowLevelSecurityManager.enhanceQueryWithRowLevelSecurity(
                originalSql, userId, datasetId);
            
            // 记录安全增强日志
            if (!originalSql.equals(enhancedSql)) {
                log.info("查询已应用行级权限控制 - 用户: {}, 数据集: {}", userId, datasetId);
                log.debug("原始SQL: {}", originalSql);
                log.debug("增强SQL: {}", enhancedSql);
            }
            
            return enhancedSql;
            
        } catch (Exception e) {
            log.error("查询安全增强失败 - 用户: {}, 数据集: {}", userId, datasetId, e);
            // 出错时返回拒绝访问的SQL
            return "SELECT 1 WHERE 1=0";
        }
    }

    /**
     * 验证用户查询权限
     */
    public boolean validateQueryPermission(String userId, Long datasetId) {
        try {
            // 这里可以添加额外的权限验证逻辑
            // 例如：检查用户是否有访问该数据集的基本权限
            
            log.debug("验证用户 {} 对数据集 {} 的查询权限", userId, datasetId);
            return true;
            
        } catch (Exception e) {
            log.error("验证查询权限失败 - 用户: {}, 数据集: {}", userId, datasetId, e);
            return false;
        }
    }

    /**
     * 检查是否需要应用行级权限
     */
    public boolean needsRowLevelSecurity(String userId) {
        return rowLevelSecurityManager.hasRowLevelRestriction(userId);
    }
}
