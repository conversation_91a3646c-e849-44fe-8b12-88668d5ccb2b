package com.qding.chatbi.agent.tool;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.metadata.service.DatasetMetadataService;
import com.qding.chatbi.metadata.service.PermissionService;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Arrays;

import com.qding.chatbi.common.dto.ColumnInfoDTO;

// Placeholder for a potential MetadataServiceClient interface
// interface MetadataServiceClient {
//     String getDatasetSchemaAndPermissions(String datasetNameOrId, List<String> userRoles);
// }

@Component
public class DatasetTools {

    private static final Logger log = LoggerFactory.getLogger(DatasetTools.class);

    private final DatasetMetadataService datasetMetadataService;
    private final PermissionService permissionService;
    private final ObjectMapper objectMapper;

    @Autowired
    public DatasetTools(DatasetMetadataService datasetMetadataService,
            PermissionService permissionService,
            ObjectMapper objectMapper) {
        this.datasetMetadataService = datasetMetadataService;
        this.permissionService = permissionService;
        this.objectMapper = objectMapper;
        log.info("DatasetTools initialized with real services.");
    }

    @Tool("Provides the schema (tables, columns, data types, relationships) and user permissions for a given dataset, or lists all available datasets for the user. Use this to understand data structure before generating a query. If datasetNameOrId is null or empty, returns all available datasets for the user.")
    public String getDatasetSchemaAndPermissions(@P("datasetNameOrId") String datasetNameOrId,
            @P("userRole") String userRole) {
        log.info("🔍 获取数据集权限: '{}'", datasetNameOrId);

        // 防护机制：处理null值
        if (datasetNameOrId == null) {
            log.info("   ℹ️ datasetNameOrId为null，将返回所有可访问的数据集");
            datasetNameOrId = ""; // 转换为空字符串以便后续逻辑处理
        }

        // 防护机制：处理userRole为null的情况
        if (userRole == null || userRole.trim().isEmpty()) {
            log.warn("   ⚠️ userRole为null或空，返回空结果");
            return "[]";
        }

        try {
            // Case 1: A specific dataset is requested
            if (StringUtils.isNotBlank(datasetNameOrId)) {
                DatasetInfoDTO datasetDetails = null;

                // 尝试解析为数字ID，如果是数字则按ID查询，否则按名称查询
                try {
                    Long datasetId = Long.parseLong(datasetNameOrId);
                    log.info("   ℹ️ 检测到数字ID: {}，按ID查询活跃数据集", datasetId);
                    datasetDetails = datasetMetadataService.getActiveDatasetDetails(datasetId);
                } catch (NumberFormatException e) {
                    log.info("   ℹ️ 检测到名称: '{}'，按名称查询活跃数据集", datasetNameOrId);
                    datasetDetails = datasetMetadataService.getActiveDatasetDetailsByName(datasetNameOrId);
                }

                if (datasetDetails != null) {
                    boolean hasAccess = permissionService.hasDatasetAccess(userRole, datasetDetails.getDatasetId());
                    if (hasAccess) {
                        log.info("   ✅ 成功获取活跃数据集信息: {} (ID: {})", datasetDetails.getDatasetName(),
                                datasetDetails.getDatasetId());
                        return objectMapper.writeValueAsString(Collections.singletonList(datasetDetails));
                    } else {
                        log.warn("   ❌ 用户 {} 无权限访问数据集 {}", userRole, datasetNameOrId);
                        return "[]"; // Return empty list if no access
                    }
                } else {
                    log.warn("   ❌ 未找到活跃状态的数据集: '{}'", datasetNameOrId);
                    return "[]"; // Return empty list if not found
                }
            }

            // Case 2: No specific dataset is requested, list all permitted datasets
            List<Long> permittedDatasetIds = permissionService.getPermittedDatasetIds(userRole);
            if (permittedDatasetIds.isEmpty()) {
                log.info("User with role {} has no permitted datasets.", userRole);
                return "[]";
            }

            log.debug("User with role {} has access to dataset IDs: {}", userRole, permittedDatasetIds);

            // 使用活跃状态的查询方法，只返回活跃的数据集和字段
            List<DatasetInfoDTO> permittedDatasets = datasetMetadataService
                    .getActiveDatasetDetailsByIds(permittedDatasetIds);

            String result = objectMapper.writeValueAsString(permittedDatasets);
            log.debug("Returning permitted active datasets for role {}: {}", userRole, result);
            return result;

        } catch (JsonProcessingException e) {
            log.error("Error serializing dataset info to JSON", e);
            return "{\"error\":\"Failed to serialize dataset information.\"}";
        } catch (Exception e) {
            log.error("An unexpected error occurred in getDatasetSchemaAndPermissions", e);
            return "{\"error\":\"An unexpected error occurred while fetching dataset information.\"}";
        }
    }

    /**
     * 根据用户角色获取所有其有权限访问的数据集的详细信息。
     * 
     * @param userRole 用户的角色
     * @return 包含数据集及其所有字段信息的DTO列表
     */
    public List<DatasetInfoDTO> getCandidateDatasets(String userRole) {
        log.info("获取用户角色 {} 的候选数据集", userRole);

        try {
            // 使用实际的服务来获取数据集信息
            if (userRole == null || userRole.trim().isEmpty()) {
                log.warn("用户角色为空，返回空数据集列表");
                return Collections.emptyList();
            }

            // 1. 获取用户有权限访问的数据集ID列表
            List<Long> permittedDatasetIds = permissionService.getPermittedDatasetIds(userRole);
            if (permittedDatasetIds.isEmpty()) {
                log.info("用户角色 {} 没有任何数据集访问权限", userRole);
                return Collections.emptyList();
            }

            log.info("用户角色 {} 有权限访问的数据集ID: {}", userRole, permittedDatasetIds);

            // 2. 根据数据集ID获取活跃状态的详细信息（只包含活跃字段）
            List<DatasetInfoDTO> candidateDatasets = datasetMetadataService
                    .getActiveDatasetDetailsByIds(permittedDatasetIds);

            // 打印每个数据集的基本信息用于调试
            candidateDatasets.forEach(dataset -> {
                log.debug("数据集详情 - ID: {}, 名称: {}, 数据库类型: {}, 表名: {}",
                        dataset.getDatasetId(),
                        dataset.getDatasetName(),
                        dataset.getDatabaseType(),
                        dataset.getTableName());
            });

            return candidateDatasets;

        } catch (Exception e) {
            log.error("获取候选数据集时发生异常", e);

            // 在异常情况下，返回模拟数据以保证系统可用性
            log.warn("降级使用模拟数据集");
            return getDefaultMockDatasets();
        }
    }

    /**
     * 获取默认的模拟数据集（用于异常情况下的降级处理）
     */
    private List<DatasetInfoDTO> getDefaultMockDatasets() {
        log.warn("使用模拟数据集作为后备方案");

        // 模拟字段
        ColumnInfoDTO col1 = new ColumnInfoDTO();
        col1.setColumnId(101L);
        col1.setColumnName("日期");
        col1.setTechnicalNameOrExpression("dt");
        col1.setSemanticType(com.qding.chatbi.common.enums.SemanticType.TIME_DIMENSION);
        col1.setDescription("订单发生的日期");
        col1.setDataType("DATE");
        col1.setIsComputed(false);

        ColumnInfoDTO col2 = new ColumnInfoDTO();
        col2.setColumnId(102L);
        col2.setColumnName("城市");
        col2.setTechnicalNameOrExpression("city");
        col2.setSemanticType(com.qding.chatbi.common.enums.SemanticType.GEO_DIMENSION);
        col2.setDescription("销售订单发生的城市");
        col2.setDataType("VARCHAR");
        col2.setIsComputed(false);

        ColumnInfoDTO col3 = new ColumnInfoDTO();
        col3.setColumnId(103L);
        col3.setColumnName("销售额");
        col3.setTechnicalNameOrExpression("sales_amount"); // 修正字段名
        col3.setSemanticType(com.qding.chatbi.common.enums.SemanticType.METRIC);
        col3.setDescription("订单的总销售额");
        col3.setDataType("DECIMAL");
        col3.setIsComputed(false);

        // 模拟数据集 - 使用MYSQL而不是CLICKHOUSE
        DatasetInfoDTO dataset = new DatasetInfoDTO();
        dataset.setDatasetId(1L);
        dataset.setDatasetName("daily_sales_summary");
        dataset.setDescription("每日销售汇总表，包含了各个城市的基本销售数据。");
        dataset.setDatabaseType("MYSQL"); // 修正为MYSQL
        dataset.setDatabaseSourceId(1L);
        dataset.setTableName("daily_sales_summary");
        dataset.setColumns(Arrays.asList(col1, col2, col3));

        return Collections.singletonList(dataset);
    }

    public String executeSqlAndGetResponse(String sql, String databaseSourceId) {
        log.info("Executing SQL on data source '{}': {}", databaseSourceId, sql);
        // ... existing code ...
        return null; // Placeholder return, actual implementation needed
    }
}
