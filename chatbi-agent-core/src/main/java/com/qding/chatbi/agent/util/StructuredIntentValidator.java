package com.qding.chatbi.agent.util;

import com.qding.chatbi.common.dto.StructuredQueryIntent;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 对 StructuredQueryIntent 进行后端校验，避免缺失关键信息直接进入下游。
 */
@UtilityClass
@Slf4j
public class StructuredIntentValidator {

    /**
     * 简易判定时间字段是否缺失。
     * 规则：filters 中存在 fieldName = date 或包含时间范围即视为有时间。
     */
    public static boolean hasTimeFilter(StructuredQueryIntent intent) {
        if (intent == null || intent.getEntities() == null)
            return false;
        Object filtersObj = intent.getEntities().get("filters");
        if (!(filtersObj instanceof List<?>))
            return false;
        List<?> filters = (List<?>) filtersObj;
        return filters.stream().anyMatch(f -> {
            if (f instanceof Map<?, ?>) {
                Object fieldName = ((Map<?, ?>) f).get("fieldName");
                return fieldName != null && "date".equalsIgnoreCase(fieldName.toString());
            }
            return false;
        });
    }

    /**
     * 完整性检测，返回 true 代表通过。
     */
    public static boolean validate(StructuredQueryIntent intent) {
        if (intent == null)
            return false;
        // 1. 必须包含 intent 字段
        if (intent.getIntent() == null)
            return false;

        // 对 DATA_QUERY 检查时间
        if ("DATA_QUERY".equals(intent.getIntent())) {
            if (!hasTimeFilter(intent)) {
                log.warn("Intent 缺少时间过滤，将强制要求澄清");
                return false;
            }
        }
        return true;
    }
}