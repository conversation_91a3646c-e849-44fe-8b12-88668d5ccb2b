package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.metadata.entity.ChatMessage;

/**
 * 对话上下文管理器
 * 负责管理和维护多轮对话的上下文信息
 */
public interface ConversationContextManager {

    /**
     * 获取最近一次的查询意图
     * 
     * @param sessionId 会话ID
     * @return 最近的StructuredQueryIntent，如果没有则返回null
     */
    StructuredQueryIntent getLastQueryIntent(String sessionId);

    /**
     * 保存当前轮的查询意图
     * 
     * @param sessionId 会话ID
     * @param intent    当前的查询意图
     */
    void saveQueryIntent(String sessionId, StructuredQueryIntent intent);

    /**
     * 获取最近N轮的对话历史（结构化形式）
     * 
     * @param sessionId 会话ID
     * @param limit     获取的轮数
     * @return 历史消息列表
     */
    java.util.List<ChatMessage> getRecentHistory(String sessionId, int limit);

    /**
     * 清理会话上下文
     * 
     * @param sessionId 会话ID
     */
    void clearContext(String sessionId);
}