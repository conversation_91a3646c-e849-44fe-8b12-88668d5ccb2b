package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.dto.StructuredQueryIntent;

/**
 * ResponseGenerator 接口定义
 */
public interface ResponseGenerator {
    /**
     * 根据查询结果和用户意图，生成最终给用户的响应。
     * @param queryResult 数据查询结果
     * @param intent 结构化的用户意图
     * @return 包含文本摘要、图表建议等的Agent响应
     */
    AgentResponse generate(QueryResult queryResult, StructuredQueryIntent intent);
} 