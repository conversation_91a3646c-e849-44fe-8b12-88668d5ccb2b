package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.service.VisualizationSuggester;
import com.qding.chatbi.common.dto.ColumnInfoDTO;
import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.DisplaySuggestion;
import com.qding.chatbi.common.dto.QueryResult;
import com.qding.chatbi.common.enums.SemanticType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.qding.chatbi.common.dto.ColumnMetadata;
import com.qding.chatbi.common.enums.DataType;

/**
 * 可视化建议生成器服务的实现。
 * 采用启发式规则，根据查询结果和元数据推荐最佳图表。
 */
@Service
public class VisualizationSuggesterImpl implements VisualizationSuggester {

    private static final String DEFAULT_CHART_TYPE = "table";

    @Override
    public DisplaySuggestion suggest(QueryResult result, DatasetInfoDTO datasetInfo) {
        if (result == null || CollectionUtils.isEmpty(result.getRows())) {
            return createDefaultSuggestion("查询结果为空");
        }

        // 使用从QueryResult直接传递过来的列元数据，这保证了与查询结果列的顺序和别名一致
        List<ColumnMetadata> queryColumnMetadata = result.getColumnMetadata();
        if (CollectionUtils.isEmpty(queryColumnMetadata)) {
            return createDefaultSuggestion("缺少列元数据，无法生成智能建议");
        }

        // 将 ColumnMetadata 转换为 ColumnInfoDTO，并使用 friendlyName
        List<ColumnInfoDTO> columnMetadata = queryColumnMetadata.stream().map(meta -> {
            ColumnInfoDTO dto = new ColumnInfoDTO();
            dto.setColumnName(meta.getFriendlyName()); // 使用中文名
            dto.setTechnicalNameOrExpression(meta.getName()); // 保留技术名
            dto.setSemanticType(meta.getSemanticType());
            dto.setDataType(safeConvertDataType(meta.getDataType()));
            return dto;
        }).collect(Collectors.toList());

        List<ColumnInfoDTO> dimensions = new ArrayList<>();
        List<ColumnInfoDTO> metrics = new ArrayList<>();

        for (ColumnInfoDTO columnMeta : columnMetadata) {
            if (columnMeta.getSemanticType() == SemanticType.DIMENSION || columnMeta.getSemanticType() == SemanticType.TIME_DIMENSION) {
                dimensions.add(columnMeta);
            } else if (columnMeta.getSemanticType() == SemanticType.METRIC) {
                metrics.add(columnMeta);
            }
        }

        int dimCount = dimensions.size();
        int metricCount = metrics.size();

        if (dimCount == 0 && metricCount == 1 && result.getRows().size() == 1) {
            return buildKpiCardSuggestion(metrics.get(0));
        }

        if (dimCount == 1 && metricCount >= 1) {
            ColumnInfoDTO dimension = dimensions.get(0);
            if (dimension.getSemanticType() == SemanticType.TIME_DIMENSION) {
                return buildLineChartSuggestion(dimension, metrics);
            } else {
                if (metricCount == 1 && result.getRows().size() > 1 && result.getRows().size() <= 8) {
                    return buildPieChartSuggestion(dimension, metrics.get(0));
                }
                return buildBarChartSuggestion(dimension, metrics);
            }
        }

        return createDefaultSuggestion("多维度数据详情");
    }

    private DisplaySuggestion buildKpiCardSuggestion(ColumnInfoDTO metric) {
        return new DisplaySuggestion("kpi_card", metric.getColumnName(), null, null, null);
    }

    private DisplaySuggestion buildLineChartSuggestion(ColumnInfoDTO timeDim, List<ColumnInfoDTO> metrics) {
        String title = String.format("%s随%s的变化趋势", metrics.stream().map(ColumnInfoDTO::getColumnName).collect(Collectors.joining("、")), timeDim.getColumnName());
        List<String> yAxisColumns = metrics.stream().map(ColumnInfoDTO::getColumnName).collect(Collectors.toList());
        return new DisplaySuggestion("line_chart", title, null, timeDim.getColumnName(), yAxisColumns);
    }

    private DisplaySuggestion buildBarChartSuggestion(ColumnInfoDTO dim, List<ColumnInfoDTO> metrics) {
        String title = String.format("按%s分析%s", dim.getColumnName(), metrics.stream().map(ColumnInfoDTO::getColumnName).collect(Collectors.joining("、")));
        List<String> yAxisColumns = metrics.stream().map(ColumnInfoDTO::getColumnName).collect(Collectors.toList());
        return new DisplaySuggestion("bar_chart", title, null, dim.getColumnName(), yAxisColumns);
    }

    private DisplaySuggestion buildPieChartSuggestion(ColumnInfoDTO dim, ColumnInfoDTO metric) {
        String title = String.format("%s的%s分布", dim.getColumnName(), metric.getColumnName());
        return new DisplaySuggestion("pie_chart", title, null, dim.getColumnName(), List.of(metric.getColumnName()));
    }

    private DisplaySuggestion createDefaultSuggestion(String title) {
        return new DisplaySuggestion("table", title, null, null, null);
    }

    private boolean isTimeType(ColumnInfoDTO column) {
        if (column.getSemanticType() == SemanticType.TIME_DIMENSION) {
            return true;
        }
        String dataType = column.getDataType().toUpperCase();
        return dataType.contains("DATE") || dataType.contains("TIME");
    }

    private String safeConvertDataType(DataType dataType) {
        return dataType != null ? dataType.name() : "STRING";
    }

    private DisplaySuggestion suggestPieChart(List<ColumnInfoDTO> dimensions, List<ColumnInfoDTO> metrics) {
        if (dimensions.size() == 1 && metrics.size() == 1) {
            // ... existing pie chart logic ...
        }
        return createDefaultSuggestion("无法确定合适的图表类型");
    }
} 