package com.qding.chatbi.agent.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 行级安全管理器
 * 基于现有的 UserRoleService 实现行级数据权限控制
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RowLevelSecurityManager {

    private final UserRoleService userRoleService;

    // 行级权限规则配置（实际项目中应该从数据库读取）
    private static final Map<String, List<RowLevelRule>> ROLE_PERMISSION_RULES = new HashMap<>();
    
    // 用户上下文信息（实际项目中应该从数据库或缓存读取）
    private static final Map<String, UserContext> USER_CONTEXTS = new HashMap<>();

    static {
        initializePermissionRules();
        initializeUserContexts();
    }

    /**
     * 为SQL查询添加行级权限过滤条件
     *
     * @param originalSql 原始SQL
     * @param userId 用户ID
     * @param datasetId 数据集ID (可选，用于更精确的权限控制)
     * @return 增强后的SQL
     */
    public String enhanceQueryWithRowLevelSecurity(String originalSql, String userId, Long datasetId) {
        try {
            log.debug("开始为用户 {} 增强SQL查询", userId);

            // 1. 获取用户角色
            List<String> userRoles = userRoleService.getUserRoles(userId);
            if (userRoles == null || userRoles.isEmpty()) {
                log.warn("用户 {} 没有分配任何角色，拒绝访问", userId);
                return addDenyAllCondition(originalSql);
            }

            // 2. 获取用户上下文
            UserContext userContext = getUserContext(userId);
            if (userContext == null) {
                log.warn("用户 {} 的上下文信息不存在", userId);
                return addDenyAllCondition(originalSql);
            }

            // 3. 生成行级权限过滤条件
            String rowLevelFilter = generateRowLevelFilter(userRoles, userContext, datasetId);

            // 4. 如果没有权限限制，返回原始SQL
            if (!StringUtils.hasText(rowLevelFilter)) {
                log.debug("用户 {} 无行级权限限制", userId);
                return originalSql;
            }

            // 5. 将过滤条件注入SQL
            String enhancedSql = injectFilterIntoSql(originalSql, rowLevelFilter);
            
            log.info("已为用户 {} 应用行级权限过滤: {}", userId, rowLevelFilter);
            return enhancedSql;

        } catch (Exception e) {
            log.error("增强SQL查询失败，用户: {}", userId, e);
            return addDenyAllCondition(originalSql); // 出错时拒绝访问
        }
    }

    /**
     * 检查用户是否有行级权限限制
     */
    public boolean hasRowLevelRestriction(String userId) {
        try {
            List<String> userRoles = userRoleService.getUserRoles(userId);
            if (userRoles == null || userRoles.isEmpty()) {
                return true; // 没有角色视为有限制
            }

            return userRoles.stream()
                    .anyMatch(role -> ROLE_PERMISSION_RULES.containsKey(role));
        } catch (Exception e) {
            log.error("检查用户 {} 行级权限限制失败", userId, e);
            return true; // 出错时视为有限制
        }
    }

    /**
     * 生成行级权限过滤条件
     */
    private String generateRowLevelFilter(List<String> userRoles, UserContext userContext, Long datasetId) {
        List<String> conditions = new ArrayList<>();

        for (String role : userRoles) {
            List<RowLevelRule> rules = ROLE_PERMISSION_RULES.get(role);
            if (rules != null) {
                for (RowLevelRule rule : rules) {
                    // 如果指定了数据集ID，检查规则是否适用
                    if (datasetId != null && rule.getDatasetId() != null && 
                        !rule.getDatasetId().equals(datasetId)) {
                        continue;
                    }

                    String condition = buildCondition(rule, userContext);
                    if (StringUtils.hasText(condition)) {
                        conditions.add(condition);
                    }
                }
            }
        }

        if (conditions.isEmpty()) {
            return "";
        }

        // 使用OR连接多个条件（用户满足任一条件即可访问）
        return "(" + String.join(" OR ", conditions) + ")";
    }

    /**
     * 构建单个权限条件
     */
    private String buildCondition(RowLevelRule rule, UserContext userContext) {
        switch (rule.getType()) {
            case DEPARTMENT:
                return String.format("%s = '%s'", rule.getColumnName(), userContext.getDepartment());
            case REGION:
                return String.format("%s = '%s'", rule.getColumnName(), userContext.getRegion());
            case USER_ID:
                return String.format("%s = '%s'", rule.getColumnName(), userContext.getUserId());
            case CUSTOM_SQL:
                return replaceVariables(rule.getSqlCondition(), userContext);
            case MULTI_VALUE:
                return String.format("%s IN (%s)", rule.getColumnName(), 
                    String.join(",", rule.getAllowedValues().stream()
                        .map(v -> "'" + v + "'").toArray(String[]::new)));
            default:
                log.warn("未知的权限规则类型: {}", rule.getType());
                return "";
        }
    }

    /**
     * 替换SQL条件中的变量
     */
    private String replaceVariables(String sqlCondition, UserContext userContext) {
        return sqlCondition
                .replace("${user.id}", "'" + userContext.getUserId() + "'")
                .replace("${user.department}", "'" + userContext.getDepartment() + "'")
                .replace("${user.region}", "'" + userContext.getRegion() + "'")
                .replace("${user.managerId}", "'" + userContext.getManagerId() + "'");
    }

    /**
     * 将过滤条件注入SQL
     */
    private String injectFilterIntoSql(String originalSql, String filter) {
        String sql = originalSql.trim();
        String upperSql = sql.toUpperCase();

        // 查找WHERE子句位置
        int whereIndex = upperSql.indexOf(" WHERE ");
        
        if (whereIndex != -1) {
            // 已有WHERE子句，使用AND连接
            return sql.substring(0, whereIndex + 7) + "(" + filter + ") AND (" + 
                   sql.substring(whereIndex + 7) + ")";
        } else {
            // 没有WHERE子句，添加WHERE
            int insertIndex = findInsertPosition(upperSql, sql.length());
            return sql.substring(0, insertIndex).trim() + " WHERE " + filter + " " + 
                   sql.substring(insertIndex);
        }
    }

    /**
     * 查找WHERE子句插入位置
     */
    private int findInsertPosition(String upperSql, int defaultIndex) {
        int insertIndex = defaultIndex;
        
        String[] keywords = {" GROUP BY ", " ORDER BY ", " LIMIT ", " HAVING "};
        for (String keyword : keywords) {
            int index = upperSql.indexOf(keyword);
            if (index != -1) {
                insertIndex = Math.min(insertIndex, index);
            }
        }
        
        return insertIndex;
    }

    /**
     * 添加拒绝所有访问的条件
     */
    private String addDenyAllCondition(String originalSql) {
        return injectFilterIntoSql(originalSql, "1=0");
    }

    /**
     * 获取用户上下文
     */
    private UserContext getUserContext(String userId) {
        return USER_CONTEXTS.get(userId);
    }

    /**
     * 初始化权限规则（实际项目中应该从数据库读取）
     */
    private static void initializePermissionRules() {
        // 销售人员：只能看自己区域的数据
        ROLE_PERMISSION_RULES.put("销售人员", Arrays.asList(
            new RowLevelRule(RuleType.REGION, "region", null, null, null, null)
        ));

        // 区域经理：可以看自己区域的数据
        ROLE_PERMISSION_RULES.put("区域经理", Arrays.asList(
            new RowLevelRule(RuleType.REGION, "region", null, null, null, null)
        ));

        // 部门经理：可以看自己部门的数据
        ROLE_PERMISSION_RULES.put("部门经理", Arrays.asList(
            new RowLevelRule(RuleType.DEPARTMENT, "department", null, null, null, null)
        ));

        // 财务人员：可以看已审核的数据
        ROLE_PERMISSION_RULES.put("财务人员", Arrays.asList(
            new RowLevelRule(RuleType.CUSTOM_SQL, null, null, 
                "status = '已审核' AND department = ${user.department}", null, null)
        ));

        // 高级管理层：可以看指定区域的数据
        ROLE_PERMISSION_RULES.put("高级管理层", Arrays.asList(
            new RowLevelRule(RuleType.MULTI_VALUE, "region", null, null, 
                Arrays.asList("华北区", "华东区", "华南区"), null)
        ));
    }

    /**
     * 初始化用户上下文（实际项目中应该从数据库读取）
     */
    private static void initializeUserContexts() {
        USER_CONTEXTS.put("user001", new UserContext("user001", "张三", "销售部", "华北区", "manager001"));
        USER_CONTEXTS.put("user002", new UserContext("user002", "李四", "销售部", "华东区", "manager002"));
        USER_CONTEXTS.put("user003", new UserContext("user003", "王五", "财务部", "华北区", "manager003"));
        USER_CONTEXTS.put("user004", new UserContext("user004", "赵六", "人事部", "华南区", "manager004"));
    }

    /**
     * 权限规则类型枚举
     */
    public enum RuleType {
        DEPARTMENT,    // 基于部门过滤
        REGION,        // 基于区域过滤
        USER_ID,       // 基于用户ID过滤
        CUSTOM_SQL,    // 自定义SQL条件
        MULTI_VALUE    // 多值过滤
    }

    /**
     * 行级权限规则
     */
    public static class RowLevelRule {
        private RuleType type;
        private String columnName;
        private Long datasetId;
        private String sqlCondition;
        private List<String> allowedValues;
        private Integer priority;

        public RowLevelRule(RuleType type, String columnName, Long datasetId, 
                           String sqlCondition, List<String> allowedValues, Integer priority) {
            this.type = type;
            this.columnName = columnName;
            this.datasetId = datasetId;
            this.sqlCondition = sqlCondition;
            this.allowedValues = allowedValues;
            this.priority = priority;
        }

        // Getters
        public RuleType getType() { return type; }
        public String getColumnName() { return columnName; }
        public Long getDatasetId() { return datasetId; }
        public String getSqlCondition() { return sqlCondition; }
        public List<String> getAllowedValues() { return allowedValues; }
        public Integer getPriority() { return priority; }
    }

    /**
     * 用户上下文
     */
    public static class UserContext {
        private String userId;
        private String userName;
        private String department;
        private String region;
        private String managerId;

        public UserContext(String userId, String userName, String department, String region, String managerId) {
            this.userId = userId;
            this.userName = userName;
            this.department = department;
            this.region = region;
            this.managerId = managerId;
        }

        // Getters
        public String getUserId() { return userId; }
        public String getUserName() { return userName; }
        public String getDepartment() { return department; }
        public String getRegion() { return region; }
        public String getManagerId() { return managerId; }
    }
}
