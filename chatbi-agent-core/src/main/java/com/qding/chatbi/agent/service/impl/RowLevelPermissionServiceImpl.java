package com.qding.chatbi.agent.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.agent.service.RowLevelPermissionService;
import com.qding.chatbi.agent.service.UserRoleService;
import com.qding.chatbi.metadata.entity.RowLevelPermission;
import com.qding.chatbi.metadata.entity.UserContext;
import com.qding.chatbi.metadata.repository.RowLevelPermissionRepository;
import com.qding.chatbi.metadata.repository.UserContextRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 行级权限服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RowLevelPermissionServiceImpl implements RowLevelPermissionService {

    private final RowLevelPermissionRepository rowLevelPermissionRepository;
    private final UserContextRepository userContextRepository;
    private final UserRoleService userRoleService;
    private final ObjectMapper objectMapper;

    // SQL注入防护的正则表达式
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript).*"
    );

    @Override
    public String generateRowLevelFilter(String userId, Long datasetId, String baseTableName) {
        try {
            // 获取用户角色
            List<String> roleNames = userRoleService.getUserRoles(userId);
            if (roleNames.isEmpty()) {
                log.warn("用户 {} 没有分配任何角色", userId);
                return "1=0"; // 没有角色则拒绝访问
            }

            // 获取用户上下文
            Map<String, Object> userContext = getUserContext(userId);

            // 生成过滤条件
            return generateRowLevelFilterByRoles(roleNames, datasetId, baseTableName, userContext);

        } catch (Exception e) {
            log.error("生成用户 {} 对数据集 {} 的行级权限过滤条件失败", userId, datasetId, e);
            return "1=0"; // 出错时拒绝访问
        }
    }

    @Override
    public String generateRowLevelFilterByRoles(List<String> roleNames, Long datasetId, 
                                               String baseTableName, Map<String, Object> userContext) {
        try {
            // 获取角色的行级权限规则
            List<RowLevelPermission> permissions = rowLevelPermissionRepository
                .findByRoleNamesAndDatasetId(roleNames, datasetId);

            if (permissions.isEmpty()) {
                log.debug("角色 {} 对数据集 {} 没有行级权限限制", roleNames, datasetId);
                return ""; // 没有权限限制
            }

            // 生成过滤条件
            List<String> conditions = new ArrayList<>();
            for (RowLevelPermission permission : permissions) {
                String condition = buildFilterCondition(permission, baseTableName, userContext);
                if (StringUtils.hasText(condition)) {
                    conditions.add(condition);
                }
            }

            if (conditions.isEmpty()) {
                return "";
            }

            // 使用 OR 连接多个权限条件（用户满足任一权限即可访问）
            return "(" + String.join(" OR ", conditions) + ")";

        } catch (Exception e) {
            log.error("生成角色 {} 对数据集 {} 的行级权限过滤条件失败", roleNames, datasetId, e);
            return "1=0"; // 出错时拒绝访问
        }
    }

    @Override
    public boolean hasRowLevelRestriction(String userId, Long datasetId) {
        try {
            List<String> roleNames = userRoleService.getUserRoles(userId);
            if (roleNames.isEmpty()) {
                return true; // 没有角色视为有限制
            }

            return roleNames.stream()
                .anyMatch(roleName -> rowLevelPermissionRepository
                    .hasRowLevelPermission(roleName, datasetId));

        } catch (Exception e) {
            log.error("检查用户 {} 对数据集 {} 的行级权限限制失败", userId, datasetId, e);
            return true; // 出错时视为有限制
        }
    }

    @Override
    public Map<String, Object> getUserContext(String userId) {
        try {
            Optional<UserContext> userContextOpt = userContextRepository
                .findByUserIdAndEnabledTrue(userId);

            if (userContextOpt.isEmpty()) {
                log.warn("用户 {} 的上下文信息不存在", userId);
                return Collections.emptyMap();
            }

            UserContext userContext = userContextOpt.get();
            Map<String, Object> contextMap = new HashMap<>();
            
            // 基础属性
            contextMap.put("id", userContext.getUserId());
            contextMap.put("name", userContext.getUserName());
            contextMap.put("department", userContext.getDepartment());
            contextMap.put("region", userContext.getRegion());
            contextMap.put("city", userContext.getCity());
            contextMap.put("position", userContext.getPosition());
            contextMap.put("level", userContext.getLevel());
            contextMap.put("managerId", userContext.getManagerId());
            contextMap.put("organizationId", userContext.getOrganizationId());
            contextMap.put("costCenter", userContext.getCostCenter());

            // 扩展属性
            if (StringUtils.hasText(userContext.getExtendedAttributes())) {
                try {
                    Map<String, Object> extendedAttrs = objectMapper.readValue(
                        userContext.getExtendedAttributes(), 
                        new TypeReference<Map<String, Object>>() {}
                    );
                    contextMap.putAll(extendedAttrs);
                } catch (Exception e) {
                    log.warn("解析用户 {} 的扩展属性失败", userId, e);
                }
            }

            return contextMap;

        } catch (Exception e) {
            log.error("获取用户 {} 的上下文信息失败", userId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public boolean validateSqlCondition(String sqlCondition) {
        if (!StringUtils.hasText(sqlCondition)) {
            return true;
        }

        // 检查SQL注入
        if (SQL_INJECTION_PATTERN.matcher(sqlCondition).matches()) {
            log.warn("检测到潜在的SQL注入: {}", sqlCondition);
            return false;
        }

        return true;
    }

    /**
     * 构建单个权限的过滤条件
     */
    private String buildFilterCondition(RowLevelPermission permission, String baseTableName, 
                                      Map<String, Object> userContext) {
        try {
            switch (permission.getFilterType()) {
                case COLUMN_VALUE:
                    return buildColumnValueCondition(permission, baseTableName);
                case SQL_CONDITION:
                    return buildSqlCondition(permission, userContext);
                case USER_ATTRIBUTE:
                    return buildUserAttributeCondition(permission, baseTableName, userContext);
                default:
                    log.warn("未知的过滤类型: {}", permission.getFilterType());
                    return "";
            }
        } catch (Exception e) {
            log.error("构建权限 {} 的过滤条件失败", permission.getId(), e);
            return "";
        }
    }

    /**
     * 构建基于列值的过滤条件
     */
    private String buildColumnValueCondition(RowLevelPermission permission, String baseTableName) {
        String column = baseTableName + "." + permission.getFilterColumn();
        String values = permission.getFilterValues();
        
        if (!StringUtils.hasText(values)) {
            return "";
        }

        try {
            switch (permission.getFilterOperator()) {
                case EQUALS:
                    return column + " = '" + values + "'";
                case NOT_EQUALS:
                    return column + " != '" + values + "'";
                case IN:
                    List<String> inValues = objectMapper.readValue(values, new TypeReference<List<String>>() {});
                    String inClause = inValues.stream()
                        .map(v -> "'" + v + "'")
                        .collect(Collectors.joining(","));
                    return column + " IN (" + inClause + ")";
                case NOT_IN:
                    List<String> notInValues = objectMapper.readValue(values, new TypeReference<List<String>>() {});
                    String notInClause = notInValues.stream()
                        .map(v -> "'" + v + "'")
                        .collect(Collectors.joining(","));
                    return column + " NOT IN (" + notInClause + ")";
                case LIKE:
                    return column + " LIKE '%" + values + "%'";
                case IS_NULL:
                    return column + " IS NULL";
                case IS_NOT_NULL:
                    return column + " IS NOT NULL";
                default:
                    log.warn("不支持的操作符: {}", permission.getFilterOperator());
                    return "";
            }
        } catch (Exception e) {
            log.error("解析过滤值失败: {}", values, e);
            return "";
        }
    }

    /**
     * 构建自定义SQL条件
     */
    private String buildSqlCondition(RowLevelPermission permission, Map<String, Object> userContext) {
        String sqlCondition = permission.getSqlCondition();
        
        if (!StringUtils.hasText(sqlCondition)) {
            return "";
        }

        // 验证SQL安全性
        if (!validateSqlCondition(sqlCondition)) {
            log.warn("SQL条件安全验证失败: {}", sqlCondition);
            return "1=0";
        }

        // 替换用户上下文变量
        return replaceUserContextVariables(sqlCondition, userContext);
    }

    /**
     * 构建基于用户属性的过滤条件
     */
    private String buildUserAttributeCondition(RowLevelPermission permission, String baseTableName, 
                                             Map<String, Object> userContext) {
        String column = baseTableName + "." + permission.getFilterColumn();
        String userAttribute = permission.getFilterValues();
        
        if (!StringUtils.hasText(userAttribute)) {
            return "";
        }

        // 移除 ${user.} 前缀
        String attributeName = userAttribute.replace("${user.", "").replace("}", "");
        Object attributeValue = userContext.get(attributeName);
        
        if (attributeValue == null) {
            log.warn("用户属性 {} 不存在", attributeName);
            return "1=0"; // 属性不存在则拒绝访问
        }

        return column + " = '" + attributeValue + "'";
    }

    /**
     * 替换SQL条件中的用户上下文变量
     */
    private String replaceUserContextVariables(String sqlCondition, Map<String, Object> userContext) {
        String result = sqlCondition;
        
        for (Map.Entry<String, Object> entry : userContext.entrySet()) {
            String placeholder = "${user." + entry.getKey() + "}";
            if (result.contains(placeholder) && entry.getValue() != null) {
                result = result.replace(placeholder, "'" + entry.getValue() + "'");
            }
        }
        
        return result;
    }
}
