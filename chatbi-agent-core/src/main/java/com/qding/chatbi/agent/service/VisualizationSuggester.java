package com.qding.chatbi.agent.service;

import com.qding.chatbi.common.dto.DatasetInfoDTO;
import com.qding.chatbi.common.dto.DisplaySuggestion;
import com.qding.chatbi.common.dto.QueryResult;

/**
 * 可视化建议服务接口。
 * 负责根据查询结果和元数据的数据特征，推荐最佳的可视化图表类型。
 */
public interface VisualizationSuggester {

    /**
     * 根据查询结果和数据集的元数据推荐一个显示建议。
     *
     * @param result 查询结果，包含数据和列头。
     * @param datasetInfo 数据集元数据，包含列的语义类型等信息。
     * @return 包含图表类型、标题和选项的显示建议。
     */
    DisplaySuggestion suggest(QueryResult result, DatasetInfoDTO datasetInfo);
} 