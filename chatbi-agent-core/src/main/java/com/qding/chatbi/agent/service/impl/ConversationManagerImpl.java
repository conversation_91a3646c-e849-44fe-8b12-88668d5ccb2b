package com.qding.chatbi.agent.service.impl;

import com.qding.chatbi.agent.dto.QueryPlan;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.agent.memory.ChatMemoryProvider; // Placeholder
import com.qding.chatbi.agent.service.ClarificationStrategy;
import com.qding.chatbi.agent.service.ConversationManager;
import com.qding.chatbi.agent.service.DataRetrievalAgent;
import com.qding.chatbi.agent.service.NluAgent;
import com.qding.chatbi.agent.service.QueryPlannerAgent;
import com.qding.chatbi.agent.service.ResponseGenerator;
import com.qding.chatbi.common.dto.*;
import com.qding.chatbi.metadata.service.PermissionService;
import dev.langchain4j.memory.ChatMemory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.qding.chatbi.common.enums.ResponseType;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.UserMessage;
import com.qding.chatbi.common.util.JsonUtil; // Placeholder
import dev.langchain4j.data.message.ChatMessageType;
import com.qding.chatbi.agent.service.ConversationContextManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

// Placeholder for ChatMemoryProvider
// interface ChatMemoryProvider { ChatMemory getMemory(String sessionId); }

// Placeholder for JsonUtil
// class JsonUtil { public static String toJson(Object obj) { return "{}"; } }

@Service
public class ConversationManagerImpl implements ConversationManager {

    private static final Logger log = LoggerFactory.getLogger(ConversationManagerImpl.class);

    private final ChatMemoryProvider chatMemoryProvider;
    private final NluAgent nluAgent;
    private final QueryPlannerAgent queryPlannerAgent;
    private final DataRetrievalAgent dataRetrievalAgent;
    private final PermissionService permissionService;
    private final ResponseGenerator responseGenerator;
    private final ClarificationStrategy clarificationStrategy;
    private final ConversationContextManager contextManager;

    public ConversationManagerImpl(ChatMemoryProvider chatMemoryProvider,
            NluAgent nluAgent,
            QueryPlannerAgent queryPlannerAgent,
            DataRetrievalAgent dataRetrievalAgent,
            PermissionService permissionService,
            ResponseGenerator responseGenerator,
            ClarificationStrategy clarificationStrategy,
            ConversationContextManager contextManager) {
        this.chatMemoryProvider = chatMemoryProvider;
        this.nluAgent = nluAgent;
        this.queryPlannerAgent = queryPlannerAgent;
        this.dataRetrievalAgent = dataRetrievalAgent;
        this.permissionService = permissionService;
        this.responseGenerator = responseGenerator;
        this.clarificationStrategy = clarificationStrategy;
        this.contextManager = contextManager;
    }

    @Override
    public AgentResponse processConversationTurn(UserQueryRequest request, UserContextDTO userContext) {
        log.debug("🔄 开始处理对话: '{}' (会话: {})", request.getQueryText(), userContext.getSessionId());

        // 0. Get user role from context
        String userRole = userContext != null ? userContext.getUserRole() : null;

        // 1. Get/Create Chat Memory - This now contains ONLY previous turns' history
        ChatMemory chatMemory = chatMemoryProvider.getMemory(userContext.getSessionId());

        // 2. 🔥 **最终修复核心逻辑** 🔥
        // a. 提取纯净的历史记录
        String chatHistory = getFormattedHistory(chatMemory);

        // b. 将当前用户查询添加到内存中，为本轮和下一轮做准备
        // 这一步至关重要，它确保了ChatMemory对象在被缓存时是完整的
        UserMessage currentUserMessage = UserMessage.from(request.getQueryText());
        chatMemory.add(currentUserMessage);

        // 3. 准备NLU的输入
        String currentUserQuery = request.getQueryText();

        // 🔍 添加详细的对话历史日志
        log.debug("纯净历史消息总数: {},发送给NLU的历史对话记录:'{}'",chatMemory.messages().size() - 1, chatHistory);
        // 4. Call NLU Agent to understand the user's query
        StructuredQueryIntent intent;
        try {
            // TODO: 从元数据服务获取真实的数据集列表
            List<DatasetInfoDTO> availableDatasets = permissionService.getPermittedDatasetInfos(userRole);
            log.info("📋NLU解析开始-用户角色:({}),用户输入:({}),会话ID:({}),历史对话记录:({}),可用数据集列表:({}) ", userContext.getUserRole(),request.getQueryText(),
                    userContext.getSessionId(), chatHistory,JsonUtil.toJson(availableDatasets),userContext.getUserRole());

            intent = nluAgent.understand(currentUserQuery, userContext.getSessionId(), chatHistory, availableDatasets, userContext.getUserRole());
            log.info("NLU解析完成: 意图={}", intent.getIntent());

            // 保存意图到上下文管理器（包括澄清意图，因为它们也包含已识别的实体）
            if (intent != null && userContext.getSessionId() != null) {
                contextManager.saveQueryIntent(userContext.getSessionId(), intent);
                log.info("已保存查询意图到上下文管理器: sessionId={}, intent={}", userContext.getSessionId(), intent.getIntent());
            }
        } catch (Exception e) {
            log.error("NLU Agent处理失败: {}", e.getMessage(), e);
            return createErrorResponse("理解您的查询时发生错误，请稍后重试。", userContext, chatMemory);
        }

        // Check for permissions right after understanding intent
        List<Long> permittedDatasetIds = permissionService.getPermittedDatasetIds(userRole);
        if (permittedDatasetIds.isEmpty()) {
            String intentType = intent.getIntent();
            // For most intents, if user has no datasets, we should inform them.
            // We make an exception for GREETING, as a simple greeting back is more natural.
            if (!"GREETING".equals(intentType)) {
                String message = "我是一个AI数据查询助手。不过，您当前的账户似乎没有查询任何数据的权限。请联系管理员为您分配相关权限。";
                return createSimpleAcknowledgement(message, userContext, chatMemory);
            }
        }

        // 3. Branching logic based on NLU intent
        if ("CLARIFICATION_NEEDED".equals(intent.getIntent())) {
            return createClarificationResponse(intent, currentUserQuery, userContext, chatMemory);
        } else if ("DATA_UNAVAILABLE".equals(intent.getIntent())) {
            return createDataUnavailableResponse(intent, userContext, chatMemory);
        } else if ("GREETING".equals(intent.getIntent())) {
            return createSimpleAcknowledgement("您好！有什么可以帮您的吗？", userContext, chatMemory);
        } else if ("CAPABILITIES_INQUIRY".equals(intent.getIntent())) {
            return createClarificationResponse(intent, currentUserQuery, userContext, chatMemory);
        } else {
            // Continue with query processing
            try {
                QueryPlan queryPlan = queryPlannerAgent.planAndGenerateSql(intent, userContext);
                QueryResult queryResult = dataRetrievalAgent.retrieve(queryPlan, userContext);

                if (queryResult != null && queryResult.getErrorMessage() != null) {
                    log.warn("查询执行错误: {}", queryResult.getErrorMessage());
                }

                AgentResponse response;

                if (queryResult != null && (queryResult.getErrorMessage() == null || queryResult.getErrorMessage().isEmpty())) {
                    // 查询成功，使用ResponseGenerator生成智能响应
                    response = responseGenerator.generate(queryResult, intent);
                    response.setSessionId(userContext.getSessionId());

                    // 添加思考过程
                    if (queryPlan.getThoughtProcess() != null) {
                        response.setThoughtProcess(queryPlan.getThoughtProcess());
                    }
                } else {
                    // 查询失败，创建错误响应
                    response = new AgentResponse();
                    response.setSessionId(userContext.getSessionId());
                    response.setResponseType(ResponseType.ERROR);
                    response.setMessageToUser(queryResult != null && queryResult.getErrorMessage() != null ? queryResult.getErrorMessage() : "查询执行失败");
                }

                // Record AI response to memory
                chatMemory.add(AiMessage.from(response.getMessageToUser()));
                return response;

            } catch (Exception e) {
                log.error("处理查询时发生异常: {}", e.getMessage(), e);
                return createErrorResponse("处理您的查询时发生错误: " + e.getMessage(), userContext, chatMemory);
            }
        }
    }

    private AgentResponse createClarificationResponse(StructuredQueryIntent intent, String userQuery,
            UserContextDTO userContext, ChatMemory chatMemory) {
        log.info("创建澄清响应。原始查询: '{}'", userQuery);

        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.CLARIFICATION_NEEDED);
        response.setMessageToUser(intent.getResponse());
        response.setClarificationOptions(intent.getClarificationOptions());
        if (intent.getInternal_context() != null) {
            // response.setOriginalQuery(intent.getInternal_context().getOriginalQuery());
            // // 暂时注释掉，在新设计中不需要
        }

        // 构建包含澄清选项的完整AI消息，以便后续处理时能够理解选项来源
        StringBuilder fullClarificationMessage = new StringBuilder();
        fullClarificationMessage.append(intent.getResponse());

        if (intent.getClarificationOptions() != null && !intent.getClarificationOptions().isEmpty()) {
            fullClarificationMessage.append("\n\n可选的澄清选项：");
            for (ClarificationOption option : intent.getClarificationOptions()) {
                fullClarificationMessage.append("\n- ").append(option.getOptionText());
                // 如果有payload信息，也可以简单描述一下
                if (option.getPayload() != null && !option.getPayload().isEmpty()) {
                    fullClarificationMessage.append(" [payload: ").append(option.getPayload().toString()).append("]");
                }
            }
        }

        // 🔧 修复：只添加一次完整的澄清消息到记忆中，避免重复
        chatMemory.add(AiMessage.from(fullClarificationMessage.toString()));
        log.info("✅ 已将完整澄清消息添加到记忆中: '{}'", fullClarificationMessage.toString());

        log.info("✅ 澄清响应已创建，包含 {} 个选项", intent.getClarificationOptions() != null ? intent.getClarificationOptions().size() : 0);

        return response;
    }

    private AgentResponse createErrorResponse(String message, UserContextDTO userContext, ChatMemory chatMemory) {
        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.ERROR);
        response.setMessageToUser(message);

        chatMemory.add(AiMessage.from(message));

        return response;
    }

    private AgentResponse createSimpleAcknowledgement(String message, UserContextDTO userContext,
            ChatMemory chatMemory) {
        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.ACKNOWLEDGEMENT);
        response.setMessageToUser(message);

        chatMemory.add(AiMessage.from(message));

        return response;
    }

    private AgentResponse createDataUnavailableResponse(StructuredQueryIntent intent, UserContextDTO userContext,
            ChatMemory chatMemory) {
        log.info("创建数据不可用响应");

        String message = intent.getResponse();
        if (message == null || message.trim().isEmpty()) {
            message = "抱歉，系统当前没有您查询的数据。请尝试查询其他指标。";
        }

        // 将AI的"数据不可用"消息添加到记忆中
        chatMemory.add(AiMessage.from(message));

        AgentResponse response = new AgentResponse();
        response.setSessionId(userContext.getSessionId());
        response.setResponseType(ResponseType.DATA_UNAVAILABLE);
        response.setMessageToUser(message);

        // 如果AI提供了替代建议，将其作为澄清选项
        if (intent.getClarificationOptions() != null && !intent.getClarificationOptions().isEmpty()) {
            response.setClarificationOptions(intent.getClarificationOptions());
        }

        log.info("✅ 数据不可用响应已创建: {}", message);
        return response;
    }

    private String getFormattedHistory(ChatMemory chatMemory) {
        List<Map<String, Object>> historyMaps = new ArrayList<>();
        String contextMarker = "\n\n[补充上下文]\n";

        for (ChatMessage message : chatMemory.messages()) {
            Map<String, Object> messageMap = new LinkedHashMap<>();
            String sender = "";
            Object content = null;

            if (message.type() == ChatMessageType.USER) {
                sender = "USER";
                content = message.text();
                messageMap.put("sender", sender);
                messageMap.put("content", content);
                historyMaps.add(messageMap);

            } else if (message.type() == ChatMessageType.AI) {
                sender = "AI";
                String text = message.text();
                Map<String, Object> aiContentMap = new LinkedHashMap<>();

                if (text != null && text.contains(contextMarker)) {
                    int markerPos = text.indexOf(contextMarker);
                    String mainText = text.substring(0, markerPos);
                    String structuredDataJson = text.substring(markerPos + contextMarker.length());

                    aiContentMap.put("text", mainText);
                    try {
                        // 尝试将补充上下文解析为JSON对象，如果失败则作为纯文本
                        Object structuredData = JsonUtil.fromJson(structuredDataJson, Object.class);
                        aiContentMap.put("structuredData", structuredData);
                    } catch (Exception e) {
                        log.warn("无法将AI消息的补充上下文解析为JSON，将作为纯文本处理。上下文: {}", structuredDataJson, e);
                        aiContentMap.put("structuredData", structuredDataJson);
                    }
                } else {
                    aiContentMap.put("text", text);
                    aiContentMap.put("structuredData", null);
                }
                messageMap.put("sender", sender);
                messageMap.put("content", aiContentMap);
                historyMaps.add(messageMap);
            }
        }
        return JsonUtil.toJson(historyMaps);
    }

    /**
     * 智能选择相关的历史消息
     * 实现您建议的历史消息管理策略
     */
    private List<ChatMessage> selectRelevantHistory(List<ChatMessage> allHistory) {
        // 规则1：最多使用最近5条消息
        int maxHistoryCount = 5;

        // 如果总数少于等于5条，直接返回（排除当前正在处理的查询）
        if (allHistory.size() <= maxHistoryCount + 1) {
            // 返回除了最后一条（当前查询）的所有历史
            return allHistory.subList(0, Math.max(0, allHistory.size() - 1));
        }

        // 规则2：取最近的5条历史消息（不包括刚添加的当前查询）
        int historyEndIndex = allHistory.size() - 1; // 排除当前查询
        int historyStartIndex = Math.max(0, historyEndIndex - maxHistoryCount);

        List<ChatMessage> recentHistory = allHistory.subList(historyStartIndex, historyEndIndex);

        // 规则3：简单的噪音过滤
        List<ChatMessage> filteredHistory = recentHistory.stream()
                .filter(this::isRelevantMessage)
                .collect(Collectors.toList());

        log.info("📊 历史消息筛选统计:");
        log.info("   - 原始历史消息数: {}", allHistory.size() - 1);
        log.info("   - 限制后消息数: {}", recentHistory.size());
        log.info("   - 过滤后消息数: {}", filteredHistory.size());

        return filteredHistory;
    }

    /**
     * 判断消息是否相关（简单的噪音过滤）
     */
    private boolean isRelevantMessage(ChatMessage message) {
        String content = message.text().toLowerCase();

        // 过滤掉明显的系统噪音
        if (content.contains("系统错误") ||
                content.contains("处理失败") ||
                content.contains("服务异常") ||
                content.length() > 1000) { // 过长的错误消息
            return false;
        }

        // 过滤掉空消息或无意义消息
        if (content.trim().length() < 2) {
            return false;
        }

        return true;
    }
}
