# ChatBI NLU Agent 提示词架构说明

## 🏗️ 架构设计演进

### V2.0 - @SystemMessage 注解架构（当前版本）

基于 **LangChain4J @SystemMessage 注解** 最佳实践，采用统一模板文件+运行时变量注入：

```
📁 prompts/
└── nlu_system_template.txt    📋 统一系统模板（支持变量注入）
```

### 设计原则

1. **运行时变量注入 (Runtime Variable Injection)**

   - 使用`@V("变量名")`注解定义模板变量
   - LangChain4J 在运行时自动注入参数值
   - 支持动态内容替换

2. **方法级注解配置 (Method-Level Annotation)**

   - `@SystemMessage(fromResource = "path")` 指定模板文件
   - `@UserMessage` 定义用户消息
   - `@V("变量名")` 定义模板占位符

3. **自动化管理 (Automated Management)**
   - 无需手动字符串拼接
   - AiServices 自动处理模板解析
   - 减少配置复杂度

---

## 📋 核心架构 (nlu_system_template.txt)

**功能**：统一的系统消息模板，包含所有 NLU 逻辑  
**内容**：

- 角色定义和核心职责
- 查询类型识别规则
- 实体提取和时间处理规则
- 工具调用指南
- JSON 输出格式规范
- 运行时变量占位符

**模板变量**：

```
{{userRole}}           - 用户角色
{{current_date}}       - 当前日期
{{chatHistory}}        - 对话历史
{{availableDatasets}}  - 可用数据集列表
{{conversationSummary}} - 对话摘要
```

---

## ⚡ 参数注入机制

### **定义阶段 - NluToolAgent 接口**

```java
@SystemMessage(fromResource = "classpath:prompts/nlu_system_template.txt")
StructuredQueryIntent process(
    @UserMessage String userQuery,
    @V("conversationSummary") String conversationSummary,
    @V("availableDatasets") String availableDatasets,
    @V("current_date") String currentDate,
    @V("userRole") String userRole,
    @V("chatHistory") String chatHistory
);
```

### **调用阶段 - NluAgentImpl**

```java
StructuredQueryIntent intent = nluToolAgent.process(
    userQuery,              // @UserMessage
    structuredHistory,      // @V("conversationSummary")
    availableDatasetsJson,  // @V("availableDatasets")
    currentDate,           // @V("current_date")
    userRole,              // @V("userRole")
    chatHistory            // @V("chatHistory")
);
```

### **运行时注入流程**

```
1. 方法调用 → 2. AiServices代理拦截 → 3. 解析@V注解
→ 4. 构建变量Map → 5. 模板替换 → 6. 发送LLM
```

---

## 🚀 优势对比

| 维度                 | V1.0 模块化文件 | V2.0 注解架构           | 提升         |
| -------------------- | --------------- | ----------------------- | ------------ |
| **变量注入**         | ❌ 静态拼接     | ✅ 运行时注入           | **关键修复** |
| **配置复杂度**       | 🟡 需要手动拼接 | ✅ 自动化处理           | **大幅简化** |
| **可维护性**         | 🟡 多文件管理   | ✅ 单文件集中           | **维护友好** |
| **LangChain4J 集成** | 🟡 部分利用     | ✅ 完全利用             | **原生支持** |
| **模板语法**         | ❌ 无效占位符   | ✅ 标准{{variable}}语法 | **标准化**   |

---

## 📝 迁移说明

### 从 V1.0 迁移的变化

1. **文件结构简化**：

   ```
   ❌ V1.0: 3个文件 (core_system.txt + business_logic.txt + context_dynamic.txt)
   ✅ V2.0: 1个文件 (nlu_system_template.txt)
   ```

2. **配置方式变更**：

   ```java
   ❌ V1.0: .systemMessageProvider(v -> fullPrompt)  // 静态字符串
   ✅ V2.0: @SystemMessage(fromResource = "...")       // 注解配置
   ```

3. **变量注入修复**：
   ```
   ❌ V1.0: {{userRole}} 等占位符未被替换
   ✅ V2.0: 运行时自动注入实际参数值
   ```

---

## 🔮 未来扩展

### 支持的增强功能

1. **条件模板**：可以根据参数值使用不同的模板片段
2. **嵌套变量**：支持复杂的变量结构和转换
3. **国际化支持**：可以基于 locale 选择不同的模板文件
4. **A/B 测试**：可以轻松切换不同的提示词版本进行效果对比

### 最佳实践建议

1. **变量命名**：使用明确、一致的变量名（如 current_date 而非 date）
2. **模板验证**：在开发阶段验证所有占位符都有对应的@V 注解
3. **性能优化**：模板文件会被缓存，避免重复加载
4. **错误处理**：为缺失的变量提供默认值或友好的错误提示

---

## 📊 技术指标

- **模板文件**：1 个 (nlu_system_template.txt, ~200 行)
- **配置代码**：~20 行 (vs V1.0 的 60 行)
- **变量支持**：5 个运行时注入变量
- **LangChain4J 兼容性**：100%
- **代码复杂度**：降低 60%

_📅 创建时间：2025 年 1 月_  
_🔄 架构版本：PipelinePromptTemplate v1.0_
