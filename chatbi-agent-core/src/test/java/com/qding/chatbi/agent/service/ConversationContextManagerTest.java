package com.qding.chatbi.agent.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qding.chatbi.agent.service.impl.ConversationContextManagerImpl;
import com.qding.chatbi.common.dto.StructuredQueryIntent;
import com.qding.chatbi.metadata.entity.ChatMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ConversationContextManager的单元测试
 */
public class ConversationContextManagerTest {

    @Mock
    private ConversationHistoryService historyService;

    private ObjectMapper objectMapper;
    private ConversationContextManager contextManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        objectMapper = new ObjectMapper();
        contextManager = new ConversationContextManagerImpl(historyService, objectMapper);
    }

    @Test
    void testSaveAndGetQueryIntent() {
        // 准备测试数据
        String sessionId = "test-session-123";
        StructuredQueryIntent intent = new StructuredQueryIntent();
        intent.setIntent("DATA_QUERY");
        intent.setQueryType("AGGREGATION_QUERY");

        Map<String, Object> entities = new HashMap<>();
        entities.put("metrics", Arrays.asList("成本"));
        entities.put("dimensions_to_group", Arrays.asList("city"));
        entities.put("filters", Arrays.asList(
                Map.of("fieldName", "city", "operator", "IN", "value", Arrays.asList("北京", "上海"))));
        intent.setEntities(entities);

        // 保存意图
        contextManager.saveQueryIntent(sessionId, intent);

        // 获取意图
        StructuredQueryIntent retrievedIntent = contextManager.getLastQueryIntent(sessionId);

        // 验证
        assertNotNull(retrievedIntent);
        assertEquals("DATA_QUERY", retrievedIntent.getIntent());
        assertEquals("AGGREGATION_QUERY", retrievedIntent.getQueryType());
        assertNotNull(retrievedIntent.getEntities());

        List<?> metrics = (List<?>) retrievedIntent.getEntities().get("metrics");
        assertTrue(metrics.contains("成本"));
    }

    @Test
    void testGetLastQueryIntentFromDatabase() throws Exception {
        // 准备测试数据
        String sessionId = "test-session-456";

        // 模拟数据库返回的历史消息
        List<ChatMessage> mockHistory = new ArrayList<>();

        // 添加一条包含结构化数据的AI消息
        ChatMessage aiMessage = new ChatMessage();
        aiMessage.setSenderType("AI_AGENT");

        StructuredQueryIntent savedIntent = new StructuredQueryIntent();
        savedIntent.setIntent("DATA_QUERY");
        savedIntent.setEntities(Map.of("metrics", Arrays.asList("销售额")));

        String structuredData = objectMapper.writeValueAsString(savedIntent);
        aiMessage.setStructuredResponseData(structuredData);

        mockHistory.add(aiMessage);

        // 配置mock
        when(historyService.getHistoryBySessionId(sessionId, 10)).thenReturn(mockHistory);

        // 测试
        StructuredQueryIntent retrievedIntent = contextManager.getLastQueryIntent(sessionId);

        // 验证
        assertNotNull(retrievedIntent);
        assertEquals("DATA_QUERY", retrievedIntent.getIntent());
        verify(historyService, times(1)).getHistoryBySessionId(sessionId, 10);
    }

    @Test
    void testClearContext() {
        // 准备测试数据
        String sessionId = "test-session-789";
        StructuredQueryIntent intent = new StructuredQueryIntent();
        intent.setIntent("DATA_QUERY");

        // 保存意图
        contextManager.saveQueryIntent(sessionId, intent);

        // 验证保存成功
        assertNotNull(contextManager.getLastQueryIntent(sessionId));

        // 清理上下文
        contextManager.clearContext(sessionId);

        // 验证清理成功（需要mock数据库返回空）
        when(historyService.getHistoryBySessionId(sessionId, 10)).thenReturn(new ArrayList<>());
        assertNull(contextManager.getLastQueryIntent(sessionId));
    }
}