package com.qding.chatbi.agent;

import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * 资源文件加载测试
 */
public class ResourceLoadTest {

    @Test
    public void testLoadPromptTemplate() throws IOException {
        // 测试不同的资源路径格式
        String[] paths = {
                "prompts/nlu_system_prompt_template.txt",
                "/prompts/nlu_system_prompt_template.txt",
                "classpath:prompts/nlu_system_prompt_template.txt"
        };

        for (String path : paths) {
            System.out.println("测试路径: " + path);
            try {
                // 方法1: 使用 ClassPathResource
                if (path.startsWith("classpath:")) {
                    ClassPathResource resource = new ClassPathResource(path.substring(10));
                    if (resource.exists()) {
                        String content = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
                        System.out.println("✅ 使用ClassPathResource成功加载，内容长度: " + content.length());
                    } else {
                        System.out.println("❌ ClassPathResource: 资源不存在");
                    }
                } else {
                    // 方法2: 使用 getResourceAsStream
                    InputStream is = this.getClass().getResourceAsStream(path.startsWith("/") ? path : "/" + path);
                    if (is != null) {
                        String content = StreamUtils.copyToString(is, StandardCharsets.UTF_8);
                        System.out.println("✅ 使用getResourceAsStream成功加载，内容长度: " + content.length());
                        is.close();
                    } else {
                        System.out.println("❌ getResourceAsStream: 资源不存在");
                    }
                }
            } catch (Exception e) {
                System.out.println("❌ 加载失败: " + e.getMessage());
            }
            System.out.println();
        }
    }
}