package com.qding.chatbi.agent.service;

import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import com.qding.chatbi.metadata.entity.QueryExample;
import com.qding.chatbi.metadata.repository.QueryExampleRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 同步QueryExample数据到Milvus向量数据库
 */
@SpringBootTest
@ActiveProfiles("dev")
public class SyncQueryExamplesToMilvus {

    @Autowired
    private QueryExampleRepository queryExampleRepository;

    @Autowired
    private KnowledgePersistenceService knowledgePersistenceService;

    @Test
    public void syncAllQueryExamplesToMilvus() {
        System.out.println("开始同步QueryExample数据到Milvus...");

        // 1. 从数据库读取所有QueryExample
        List<QueryExample> allExamples = queryExampleRepository.findAll();
        System.out.println("找到 " + allExamples.size() + " 条QueryExample记录");

        // 2. 逐条同步到Milvus
        int successCount = 0;
        int failCount = 0;

        for (QueryExample example : allExamples) {
            try {
                System.out.println("正在同步: " + example.getUserQuestion());
                knowledgePersistenceService.saveOrUpdateExample(example);
                successCount++;
                System.out.println("✓ 成功同步ID: " + example.getId());
            } catch (Exception e) {
                failCount++;
                System.err.println("✗ 同步失败ID: " + example.getId() + ", 错误: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 3. 输出统计结果
        System.out.println("\n同步完成!");
        System.out.println("成功: " + successCount + " 条");
        System.out.println("失败: " + failCount + " 条");
        System.out.println("总计: " + allExamples.size() + " 条");

        // 4. 测试搜索功能
        if (successCount > 0) {
            System.out.println("\n测试向量搜索功能...");
            testVectorSearch();
        }
    }

    private void testVectorSearch() {
        String[] testQueries = {
                "北京的销售额",
                "同比增长",
                "排名前五"
        };

        for (String query : testQueries) {
            System.out.println("\n搜索: '" + query + "'");
            try {
                List<QueryExample> results = knowledgePersistenceService.searchSimilarExamples(query, 3);
                if (results.isEmpty()) {
                    System.out.println("  未找到相似示例");
                } else {
                    for (QueryExample result : results) {
                        System.out.println("  - " + result.getUserQuestion());
                    }
                }
            } catch (Exception e) {
                System.err.println("  搜索失败: " + e.getMessage());
            }
        }
    }
}