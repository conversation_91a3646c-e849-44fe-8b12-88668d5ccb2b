# LangChain4j configuration from application.yml
langchain4j.dashscope.chat-model.api-key=sk-d37335951c6744ba80c147a8110acced
langchain4j.dashscope.chat-model.model-name=qwen-plus
langchain4j.dashscope.chat-model.temperature=0.7
langchain4j.dashscope.chat-model.log-requests=true
langchain4j.dashscope.chat-model.log-responses=true

# Milvus configuration from application.yml
langchain4j.embedding-store.milvus.host=localhost
langchain4j.embedding-store.milvus.port=19530
langchain4j.embedding-store.milvus.collection-name=default_collection
langchain4j.embedding-store.milvus.dimension=1536

# DataSource Configuration for H2 (Test specific)
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for H2 (Test specific)
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update 