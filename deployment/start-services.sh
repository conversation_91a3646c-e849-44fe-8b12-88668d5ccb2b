#!/bin/bash
set -e

# 获取脚本所在的目录
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# 切换到脚本所在目录，确保 docker-compose.yml 文件可以被找到
cd "$SCRIPT_DIR"

echo "========================================================="
echo "Starting all services using Docker Compose..."
echo "================================d========================="

docker compose up -d

echo "========================================================="
echo "All services started in detached mode."
echo "You can check the status using 'docker compose ps'."
echo "=========================================================" 