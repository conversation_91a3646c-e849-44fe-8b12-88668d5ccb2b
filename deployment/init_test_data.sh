#!/bin/bash
# =================================================================
# ChatBI 测试数据初始化脚本
# =================================================================

set -e # Exit immediately if a command exits with a non-zero status.

# --- Configuration ---
MYSQL_CONTAINER_NAME="chatbi-mysql"
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BUSINESS_DATA_SQL="init/test_data/01_create_property_business_data.sql"
METADATA_SQL="init/test_data/02_create_property_metadata.sql"

# --- Functions ---
function info() {
    echo "[INFO] $1"
}

function error() {
    echo "[ERROR] $1" >&2
    exit 1
}

# --- Main ---
info "Starting ChatBI test data initialization..."

# 1. Find MySQL container ID
CONTAINER_ID=$(docker ps -qf "name=${MYSQL_CONTAINER_NAME}")
if [ -z "${CONTAINER_ID}" ]; then
    error "MySQL container '${MYSQL_CONTAINER_NAME}' is not running. Please start the services first with 'start-services.sh'."
fi
info "Found MySQL container: ${CONTAINER_ID}"

# 2. Copy SQL scripts to container
info "Copying SQL scripts into the container..."
docker cp "${SCRIPT_DIR}/test_data/01_create_property_business_data.sql" "${CONTAINER_ID}:/tmp/01_business_data.sql"
docker cp "${SCRIPT_DIR}/test_data/02_create_property_metadata.sql" "${CONTAINER_ID}:/tmp/02_metadata.sql"

# 3. Execute SQL scripts inside container
info "Executing business data script (01_create_property_business_data.sql)..."
docker exec "${CONTAINER_ID}" mysql -uroot -p"password" -e "SOURCE /tmp/01_business_data.sql;"

info "Executing metadata script (02_create_property_metadata.sql)..."
docker exec "${CONTAINER_ID}" mysql -uroot -p"password" -e "SOURCE /tmp/02_metadata.sql;"

# 4. Clean up copied files
info "Cleaning up temporary SQL files from container..."
docker exec "${CONTAINER_ID}" rm /tmp/01_business_data.sql /tmp/02_metadata.sql

info "----------------------------------------------------------------"
info "✅ Test data initialization completed successfully!"
info ""
info "Next Step: Populate the vector database."
info "Please run the following command to restart the Milvus initializer:"
info "  docker-compose restart milvus-init"
info "----------------------------------------------------------------" 