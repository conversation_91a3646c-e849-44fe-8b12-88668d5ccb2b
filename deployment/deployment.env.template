# -----------------------------------------------------------------------------
# Environment variables for ChatBI Deployment
#
# Instructions:
# 1. Copy this file to a new file named '.env' in the same directory.
#    cp deployment.env.template .env
# 2. Edit the '.env' file with your specific settings.
# 3. The 'start-services.sh' script will automatically load variables from '.env'.
# -----------------------------------------------------------------------------

# --- Docker Volumes ---
# Sets the base directory where all persistent data (MySQL, Milvus, etc.) will be stored.
# If unset, it defaults to a 'volumes' directory inside the 'deployment' folder.
# DOCKER_VOLUME_DIRECTORY=./volumes

# --- MySQL Database ---
# The root password for the MySQL database.
# IMPORTANT: Change this to a strong, secure password.
MYSQL_ROOT_PASSWORD=your_strong_password

# --- LLM Provider API Keys ---
# API Key for the Dashscope (Qwen) models used by LangChain4j.
LANGCHAIN4J_DASHSCOPE_API_KEY=sk-d37335951c6744ba80c147a8110acced

# --- MinIO Object Storage ---
# Credentials for the MinIO service. These are the default values used by Milvus.
# It's recommended to change them for a production environment.
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin 