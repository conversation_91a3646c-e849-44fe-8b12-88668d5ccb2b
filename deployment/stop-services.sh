#!/bin/bash
set -e

# 获取脚本所在的目录
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# 切换到脚本所在目录，确保 docker-compose.yml 文件可以被找到
cd "$SCRIPT_DIR"

echo "========================================================="
echo "Stopping all services using Docker Compose..."
echo "========================================================="

docker compose down

echo "========================================================="
echo "All services have been stopped and removed."
echo "=========================================================" 