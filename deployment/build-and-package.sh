#!/bin/bash
set -e

# 设置项目根目录
PROJECT_ROOT=$(git rev-parse --show-toplevel)
cd "$PROJECT_ROOT"

echo "========================================================="
echo "Building all Java modules using Maven..."
echo "========================================================="
# 使用 -DskipTests 跳过测试，加快构建速度
mvn clean package -DskipTests

echo "========================================================="
echo "Building frontend application..."
echo "========================================================="
cd "$PROJECT_ROOT/chatbi-admin-frontend"
# 如果需要，可以先执行 npm install
npm install
npm run build

# 返回项目根目录
cd "$PROJECT_ROOT"

echo "========================================================="
echo "Building Docker images..."
echo "========================================================="

# 构建主应用服务镜像
echo "Building chatbi-application image..."
cd "$PROJECT_ROOT/chatbi-application"
docker build -t chatbi-application:latest .

# 构建前端Nginx镜像
echo "Building chatbi-admin-frontend image..."
cd "$PROJECT_ROOT/chatbi-admin-frontend"
docker build -t chatbi-admin-frontend:latest .


echo "========================================================="
echo "Build and packaging complete."
echo "========================================================="
echo "You can now use 'docker-compose up' to start the application." 