import os
import time
import json
from sqlalchemy import create_engine, text
from pymilvus import connections, utility, Collection, DataType, FieldSchema, CollectionSchema
import dashscope

# --- Configuration ---
MYSQL_USER = os.environ.get("MYSQL_USER", "root")
MYSQL_PASSWORD = os.environ.get("MYSQL_ROOT_PASSWORD", "password")
MYSQL_HOST = os.environ.get("MYSQL_HOST", "mysql")
MYSQL_PORT = os.environ.get("MYSQL_PORT", "3306")
MYSQL_DATABASE = os.environ.get("MYSQL_DATABASE", "chatbi_metadata")

MILVUS_HOST = os.environ.get("MILVUS_HOST", "milvus")
MILVUS_PORT = os.environ.get("MILVUS_PORT", "19530")

DASHSCOPE_API_KEY = os.environ.get("LANGCHAIN4J_DASHSCOPE_API_KEY")

# Milvus collection parameters
VECTOR_DIMENSION = 1024 # As per memory for text-embedding-v4
METRIC_TYPE = "COSINE"

# --- Helper Functions ---

def get_mysql_engine():
    """Creates and returns a SQLAlchemy engine for MySQL."""
    return create_engine(
        f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"
    )

def get_embedding(text_to_embed):
    """Generates embedding for a given text using DashScope API."""
    if not text_to_embed:
        return None
    try:
        resp = dashscope.TextEmbedding.call(
            model=dashscope.TextEmbedding.Models.text_embedding_v2, # Using v2 as v4 might not be in sdk, but dim is 1024
            input=text_to_embed
        )
        if resp.status_code == 200:
            return resp.output['embeddings'][0]['embedding']
        else:
            print(f"Error getting embedding for '{text_to_embed}': {resp.message}")
            return None
    except Exception as e:
        print(f"Exception while getting embedding: {e}")
        return None

def wait_for_service(host, port, service_name):
    """Waits for a service to be available."""
    print(f"Waiting for {service_name} at {host}:{port}...")
    while True:
        try:
            # A simple socket connection check
            with socket.create_connection((host, port), timeout=5):
                print(f"{service_name} is available.")
                break
        except OSError:
            time.sleep(5)

# --- Main Logic ---

def create_milvus_collection(collection_name, description):
    """Creates a Milvus collection if it doesn't exist."""
    if utility.has_collection(collection_name):
        print(f"Collection '{collection_name}' already exists.")
        return Collection(collection_name)

    # Define schema
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=False),
        FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=VECTOR_DIMENSION)
    ]
    schema = CollectionSchema(fields, description)
    
    collection = Collection(collection_name, schema)
    print(f"Collection '{collection_name}' created.")

    # Create index
    index_params = {
        "metric_type": METRIC_TYPE,
        "index_type": "IVF_FLAT",
        "params": {"nlist": 100}
    }
    collection.create_index(field_name="vector", index_params=index_params)
    print(f"Index created for collection '{collection_name}'.")
    return collection

def process_business_terms(mysql_conn, milvus_collection):
    """Fetches business terms, generates embeddings, and inserts into Milvus."""
    print("Processing business terms...")
    
    # In the memory, it says the vector is enriched. This is a simple implementation.
    # A more complex one might combine term, description, and reference name.
    query = "SELECT id, business_term, context_description FROM business_terminology"
    result = mysql_conn.execute(text(query)).fetchall()

    ids = []
    vectors = []
    
    for row in result:
        # Simple enrichment
        content_to_embed = f"业务术语: {row[1]}. 描述: {row[2]}"
        vector = get_embedding(content_to_embed)
        if vector:
            ids.append(row[0])
            vectors.append(vector)

    if ids:
        milvus_collection.insert([ids, vectors])
        print(f"Inserted {len(ids)} business terms into Milvus.")


def process_query_examples(mysql_conn, milvus_collection):
    """Fetches query examples, generates embeddings, and inserts into Milvus."""
    print("Processing query examples...")

    query = "SELECT id, user_question, notes FROM query_examples"
    result = mysql_conn.execute(text(query)).fetchall()
    
    ids = []
    vectors = []
    
    for row in result:
        content_to_embed = f"用户问题: {row[1]}. 相关说明: {row[2]}"
        vector = get_embedding(content_to_embed)
        if vector:
            ids.append(row[0])
            vectors.append(vector)

    if ids:
        milvus_collection.insert([ids, vectors])
        print(f"Inserted {len(ids)} query examples into Milvus.")


def main():
    """Main function to orchestrate the Milvus initialization."""
    if not DASHSCOPE_API_KEY:
        print("Error: DASHSCOPE_API_KEY environment variable not set. Exiting.")
        return

    dashscope.api_key = DASHSCOPE_API_KEY
    
    wait_for_service(MYSQL_HOST, int(MYSQL_PORT), "MySQL")
    wait_for_service(MILVUS_HOST, int(MILVUS_PORT), "Milvus")

    # Connect to services
    mysql_engine = get_mysql_engine()
    connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)
    
    print("Successfully connected to MySQL and Milvus.")

    # Create collections
    terms_collection = create_milvus_collection("business_terms", "Business Terminology Embeddings")
    examples_collection = create_milvus_collection("query_examples", "Query Example Embeddings")

    # Process and insert data
    with mysql_engine.connect() as conn:
        process_business_terms(conn, terms_collection)
        process_query_examples(conn, examples_collection)

    # Load collections into memory for searching
    terms_collection.load()
    examples_collection.load()
    
    print("Milvus collections loaded into memory.")
    print("Milvus initialization completed successfully!")


if __name__ == "__main__":
    import socket
    main() 