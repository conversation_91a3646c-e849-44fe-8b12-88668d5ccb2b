-- =================================================================
-- ChatBI 物业公司模拟业务数据初始化脚本 (反规范化版)
-- 所有维度数据冗余到事实表中，无JOIN。
-- =================================================================

-- 创建业务数据库
CREATE DATABASE IF NOT EXISTS `chatbi_metadata` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `chatbi_metadata`;

-- =================================================================
-- 1. 月度经营数据事实表 (fact_monthly_financials)
-- 包含冗余的项目和组织架构维度
-- =================================================================
DROP TABLE IF EXISTS `fact_monthly_financials`;
CREATE TABLE `fact_monthly_financials` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `month` DATE NOT NULL COMMENT '月份(当月第一天)',
  `project_name` VARCHAR(255) NOT NULL,
  `district_name` VARCHAR(100) NOT NULL,
  `city_name` VARCHAR(100) NOT NULL,
  `region_name` VARCHAR(100) NOT NULL,
  `project_type` VARCHAR(50) NOT NULL,
  `management_area_sqm` DECIMAL(10, 2) NOT NULL,
  `property_fee_income` DECIMAL(15, 2) NOT NULL,
  `value_added_service_income` DECIMAL(15, 2) NOT NULL,
  `energy_cost` DECIMAL(15, 2) NOT NULL,
  `maintenance_cost` DECIMAL(15, 2) NOT NULL,
  `labor_cost` DECIMAL(15, 2) NOT NULL,
  `total_income` DECIMAL(15, 2) GENERATED ALWAYS AS (property_fee_income + value_added_service_income) STORED,
  `total_cost` DECIMAL(15, 2) GENERATED ALWAYS AS (energy_cost + maintenance_cost + labor_cost) STORED,
  `gross_profit` DECIMAL(15, 2) GENERATED ALWAYS AS ((property_fee_income + value_added_service_income) - (energy_cost + maintenance_cost + labor_cost)) STORED,
  INDEX `idx_month_region_city` (`month`, `region_name`, `city_name`)
) ENGINE=InnoDB COMMENT='月度经营数据事实表(反规范化)';


-- =================================================================
-- 2. 每日运营数据事实表 (fact_daily_operations)
-- 包含冗余的项目和组织架构维度
-- =================================================================
DROP TABLE IF EXISTS `fact_daily_operations`;
CREATE TABLE `fact_daily_operations` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `date` DATE NOT NULL,
  `project_name` VARCHAR(255) NOT NULL,
  `district_name` VARCHAR(100) NOT NULL,
  `city_name` VARCHAR(100) NOT NULL,
  `region_name` VARCHAR(100) NOT NULL,
  `project_type` VARCHAR(50) NOT NULL,
  `work_order_count` INT NOT NULL,
  `complaint_count` INT NOT NULL,
  `repair_count` INT NOT NULL,
  `avg_satisfaction_score` DECIMAL(3, 2),
  `fee_collection_rate` DECIMAL(5, 4),
  INDEX `idx_date_region_city` (`date`, `region_name`, `city_name`)
) ENGINE=InnoDB COMMENT='每日运营数据事实表(反规范化)';

-- =================================================================
-- 3. 插入模拟数据
-- 使用临时表和存储过程来生成反规范化的数据
-- =================================================================
DELIMITER $$

CREATE PROCEDURE `generate_denormalized_data`()
BEGIN
    -- 1. 创建一个临时表来存储维度信息
    DROP TEMPORARY TABLE IF EXISTS temp_dim_projects;
    CREATE TEMPORARY TABLE temp_dim_projects (
      `project_name` VARCHAR(255) NOT NULL, `district_name` VARCHAR(100) NOT NULL,
      `city_name` VARCHAR(100) NOT NULL, `region_name` VARCHAR(100) NOT NULL,
      `project_type` VARCHAR(50) NOT NULL, `management_area_sqm` DECIMAL(10, 2) NOT NULL
    );

    INSERT INTO temp_dim_projects VALUES
    ('东方明珠苑', '浦东新区', '上海', '华东大区', '住宅', 120000.00),
    ('未来科技城A座', '余杭区', '杭州', '华东大区', '写字楼', 85000.00),
    ('金陵国际广场', '玄武区', '南京', '华东大区', '商业综合体', 150000.00),
    ('静安府', '静安区', '上海', '华东大区', '住宅', 95000.00),
    ('珠江新城壹号', '天河区', '广州', '华南大区', '写字楼', 110000.00),
    ('深圳湾一号', '南山区', '深圳', '华南大区', '住宅', 200000.00),
    ('绿城桂语兰庭', '青秀区', '南宁', '华南大区', '住宅', 75000.00),
    ('国贸中心三期', '朝阳区', '北京', '华北大区', '商业综合体', 250000.00),
    ('金融街B座', '西城区', '北京', '华北大区', '写字楼', 130000.00),
    ('海河之星', '和平区', '天津', '华北大区', '住宅', 80000.00);

    -- 2. 循环临时表生成数据
    BEGIN
        DECLARE v_project_name VARCHAR(255);
        DECLARE v_district_name, v_city_name, v_region_name, v_project_type VARCHAR(100);
        DECLARE v_area DECIMAL(10,2);
        DECLARE done INT DEFAULT FALSE;
        DECLARE cur_project CURSOR FOR SELECT * FROM temp_dim_projects;
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        OPEN cur_project;
        read_loop: LOOP
            FETCH cur_project INTO v_project_name, v_district_name, v_city_name, v_region_name, v_project_type, v_area;
            IF done THEN
                LEAVE read_loop;
            END IF;

            -- 生成月度财务数据
            SET @d_fin = '2024-01-01';
            WHILE @d_fin <= '2024-06-01' DO
                INSERT INTO fact_monthly_financials(month, project_name, district_name, city_name, region_name, project_type, management_area_sqm, property_fee_income, value_added_service_income, energy_cost, maintenance_cost, labor_cost)
                VALUES (@d_fin, v_project_name, v_district_name, v_city_name, v_region_name, v_project_type, v_area,
                    v_area * (15 + RAND() * 5), v_area * (2 + RAND() * 3), v_area * (3 + RAND() * 2),
                    v_area * (1.5 + RAND()), v_area * (5 + RAND() * 2));
                SET @d_fin = DATE_ADD(@d_fin, INTERVAL 1 MONTH);
            END WHILE;

            -- 生成每日运营数据
            SET @d_ops = '2024-06-01';
            WHILE @d_ops <= '2024-06-30' DO
                SET @work_orders = 5 + FLOOR(RAND() * 20);
                SET @complaints = FLOOR(@work_orders * RAND() * 0.2);
                INSERT INTO fact_daily_operations(date, project_name, district_name, city_name, region_name, project_type, work_order_count, complaint_count, repair_count, avg_satisfaction_score, fee_collection_rate)
                VALUES (@d_ops, v_project_name, v_district_name, v_city_name, v_region_name, v_project_type,
                    @work_orders, @complaints, @work_orders - @complaints, 0.85 + (RAND() * 0.14), 0.92 + (RAND() * 0.079));
                SET @d_ops = DATE_ADD(@d_ops, INTERVAL 1 DAY);
            END WHILE;
        END LOOP;
        CLOSE cur_project;
    END;
    
    -- 3. 删除临时表
    DROP TEMPORARY TABLE IF EXISTS temp_dim_projects;
END$$

DELIMITER ;

-- 调用存储过程生成数据
CALL generate_denormalized_data();

-- 删除存储过程
DROP PROCEDURE `generate_denormalized_data`; 