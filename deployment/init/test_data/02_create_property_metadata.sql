-- =================================================================
-- ChatBI 物业公司模拟元数据初始化脚本 (反规范化版)
-- =================================================================

USE `chatbi_metadata`;

-- ============================================
-- 1. 数据库源配置 (database_source_configs)
-- (假设 ID=2 用于测试数据源)
-- ============================================
INSERT INTO `database_source_configs` (
  `id`, `source_name`, `database_type`, `connection_parameters`, `description`, `status`
) VALUES (
  2, '物业模拟业务数据源', 'MYSQL',
  '{
    "host": "mysql", "port": 3306, "username": "root",
    "password": "password", "databaseName": "chatbi_business_data"
  }',
  '用于ChatBI物业管理场景端到端测试的MySQL数据库', 'active'
) ON DUPLICATE KEY UPDATE id=VALUES(id);

-- ============================================
-- 2. 角色和权限 (roles & dataset_access_permissions)
-- ============================================
TRUNCATE TABLE `roles`;
INSERT INTO `roles` (`id`, `role_name`, `description`) VALUES
(1, '集团高管', '拥有所有数据的最高查询权限'),
(2, '大区运营总监', '负责指定大区的整体运营和财务状况监控'),
(3, '城市物业经理', '负责指定城市的项目运营细节和业主满意度');

TRUNCATE TABLE `dataset_access_permissions`;
-- 集团高管可以访问所有数据集
INSERT INTO `dataset_access_permissions` (`role_id`, `dataset_id`) VALUES (1, 10), (1, 11);
-- 大区总监可以访问所有数据集 (应用层需根据其所属大区进行数据行级限制)
INSERT INTO `dataset_access_permissions` (`role_id`, `dataset_id`) VALUES (2, 10), (2, 11);
-- 城市经理可以访问所有数据集 (应用层需根据其所属城市进行数据行级限制)
INSERT INTO `dataset_access_permissions` (`role_id`, `dataset_id`) VALUES (3, 10), (3, 11);

-- ============================================
-- 3. 可查询数据集 (queryable_datasets)
-- (为避免ID冲突, ID从10开始)
-- ============================================
TRUNCATE TABLE `queryable_datasets`;
INSERT INTO `queryable_datasets` (
  `id`, `dataset_name`, `description`, `database_source_id`, `base_object_name`, `base_object_type`, `synonyms`, `data_owner`
) VALUES
(10, '项目月度经营分析', '各项目每月收入、成本、利润等核心财务指标分析', 2, 'fact_monthly_financials', 'TABLE', '["月度财务", "经营状况", "收入报表"]', '财务部'),
(11, '项目每日运营分析', '各项目每日工单、满意度、收缴率等核心运营指标分析', 2, 'fact_daily_operations', 'TABLE', '["每日运营", "工单分析", "服务质量"]', '运营部');

-- ============================================
-- 4. 数据集字段 (dataset_columns)
-- ============================================
TRUNCATE TABLE `dataset_columns`;
-- 4.1 项目月度经营分析 (Dataset ID: 10)
INSERT INTO `dataset_columns` (`dataset_id`, `column_name`, `technical_name_or_expression`, `semantic_type`, `data_type`, `description`, `synonyms`) VALUES
-- Dimensions
(10, '月份', 'month', 'TIME_DIMENSION', 'DATE', '经营数据所属月份', '["时间"]'),
(10, '大区', 'region_name', 'DIMENSION', 'VARCHAR', '项目所属大区', '["区域"]'),
(10, '城市', 'city_name', 'DIMENSION', 'VARCHAR', '项目所属城市', '[]'),
(10, '片区', 'district_name', 'DIMENSION', 'VARCHAR', '项目所属片区', '[]'),
(10, '项目名称', 'project_name', 'DIMENSION', 'VARCHAR', '物业项目具体名称', '["项目"]'),
(10, '项目类型', 'project_type', 'DIMENSION', 'VARCHAR', '项目类型，如住宅、写字楼', '[]'),
-- Metrics
(10, '物业费收入', 'property_fee_income', 'METRIC', 'DECIMAL', '物业费总收入', '["物业收入"]'),
(10, '增值服务收入', 'value_added_service_income', 'METRIC', 'DECIMAL', '社区零售、广告等增值服务收入', '["增收"]'),
(10, '总收入', 'total_income', 'METRIC', 'DECIMAL', '总收入（物业费+增值服务）', '["全部收入"]'),
(10, '能耗成本', 'energy_cost', 'METRIC', 'DECIMAL', '公区水电等能源消耗成本', '[]'),
(10, '维保成本', 'maintenance_cost', 'METRIC', 'DECIMAL', '设备设施维护保养成本', '["维修成本"]'),
(10, '人工成本', 'labor_cost', 'METRIC', 'DECIMAL', '员工薪酬福利等人工成本', '["人力成本"]'),
(10, '总成本', 'total_cost', 'METRIC', 'DECIMAL', '总成本（能耗+维保+人工）', '["全部成本"]'),
(10, '毛利润', 'gross_profit', 'METRIC', 'DECIMAL', '毛利润（总收入-总成本）', '["利润"]'),
(10, '管理面积', 'management_area_sqm', 'METRIC', 'DECIMAL', '项目管理的建筑总面积', '["面积"]'),
(10, '管理坪效', 'total_income / management_area_sqm', 'METRIC', 'DECIMAL', '单位管理面积产生的收入', '["坪效"]');

-- 4.2 项目每日运营分析 (Dataset ID: 11)
INSERT INTO `dataset_columns` (`dataset_id`, `column_name`, `technical_name_or_expression`, `semantic_type`, `data_type`, `description`, `synonyms`) VALUES
-- Dimensions
(11, '日期', 'date', 'TIME_DIMENSION', 'DATE', '运营数据所属日期', '["时间"]'),
(11, '大区', 'region_name', 'DIMENSION', 'VARCHAR', '项目所属大区', '["区域"]'),
(11, '城市', 'city_name', 'DIMENSION', 'VARCHAR', '项目所属城市', '[]'),
(11, '片区', 'district_name', 'DIMENSION', 'VARCHAR', '项目所属片区', '[]'),
(11, '项目名称', 'project_name', 'DIMENSION', 'VARCHAR', '物业项目具体名称', '["项目"]'),
(11, '项目类型', 'project_type', 'DIMENSION', 'VARCHAR', '项目类型，如住宅、写字楼', '[]'),
-- Metrics
(11, '工单总量', 'work_order_count', 'METRIC', 'INT', '当日产生的工单总数', '["工单数", "报事数"]'),
(11, '投诉工单数', 'complaint_count', 'METRIC', 'INT', '当日产生的投诉类工单数', '["投诉数"]'),
(11, '维修工单数', 'repair_count', 'METRIC', 'INT', '当日产生的维修类工单数', '["维修数"]'),
(11, '投诉率', 'complaint_count / work_order_count', 'METRIC', 'DECIMAL', '投诉工单占总工单的比例', '["投诉占比"]'),
(11, '业主满意度', 'avg_satisfaction_score', 'METRIC', 'DECIMAL', '当日工单的平均满意度评分', '["满意度"]'),
(11, '物业费收缴率', 'fee_collection_rate', 'METRIC', 'DECIMAL', '当期物业费的收缴完成比例', '["收缴率"]');


-- ============================================
-- 5. 业务术语 (business_terminology)
-- ============================================
TRUNCATE TABLE `business_terminology`;
INSERT INTO `business_terminology` (`business_term`, `standard_reference_type`, `standard_reference_name`) VALUES
('坪效', 'DatasetColumn', '管理坪效'),
('增收', 'DatasetColumn', '增值服务收入'),
('报事', 'DatasetColumn', '工单总量');

-- ============================================
-- 6. 查询示例 (query_examples)
-- ============================================
TRUNCATE TABLE `query_examples`;
INSERT INTO `query_examples` (`user_question`, `target_dataset_id`, `notes`) VALUES
('上个月华南大区的总收入和总成本分别是多少？', 10, '大区级归集查询'),
('对比一下北京和上海所有写字楼项目的月均坪效', 10, '跨城市、带筛选条件的对比查询'),
('深圳南山片区哪个项目的毛利润最高？', 10, '片区级下钻排名查询'),
('昨天哪个大区的投诉率最高？', 11, '指标计算和层级归集查询'),
('近一周杭州所有住宅项目的平均满意度趋势', 11, '时间趋势分析查询'); 