version: "3.8"

networks:
  chatbi-net:
    driver: bridge

services:
  etcd:
    image: quay.io/coreos/etcd:v3.5.13
    container_name: milvus-etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-./volumes}/etcd:/etcd
    networks:
      - chatbi-net

  minio:
    image: quay.io/minio/minio:RELEASE.2024-05-22T03-12-59Z
    container_name: milvus-minio
    command: minio server /minio_data --console-address ":9090"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin}
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-./volumes}/minio:/minio_data
    ports:
      - "9001:9000"
      - "9091:9090"
    networks:
      - chatbi-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  milvus-standalone:
    image: milvusdb/milvus:v2.3.21-cpu
    container_name: milvus-standalone
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-./volumes}/milvus:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9092:9091"
    depends_on:
      - etcd
      - minio
    networks:
      - chatbi-net

  mysql:
    image: mysql:8.0
    container_name: chatbi-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-password}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-chatbi_metadata}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./deployment/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - chatbi-net

  chatbi-application:
    image: chatbi-application:latest
    container_name: chatbi-application
    restart: on-failure
    ports:
      - "8081:8081"
    environment:
      - SPRING_DATASOURCE_URL=*****************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_ROOT_PASSWORD:-password}
      - LANGCHAIN4J_EMBEDDING_STORE_MILVUS_HOST=milvus-standalone
      - MILVUS_HOST=milvus-standalone
      - LANGCHAIN4J_DASHSCOPE_CHAT_MODEL_API_KEY=${LANGCHAIN4J_DASHSCOPE_API_KEY}
      - LANGCHAIN4J_DASHSCOPE_EMBEDDING_MODEL_API_KEY=${LANGCHAIN4J_DASHSCOPE_API_KEY}
    depends_on:
      mysql:
        condition: service_healthy
      milvus-standalone:
        condition: service_started # Milvus doesn't have a simple healthcheck
      etcd:
        condition: service_started
      minio:
        condition: service_started
    networks:
      - chatbi-net

  chatbi-admin-frontend:
    image: chatbi-admin-frontend:latest
    container_name: chatbi-admin-frontend
    restart: on-failure
    ports:
      - "3000:80"
    depends_on:
      - chatbi-application
    networks:
      - chatbi-net

  milvus-init:
    build:
      context: .
      dockerfile: deployment/init/milvus-init.Dockerfile
    container_name: chatbi-milvus-init
    restart: "no"
    environment:
      MYSQL_USER: root
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-password}
      MYSQL_HOST: mysql
      MYSQL_DATABASE: ${MYSQL_DATABASE:-chatbi_metadata}
      MILVUS_HOST: milvus-standalone
      LANGCHAIN4J_DASHSCOPE_API_KEY: ${LANGCHAIN4J_DASHSCOPE_API_KEY}
    depends_on:
      chatbi-application: # Depends on application because app might do some schema migration on startup
        condition: service_started
      mysql:
        condition: service_healthy
      milvus-standalone:
        condition: service_started
    networks:
      - chatbi-net

volumes:
  etcd:
  minio:
  milvus:
  mysql-data:
