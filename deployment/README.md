# ChatBI 应用部署指南

本文档提供了在本地环境中使用 Docker Compose 部署 ChatBI 应用所需的所有步骤。

## 目录

- [先决条件](#先决条件)
- [部署结构](#部署结构)
- [配置](#配置)
- [部署步骤](#部署步骤)
- [管理服务](#管理服务)
- [清理环境](#清理环境)

## 先决条件

在开始之前，请确保您的系统已安装以下软件：

- **Docker 和 Docker Compose**: 用于容器化部署和编排。
- **Git**: 用于克隆项目代码。
- **Java 17 (or later)**: 用于构建后端 Java 模块。
- **Node.js 18 (or later)**: 用于构建前端应用。
- **Maven**: 用于构建 Java 项目。
- **npm**: 用于管理 Node.js 项目依赖。

## 部署结构

所有部署相关的文件都位于本 `deployment` 目录中。

- `build-and-package.sh`: 编译所有源代码并构建 Docker 镜像的脚本。
- `docker-compose.yml`: 定义所有服务（后端、前端、数据库、向量存储等）的容器化配置。
- `deployment.env.template`: 环境变量模板文件。
- `start-services.sh`: 启动所有服务的脚本。
- `stop-services.sh`: 停止所有服务的脚本。
- `cleanup.sh`: 完全清理部署环境（包括数据卷）的脚本。

## 配置

在部署之前，您需要配置环境变量。

1.  **创建 `.env` 文件**:
    将 `deployment.env.template` 文件复制为 `.env`。此文件被 git 忽略，可安全存放敏感信息。

    ```bash
    cp deployment/deployment.env.template deployment/.env
    ```

2.  **编辑 `.env` 文件**:
    打开 `deployment/.env` 文件，根据您的需求修改以下变量：
    - `MYSQL_ROOT_PASSWORD`: 设置一个安全的 MySQL 数据库 root 密码。
    - `LANGCHAIN4J_DASHSCOPE_API_KEY`: 填入您的 Dashscope（通义千问）API 密钥。
    - `DOCKER_VOLUME_DIRECTORY` (可选): 如果您想将 Docker 数据卷存储在默认的 `deployment/volumes` 之外的其它位置，请取消注释并设置此变量。

## 部署步骤

请按照以下步骤执行全自动部署：

1.  **给予脚本执行权限**:
    (此步骤仅需在首次部署时执行)

    ```bash
    chmod +x deployment/*.sh
    ```

2.  **构建和打包**:
    运行此脚本会编译所有 Java 和前端代码，并构建所有需要的 Docker 镜像。

    ```bash
    ./deployment/build-and-package.sh
    ```

3.  **启动服务**:
    构建完成后，运行此脚本以启动所有服务。

    ```bash
    ./deployment/start-services.sh
    ```

    启动后，您可以通过以下地址访问应用：

    - **管理后台**: `http://localhost:3000`
    - **主应用 API**: `http://localhost:8081`
    - **MinIO Console**: `http://localhost:9091`
    - **Milvus Attu UI**: `http://localhost:9092` (新版 Milvus 可能不自带 Attu，需单独部署)

## 管理服务

- **查看服务状态**:

  ```bash
  docker compose -f deployment/docker-compose.yml ps
  ```

- **查看服务日志**:

  ```bash
  # 查看所有服务的日志
  docker compose -f deployment/docker-compose.yml logs -f

  # 查看特定服务的日志 (例如 chatbi-application)
  docker compose -f deployment/docker-compose.yml logs -f chatbi-application
  ```

- **停止服务**:
  此命令会停止并移除所有服务容器，但不会删除数据卷。
  ```bash
  ./deployment/stop-services.sh
  ```

## 清理环境

如果您希望彻底清理部署环境，包括删除所有容器、网络和**数据卷**（MySQL 数据、Milvus 数据等），请运行：

> **警告**: 此操作会删除所有持久化数据，请谨慎操作！

```bash
./deployment/cleanup.sh
```
