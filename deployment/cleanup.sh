#!/bin/bash
set -e

# 获取脚本所在的目录
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# 切换到脚本所在目录，确保 docker-compose.yml 文件可以被找到
cd "$SCRIPT_DIR"

echo "========================================================="
echo "Stopping and removing all services, volumes, and networks..."
echo "========================================================="

# --volumes 会移除 docker-compose.yml 中定义的命名卷
docker compose down --volumes

echo "========================================================="
echo "Services and volumes removed."
echo "========================================================="

echo "========================================================="
echo "Removing dangling Docker images..."
echo "========================================================="

# 清理未被使用的（悬空）镜像，释放磁盘空间
docker image prune -f

echo "========================================================="
echo "Cleanup complete."
echo "=========================================================" 