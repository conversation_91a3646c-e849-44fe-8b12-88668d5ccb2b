package com.qding.chatbi.api.vo;

import java.util.Map;

/**
 * Represents the payload for a user's response to a clarification request.
 */
public class ClarificationApiRequest {

    private String optionId;
    private Map<String, Object> payload;

    // Getters and Setters

    public String getOptionId() {
        return optionId;
    }

    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    public Map<String, Object> getPayload() {
        return payload;
    }

    public void setPayload(Map<String, Object> payload) {
        this.payload = payload;
    }

    // Optional: toString, equals, hashCode if deemed necessary
}
