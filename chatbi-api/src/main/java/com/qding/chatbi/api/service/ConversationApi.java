package com.qding.chatbi.api.service;

import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.UserQueryRequest;
import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.dto.SessionInfoDTO; // Assuming this DTO exists

import java.util.Map;

/**
 * Defines the core conversation processing service interface.
 * This contract is to be implemented by the chatbi-agent-core module.
 */
public interface ConversationApi {

    /**
     * Processes a user's query.
     *
     * @param request The user query request, containing user ID, session ID, query text, etc.
     * @return AgentResponse The AI Agent's processing result.
     * @throws ChatBiException If a business-level error occurs.
     */
    AgentResponse processUserQuery(UserQueryRequest request) throws ChatBiException;

    /**
     * Processes a user's response to a clarification request from the agent.
     *
     * @param sessionId The current session ID.
     * @param optionId The ID of the clarification option selected by the user.
     * @param payload Additional data associated with the selected option.
     * @return AgentResponse The AI Agent's processing result after clarification.
     * @throws ChatBiException If a business-level error occurs.
     */
    AgentResponse processClarificationResponse(String sessionId, String optionId, Map<String, Object> payload) throws ChatBiException;

    /**
     * (Optional) Retrieves detailed information or history for a specific session.
     *
     * @param sessionId The ID of the session to retrieve details for.
     * @return SessionInfoDTO Containing session details.
     * @throws ChatBiException If an error occurs while fetching session details.
     */
    SessionInfoDTO getSessionDetails(String sessionId) throws ChatBiException;
}
