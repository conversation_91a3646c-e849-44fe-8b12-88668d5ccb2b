package com.qding.chatbi.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于检查系统组件状态
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired(required = false)
    @Qualifier("nluSystemPromptTemplate")
    private String nluSystemPromptTemplate;

    @GetMapping("/nlu-status")
    public Map<String, Object> checkNluStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("timestamp", System.currentTimeMillis());

        if (nluSystemPromptTemplate != null) {
            status.put("nluTemplateLoaded", true);
            status.put("templateLength", nluSystemPromptTemplate.length());
            status.put("templatePreview",
                    nluSystemPromptTemplate.substring(0, Math.min(100, nluSystemPromptTemplate.length())));
        } else {
            status.put("nluTemplateLoaded", false);
            status.put("error", "NLU系统提示词模板未加载");
        }

        return status;
    }
}