package com.qding.chatbi.api.controller;

import com.qding.chatbi.api.service.ConversationApi;
import com.qding.chatbi.api.vo.ClarificationApiRequest; // Created in previous step
import com.qding.chatbi.common.dto.AgentResponse;
import com.qding.chatbi.common.dto.SessionInfoDTO; // Added
import com.qding.chatbi.common.dto.UserQueryRequest;
// Assuming ChatBiException is a runtime exception or handled by a global handler
// import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.enums.ResponseType; // Added for checking response type

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus; // Added for HttpStatus.INTERNAL_SERVER_ERROR
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping; // Added
import org.springframework.web.bind.annotation.PathVariable; // Already present implicitly via other annotations
import org.springframework.web.bind.annotation.PostMapping; // Already present implicitly via other annotations
import org.springframework.web.bind.annotation.RequestBody; // Already present implicitly via other annotations
import org.springframework.web.bind.annotation.RequestMapping; // Already present implicitly via other annotations
import org.springframework.web.bind.annotation.RestController; // Already present implicitly via other annotations


import jakarta.validation.Valid; // For @Valid annotation - Changed from javax.validation.Valid
import java.util.HashMap;
import java.util.Map; // Fallback if ClarificationApiRequest was not used, but it is.

/**
 * REST Controller for handling chat conversations with the BI agent.
 */
@RestController
@RequestMapping("/api/v1/chatbi/conversation")
public class ConversationController {

    private static final Logger log = LoggerFactory.getLogger(ConversationController.class);

    // TODO: These constants should ideally be referenced from a common ErrorCode enum/class
    // e.g., import com.qding.chatbi.common.enums.ErrorCode;
    private static final String ERROR_CODE_INVALID_INPUT = "BIE001";
    private static final String ERROR_CODE_PERMISSION_DENIED = "BIE003";

    private final ConversationApi conversationService;

    @Autowired
    public ConversationController(ConversationApi conversationService) {
        this.conversationService = conversationService;
    }

    /**
     * Handles a user's query.
     *
     * @param request The user query request.
     * @return The agent's response.
     */
    @PostMapping("/query")
    public ResponseEntity<AgentResponse> handleUserQuery(@RequestBody @Valid UserQueryRequest request) {
        log.info("==================== 收到用户查询请求 ====================");
        log.info("用户ID: {}，会话ID: {}，查询内容: {}，附加参数: {}", request.getUserId(),request.getSessionId(),request.getQueryText(),request.getAdditionalParams());
        long startTime = System.currentTimeMillis();
        
        try {
            AgentResponse response = conversationService.processUserQuery(request);
            long processingTime = System.currentTimeMillis() - startTime;
            log.info("==================== 用户查询处理完成 ====================");
            log.info("处理耗时: {}ms,用户ID: {},响应类型: {},会话ID: {},响应消息: {},原始查询: {},实际执行的SQL语句: {}",
                    processingTime,request.getUserId(),response.getResponseType(),response.getSessionId(),
                    response.getMessageToUser(),response.getOriginalQuery(),response.getQueryResult()==null?null:response.getQueryResult().getQuerySql());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("==================== 用户查询处理失败 ====================");
            log.error("处理耗时: {}ms", processingTime);
            log.error("错误信息: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Handles a user's response to a clarification request.
     *
     * @param sessionId The ID of the ongoing session.
     * @param clarificationRequest The payload containing the user's selected option and any associated data.
     * @return ResponseEntity containing the AgentResponse.
     */
    @PostMapping("/{sessionId}/clarify")
    public ResponseEntity<AgentResponse> handleClarification(
            @PathVariable String sessionId,
            @RequestBody ClarificationApiRequest clarificationRequest) {
        
        log.info("==================== 收到澄清响应请求 ====================");
        log.info("会话ID: {}", sessionId);
        log.info("选择的选项ID: {}", clarificationRequest.getOptionId());
        log.info("附加载荷: {}", clarificationRequest.getPayload());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Call the core service
            AgentResponse response = conversationService.processClarificationResponse(
                    sessionId,
                    clarificationRequest.getOptionId(),
                    clarificationRequest.getPayload());

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("==================== 澄清响应处理完成 ====================");
            log.info("处理耗时: {}ms", processingTime);
            log.info("响应类型: {}", response.getResponseType());
            
            // Apply the same response logic as in handleUserQuery
            if (ResponseType.ERROR.equals(response.getResponseType())) {
                String errorCode = response.getErrorCode();
                if (ERROR_CODE_INVALID_INPUT.equals(errorCode) || ERROR_CODE_PERMISSION_DENIED.equals(errorCode)) {
                    return ResponseEntity.badRequest().body(response);
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                }
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("==================== 澄清响应处理失败 ====================");
            log.error("处理耗时: {}ms", processingTime);
            log.error("错误信息: {}", e.getMessage(), e);
            throw e;
        }
    }

    // It's good practice to have a @ControllerAdvice class to handle exceptions globally,
    // converting ChatBiException (and other exceptions) to appropriate HTTP responses.

    /**
     * Retrieves details for a specific session.
     *
     * @param sessionId The ID of the session.
     * @return The session details.
     */
    @GetMapping("/{sessionId}")
    public ResponseEntity<SessionInfoDTO> handleGetSessionDetails(@PathVariable String sessionId) {
        log.info("==================== 获取会话详情请求 ====================");
        log.info("会话ID: {}", sessionId);
        
        try {
            SessionInfoDTO sessionInfo = conversationService.getSessionDetails(sessionId);
            if (sessionInfo != null) {
                log.info("会话详情获取成功，消息数量: {}", sessionInfo.getMessageCount());
                return ResponseEntity.ok(sessionInfo);
            } else {
                log.warn("会话不存在: {}", sessionId);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取会话详情失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
