<html><head>
    <meta charset="utf-8"/>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B600%3B700&amp;family=Noto+Sans%3Awght%40400%3B500%3B600%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <title>Enterprise Login</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
          :root {
            --brand-primary: #0c7ff2;
            --brand-secondary: #e7edf4;
            --text-primary: #0d141c;
            --text-secondary: #49739c;
            --surface-primary: #ffffff;
            --surface-secondary: #f8fafc;}
          body {
            font-family: "Inter", "Noto Sans", sans-serif;
          }
        </style>
    </head>
    <body class="bg-[var(--surface-secondary)]">
    <div class="relative flex min-h-screen flex-col items-center justify-center group/design-root overflow-x-hidden">
    <div class="flex flex-col w-full max-w-md p-6 md:p-8 bg-[var(--surface-primary)] shadow-xl rounded-xl">
    <header class="flex flex-col items-center mb-8">
    <div class="flex items-center gap-3 mb-2">
    <div class="size-8 text-[var(--brand-primary)]">
    <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_6_319)">
    <path d="M8.57829 8.57829C5.52816 11.6284 3.451 15.5145 2.60947 19.7452C1.76794 23.9758 2.19984 28.361 3.85056 32.3462C5.50128 36.3314 8.29667 39.7376 11.8832 42.134C15.4698 44.5305 19.6865 45.8096 24 45.8096C28.3135 45.8096 32.5302 44.5305 36.1168 42.134C39.7033 39.7375 42.4987 36.3314 44.1494 32.3462C45.8002 28.361 46.2321 23.9758 45.3905 19.7452C44.549 15.5145 42.4718 11.6284 39.4217 8.57829L24 24L8.57829 8.57829Z" fill="currentColor"></path>
    </g>
    <defs>
    <clipPath id="clip0_6_319"><rect fill="white" height="48" width="48"></rect></clipPath>
    </defs>
    </svg>
    </div>
    <h1 class="text-[var(--text-primary)] text-2xl font-bold tracking-tight">Enterprise System</h1>
    </div>
    <p class="text-[var(--text-secondary)] text-center">Login to access your account.</p>
    </header>
    <form class="flex flex-col gap-y-6">
    <div>
    <label class="block text-sm font-medium text-[var(--text-primary)] mb-1.5" for="mobile-number">Mobile Number</label>
    <input class="form-input w-full rounded-lg border border-gray-300 bg-[var(--surface-secondary)] px-4 py-3 text-[var(--text-primary)] placeholder:text-gray-400 focus:border-[var(--brand-primary)] focus:ring-[var(--brand-primary)] transition-colors" id="mobile-number" placeholder="Enter your mobile number" type="tel"/>
    </div>
    <div>
    <label class="block text-sm font-medium text-[var(--text-primary)] mb-1.5" for="password">Password</label>
    <input class="form-input w-full rounded-lg border border-gray-300 bg-[var(--surface-secondary)] px-4 py-3 text-[var(--text-primary)] placeholder:text-gray-400 focus:border-[var(--brand-primary)] focus:ring-[var(--brand-primary)] transition-colors" id="password" placeholder="Enter your password" type="password"/>
    </div>
    <div class="flex items-center justify-end">
    <a class="text-sm font-medium text-[var(--brand-primary)] hover:underline" href="#">Forgot password?</a>
    </div>
    <button class="w-full rounded-lg bg-[var(--brand-primary)] py-3.5 text-base font-semibold text-white shadow-sm hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-[var(--brand-primary)] focus:ring-offset-2 transition-colors" type="submit">
                Login
              </button>
    </form>
    <p class="mt-8 text-center text-sm text-[var(--text-secondary)]">
              Need help? <a class="font-medium text-[var(--brand-primary)] hover:underline" href="#">Contact Support</a>
    </p>
    </div>
    <footer class="mt-10 text-center text-xs text-gray-500">
            © 2024 Acme Co. All rights reserved.
          </footer>
    </div>
    
    </body></html>