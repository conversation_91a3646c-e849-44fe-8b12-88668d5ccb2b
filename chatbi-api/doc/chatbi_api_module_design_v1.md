## **Chat BI \- API 模块详细设计 (chatbi-api)**

本模块是 Chat BI 服务的对外门户，定义了外部系统（如IM、前端应用等）与 AI Agent 交互的核心 API 接口。它负责接收请求、进行初步校验、将请求路由到核心业务逻辑层 (chatbi-agent-core)，并返回处理结果。

**包名**: com.qding.chatbi.api (与 chatbi-common 中定义的包名保持一致)

### **1\. 包结构**

com.qding.chatbi.api  
├── controller/  
├── vo/  
└── service/

### **2\. com.qding.chatbi.api.service (API 接口定义)**

此包定义了服务契约，即 chatbi-agent-core 模块需要实现的接口。

#### **2.1. ConversationApi.java (Interface)**

* **用途**: 定义核心的对话处理服务接口。  
* **依赖**: chatbi-common 模块中的 DTOs (e.g., UserQueryRequest, AgentResponse)。  
* **核心方法**:  
  * AgentResponse processUserQuery(UserQueryRequest request);

    * **参数**: UserQueryRequest request \- 包含用户ID、会话ID、查询文本等信息。  
    * **返回**: AgentResponse \- 包含AI Agent的处理结果，可能是数据、澄清问题或错误信息。  
    * **异常**: 可声明抛出 ChatBiException 或其子类 (定义在 chatbi-common)，用于指示业务级错误。  
  * (可选) 与澄清流程相关的方法:  
    以下方法用于处理 AI Agent 在无法完全理解用户意图时，向用户请求澄清的场景。  
    * AgentResponse processClarificationResponse(String sessionId, String optionId, Map\<String, Object\> payload);

      * **用途**: 用户对AI提出的澄清问题做出选择后，调用此接口继续对话。  
      * **参数**:  
        * String sessionId: 当前会话ID。  
        * String optionId: 用户选择的澄清选项ID。  
        * Map\<String, Object\> payload: 该选项关联的附加信息。  
      * **返回**: AgentResponse。  
  * **(可选) 其他与对话相关的接口**:  
    * SessionInfoDTO getSessionDetails(String sessionId);

      * **用途**: 获取特定会话的详细信息或历史记录摘要（如果需要此类功能）。  
      * **返回**: SessionInfoDTO (如果需要，需在 chatbi-common.dto 中定义)。

#### **2.1.1. 澄清流程 (Clarification Flow) 场景说明**

当用户向 AI Agent 发出的查询请求存在**歧义**或**信息不完整**时，AI Agent 可能无法直接给出准确的答案。例如：

* 用户问：“查一下苹果的销量”，这里的“苹果”可能指水果、苹果公司产品等。  
* 用户问：“告诉我销售额”，AI Agent 可能需要知道具体的时间范围、产品、地区或销售额的类型。

为了提高查询的准确性，AI Agent 会主动向用户请求澄清。此流程大致如下：

1. **AI检测到不明确**: NLU 模块解析用户输入后，判断需要澄清。  
2. **AI生成澄清选项**: AI Agent 生成一个或多个澄清问题及选项。  
3. **API返回澄清请求**:  
   * 核心服务返回的 AgentResponse 其 responseType 字段为 CLARIFICATION\_NEEDED。  
   * messageToUser 字段包含澄清问题文本。  
   * clarificationOptions 字段 (一个 List\<ClarificationOption\>) 包含具体的选项 (每个选项有 optionId 和 optionText)。  
4. **用户选择澄清项**: 用户在客户端界面选择一个选项。  
5. **客户端回调澄清接口**: 用户的选择（通常是 optionId 和可能的 payload）通过调用本 API 模块中定义的 processClarificationResponse 方法（通常对应于 ConversationController 中的一个特定端点）回传给 Chat BI 服务。  
6. **AI继续处理**: AI Agent 核心服务接收到用户的澄清选择后，结合这个新的、更明确的信息，重新理解用户意图，并继续后续的查询生成和执行流程。

此澄清机制旨在逐步引导用户明确其查询意图，从而显著提升 Chat BI 系统的智能性和可用性。

### **3\. com.qding.chatbi.api.vo (View Objects \- 可选)**

此包用于定义专属于 API 层的请求/响应对象。如果 chatbi-common 中的 DTO 已经完全满足 API 暴露的需求，则此包可以为空或不创建。

* **创建VO的场景**:  
  * 当API的请求/响应结构与内部DTO不完全一致时（例如，API层需要额外的包装字段，或者想隐藏某些内部字段）。  
  * 当需要对日期格式、枚举表示等进行特定于API的转换时。  
* **示例 (如果需要)**:  
  * UserQueryApiRequest.java  
  * AgentApiOutput.java  
* **当前建议**: **初期可以优先直接使用 chatbi-common 中的 DTO** (UserQueryRequest, AgentResponse)。如果后续发现确实有必要进行适配，再引入VO。这样可以保持设计的简洁性。

### **4\. com.qding.chatbi.api.controller (HTTP 接口控制器)**

此包包含 Spring MVC/WebFlux Controllers，用于接收外部 HTTP 请求，并将其路由到 ConversationApi 的实现。

#### **4.1. ConversationController.java**

* **注解**: @RestController, @RequestMapping("/api/v1/chatbi/conversation") (或其他合适的根路径)。  
* **依赖**:  
  * ConversationApi conversationService (通过 @Autowired 注入 chatbi-agent-core 中该接口的实现)。  
  * chatbi-common 中的 DTOs 和 Enums。  
* **核心方法 (RESTful Endpoints)**:  
  * **处理用户查询**:  
    * **注解**: @PostMapping("/query")  
    * **方法签名**:  
      public ResponseEntity\<AgentResponse\> handleUserQuery(@RequestBody @Valid UserQueryRequest request, HttpServletRequest httpRequest) {  
          // 1\. (可选) 从 httpRequest 中获取额外信息，如IP、认证Token等，并填充到 UserContextDTO  
          // 2\. (可选) 进行额外的API层参数校验  
          // 3\. 调用核心服务  
          AgentResponse response \= conversationService.processUserQuery(request);  
          // 4\. 根据 AgentResponse 中的 responseType 返回合适的 HTTP 状态码  
          //    例如，如果 response.getErrorCode() 不为空，可以返回4xx或5xx错误  
          return ResponseEntity.ok(response);  
      }

    * **参数校验**: 可以使用 @Valid 结合 JSR 303/380 (Bean Validation) 对 UserQueryRequest 的字段进行校验。  
    * **全局异常处理**: 建议配置全局异常处理器 (@ControllerAdvice) 来统一处理 ChatBiException 及其子类，并将其转换为合适的 HTTP 错误响应。  
  * **(可选) 处理用户澄清响应 (如果 ConversationApi 中定义了相应方法)**:  
    * **注解**: @PostMapping("/{sessionId}/clarify")  
    * **方法签名**:  
      public ResponseEntity\<AgentResponse\> handleClarification(  
              @PathVariable String sessionId,  
              @RequestBody ClarificationPayload payload // ClarificationPayload 需定义，包含 optionId 和其他数据  
      ) {  
          // AgentResponse response \= conversationService.processClarificationResponse(sessionId, payload.getOptionId(), payload.getData());  
          // return ResponseEntity.ok(response);  
      }

      (这里的 ClarificationPayload 需要在 vo 或 dto 中定义，例如可以创建一个 ClarificationApiRequest.java 包含 optionId 和 Map\<String, Object\> payload)。  
* **其他考虑**:  
  * **安全性**: Controller 层需要考虑 API 的安全性，例如通过 Spring Security 集成认证和授权机制（这部分可能由API网关统一处理，Controller层面只需确保必要的安全上下文能传递给下游服务）。  
  * **日志**: 记录请求入口和出口的关键信息。  
  * **API文档**: 使用 Swagger/OpenAPI (通过 SpringDoc 等工具) 自动生成API文档。

#### **4.2. API 请求体详细约定 (/query 接口)**

本节详细约定了 UserQueryRequest DTO 中每个字段的含义和使用方式。

##### **userId (String, 必填)**

* **含义**: 唯一标识当前操作用户的字符串。  
* **用途**:  
  * **权限校验**: 这是进行权限控制的核心依据。后端服务会使用此ID来获取用户的角色列表。  
  * **历史记录**: 在 chat\_messages 表中记录是哪个用户发起的查询。  
* **示例值**: "user-001", "<EMAIL>"

##### **sessionId (String, 可选)**

* **含义**: 标识一次完整的多轮对话。  
* **用途**:  
  * **上下文管理**: 后端服务使用 sessionId 来维持对话的上下文记忆。  
  * **历史追溯**: 用于查询一次完整对话的所有交互历史。  
* **约定**:  
  * **首次请求**: 客户端在发起新对话时，可以将此字段留空或设为 null。后端在发现 sessionId 为空时，会**生成一个新的UUID**作为本次对话的 sessionId，并将其包含在响应中返回。  
  * **后续请求**: 客户端需要**缓存**从首次响应中获取的 sessionId，并在该次对话的后续所有请求中（包括用户再次提问、响应澄清等）都携带这个相同的 sessionId。  
* **示例值**: "a8a1c12d-4f1c-4b5a-9d0a-9e1c2a3b4d5e"

##### **queryText (String, 必填)**

* **含义**: 用户输入的原始自然语言文本。  
* **用途**:  
  * 这是自然语言理解 (NLU) 的主要输入。  
  * 它会被完整地记录到对话历史中。  
* **约定**:  
  * 对于用户的澄清响应（例如，用户点击了一个澄清选项），客户端应将能代表用户选择的文本（例如选项的文本内容）作为 queryText 发送过来，这有助于保持对话历史的可读性。  
* **示例值**: "查一下上个月北京地区的GMV是多少？", "选项A", "给我看看销售额最高的五个产品"

##### **additionalParams (Map\<String, Object\>, 可选)**

* **含义**: 一个灵活的键值对集合，用于传递上述标准字段之外的任何附加信息。  
* **用途**: 增强系统的灵活性和扩展性。  
* **建议的键 (Key) 和场景**:  
  * "clarifiedOptionId" (String): 当用户响应一个澄清问题时，客户端应将用户选择的 ClarificationOption 的 optionId 放在这里。  
  * "clientIp" (String): 客户端的IP地址，可用于审计或日志。  
  * "userAgent" (String): 客户端的User-Agent信息。  
  * "timeZone" (String): 客户端的时区信息（如 "Asia/Shanghai"），这对于处理相对时间（如“今天”、“昨天”）非常重要。  
* **示例值**:  
  * 对于一个澄清响应:  
    {  
      "clarifiedOptionId": "opt\_total\_sales\_current\_month"  
    }

  * 对于一个带有客户端信息的普通查询:  
    {  
      "clientIp": "***************",  
      "userAgent": "Mozilla/5.0 ...",  
      "timeZone": "Asia/Shanghai"  
    }

### **5\. API 测试页面 (用于开发和调试)**

为了方便开发和测试，可以在 chatbi-api 模块中包含一个简单的静态HTML页面。

#### **5.1. 用途与部署**

* **用途**: 提供一个无需依赖外部IM系统或复杂前端工程即可直接与后端API交互的界面。  
* **部署**: 将下面的HTML代码保存为 test-chat.html 文件，并放置在 chatbi-api Spring Boot项目的 src/main/resources/static/ 目录下。当应用启动后，可以通过访问 http://\<your-host\>:\<port\>/test-chat.html 来使用。

#### **5.2. test-chat.html 代码**

\<\!DOCTYPE html\>  
\<html lang="zh-CN"\>  
\<head\>  
    \<meta charset="UTF-8"\>  
    \<meta name="viewport" content="width=device-width, initial-scale=1.0"\>  
    \<title\>Chat BI \- API 测试页面\</title\>  
    \<script src="https://cdn.tailwindcss.com"\>\</script\>  
    \<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700\&display=swap" rel="stylesheet"\>  
    \<style\>  
        body {  
            font-family: 'Inter', sans-serif;  
        }  
        \#chat-window {  
            height: calc(100vh \- 12rem);  
        }  
        .message-bubble {  
            max-width: 80%;  
        }  
        .clarification-btn {  
            transition: background-color 0.2s;  
        }  
    \</style\>  
\</head\>  
\<body class="bg-gray-100 flex items-center justify-center min-h-screen"\>  
    \<div class="w-full max-w-2xl mx-auto bg-white rounded-2xl shadow-2xl flex flex-col h-\[95vh\]"\>  
        \<header class="bg-gray-800 text-white p-4 rounded-t-2xl"\>  
            \<h1 class="text-xl font-bold text-center"\>Chat BI \- 对话测试\</h1\>  
        \</header\>

        \<main id="chat-window" class="flex-1 p-6 overflow-y-auto space-y-6"\>  
            \<\!-- 聊天消息将在这里动态添加 \--\>  
            \<div class="flex justify-start"\>  
                \<div class="message-bubble bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none"\>  
                    \<p class="text-sm"\>您好！我是您的BI助手，请问有什么可以帮您查询的吗？例如，您可以问我：“上个月北京地区的GMV是多少？”\</p\>  
                \</div\>  
            \</div\>  
        \</main\>

        \<footer class="p-4 border-t border-gray-200"\>  
            \<div class="flex items-center space-x-3"\>  
                \<input type="text" id="user-input" class="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入您的问题..."\>  
                \<button id="send-btn" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300"\>  
                    发送  
                \</button\>  
            \</div\>  
        \</footer\>  
    \</div\>

    \<script\>  
        // \--- 核心变量 \---  
        const chatWindow \= document.getElementById('chat-window');  
        const userInput \= document.getElementById('user-input');  
        const sendBtn \= document.getElementById('send-btn');  
          
        let userId \= 'test-user-001'; // 固定的测试用户ID  
        let sessionId \= null; // 初始化会话ID

        // \--- 事件监听 \---  
        sendBtn.addEventListener('click', sendMessage);  
        userInput.addEventListener('keydown', (event) \=\> {  
            if (event.key \=== 'Enter' && \!event.shiftKey) {  
                event.preventDefault();  
                sendMessage();  
            }  
        });

        // \--- 函数定义 \---

        // 发送消息  
        async function sendMessage() {  
            const queryText \= userInput.value.trim();  
            if (\!queryText) return;

            // 禁用输入和按钮  
            toggleInput(false);  
            addMessage(queryText, 'user');  
            userInput.value \= '';

            try {  
                const requestBody \= {  
                    userId: userId,  
                    sessionId: sessionId,  
                    queryText: queryText,  
                    additionalParams: {  
                        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone  
                    }  
                };

                const response \= await fetch('/api/v1/chatbi/conversation/query', {  
                    method: 'POST',  
                    headers: { 'Content-Type': 'application/json' },  
                    body: JSON.stringify(requestBody)  
                });  
                  
                if (\!response.ok) {  
                    const errorData \= await response.json().catch(() \=\> ({ message: \`HTTP错误\! 状态: ${response.status}\` }));  
                    throw new Error(errorData.message || \`HTTP错误\! 状态: ${response.status}\`);  
                }

                const data \= await response.json();  
                handleAgentResponse(data);

            } catch (error) {  
                console.error('发送消息时出错:', error);  
                addMessage(\`抱歉，连接服务器时发生错误: ${error.message}\`, 'agent', 'ERROR');  
            } finally {  
                toggleInput(true);  
            }  
        }

        // 处理澄清选项点击  
        async function handleClarificationClick(optionId, optionText) {  
            addMessage(\`我选择了: "${optionText}"\`, 'user');  
            toggleInput(false);

            try {  
                const requestBody \= {  
                    userId: userId,  
                    sessionId: sessionId,  
                    queryText: \`用户选择的澄清选项: ${optionText}\`, // 将选择的文本作为queryText  
                    additionalParams: {  
                        clarifiedOptionId: optionId  
                    }  
                };  
                  
                // 统一使用 /query 接口  
                const response \= await fetch('/api/v1/chatbi/conversation/query', {  
                    method: 'POST',  
                    headers: { 'Content-Type': 'application/json' },  
                    body: JSON.stringify(requestBody)  
                });

                 if (\!response.ok) {  
                    const errorData \= await response.json().catch(() \=\> ({ message: \`HTTP错误\! 状态: ${response.status}\` }));  
                    throw new Error(errorData.message || \`HTTP错误\! 状态: ${response.status}\`);  
                }

                const data \= await response.json();  
                handleAgentResponse(data);

            } catch (error) {  
                 console.error('发送澄清响应时出错:', error);  
                addMessage(\`抱歉，处理您的选择时发生错误: ${error.message}\`, 'agent', 'ERROR');  
            } finally {  
                toggleInput(true);  
            }  
        }

        // 处理AI Agent的响应  
        function handleAgentResponse(data) {  
            // 更新会话ID  
            if (data.sessionId) {  
                sessionId \= data.sessionId;  
            }

            // 添加AI的文本消息  
            if (data.messageToUser) {  
                addMessage(data.messageToUser, 'agent', data.responseType);  
            }  
              
            // 如果有查询结果，渲染表格  
            if (data.responseType \=== 'DATA\_RESULT' && data.queryResult && data.queryResult.rows) {  
                renderQueryResult(data.queryResult);  
            }

            // 如果需要澄清，渲染澄清选项按钮  
            if (data.responseType \=== 'CLARIFICATION\_NEEDED' && data.clarificationOptions) {  
                renderClarificationOptions(data.clarificationOptions);  
            }  
        }

        // 渲染查询结果表格  
        function renderQueryResult(queryResult) {  
            const tableContainer \= document.createElement('div');  
            tableContainer.className \= 'mt-2 bg-gray-50 p-3 rounded-lg overflow-x-auto';  
              
            const table \= document.createElement('table');  
            table.className \= 'min-w-full divide-y divide-gray-200';

            const thead \= document.createElement('thead');  
            thead.className \= 'bg-gray-100';  
            const headerRow \= document.createElement('tr');  
            queryResult.columnHeaders.forEach(headerText \=\> {  
                const th \= document.createElement('th');  
                th.className \= 'px-4 py-2 text-left text-xs font-bold text-gray-600 uppercase tracking-wider';  
                th.textContent \= headerText;  
                headerRow.appendChild(th);  
            });  
            thead.appendChild(headerRow);  
            table.appendChild(thead);

            const tbody \= document.createElement('tbody');  
            tbody.className \= 'bg-white divide-y divide-gray-200';  
            queryResult.rows.forEach(rowData \=\> {  
                const row \= document.createElement('tr');  
                rowData.forEach(cellData \=\> {  
                    const td \= document.createElement('td');  
                    td.className \= 'px-4 py-2 whitespace-nowrap text-sm text-gray-700';  
                    td.textContent \= cellData \=== null ? 'NULL' : cellData;  
                    row.appendChild(td);  
                });  
                tbody.appendChild(row);  
            });  
            table.appendChild(tbody);  
            tableContainer.appendChild(table);

            chatWindow.appendChild(tableContainer);  
            chatWindow.scrollTop \= chatWindow.scrollHeight;  
        }

        // 渲染澄清选项  
        function renderClarificationOptions(options) {  
            const optionsContainer \= document.createElement('div');  
            optionsContainer.className \= 'flex flex-wrap gap-2 mt-2';  
              
            options.forEach(option \=\> {  
                const button \= document.createElement('button');  
                button.className \= 'clarification-btn bg-white border border-blue-500 text-blue-500 text-sm px-3 py-1 rounded-full hover:bg-blue-50 focus:outline-none';  
                button.textContent \= option.optionText;  
                button.onclick \= () \=\> {  
                    document.querySelectorAll('.clarification-btn').forEach(btn \=\> btn.disabled \= true);  
                    button.classList.add('bg-blue-100', 'font-semibold');  
                    handleClarificationClick(option.optionId, option.optionText);  
                };  
                optionsContainer.appendChild(button);  
            });

            const lastMessageBubble \= chatWindow.querySelector('.message-wrapper:last-child .message-bubble');  
            if(lastMessageBubble) {  
                 lastMessageBubble.appendChild(optionsContainer);  
            } else {  
                 chatWindow.appendChild(optionsContainer);  
            }  
            chatWindow.scrollTop \= chatWindow.scrollHeight;  
        }

        // 添加消息到聊天窗口  
        function addMessage(text, sender, type \= 'NORMAL') {  
            const messageWrapper \= document.createElement('div');  
            messageWrapper.className \= \`flex message-wrapper ${sender \=== 'user' ? 'justify-end' : 'justify-start'}\`;

            const bubble \= document.createElement('div');  
            bubble.className \= 'message-bubble p-3 rounded-lg shadow';  
              
            if (sender \=== 'user') {  
                bubble.classList.add('bg-blue-600', 'text-white', 'rounded-br-none');  
            } else {  
                bubble.classList.add('bg-gray-200', 'text-gray-800', 'rounded-bl-none');  
                if (type \=== 'ERROR') {  
                    bubble.classList.remove('bg-gray-200');  
                    bubble.classList.add('bg-red-100', 'text-red-800');  
                }  
            }  
              
            const p \= document.createElement('p');  
p.className \= 'text-sm';  
            p.textContent \= text;  
              
            bubble.appendChild(p);  
            messageWrapper.appendChild(bubble);  
            chatWindow.appendChild(messageWrapper);  
              
            chatWindow.scrollTop \= chatWindow.scrollHeight;  
        }

        // 切换输入状态  
        function toggleInput(enabled) {  
            userInput.disabled \= \!enabled;  
            sendBtn.disabled \= \!enabled;  
            if (enabled) {  
                userInput.focus();  
            }  
        }

    \</script\>  
\</body\>  
\</html\>  
