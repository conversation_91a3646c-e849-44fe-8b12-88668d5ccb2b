# **ChatBI 管理后台详细设计**

## **1\. 目标与定位**

ChatBI 管理后台是整个系统的“配置中心”和“大脑控制室”。它的核心目标是为系统管理员提供一个图形化界面，用以完成以下关键任务：

* **数据建模**: 连接数据源，定义可供 AI 查询的逻辑数据集，并丰富其业务元数据。  
* **AI 知识库管理**: 维护业务术语库和高质量的查询范例，提升 AI 的理解能力和查询准确性。  
* **权限管控**: 精细化管理不同业务角色对数据集的访问权限，确保数据安全。  
* **系统运维**: 配置核心服务依赖（如大模型、数据库），并管理后台操作员账号。

该后台由两部分组成：chatbi-admin-backend (提供 RESTful API) 和 chatbi-admin-frontend (基于 Vue.js 的前端界面)。

## **2\. 后端设计 (chatbi-admin-backend)**

此模块作为 Spring Boot 应用，为前端提供所有管理功能所需的 API 接口。

### **2.1. 模块职责与依赖**

* **职责**:  
  * 提供安全的、基于 RESTful 风格的 API。  
  * 处理前端发送的增、删、改、查 (CRUD) 请求。  
  * 调用下游服务 (如 chatbi-metadata-service, chatbi-knowledge-service) 来持久化和检索数据。  
  * 实现后台用户的认证与授权逻辑。  
* **依赖**:  
  * chatbi-api: 获取 API 接口定义，如 ConversationApi，尽管后台不直接处理对话，但可能需要引用其中的 DTO。  
  * chatbi-common: 使用共享的 DTO、枚举和异常类。  
  * chatbi-metadata-service: (作为库或客户端) 调用其服务来管理元数据和权限。  
  * chatbi-knowledge-service: (作为库或客户端) 调用其服务来管理知识库。  
  * spring-boot-starter-web, spring-boot-starter-security 等。

### **2.2. API 端点设计 (Controllers)**

我们将为每个核心管理功能设计一个专门的 Controller。所有 API 路径建议以 /api/admin 为前缀。

#### **DataSourceAdminController \- 数据源管理**

* **根路径**: /api/admin/data-sources  
* **功能**: 管理 database\_source\_configs 表。

| HTTP 方法 | 路径 | 描述 | 请求体/参数 | 成功响应 (200 OK) |
| :---- | :---- | :---- | :---- | :---- |
| GET | / | 获取所有数据源列表 | \- | List\<DataSourceDTO\> |
| POST | / | 新建一个数据源 | CreateDataSourceRequest | DataSourceDTO |
| POST | /test-connection | 测试数据库连接 | TestConnectionRequest | { "success": true, "message": "连接成功" } |
| PUT | /{id} | 更新指定ID的数据源 | UpdateDataSourceRequest | DataSourceDTO |
| DELETE | /{id} | 删除指定ID的数据源 | \- | { "success": true } |

#### **DatasetAdminController \- 数据集管理**

* **根路径**: /api/admin/datasets  
* **功能**: 管理 queryable\_datasets 和 dataset\_columns 表。

| HTTP 方法 | 路径 | 描述 | 请求体/参数 | 成功响应 (200 OK) |
| :---- | :---- | :---- | :---- | :---- |
| GET | / | 获取所有数据集的摘要列表 | \- | List\<DatasetSummaryDTO\> |
| GET | /{id} | 获取单个数据集的完整配置（包含所有字段） | \- | DatasetDetailDTO |
| POST | / | 创建一个新的数据集 | CreateDatasetRequest | DatasetDetailDTO |
| PUT | /{id} | 更新数据集的基本信息（名称、描述等） | UpdateDatasetRequest | DatasetDetailDTO |
| DELETE | /{id} | 删除整个数据集及其字段 | \- | { "success": true } |
| GET | /{id}/columns | 获取数据集的所有字段配置 | \- | List\<ColumnConfigDTO\> |
| POST | /{id}/columns/sync | 从物理表/视图重新同步字段 | \- | List\<ColumnConfigDTO\> |
| PUT | /{id}/columns/{columnId} | 更新单个字段的配置 | UpdateColumnConfigRequest | ColumnConfigDTO |
| POST | /{id}/columns/computed | 添加计算指标 | CreateComputedColumnRequest | ColumnConfigDTO |

#### **KnowledgeBaseAdminController \- 知识库管理**

* **根路径**: /api/admin/knowledge-base  
* **功能**: 管理 business\_terminology 和 query\_examples 表。

| HTTP 方法 | 路径 | 描述 | 请求体/参数 | 成功响应 (200 OK) |
| :---- | :---- | :---- | :---- | :---- |
| GET | /terms | 获取所有业务术语 | \- | List\<TerminologyDTO\> |
| POST | /terms | 新增业务术语 | CreateTermRequest | TerminologyDTO |
| PUT | /terms/{id} | 更新业务术语 | UpdateTermRequest | TerminologyDTO |
| DELETE | /terms/{id} | 删除业务术语 | \- | { "success": true } |
| GET | /examples | 获取所有查询范例 | \- | List\<QueryExampleDTO\> |
| POST | /examples | 新增查询范例 | CreateExampleRequest | QueryExampleDTO |
| PUT | /examples/{id} | 更新查询范例 | UpdateExampleRequest | QueryExampleDTO |
| DELETE | /examples/{id} | 删除查询范例 | \- | { "success": true } |

#### **PermissionAdminController \- 权限管理**

* **根路径**: /api/admin/permissions  
* **功能**: 管理 roles 和 dataset\_access\_permissions 表。

| HTTP 方法 | 路径 | 描述 | 请求体/参数 | 成功响应 (200 OK) |
| :---- | :---- | :---- | :---- | :---- |
| GET | /roles | 获取所有查询角色 | \- | List\<RoleDTO\> |
| POST | /roles | 创建一个新角色 | { "name": "...", "description": "..." } | RoleDTO |
| PUT | /roles/{id} | 更新角色信息 | { "name": "...", "description": "..." } | RoleDTO |
| DELETE | /roles/{id} | 删除角色 | \- | { "success": true } |
| GET | /assignments | 获取所有角色-数据集的权限分配矩阵 | \- | List\<PermissionAssignmentDTO\> |
| PUT | /assignments | 批量更新权限分配 | List\<UpdateAssignmentRequest\> | { "success": true } |

#### **UserAdminController \- 后台用户管理 (新增)**

* **根路径**: /api/admin/users  
* **功能**: 管理后台系统的操作员账号。**所有接口仅限“超级管理员”调用**。

| HTTP 方法 | 路径 | 描述 | 请求体/参数 | 成功响应 (200 OK) |
| :---- | :---- | :---- | :---- | :---- |
| GET | / | 获取所有后台用户列表 | \- | List\<AdminUserDTO\> |
| POST | / | 创建一个新的后台管理员 | CreateAdminUserRequest | AdminUserDTO |
| PUT | /{id} | 更新管理员信息（如姓名） | UpdateAdminUserRequest | AdminUserDTO |
| PATCH | /{id}/status | 更改用户状态（启用/禁用） | { "enabled": true } | { "success": true } |
| DELETE | /{id} | 删除一个管理员 | \- | { "success": true } |

*注意: DTOs (如 CreateDataSourceRequest, DatasetDetailDTO 等) 需要在 chatbi-common 或 chatbi-admin-backend 的 vo 包中定义。*

## **3\. 前端设计 (chatbi-admin-frontend)**

前端项目基于 Vue.js (推荐 Vue 3 \+ Vite) 和一个 UI 组件库 (如 Element Plus, Ant Design Vue) 来构建。

### **3.1. 项目结构**

chatbi-admin-frontend/  
├── public/  
│   └── index.html  
├── src/  
│   ├── api/          \# API 请求服务，封装对后端接口的调用  
│   │   ├── dataSource.js  
│   │   ├── dataset.js  
│   │   └── ...  
│   ├── assets/       \# 静态资源 (CSS, images)  
│   ├── components/   \# 可复用的通用组件 (如布局、表格、表单)  
│   │   └── layout/  
│   │       ├── AppLayout.vue  
│   │       ├── SideMenu.vue  
│   │       └── PageHeader.vue  
│   ├── router/       \# Vue Router 路由配置  
│   │   └── index.js  
│   ├── store/        \# Pinia/Vuex 状态管理  
│   ├── utils/        \# 通用工具函数  
│   ├── views/        \# 页面级组件，对应每个管理功能  
│   │   ├── login/  
│   │   │   └── LoginPage.vue  
│   │   ├── model/  
│   │   │   ├── DataSourceList.vue  
│   │   │   ├── DatasetList.vue  
│   │   │   └── DatasetConfig.vue  
│   │   ├── knowledge/  
│   │   │   ├── TerminologyList.vue  
│   │   │   └── QueryExampleList.vue  
│   │   ├── permissions/  
│   │   │   ├── RoleList.vue  
│   │   │   └── PermissionMatrix.vue  
│   │   └── system/  
│   │       └── AdminUserList.vue  
│   ├── App.vue       \# 根组件  
│   └── main.js       \# 应用入口  
├── package.json  
└── vite.config.js

### **3.2. 页面规划与核心组件**

#### **AppLayout.vue \- 应用主布局**

* 包含一个固定的侧边栏菜单 (SideMenu.vue) 和一个内容显示区域 (\<router-view\>)。  
* SideMenu.vue 根据路由配置生成导航菜单，如“模型管理”、“权限管理”等。

#### **登录页 (LoginPage.vue)**

* 提供手机号和密码输入框。  
* 调用登录 API，成功后保存 Token (如 JWT) 并跳转到后台首页。

#### **数据源管理 (DataSourceList.vue)**

* 使用表格组件展示从 /api/admin/data-sources 获取的数据。  
* 提供“新建”、“编辑”、“删除”按钮，点击后弹出模态框 (Dialog) 或跳转到表单页面。  
* 模态框中的表单包含所有数据源配置项，并有一个“测试连接”按钮，调用 /test-connection 接口。

#### **数据集管理 (DatasetList.vue \-\> DatasetConfig.vue)**

1. **DatasetList.vue (列表页)**  
   * 展示所有数据集。  
   * “新建数据集”按钮引导用户分步完成创建流程（选择数据源 \-\> 选择表/写SQL）。  
   * 每行提供“配置”按钮，点击后通过路由 router.push('/datasets/' \+ id) 跳转到配置页。  
2. **DatasetConfig.vue (配置页)**  
   * 接收路由参数中的数据集 id，并调用 /api/admin/datasets/{id} 获取详细信息。  
   * 使用标签页 (Tabs) 切换“字段配置”和“计算指标”。  
   * **字段配置 Tab**:  
     * 表格展示所有字段，每行都有“编辑”按钮。  
     * 点击“编辑”弹出模态框，允许修改业务名称、描述、语义类型（下拉框）、同义词（标签输入框）等。  
     * 提供“从数据源同步”按钮，调用 /sync 接口刷新字段列表。  
   * **计算指标 Tab**:  
     * 提供“添加计算指标”功能，表单中包含“指标名称”和“SQL表达式”输入框。

#### **权限分配 (PermissionMatrix.vue)**

* 这是最核心和交互最复杂的页面之一。  
* **布局**:  
  * 左侧为角色列表 (从 /api/admin/roles 获取)。  
  * 右侧为数据集列表 (从 /api/admin/datasets 获取)。  
  * 中间是权限矩阵，使用复选框 (Checkbox) 表示某个角色是否拥有某个数据集的权限。  
* **交互**:  
  * 页面加载时获取完整的权限分配关系 (/api/admin/assignments) 并填充复选框。  
  * 用户修改任何一个复选框的状态后，页面标记为“已修改”。  
  * 提供一个“保存”按钮，点击后将所有修改过的权限关系一次性通过 PUT /api/admin/assignments 提交给后端。

#### **后台用户管理 (AdminUserList.vue) (新增)**

* 此页面应通过路由配置，且**只对“超级管理员”角色可见**。  
* **布局**:  
  * 页面顶部有一个“新建管理员”按钮。  
  * 使用表格组件展示从 /api/admin/users 获取的后台用户列表。  
* **表格列**: 姓名, 登录手机号, 角色 (超级管理员/管理员), 状态 (启用/禁用), 创建时间, 操作。  
* **交互**:  
  * **新建管理员**: 点击按钮后，弹出一个表单模态框，包含字段：姓名、登录手机号、初始密码。提交后调用 POST /api/admin/users。  
  * **操作列**:  
    * 编辑: 弹出模态框，允许修改管理员的姓名等非敏感信息。提交后调用 PUT /api/admin/users/{id}。  
    * 状态切换: 使用一个开关 (Switch) 组件，直接调用 PATCH /api/admin/users/{id}/status 来启用或禁用用户。  
    * 删除: 点击后弹出确认对话框，确认后调用 DELETE /api/admin/users/{id}。

## **4\. 总结与后续步骤**

本文档详细规划了 ChatBI 管理后台的后端 API 接口和前端页面组件。这个设计将功能模块化，使得前后端可以并行开发。

**下一步工作**:

1. **定义 DTOs**: 在 chatbi-common 中精确定义所有在 API 设计中提到的请求和响应 DTO。  
2. **后端开发**:  
   * 在 chatbi-admin-backend 模块中创建相应的 Controller、Service 类。  
   * 实现每个 API 端点的业务逻辑，重点是调用 chatbi-metadata-service 等下游服务。  
   * 配置 Spring Security 实现后台用户的认证和授权。  
3. **前端开发**:  
   * 搭建 Vue.js 项目骨架。  
   * 实现 api 服务，封装对后端所有接口的调用。  
   * 按照页面规划，逐个开发 Vue 组件和页面。  
   * 使用状态管理 (Pinia) 来管理全局状态，如登录用户信息、角色列表等。

这个详细的设计为管理后台的实现奠定了坚实的基础。