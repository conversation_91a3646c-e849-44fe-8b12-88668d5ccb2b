# **ChatBI 后台管理后端 (chatbi-admin-backend) 详细设计**

## **1\. 模块定位与职责**

在我们的单体应用架构中，chatbi-admin-backend 模块扮演着**后台管理功能集**的角色。它的核心职责是：

1. **提供 RESTful API**: 为 chatbi-admin-frontend 提供所有管理功能所需的、安全的、结构清晰的 HTTP 接口。  
2. **封装业务逻辑**: 实现所有管理操作的业务逻辑，例如创建数据源时的连接测试、新建数据集时对来源的解析、权限分配的更新等。  
3. **协调数据服务**: 作为协调者，调用底层的数据服务（在我们的架构中，这些服务如 ModelManagementService 等也位于此模块或可直接注入）来完成对数据库的读写操作。  
4. **安全与认证**: 负责后台管理员的身份认证和权限控制，确保只有授权用户才能执行相应的管理操作。

## **2\. 包结构设计**

为了保持代码的清晰和高内聚，chatbi-admin-backend 内部应遵循以下包结构。这里详细列出了每个包下的核心类：

com.qding.chatbi.admin  
├── config/  
│   └── AdminSecurityConfig.java      \# Spring Security配置，用于后台用户认证授权  
│  
├── controller/  
│   ├── DataSourceAdminController.java  \# 管理数据源的API  
│   ├── DatasetAdminController.java     \# 管理数据集的API  
│   ├── KnowledgeBaseAdminController.java \# 管理知识库（术语、范例）的API  
│   ├── PermissionAdminController.java  \# 管理角色和权限分配的API  
│   └── UserAdminController.java        \# 管理后台操作员账号的API  
│  
├── service/  
│   ├── ModelManagementService.java     \# 封装数据源、数据集、知识库的核心业务逻辑  
│   ├── PermissionService.java        \# 封装角色和权限分配的业务逻辑  
│   └── AdminUserService.java           \# 封装后台用户管理的业务逻辑  
│  
├── vo/ (View Objects)  
│   ├── PagedResponse.java            \# 通用分页响应视图对象  
│   │  
│   ├── datasource/  
│   │   ├── DataSourceDTO.java  
│   │   └── CreateDataSourceRequest.java  
│   │  
│   ├── dataset/  
│   │   ├── DatasetSummaryDTO.java  
│   │   ├── DatasetDetailDTO.java  
│   │   ├── ColumnConfigDTO.java  
│   │   └── CreateDatasetRequest.java  
│   │  
│   ├── permission/  
│   │   ├── RoleDTO.java  
│   │   └── PermissionAssignmentDTO.java  
│   │  
│   └── user/  
│       ├── AdminUserDTO.java  
│       └── CreateAdminUserRequest.java  
│  
└── entity/ & repository/  
    (在此单体架构下，所有JPA实体和仓库可以统一管理，由Service层直接调用)  
    ├── entity/  
    │   ├── DatabaseSourceConfig.java  
    │   ├── QueryableDataset.java  
    │   ├── DatasetColumn.java  
    │   ├── Role.java  
    │   ├── DatasetAccessPermission.java  
    │   ├── BusinessTerminology.java  
    │   ├── QueryExample.java  
    │   ├── ChatMessage.java          \# 尽管主要由Agent使用，但实体定义在此  
    │   └── AdminUser.java            \# 后台管理员账号实体  
    └── repository/  
        ├── DataSourceRepository.java  
        ├── DatasetRepository.java  
        ├── DatasetColumnRepository.java  
        ├── RoleRepository.java  
        ├── DatasetAccessPermissionRepository.java  
        ├── BusinessTerminologyRepository.java  
        ├── QueryExampleRepository.java  
        ├── ChatMessageRepository.java  
        └── AdminUserRepository.java

## **3\. 核心服务 (Service) 设计**

Service 层是业务逻辑的核心。

### **3.1. ModelManagementService.java**

这个服务是后台功能中最复杂的，它处理所有与数据模型和知识库相关的逻辑。

* **职责**:  
  * 处理数据源的增删改查及连接测试。  
  * 处理数据集的增删改查。  
  * 提供从物理表中同步字段（列）的功能。  
  * 处理数据集字段的配置更新。  
  * 处理业务术语和查询范例的增删改查。  
* **依赖**:  
  * DataSourceRepository, DatasetRepository, DatasetColumnRepository, BusinessTerminologyRepository, QueryExampleRepository (JPA仓库)。  
* **核心方法**:  
  * Page\<DataSourceDTO\> getAllDataSources(Pageable pageable)  
  * DataSourceDTO createDataSource(CreateDataSourceRequest request)  
  * boolean testConnection(CreateDataSourceRequest request)  
  * Page\<DatasetSummaryDTO\> getAllDatasets(Pageable pageable)  
  * DatasetDetailDTO getDatasetDetails(Long id)  
  * DatasetDetailDTO createDatasetFromTable(CreateDatasetRequest request)  
  * DatasetDetailDTO createDatasetFromSql(CreateDatasetRequest request)  
  * List\<ColumnConfigDTO\> syncColumnsFromSource(Long datasetId)  
  * ColumnConfigDTO updateColumnConfig(Long columnId, UpdateColumnConfigRequest request)  
  * (其他知识库相关的CRUD方法)

### **3.2. PermissionService.java**

* **职责**:  
  * 处理查询角色 (Role) 的增删改查。  
  * 处理角色与数据集之间的权限分配。  
* **依赖**:  
  * RoleRepository, DatasetAccessPermissionRepository。  
* **核心方法**:  
  * List\<RoleDTO\> getAllRoles()  
  * RoleDTO createRole(CreateRoleRequest request)  
  * List\<PermissionAssignmentDTO\> getPermissionAssignments()  
  * void updateAssignments(List\<UpdateAssignmentRequest\> requests)

### **3.3. AdminUserService.java**

* **职责**:  
  * 管理后台操作员（管理员/超级管理员）的账户。  
* **依赖**:  
  * AdminUserRepository (JPA仓库)。  
  * PasswordEncoder (用于密码加密)。  
* **核心方法**:  
  * Page\<AdminUserDTO\> getAllAdminUsers(Pageable pageable)  
  * AdminUserDTO createAdminUser(CreateAdminUserRequest request)  
  * void updateUserStatus(Long userId, boolean isEnabled)

## **4\. 控制器 (Controller) 设计**

Controller 层是 API 的直接入口，负责接收请求、调用 Service 并返回标准响应。

* **通用实践**:  
  * 使用 @RestController 和 @RequestMapping。  
  * 对请求体验证使用 @Valid。  
  * 使用 ResponseEntity 来包装响应，以便控制 HTTP 状态码。  
  * 使用 @PreAuthorize 注解进行方法级别的权限控制。

// 以 UserAdminController 为例  
@RestController  
@RequestMapping("/api/admin/users")  
// 使用注解确保只有超级管理员能访问这个Controller下的所有API  
@PreAuthorize("hasRole('SUPER\_ADMIN')")   
public class UserAdminController {

    @Autowired  
    private AdminUserService adminUserService;

    @Autowired  
    private PasswordEncoder passwordEncoder;

    @GetMapping("/")  
    public ResponseEntity\<PagedResponse\<AdminUserDTO\>\> listUsers(  
        @PageableDefault(size \= 20\) Pageable pageable) {  
        Page\<AdminUserDTO\> page \= adminUserService.getAllAdminUsers(pageable);  
        // ... 将 Page 转换为 PagedResponse ...  
        return ResponseEntity.ok(pagedResponse);  
    }  
}  
