// =================================================================
// File: chatbi-admin-backend/pom.xml
// =================================================================
/*
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qding.chatbi</groupId>
        <artifactId>chatbi-service</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-admin-backend</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- 假设 metadata-service 和 knowledge-service 的逻辑作为库被直接依赖 -->
        <!-- 如果它们作为独立服务，这里会是 feign-client 或类似的依赖 -->
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-metadata-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-knowledge-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
*/

// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/AdminBackendApplication.java
// Note: This class is for isolated testing of the admin module. 
// The main entry point is in 'chatbi-application'.
// =================================================================
package com.qding.chatbi.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * The Spring Boot application class for the admin backend module.
 * This can be used for isolated testing and development. The main application entry point
 * for the entire service is in the 'chatbi-application' module.
 */
@SpringBootApplication(scanBasePackages = {"com.qding.chatbi.admin", "com.qding.chatbi.common"})
public class AdminBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminBackendApplication.class, args);
    }
}


// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/config/AdminSecurityConfig.java
// =================================================================
package com.qding.chatbi.admin.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * Spring Security configuration for the admin backend.
 * Secures all /api/admin/** endpoints.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class AdminSecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable()) // 在生产中应考虑更安全的CSRF策略
            .authorizeHttpRequests(authz -> authz
                // 仅允许拥有 SUPER_ADMIN 角色的用户访问用户管理API
                .requestMatchers("/api/admin/users/**").hasRole("SUPER_ADMIN")
                // 其他所有 /api/admin/ 下的路径需要 ADMIN 角色
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                // 其他所有请求允许访问（例如健康检查或swagger-ui）
                .anyRequest().permitAll() 
            )
            .httpBasic(withDefaults()); // 使用HTTP Basic认证
        return http.build();
    }

    /**
     * 配置内存中的用户，用于演示。
     * 在实际应用中，应替换为基于数据库的 `UserDetailsService` 实现。
     * @return UserDetailsService
     */
    @Bean
    public UserDetailsService userDetailsService() {
        // 超级管理员，拥有两个角色
        UserDetails superAdmin = User.builder()
            .username("superadmin")
            .password(passwordEncoder().encode("password123"))
            .roles("SUPER_ADMIN", "ADMIN")
            .build();

        // 普通管理员
        UserDetails admin = User.builder()
            .username("admin")
            .password(passwordEncoder().encode("password123"))
            .roles("ADMIN")
            .build();
            
        return new InMemoryUserDetailsManager(superAdmin, admin);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}


// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/AdminUserVO.java
// =================================================================
package com.qding.chatbi.admin.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class AdminUserVO {
    private Long id;
    private String name;
    private String mobile;
    private List<String> roles;
    private boolean enabled;
    private Date createdAt;
}

// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/vo/CreateAdminUserRequest.java
// =================================================================
package com.qding.chatbi.admin.vo;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class CreateAdminUserRequest {
    @NotBlank(message = "姓名不能为空")
    private String name;

    @NotBlank(message = "手机号不能为空")
    @Size(min = 11, max = 11, message = "手机号必须为11位")
    private String mobile;

    @NotBlank(message = "初始密码不能为空")
    @Size(min = 6, message = "密码至少为6位")
    private String password;
}

// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/UserAdminController.java
// =================================================================
package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.vo.AdminUserVO;
import com.qding.chatbi.admin.vo.CreateAdminUserRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/users")
@PreAuthorize("hasRole('SUPER_ADMIN')") // 确保整个Controller只有超级管理员能访问
public class UserAdminController {

    // 实际应注入 AdminUserService
    // private final AdminUserService adminUserService;

    @GetMapping("/")
    public ResponseEntity<List<AdminUserVO>> getAllUsers() {
        // List<AdminUserVO> users = adminUserService.getAllUsers();
        // 模拟返回
        return ResponseEntity.ok(Collections.emptyList());
    }

    @PostMapping("/")
    public ResponseEntity<AdminUserVO> createUser(@Valid @RequestBody CreateAdminUserRequest request) {
        // AdminUserVO createdUser = adminUserService.createUser(request);
        // 模拟返回
        AdminUserVO createdUser = new AdminUserVO();
        createdUser.setName(request.getName());
        createdUser.setMobile(request.getMobile());
        return ResponseEntity.ok(createdUser);
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<Map<String, Boolean>> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Boolean> status) {
        // adminUserService.updateUserStatus(id, status.get("enabled"));
        return ResponseEntity.ok(Collections.singletonMap("success", true));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> deleteUser(@PathVariable Long id) {
        // adminUserService.deleteUser(id);
        return ResponseEntity.ok(Collections.singletonMap("success", true));
    }

    // 其他更新方法...
}

// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/DataSourceAdminController.java
// =================================================================
package com.qding.chatbi.admin.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/data-sources")
public class DataSourceAdminController {

    @GetMapping("/")
    public ResponseEntity<List<?>> getAllDataSources() {
        // 实际应调用服务层
        return ResponseEntity.ok(Collections.emptyList());
    }

    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@RequestBody Object request) {
        // 实际应调用服务层
        return ResponseEntity.ok(Collections.singletonMap("success", true));
    }
    // 其他增删改查端点...
}


// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/DatasetAdminController.java
// =================================================================
package com.qding.chatbi.admin.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/api/admin/datasets")
public class DatasetAdminController {

    @GetMapping("/")
    public ResponseEntity<List<?>> getAllDatasets() {
        // 实际应调用服务层
        return ResponseEntity.ok(Collections.emptyList());
    }
    
    // 其他增删改查端点...
}

// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/KnowledgeBaseAdminController.java
// =================================================================
package com.qding.chatbi.admin.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/api/admin/knowledge-base")
public class KnowledgeBaseAdminController {
    
    @GetMapping("/terms")
    public ResponseEntity<List<?>> getAllTerms() {
         // 实际应调用服务层
        return ResponseEntity.ok(Collections.emptyList());
    }

    @GetMapping("/examples")
    public ResponseEntity<List<?>> getAllExamples() {
         // 实际应调用服务层
        return ResponseEntity.ok(Collections.emptyList());
    }
    
    // 其他增删改查端点...
}

// =================================================================
// File: chatbi-admin-backend/src/main/java/com/qding/chatbi/admin/controller/PermissionAdminController.java
// =================================================================
package com.qding.chatbi.admin.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/api/admin/permissions")
public class PermissionAdminController {

    @GetMapping("/roles")
    public ResponseEntity<List<?>> getAllRoles() {
         // 实际应调用服务层
        return ResponseEntity.ok(Collections.emptyList());
    }

    @GetMapping("/assignments")
    public ResponseEntity<List<?>> getAssignments() {
         // 实际应调用服务层
        return ResponseEntity.ok(Collections.emptyList());
    }

    // 其他增删改查端点...
}
