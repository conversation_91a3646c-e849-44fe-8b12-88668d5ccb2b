# Spring Boot application configuration for chatbi-admin-backend
# Example:
# server:
#   port: 8080 # Often the main admin/gateway port
# spring:
#   application:
#     name: chatbi-admin-backend
#   # Security configuration might be needed here
#   # security:
#   #   user:
#   #     name: admin
#   #     password: adminpassword # Store securely!
# # Client configurations for accessing other services (e.g., metadata, knowledge)
# app:
#   services:
#     metadata-url: http://localhost:8083/api/metadata
#     knowledge-url: http://localhost:8082/api/knowledge

server:
  servlet:
    session:
      timeout: 30m
