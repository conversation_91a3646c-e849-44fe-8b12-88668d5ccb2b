package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.PermissionService;
import com.qding.chatbi.admin.vo.PagedResponse;
import com.qding.chatbi.admin.vo.permission.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/admin/permissions")
public class PermissionAdminController {

    @Autowired
    private PermissionService permissionService;

    // 角色管理端点
    @GetMapping("/roles")
    public ResponseEntity<PagedResponse<RoleDTO>> listRoles(
        @RequestParam(required = false) String search,
        Pageable pageable) {
        
        Page<RoleDTO> page = permissionService.searchOrListRoles(search, pageable);
        PagedResponse<RoleDTO> response = new PagedResponse<>(page);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/roles")
    public ResponseEntity<RoleDTO> createRole(@Valid @RequestBody CreateOrUpdateRoleRequest request) {
        return ResponseEntity.ok(permissionService.createRole(request));
    }

    @PutMapping("/roles/{id}")
    public ResponseEntity<RoleDTO> updateRole(@PathVariable Long id, @Valid @RequestBody CreateOrUpdateRoleRequest request) {
        return ResponseEntity.ok(permissionService.updateRole(id, request));
    }

    @DeleteMapping("/roles/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        permissionService.deleteRole(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/roles/all")
    public ResponseEntity<List<String>> getAllRoleNames() {
        return ResponseEntity.ok(permissionService.getAllRoleNames());
    }

    // --- 新的、以数据集为中心的权限分配端点 ---

    @GetMapping("/datasets-permissions")
    public ResponseEntity<List<DatasetPermissionDTO>> listDatasetPermissions() {
        return ResponseEntity.ok(permissionService.getAllDatasetPermissions());
    }

    @PutMapping("/datasets-permissions/{datasetId}/roles")
    public ResponseEntity<Void> updateDatasetRoles(
            @PathVariable Long datasetId,
            @Valid @RequestBody UpdateDatasetRolesRequest request) {
        permissionService.updateRolesForDataset(datasetId, request.getRoleIds());
        return ResponseEntity.noContent().build();
    }

    // --- 旧的权限分配端点 (保留以兼容) ---
    @GetMapping("/assignments")
    public ResponseEntity<PermissionAssignmentDTO> getAssignments() {
        return ResponseEntity.ok(permissionService.getPermissionAssignments());
    }

    @PutMapping("/assignments")
    public ResponseEntity<Void> updateAssignments(@RequestBody List<UpdateAssignmentsRequest> requests) {
        permissionService.updateAssignments(requests);
        return ResponseEntity.noContent().build();
    }
} 