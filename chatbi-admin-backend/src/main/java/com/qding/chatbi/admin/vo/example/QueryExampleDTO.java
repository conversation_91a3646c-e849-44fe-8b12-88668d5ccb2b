package com.qding.chatbi.admin.vo.example;

import lombok.Data;
import java.util.Date;

@Data
public class QueryExampleDTO {
    private Long id;
    private String userQuestion;
    private String targetQueryRepresentation;
    private Long targetDatasetId;
    private String targetDatasetName;
    private String involvedColumnIds;
    private String notes;
    private String difficultyLevel;
    private String status;
    private Date createdAt;
    private Date updatedAt;
} 