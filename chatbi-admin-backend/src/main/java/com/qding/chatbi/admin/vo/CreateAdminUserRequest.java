package com.qding.chatbi.admin.vo;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Email;

@Data
public class CreateAdminUserRequest {
    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "初始密码不能为空")
    @Size(min = 6, message = "密码至少为6位")
    private String password;

    @NotBlank(message = "姓名不能为空")
    private String full_name;

    @NotBlank(message = "角色不能为空")
    private String role;

    @Email(message = "邮箱格式不正确")
    private String email;
} 