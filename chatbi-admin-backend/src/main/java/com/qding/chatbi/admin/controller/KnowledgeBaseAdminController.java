package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.KnowledgeBaseAdminService;
import com.qding.chatbi.admin.vo.BatchOperationReport;
import com.qding.chatbi.admin.vo.PagedResponse;
import com.qding.chatbi.admin.vo.example.CreateOrUpdateExampleRequest;
import com.qding.chatbi.admin.vo.example.QueryExampleDTO;
import com.qding.chatbi.admin.vo.terminology.CreateOrUpdateTermRequest;
import com.qding.chatbi.admin.vo.terminology.TerminologyDTO;
import com.qding.chatbi.metadata.entity.QueryableDataset;
import com.qding.chatbi.metadata.repository.QueryableDatasetRepository;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/knowledge-base")
public class KnowledgeBaseAdminController {

    @Autowired
    private KnowledgeBaseAdminService knowledgeBaseAdminService;
    
    @Autowired
    private QueryableDatasetRepository datasetRepository;

    // --- Terminology Endpoints ---

    @GetMapping("/terms")
    public ResponseEntity<PagedResponse<TerminologyDTO>> listTerms(
            @RequestParam(required = false) String search,
            Pageable pageable) {
        Page<TerminologyDTO> page = knowledgeBaseAdminService.searchTerms(search, pageable);
        return ResponseEntity.ok(new PagedResponse<>(page));
    }

    @PostMapping("/terms")
    public ResponseEntity<TerminologyDTO> createTerm(@Valid @RequestBody CreateOrUpdateTermRequest request) {
        return ResponseEntity.ok(knowledgeBaseAdminService.createTerm(request));
    }

    @PutMapping("/terms/{id}")
    public ResponseEntity<TerminologyDTO> updateTerm(@PathVariable Long id, @Valid @RequestBody CreateOrUpdateTermRequest request) {
        return ResponseEntity.ok(knowledgeBaseAdminService.updateTerm(id, request));
    }

    @DeleteMapping("/terms/{id}")
    public ResponseEntity<Void> deleteTerm(@PathVariable Long id) {
        knowledgeBaseAdminService.deleteTerm(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/terms/batch")
    public ResponseEntity<BatchOperationReport> createTermsInBatch(@Valid @RequestBody List<CreateOrUpdateTermRequest> requests) {
        return ResponseEntity.ok(knowledgeBaseAdminService.createTermsInBatch(requests));
    }

    // --- Query Example Endpoints ---

    @GetMapping("/examples")
    public ResponseEntity<PagedResponse<QueryExampleDTO>> listOrSearchExamples(
            @RequestParam(required = false) String search,
            Pageable pageable) {
        Page<QueryExampleDTO> page = knowledgeBaseAdminService.searchOrListExamples(search, pageable);
        return ResponseEntity.ok(new PagedResponse<>(page));
    }

    @PostMapping("/examples")
    public ResponseEntity<QueryExampleDTO> createExample(@Valid @RequestBody CreateOrUpdateExampleRequest request) {
        QueryExampleDTO createdExample = knowledgeBaseAdminService.createExample(request);
        return ResponseEntity.ok(createdExample);
    }

    @PutMapping("/examples/{id}")
    public ResponseEntity<QueryExampleDTO> updateExample(
            @PathVariable Long id,
            @Valid @RequestBody CreateOrUpdateExampleRequest request) {
        QueryExampleDTO updatedExample = knowledgeBaseAdminService.updateExample(id, request);
        return ResponseEntity.ok(updatedExample);
    }

    @DeleteMapping("/examples/{id}")
    public ResponseEntity<Void> deleteExample(@PathVariable Long id) {
        knowledgeBaseAdminService.deleteExample(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/examples/batch")
    public ResponseEntity<BatchOperationReport> createExamplesInBatch(@Valid @RequestBody List<CreateOrUpdateExampleRequest> requests) {
        BatchOperationReport report = knowledgeBaseAdminService.createExamplesInBatch(requests);
        return ResponseEntity.ok(report);
    }
    
    // --- Dataset Endpoints ---
    
    @GetMapping("/datasets")
    public ResponseEntity<List<DatasetSummaryDTO>> listDatasets() {
        List<QueryableDataset> datasets = datasetRepository.findAll();
        List<DatasetSummaryDTO> datasetSummaries = datasets.stream()
                .map(this::convertToDatasetSummaryDTO)
                .toList();
        return ResponseEntity.ok(datasetSummaries);
    }
    
    private DatasetSummaryDTO convertToDatasetSummaryDTO(QueryableDataset dataset) {
        DatasetSummaryDTO dto = new DatasetSummaryDTO();
        dto.setId(dataset.getId());
        dto.setDatasetName(dataset.getDatasetName());
        dto.setDescription(dataset.getDescription());
        dto.setStatus(dataset.getStatus());
        return dto;
    }
    
    // 简单的数据集摘要DTO
    public static class DatasetSummaryDTO {
        private Long id;
        private String datasetName;
        private String description;
        private String status;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getDatasetName() { return datasetName; }
        public void setDatasetName(String datasetName) { this.datasetName = datasetName; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
} 