package com.qding.chatbi.admin.vo.terminology;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

@Data
public class CreateTermRequest {
    @NotBlank
    private String businessTerm;

    @NotBlank
    private String standardReferenceType; // "DatasetColumn" 或 "Custom"

    // 如果 type 是 DatasetColumn，则此项必填
    private Long standardReferenceId; // 对应 dataset_columns.id

    @NotBlank
    private String contextDescription;
}