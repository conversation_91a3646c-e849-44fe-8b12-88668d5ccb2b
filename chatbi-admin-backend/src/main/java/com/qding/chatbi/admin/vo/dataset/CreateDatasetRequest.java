package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.BaseObjectType;
import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class CreateDatasetRequest {
    // ... (基本信息)
    @NotBlank
    private String datasetName;
    private String description;
    @NotNull
    private Long dataSourceId;
    @NotNull
    private BaseObjectType baseObjectType;
    @NotBlank
    private String sourceDefinition;
    
    // 【新增】包含用户在第三步选择并配置好的字段列表
    @Valid
    private List<ColumnInitialConfig> columns;
} 