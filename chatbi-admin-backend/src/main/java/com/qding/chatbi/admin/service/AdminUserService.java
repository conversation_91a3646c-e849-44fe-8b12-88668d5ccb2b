package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.entity.AdminUser;
import com.qding.chatbi.admin.repository.AdminUserRepository;
import com.qding.chatbi.admin.vo.AdminUserVO;
import com.qding.chatbi.admin.vo.CreateAdminUserRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityNotFoundException;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AdminUserService {

    private final AdminUserRepository adminUserRepository;
    private final PasswordEncoder passwordEncoder;

    public Page<AdminUserVO> getAllAdminUsers(Pageable pageable) {
        Page<AdminUser> adminUserPage = adminUserRepository.findAll(pageable);
        return adminUserPage.map(this::convertToVO);
    }

    public AdminUserVO createAdminUser(CreateAdminUserRequest request) {
        if (adminUserRepository.findByUsername(request.getUsername()).isPresent()) {
            throw new IllegalArgumentException("Username already exists");
        }

        AdminUser adminUser = new AdminUser();
        adminUser.setUsername(request.getUsername());
        adminUser.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        adminUser.setEmail(request.getEmail() != null ? request.getEmail() : request.getUsername() + "@example.com");
        adminUser.setFullName(request.getFull_name());
        adminUser.setRole(request.getRole());
        adminUser.setStatus("active");
        adminUser.setEnabled(true);

        AdminUser savedUser = adminUserRepository.save(adminUser);
        return convertToVO(savedUser);
    }

    public void updateUserStatus(Long userId, String status) {
        AdminUser adminUser = adminUserRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("AdminUser not found with id: " + userId));
        adminUser.setStatus(status);
        adminUser.setEnabled("active".equals(status));
        adminUserRepository.save(adminUser);
    }

    public AdminUserVO updateUser(Long userId, String fullName, String role) {
        AdminUser adminUser = adminUserRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("AdminUser not found with id: " + userId));
        
        if (fullName != null) {
            adminUser.setFullName(fullName);
        }
        if (role != null) {
            adminUser.setRole(role);
        }
        
        AdminUser savedUser = adminUserRepository.save(adminUser);
        return convertToVO(savedUser);
    }

    private AdminUserVO convertToVO(AdminUser adminUser) {
        AdminUserVO vo = new AdminUserVO();
        vo.setId(adminUser.getId());
        vo.setUsername(adminUser.getUsername());
        vo.setEmail(adminUser.getEmail());
        vo.setFull_name(adminUser.getFullName());
        vo.setRole(adminUser.getRole());
        vo.setStatus(adminUser.getStatus());
        vo.setEnabled(adminUser.isEnabled());
        vo.setCreated_at(adminUser.getCreatedAt());
        vo.setUpdated_at(adminUser.getUpdatedAt());
        return vo;
    }

    public AdminUser createUser(String username, String password, String email) {
        AdminUser newUser = new AdminUser();
        newUser.setUsername(username);
        newUser.setPasswordHash(passwordEncoder.encode(password));
        newUser.setEmail(email);
        newUser.setRole("admin");
        newUser.setStatus("active");
        return adminUserRepository.save(newUser);
    }

    public Optional<AdminUser> findByUsername(String username) {
        return adminUserRepository.findByUsername(username);
    }

    public void deleteUser(Long userId) {
        AdminUser adminUser = adminUserRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("AdminUser not found with id: " + userId));
        
        // 可以在这里添加业务逻辑检查，比如：
        // - 不能删除最后一个超级管理员
        // - 不能删除当前登录的用户
        
        adminUserRepository.delete(adminUser);
    }
} 