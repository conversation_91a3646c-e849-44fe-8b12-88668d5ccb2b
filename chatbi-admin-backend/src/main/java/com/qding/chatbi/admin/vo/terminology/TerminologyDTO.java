package com.qding.chatbi.admin.vo.terminology;

import lombok.Data;
import java.util.Date;

@Data
public class TerminologyDTO {
    private Long id;
    private String businessTerm; // 业务术语, e.g., "GMV"
    private String standardReferenceType; // e.g., "DatasetColumn"
    private String standardReferenceId; // e.g., "1" (字段ID)
    private String standardReferenceName; // e.g., "销售额"
    private String contextDescription;
    private Date updatedAt;
} 