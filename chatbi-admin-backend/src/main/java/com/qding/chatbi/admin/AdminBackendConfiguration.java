package com.qding.chatbi.admin;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for the admin backend module.
 * This module is designed to be used as a library by the main application.
 * The main application entry point is in the 'chatbi-application' module.
 */
@Configuration
@ComponentScan(basePackages = {"com.qding.chatbi.admin"})
public class AdminBackendConfiguration {

    // This class serves as a configuration entry point for the admin backend module
    // when it's used as a dependency in the main application

}
