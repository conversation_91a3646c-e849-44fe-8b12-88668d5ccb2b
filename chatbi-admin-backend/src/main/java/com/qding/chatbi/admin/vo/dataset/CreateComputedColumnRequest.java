package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.SemanticType;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class CreateComputedColumnRequest {
    @NotNull
    private Long datasetId;

    @NotBlank
    private String columnName; // 业务名称, e.g., "客单价"

    @NotBlank
    private String expression; // SQL表达式, e.g., "sale_amount / order_count"

    @NotNull
    private SemanticType semanticType; // 通常是 METRIC

    @NotBlank
    private String dataType; // 预期的返回数据类型

    private String description;
} 