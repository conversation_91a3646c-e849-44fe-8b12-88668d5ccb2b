package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.AdminUserService;
import com.qding.chatbi.admin.vo.AdminUserVO;
import com.qding.chatbi.admin.vo.CreateAdminUserRequest;
import com.qding.chatbi.admin.vo.PagedResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/users")
@PreAuthorize("hasRole('SUPER_ADMIN')") // 确保整个Controller只有超级管理员能访问
@RequiredArgsConstructor
public class UserAdminController {

    private final AdminUserService adminUserService;

    @GetMapping("/")
    public ResponseEntity<PagedResponse<AdminUserVO>> listUsers(Pageable pageable) {
        Page<AdminUserVO> page = adminUserService.getAllAdminUsers(pageable);
        return ResponseEntity.ok(new PagedResponse<>(page));
    }

    @PostMapping("/")
    public ResponseEntity<AdminUserVO> createUser(@Valid @RequestBody CreateAdminUserRequest request) {
        AdminUserVO createdUser = adminUserService.createAdminUser(request);
        return ResponseEntity.ok(createdUser);
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<Void> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Object> statusRequest) {
        Object statusValue = statusRequest.get("status");
        if (statusValue == null) {
            return ResponseEntity.badRequest().build();
        }
        
        String status;
        if (statusValue instanceof Boolean) {
            // 兼容旧的Boolean格式
            status = ((Boolean) statusValue) ? "active" : "inactive";
        } else if (statusValue instanceof String) {
            status = (String) statusValue;
        } else {
            return ResponseEntity.badRequest().build();
        }
        
        adminUserService.updateUserStatus(id, status);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/{id}")
    public ResponseEntity<AdminUserVO> updateUser(@PathVariable Long id, @RequestBody Map<String, String> updateRequest) {
        String fullName = updateRequest.get("full_name");
        String role = updateRequest.get("role");
        
        AdminUserVO updatedUser = adminUserService.updateUser(id, fullName, role);
        return ResponseEntity.ok(updatedUser);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        adminUserService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }
} 