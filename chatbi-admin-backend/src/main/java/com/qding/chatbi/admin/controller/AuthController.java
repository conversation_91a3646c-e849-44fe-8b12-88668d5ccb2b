package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.entity.AdminUser;
import com.qding.chatbi.admin.service.AdminUserService;
import com.qding.chatbi.admin.util.JwtUtil;
import com.qding.chatbi.admin.vo.LoginRequest;
import com.qding.chatbi.admin.vo.LoginResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.Optional;

/**
 * 认证控制器
 * 处理登录和登出请求
 */
@RestController
@RequestMapping("/api/admin")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    public AuthController() {
        logger.info("==================== AuthController 被创建 ====================");
        logger.info("==================== admin-backend Controller 正在初始化 ====================");
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            logger.debug("==================== 处理登录请求: {} ====================", loginRequest.getUsername());
            
            // 查找用户
            Optional<AdminUser> userOptional = adminUserService.findByUsername(loginRequest.getUsername());
            if (userOptional.isEmpty()) {
                return ResponseEntity.status(401)
                    .body(new ErrorResponse("INVALID_CREDENTIALS", "用户名或密码错误"));
            }

            AdminUser user = userOptional.get();

            // 检查用户状态
            if (!"active".equals(user.getStatus())) {
                return ResponseEntity.status(401)
                    .body(new ErrorResponse("ACCOUNT_DISABLED", "账户已被禁用"));
            }

            // 验证密码
            if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPasswordHash())) {
                logger.warn("用户 '{}' 密码验证失败", loginRequest.getUsername());
                return ResponseEntity.status(401)
                    .body(new ErrorResponse("INVALID_CREDENTIALS", "用户名或密码错误"));
            }

            // 生成JWT token - 确保角色格式正确
            String userRole = user.getRole().toUpperCase();
            // 如果是super_admin，转换为SUPER_ADMIN格式
            if ("SUPER_ADMIN".equals(userRole)) {
                userRole = "SUPER_ADMIN";
            } else if ("ADMIN".equals(userRole)) {
                userRole = "ADMIN";
            }
            String token = jwtUtil.generateToken(user.getUsername(), Arrays.asList(userRole));

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(token);
            response.setUsername(user.getUsername());
            response.setRoles(Arrays.asList(userRole));

            logger.info("==================== 登录成功，生成token for user: {} ====================", user.getUsername());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("登录过程中发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(500)
                .body(new ErrorResponse("LOGIN_ERROR", "登录过程中发生错误: " + e.getMessage()));
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logout() {
        logger.info("==================== 处理登出请求 ====================");
        return ResponseEntity.ok(new SuccessResponse("登出成功"));
    }

    /**
     * 测试端点
     */
    @GetMapping("/test")
    public ResponseEntity<String> test() {
        logger.info("==================== 处理测试请求 ====================");
        return ResponseEntity.ok("Hello from AuthController!");
    }

    /**
     * 错误响应类
     */
    public static class ErrorResponse {
        private String error;
        private String message;

        public ErrorResponse(String error, String message) {
            this.error = error;
            this.message = message;
        }

        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 成功响应类
     */
    public static class SuccessResponse {
        private String message;

        public SuccessResponse(String message) {
            this.message = message;
        }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
} 