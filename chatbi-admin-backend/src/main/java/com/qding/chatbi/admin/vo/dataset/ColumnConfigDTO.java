package com.qding.chatbi.admin.vo.dataset;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ColumnConfigDTO {
    private Long id;
    private String columnName; // 指标名称
    private String technicalNameOrExpression; // 技术字段 (物理列名或表达式)
    private String description; // 字段的详细描述及计算口径
    private List<String> synonyms; // 同义词
    private String dataType; // 物理数据类型
    private String semanticType; // 语义类型
    private String status; // 字段状态 (active/inactive)
    private Boolean isComputed; // 是否为计算字段
    private boolean filterable;
    private boolean groupable;
}