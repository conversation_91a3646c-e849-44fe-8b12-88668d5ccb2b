package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.BatchOperationReport;
import com.qding.chatbi.admin.vo.example.CreateOrUpdateExampleRequest;
import com.qding.chatbi.admin.vo.example.QueryExampleDTO;
import com.qding.chatbi.admin.vo.terminology.*;
import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import com.qding.chatbi.knowledge.dto.SearchResultDTO;
import com.qding.chatbi.metadata.entity.BusinessTerminology;
import com.qding.chatbi.metadata.entity.DatasetColumn;
import com.qding.chatbi.metadata.entity.QueryExample;
import com.qding.chatbi.metadata.repository.BusinessTerminologyRepository;
import com.qding.chatbi.metadata.repository.DatasetColumnRepository;
import com.qding.chatbi.metadata.repository.DatasetRepository;
import com.qding.chatbi.metadata.repository.QueryExampleRepository;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

@Service
public class KnowledgeBaseAdminService {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeBaseAdminService.class);

    private final BusinessTerminologyRepository termRepository;
    private final DatasetColumnRepository datasetColumnRepository;
    private final KnowledgePersistenceService knowledgePersistenceService;
    @Autowired
    private QueryExampleRepository exampleRepository;
    @Autowired
    private DatasetRepository datasetRepository;

    @Autowired
    public KnowledgeBaseAdminService(BusinessTerminologyRepository termRepository,
            DatasetColumnRepository datasetColumnRepository,
            KnowledgePersistenceService knowledgePersistenceService) {
        this.termRepository = termRepository;
        this.datasetColumnRepository = datasetColumnRepository;
        this.knowledgePersistenceService = knowledgePersistenceService;
    }

    /**
     * 【核心变更】根据搜索词和分页信息查询业务术语。
     * 
     * @param query    可选的搜索关键词。
     * @param pageable 分页信息。
     * @return 分页的术语DTO。
     */
    public Page<TerminologyDTO> searchTerms(String query, Pageable pageable) {
        if (query == null || query.isBlank()) {
            logger.info("搜索词为空，返回所有术语 (分页: page={}, size={})", pageable.getPageNumber(), pageable.getPageSize());
            return termRepository.findAll(pageable).map(this::convertToDto);
        } else {
            logger.info("🔍 开始搜索业务术语 - 搜索词: [{}], 分页: page={}, size={}", query, pageable.getPageNumber(),
                    pageable.getPageSize());

            // The method signature in the persistence service was updated.
            // We adapt the call here. For fieldsToSearch, we pass an empty list as a
            // default.
            List<SearchResultDTO> searchResults = knowledgePersistenceService.searchSimilarTerms(query,
                    pageable.getPageSize(), List.of());
            List<TerminologyDTO> dtoList = searchResults.stream()
                    .map(result -> convertToDto(result.getTerm()))
                    .collect(Collectors.toList());
            Page<TerminologyDTO> resultPage = new PageImpl<>(dtoList, pageable, dtoList.size());

            logger.info("🎯 搜索结果返回 - 总数: {}, 当前页: {}", resultPage.getTotalElements(), resultPage.getContent().size());

            return resultPage;
        }
    }

    @Transactional
    public TerminologyDTO createTerm(CreateOrUpdateTermRequest request) {
        logger.info("📝 开始创建新业务术语 - 术语名称: [{}]", request.getBusinessTerm());
        BusinessTerminology term = buildTermEntityFromRequest(null, request);
        BusinessTerminology savedTerm = knowledgePersistenceService.saveOrUpdateTerm(term);
        logger.info("✅ 业务术语创建成功 - ID: {}, 名称: [{}]", savedTerm.getId(), savedTerm.getBusinessTerm());
        return convertToDto(savedTerm);
    }

    @Transactional
    public TerminologyDTO updateTerm(Long id, CreateOrUpdateTermRequest request) {
        logger.info("✏️ 开始更新业务术语 - ID: {}, 术语名称: [{}]", id, request.getBusinessTerm());
        BusinessTerminology term = buildTermEntityFromRequest(id, request);
        BusinessTerminology updatedTerm = knowledgePersistenceService.saveOrUpdateTerm(term);
        logger.info("✅ 业务术语更新成功 - ID: {}, 名称: [{}]", updatedTerm.getId(), updatedTerm.getBusinessTerm());
        return convertToDto(updatedTerm);
    }

    @Transactional
    public BatchOperationReport createTermsInBatch(List<CreateOrUpdateTermRequest> requests) {
        int successCount = 0;
        List<String> errorMessages = new ArrayList<>();
        for (int i = 0; i < requests.size(); i++) {
            CreateOrUpdateTermRequest request = requests.get(i);
            try {
                createTerm(request);
                successCount++;
            } catch (Exception e) {
                errorMessages
                        .add(String.format("第 %d 行 '%s' 创建失败: %s", i + 1, request.getBusinessTerm(), e.getMessage()));
            }
        }
        return new BatchOperationReport(successCount, requests.size() - successCount, errorMessages);
    }

    private BusinessTerminology buildTermEntityFromRequest(Long id, CreateOrUpdateTermRequest request) {
        BusinessTerminology term = id == null ? new BusinessTerminology()
                : termRepository.findById(id)
                        .orElseThrow(() -> new ResourceNotFoundException("业务术语", id));

        // 验证 standardReferenceType 只能是 DatasetColumn 或 Custom
        if (!"DatasetColumn".equals(request.getStandardReferenceType()) &&
                !"Custom".equals(request.getStandardReferenceType())) {
            throw new IllegalArgumentException("standardReferenceType 只能是 'DatasetColumn' 或 'Custom'");
        }

        BeanUtils.copyProperties(request, term);

        if ("DatasetColumn".equals(request.getStandardReferenceType())) {
            List<Long> path = request.getStandardReferenceIdPath();
            if (path == null || path.size() != 2) {
                throw new IllegalArgumentException("映射到字段时，必须提供[数据集ID, 字段ID]");
            }
            Long columnId = path.get(1);
            DatasetColumn column = datasetColumnRepository.findById(columnId)
                    .orElseThrow(() -> new ResourceNotFoundException("数据集字段", columnId));

            term.setStandardReferenceId(String.valueOf(column.getId()));
            term.setStandardReferenceName(column.getColumnName());
        } else if ("Custom".equals(request.getStandardReferenceType())) {
            // Custom 类型不需要引用具体的字段
            term.setStandardReferenceId(null);
            term.setStandardReferenceName(null);
        }
        return term;
    }

    @Transactional
    public void deleteTerm(Long id) {
        logger.info("🗑️ 开始删除业务术语 - ID: {}", id);
        // 调用 knowledge service 来处理删除
        knowledgePersistenceService.deleteTerm(id);
        logger.info("✅ 业务术语删除完成 - ID: {}", id);
    }

    private TerminologyDTO convertToDto(BusinessTerminology entity) {
        TerminologyDTO dto = new TerminologyDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    public Page<QueryExampleDTO> searchOrListExamples(String searchTerm, Pageable pageable) {
        if (searchTerm == null || searchTerm.isBlank()) {
            Page<QueryExample> examplePage = exampleRepository.findAll(pageable);
            return examplePage.map(this::convertExampleToDto);
        }
        List<QueryExample> results = knowledgePersistenceService.searchSimilarExamples(searchTerm,
                pageable.getPageSize());
        List<QueryExampleDTO> dtoList = results.stream().map(this::convertExampleToDto).collect(Collectors.toList());
        return new PageImpl<>(dtoList, pageable, dtoList.size());
    }

    @Transactional
    public QueryExampleDTO createExample(CreateOrUpdateExampleRequest request) {
        QueryExample example = new QueryExample();
        BeanUtils.copyProperties(request, example);
        validateDataset(example.getTargetDatasetId());
        knowledgePersistenceService.saveOrUpdateExample(example);
        return convertExampleToDto(example);
    }

    @Transactional
    public QueryExampleDTO updateExample(Long id, CreateOrUpdateExampleRequest request) {
        QueryExample example = exampleRepository.findById(id)
                .orElseThrow(() -> new NoSuchElementException("QueryExample not found with id: " + id));
        BeanUtils.copyProperties(request, example);
        validateDataset(example.getTargetDatasetId());
        knowledgePersistenceService.saveOrUpdateExample(example);
        return convertExampleToDto(example);
    }

    @Transactional
    public void deleteExample(Long id) {
        if (!exampleRepository.existsById(id)) {
            throw new NoSuchElementException("QueryExample not found with id: " + id);
        }
        knowledgePersistenceService.deleteExample(id);
    }

    @Transactional
    public BatchOperationReport createExamplesInBatch(List<CreateOrUpdateExampleRequest> requests) {
        int successCount = 0;
        List<String> errorMessages = new ArrayList<>();
        for (int i = 0; i < requests.size(); i++) {
            CreateOrUpdateExampleRequest request = requests.get(i);
            try {
                if (request.getUserQuestion() == null || request.getUserQuestion().isBlank() ||
                        request.getTargetDatasetId() == null ||
                        request.getTargetQueryRepresentation() == null
                        || request.getTargetQueryRepresentation().isBlank()) {
                    throw new IllegalArgumentException("用户问题、关联数据集和查询表示不能为空。");
                }
                createExample(request);
                successCount++;
            } catch (Exception e) {
                errorMessages.add(String.format("第 %d 行范例创建失败: %s", i + 1, e.getMessage()));
            }
        }
        return new BatchOperationReport(successCount, requests.size() - successCount, errorMessages);
    }

    private QueryExampleDTO convertExampleToDto(QueryExample entity) {
        QueryExampleDTO dto = new QueryExampleDTO();
        BeanUtils.copyProperties(entity, dto);
        datasetRepository.findById(entity.getTargetDatasetId())
                .ifPresent(d -> dto.setTargetDatasetName(d.getDatasetName()));
        return dto;
    }

    private void validateDataset(Long datasetId) {
        datasetRepository.findById(datasetId)
                .orElseThrow(() -> new NoSuchElementException("Dataset not found with id: " + datasetId));
    }
}