package com.qding.chatbi.admin.vo.dataset;

import com.qding.chatbi.common.enums.SemanticType;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class ColumnInitialConfig {
    @NotBlank
    private String technicalName; // 原始物理字段名

    private String dataType; // 原始数据类型

    private String status = "active"; // 字段状态，默认为活跃

    @NotBlank
    private String columnName; // 业务名称 (可由前端预填充为技术名)

    @NotNull
    private SemanticType semanticType; // 语义类型
}