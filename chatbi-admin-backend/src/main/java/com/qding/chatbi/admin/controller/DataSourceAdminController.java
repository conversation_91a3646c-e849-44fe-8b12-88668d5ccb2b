package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.ModelManagementService;
import com.qding.chatbi.admin.vo.CreateDataSourceRequest;
import com.qding.chatbi.admin.vo.DataSourceDTO;
import com.qding.chatbi.admin.vo.PagedResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/data-sources")
public class DataSourceAdminController {

    @Autowired
    private ModelManagementService modelService;

    @GetMapping("")
    public ResponseEntity<PagedResponse<DataSourceDTO>> listDataSources(
        @PageableDefault(size = 20, sort = "id", direction = Sort.Direction.DESC) Pageable pageable) {
        
        Page<DataSourceDTO> page = modelService.getAllDataSources(pageable);
        
        return ResponseEntity.ok(new PagedResponse<>(page));
    }

    @PostMapping("")
    public ResponseEntity<DataSourceDTO> createDataSource(@Valid @RequestBody CreateDataSourceRequest request) {
        return ResponseEntity.ok(modelService.createDataSource(request));
    }

    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@Valid @RequestBody CreateDataSourceRequest request) {
        boolean success = modelService.testConnection(request);
        String message = success ? "连接成功" : "连接失败";
        return ResponseEntity.ok(Map.of("success", success, "message", message));
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<DataSourceDTO> updateDataSource(
            @PathVariable Long id, 
            @Valid @RequestBody CreateDataSourceRequest request) {
        return ResponseEntity.ok(modelService.updateDataSource(id, request));
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDataSource(@PathVariable Long id) {
        modelService.deleteDataSource(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}/tables")
    public ResponseEntity<List<Map<String, Object>>> listTables(@PathVariable Long id) {
        List<Map<String, Object>> tables = modelService.getTablesAndViews(id);
        return ResponseEntity.ok(tables);
    }
} 