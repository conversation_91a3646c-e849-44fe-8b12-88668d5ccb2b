package com.qding.chatbi.admin.service;

import com.qding.chatbi.common.enums.BaseObjectType;
import com.qding.chatbi.metadata.entity.DatabaseSourceConfig;
import com.qding.chatbi.metadata.entity.DatasetColumn;
import com.qding.chatbi.metadata.entity.QueryableDataset;
import com.qding.chatbi.metadata.repository.DataSourceRepository;
import com.qding.chatbi.metadata.repository.DatasetColumnRepository;
import com.qding.chatbi.metadata.repository.QueryableDatasetRepository;
import com.qding.chatbi.admin.vo.CreateDataSourceRequest;
import com.qding.chatbi.admin.vo.DataSourceDTO;
import com.qding.chatbi.admin.vo.dataset.ColumnConfigDTO;
import com.qding.chatbi.admin.vo.dataset.DatasetDetailDTO;
import com.qding.chatbi.admin.vo.dataset.DatasetSummaryDTO;
import com.qding.chatbi.admin.vo.dataset.CreateDatasetRequest;
import com.qding.chatbi.admin.vo.dataset.CreateComputedColumnRequest;
import com.qding.chatbi.admin.vo.dataset.ValidateExpressionRequest;
import com.qding.chatbi.admin.vo.dataset.ValidateExpressionResponse;
import com.qding.chatbi.admin.vo.dataset.PreviewSourceRequest;
import com.qding.chatbi.admin.vo.dataset.ColumnInitialConfig;
import com.qding.chatbi.admin.vo.dataset.UpdateDatasetRequest;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import com.qding.chatbi.common.exception.InvalidInputException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 封装模型管理相关的所有业务逻辑，包括数据源和数据集的管理。
 */
@Service
@Transactional(readOnly = true)
@Slf4j
public class ModelManagementService {

    private static final String MYSQL_CONNECTION_PARAMS = "useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
    private static final String MYSQL_CONNECTION_PARAMS_JSON = "{\"useSSL\":\"false\", \"allowPublicKeyRetrieval\":\"true\", \"serverTimezone\":\"UTC\"}";

    private final DataSourceRepository dataSourceRepository;
    private final QueryableDatasetRepository datasetRepository;
    private final DatasetColumnRepository datasetColumnRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    public ModelManagementService(DataSourceRepository dataSourceRepository,
            QueryableDatasetRepository datasetRepository,
            DatasetColumnRepository datasetColumnRepository) {
        this.dataSourceRepository = dataSourceRepository;
        this.datasetRepository = datasetRepository;
        this.datasetColumnRepository = datasetColumnRepository;
    }

    // =================================================================
    // 数据源管理 (Data Source Management)
    // =================================================================

    /**
     * 获取数据源的分页列表。
     * 
     * @param pageable 包含分页和排序信息的对象，由Controller传入。
     * @return 包含DTO的分页结果。
     */
    public Page<DataSourceDTO> getAllDataSources(Pageable pageable) {
        Page<DatabaseSourceConfig> page = dataSourceRepository.findAll(pageable);
        return page.map(this::convertToDataSourceDTO);
    }

    /**
     * 根据ID获取单个数据源详情。
     */
    public DataSourceDTO getDataSourceById(Long id) {
        return dataSourceRepository.findById(id)
                .map(this::convertToDataSourceDTO)
                .orElseThrow(() -> new ResourceNotFoundException("数据源", id));
    }

    /**
     * 创建一个新的数据源。
     */
    @Transactional
    public DataSourceDTO createDataSource(CreateDataSourceRequest request) {
        DatabaseSourceConfig entity = new DatabaseSourceConfig();
        BeanUtils.copyProperties(request, entity);

        // 根据数据库类型设置推荐的连接参数
        if (request.getDatabaseType() != null) {
            switch (request.getDatabaseType()) {
                case MYSQL:
                    entity.setConnectionParameters(MYSQL_CONNECTION_PARAMS_JSON);
                    break;
                case POSTGRESQL:
                    // PostgreSQL 在此场景下通常不需要额外参数，设置为空的JSON对象
                    entity.setConnectionParameters("{}");
                    break;
                default:
                    entity.setConnectionParameters("{}");
                    break;
            }
        } else {
            entity.setConnectionParameters("{}");
        }

        // 在实际应用中，密码应该加密存储
        // entity.setPassword(passwordEncoder.encode(request.getPassword()));
        DatabaseSourceConfig savedEntity = dataSourceRepository.save(entity);
        return convertToDataSourceDTO(savedEntity);
    }

    /**
     * 更新一个已存在的数据源。
     */
    @Transactional
    public DataSourceDTO updateDataSource(Long id, CreateDataSourceRequest request) {
        DatabaseSourceConfig entity = dataSourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("数据源", id));
        BeanUtils.copyProperties(request, entity, "id", "createdAt");

        // 同样在更新时，也根据数据库类型设置连接参数
        if (request.getDatabaseType() != null) {
            switch (request.getDatabaseType()) {
                case MYSQL:
                    entity.setConnectionParameters(MYSQL_CONNECTION_PARAMS_JSON);
                    break;
                case POSTGRESQL:
                    entity.setConnectionParameters("{}");
                    break;
                default:
                    entity.setConnectionParameters("{}");
                    break;
            }
        }

        // 如果密码字段不为空，则更新密码
        if (request.getPassword() != null && !request.getPassword().isEmpty()) {
            // entity.setPassword(passwordEncoder.encode(request.getPassword()));
            entity.setPassword(request.getPassword());
        }
        DatabaseSourceConfig updatedEntity = dataSourceRepository.save(entity);
        return convertToDataSourceDTO(updatedEntity);
    }

    /**
     * 删除一个数据源。
     */
    @Transactional
    public void deleteDataSource(Long id) {
        if (!dataSourceRepository.existsById(id)) {
            throw new ResourceNotFoundException("数据源", id);
        }
        // TODO: 在删除前，应检查该数据源下是否有关联的数据集
        dataSourceRepository.deleteById(id);
    }

    /**
     * 测试数据库连接。
     */
    public boolean testConnection(CreateDataSourceRequest request) {
        // 注意：这是一个简化的JDBC直连测试，实际项目中可能需要更复杂的驱动加载和连接池管理
        String url;
        try {
            switch (request.getDatabaseType()) {
                case MYSQL:
                    Class.forName("com.mysql.cj.jdbc.Driver");
                    url = "jdbc:mysql://" + request.getHost() + ":" + request.getPort() + "/"
                            + request.getDatabaseName() + "?" + MYSQL_CONNECTION_PARAMS;
                    break;
                case POSTGRESQL:
                    Class.forName("org.postgresql.Driver");
                    url = "jdbc:postgresql://" + request.getHost() + ":" + request.getPort() + "/"
                            + request.getDatabaseName();
                    break;
                // 为其他数据库类型添加case
                default:
                    throw new UnsupportedOperationException("不支持的数据库类型: " + request.getDatabaseType());
            }
            try (Connection connection = DriverManager.getConnection(url, request.getUsername(),
                    request.getPassword())) {
                return connection.isValid(2); // 2秒超时
            }
        } catch (Exception e) {
            // 记录详细的错误日志
            System.err.println("连接测试失败，详细错误信息: ");
            e.printStackTrace(System.err);
            return false;
        }
    }

    /**
     * 获取指定数据源下的所有表和视图。
     */
    public List<Map<String, Object>> getTablesAndViews(Long dataSourceId) {
        DatabaseSourceConfig config = dataSourceRepository.findById(dataSourceId)
                .orElseThrow(() -> new ResourceNotFoundException("数据源", dataSourceId));

        List<Map<String, Object>> tables = new ArrayList<>();
        String url = buildJdbcUrl(config);
        log.info("获取数据库链接:{},{},{},{}",dataSourceId,url,config.getUsername(),config.getPassword());

        try (Connection connection = DriverManager.getConnection(url, config.getUsername(), config.getPassword())) {
            DatabaseMetaData metaData = connection.getMetaData();
            // 同时获取 TABLE 和 VIEW
            try (ResultSet rs = metaData.getTables(config.getDatabaseName(), null, "%",
                    new String[] { "TABLE", "VIEW" })) {
                while (rs.next()) {
                    Map<String, Object> tableInfo = new HashMap<>();
                    tableInfo.put("name", rs.getString("TABLE_NAME"));
                    tableInfo.put("type", rs.getString("TABLE_TYPE"));
                    tableInfo.put("comment", rs.getString("REMARKS"));
                    tables.add(tableInfo);
                }
            }
        } catch (Exception e) {
            System.err.println("获取表和视图列表失败: " + e.getMessage());
            e.printStackTrace(System.err);
            // 在生产环境中，应该抛出一个自定义异常
            throw new RuntimeException("无法连接到数据库并获取表列表", e);
        }
        return tables;
    }

    private String buildJdbcUrl(DatabaseSourceConfig config) {
        switch (config.getDatabaseType()) {
            case MYSQL:
                return "jdbc:mysql://" + config.getHost() + ":" + config.getPort() + "/" + config.getDatabaseName()
                        + "?" + MYSQL_CONNECTION_PARAMS;
            case POSTGRESQL:
                return "jdbc:postgresql://" + config.getHost() + ":" + config.getPort() + "/"
                        + config.getDatabaseName();
            default:
                throw new UnsupportedOperationException("不支持的数据库类型: " + config.getDatabaseType());
        }
    }

    private String mapJdbcTypeToStandard(int jdbcType, String typeName) {
        switch (jdbcType) {
            case java.sql.Types.VARCHAR:
            case java.sql.Types.CHAR:
            case java.sql.Types.LONGVARCHAR:
            case java.sql.Types.NVARCHAR:
            case java.sql.Types.NCHAR:
            case java.sql.Types.LONGNVARCHAR:
                return "STRING";
            case java.sql.Types.INTEGER:
            case java.sql.Types.BIGINT:
            case java.sql.Types.SMALLINT:
            case java.sql.Types.TINYINT:
            case java.sql.Types.NUMERIC:
            case java.sql.Types.DECIMAL:
            case java.sql.Types.FLOAT:
            case java.sql.Types.DOUBLE:
            case java.sql.Types.REAL:
                return "NUMBER";
            case java.sql.Types.DATE:
            case java.sql.Types.TIME:
            case java.sql.Types.TIMESTAMP:
                return "DATETIME";
            case java.sql.Types.BOOLEAN:
            case java.sql.Types.BIT:
                return "BOOLEAN";
            default:
                // 对于不确定的类型，可以根据typeName做一些猜测
                String lowerTypeName = typeName.toLowerCase();
                if (lowerTypeName.contains("int") || lowerTypeName.contains("decimal")
                        || lowerTypeName.contains("numeric")) {
                    return "NUMBER";
                }
                if (lowerTypeName.contains("char") || lowerTypeName.contains("text")) {
                    return "STRING";
                }
                if (lowerTypeName.contains("date") || lowerTypeName.contains("time")) {
                    return "DATETIME";
                }
                return "UNKNOWN";
        }
    }

    private com.qding.chatbi.common.enums.SemanticType recommendSemanticType(String standardType) {
        if ("NUMBER".equals(standardType)) {
            return com.qding.chatbi.common.enums.SemanticType.METRIC;
        }
        return com.qding.chatbi.common.enums.SemanticType.DIMENSION;
    }

    // =================================================================
    // 数据集管理 (Dataset Management)
    // =================================================================

    /**
     * 获取数据集的分页列表。
     */
    public Page<DatasetSummaryDTO> getAllDatasets(Pageable pageable) {
        Page<QueryableDataset> page = datasetRepository.findAll(pageable);
        return page.map(this::convertToDatasetSummaryDTO);
    }

    /**
     * 获取单个数据集的完整详情，包含所有字段配置。
     */
    public DatasetDetailDTO getDatasetDetails(Long id) {
        QueryableDataset dataset = datasetRepository.findByIdWithColumnsAndDataSource(id)
                .orElseThrow(() -> new ResourceNotFoundException("Dataset not found with id: " + id));
        return convertToDatasetDetailDTO(dataset);
    }

    /**
     * 检查数据集名称是否已存在（用于创建时检查）
     */
    public boolean isDatasetNameExists(String datasetName) {
        return datasetRepository.existsByDatasetName(datasetName);
    }

    /**
     * 检查数据集名称是否已存在，排除指定ID的数据集（用于更新时检查）
     */
    public boolean isDatasetNameExists(String datasetName, Long excludeId) {
        return datasetRepository.existsByDatasetNameAndIdNot(datasetName, excludeId);
    }

    /**
     * 创建一个新的数据集。
     */
    @Transactional
    public DatasetDetailDTO createDataset(CreateDatasetRequest request) {
        // 首先检查数据集名称是否已存在
        if (datasetRepository.existsByDatasetName(request.getDatasetName())) {
            throw new InvalidInputException("数据集名称 '" + request.getDatasetName() + "' 已存在，请使用其他名称", true);
        }

        QueryableDataset dataset = new QueryableDataset();

        // 验证数据源是否存在
        dataSourceRepository.findById(request.getDataSourceId())
                .orElseThrow(() -> new ResourceNotFoundException("数据源", request.getDataSourceId()));

        dataset.setDatasetName(request.getDatasetName());
        dataset.setDescription(request.getDescription());
        dataset.setDatabaseSourceId(request.getDataSourceId());
        dataset.setBaseObjectType(request.getBaseObjectType().name());

        // 关键：将源定义（表名或SQL）同时设置到 base_object_name 和 technical_definition
        // 以兼容旧的表结构，确保 base_object_name 不为空
        dataset.setBaseObjectName(request.getSourceDefinition());
        dataset.setTechnicalDefinition(request.getSourceDefinition());

        QueryableDataset savedDataset = datasetRepository.save(dataset);

        // 【新增】如果请求中包含字段配置，则批量创建字段
        if (request.getColumns() != null && !request.getColumns().isEmpty()) {
            for (ColumnInitialConfig columnConfig : request.getColumns()) {
                if ("active".equals(columnConfig.getStatus())) {
                    DatasetColumn column = new DatasetColumn();
                    column.setDataset(savedDataset);
                    column.setColumnName(columnConfig.getColumnName());
                    column.setTechnicalNameOrExpression(columnConfig.getTechnicalName());
                    column.setSemanticType(columnConfig.getSemanticType().name());
                    column.setDataType(columnConfig.getDataType());
                    column.setComputed(false);
                    column.setStatus("active");
                    column.setFilterable(true);
                    column.setGroupable(columnConfig.getSemanticType().name().equals("DIMENSION"));
                    datasetColumnRepository.save(column);
                }
            }
        }

        return getDatasetDetails(savedDataset.getId());
    }

    /**
     * 【新增】预览数据源的字段信息
     */
    public List<ColumnInitialConfig> previewColumns(PreviewSourceRequest request) {
        if (request.getDataSourceId() == null) {
            throw new InvalidInputException("预览数据需要提供数据源ID。");
        }

        DatabaseSourceConfig config = dataSourceRepository.findById(request.getDataSourceId())
                .orElseThrow(() -> new ResourceNotFoundException("数据源", request.getDataSourceId()));

        List<ColumnInitialConfig> columns = new ArrayList<>();
        String url = buildJdbcUrl(config);

        if (request.getBaseObjectType() == BaseObjectType.TABLE) {
            if (request.getSourceDefinition() == null || request.getSourceDefinition().isEmpty()) {
                throw new InvalidInputException("预览数据表类型时，必须提供表名。");
            }
            // Logic for Table-based preview
            try (Connection connection = DriverManager.getConnection(url, config.getUsername(), config.getPassword())) {
                DatabaseMetaData metaData = connection.getMetaData();
                String tableNamePattern = request.getSourceDefinition().toLowerCase();
                try (ResultSet rs = metaData.getColumns(config.getDatabaseName(), null, tableNamePattern, "%")) {
                    while (rs.next()) {
                        columns.add(mapResultSetToColumnInitialConfig(rs));
                    }
                }
            } catch (SQLException e) {
                System.err.println("预览数据源表列时发生错误: " + e.getMessage());
                e.printStackTrace(System.err);
                throw new RuntimeException("无法从数据源加载表列信息", e);
            }
        } else if (request.getBaseObjectType() == BaseObjectType.QUERY) {
            if (request.getSourceDefinition() == null || request.getSourceDefinition().isEmpty()) {
                throw new InvalidInputException("预览SQL查询类型时，必须提供SQL语句。");
            }
            // Logic for SQL Query-based preview
            String sql = request.getSourceDefinition();
            // Append LIMIT 1 to avoid fetching large data sets, this is a safe way to get
            // metadata
            String limitedSql = sql.trim().endsWith(";") ? sql.trim().substring(0, sql.trim().length() - 1)
                    : sql.trim();
            limitedSql = "SELECT * FROM (" + limitedSql + ") AS subquery LIMIT 1";

            try (Connection connection = DriverManager.getConnection(url, config.getUsername(), config.getPassword());
                    Statement statement = connection.createStatement();
                    ResultSet rs = statement.executeQuery(limitedSql)) {

                java.sql.ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                for (int i = 1; i <= columnCount; i++) {
                    ColumnInitialConfig col = new ColumnInitialConfig();
                    col.setColumnName(metaData.getColumnLabel(i)); // Use getColumnLabel to support aliases
                    col.setTechnicalName(metaData.getColumnName(i));
                    col.setDataType(mapJdbcTypeToStandard(metaData.getColumnType(i), metaData.getColumnTypeName(i)));
                    col.setStatus("active");
                    col.setSemanticType(recommendSemanticType(col.getDataType()));
                    columns.add(col);
                }
            } catch (SQLException e) {
                System.err.println("预览SQL查询列时发生错误: " + e.getMessage());
                e.printStackTrace(System.err);
                throw new RuntimeException("执行SQL查询并加载列信息失败: " + e.getMessage(), e);
            }
        } else {
            throw new InvalidInputException("不支持的预览类型: " + request.getBaseObjectType());
        }

        return columns;
    }

    private ColumnInitialConfig mapResultSetToColumnInitialConfig(ResultSet rs) throws SQLException {
        ColumnInitialConfig col = new ColumnInitialConfig();
        col.setColumnName(rs.getString("COLUMN_NAME"));
        // 映射JDBC类型到我们自己的标准类型
        col.setDataType(mapJdbcTypeToStandard(rs.getInt("DATA_TYPE"), rs.getString("TYPE_NAME")));
        // 默认启用所有预览字段
        col.setStatus("active");
        // 默认技术名称和业务名称一致
        col.setTechnicalName(rs.getString("COLUMN_NAME"));
        // 智能推荐语义类型
        col.setSemanticType(recommendSemanticType(col.getDataType()));
        return col;
    }

    /**
     * 【新增】更新数据集基本信息
     */
    @Transactional
    public DatasetDetailDTO updateDataset(Long id, UpdateDatasetRequest request) {
        QueryableDataset dataset = datasetRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("数据集", id));

        // 检查数据集名称是否与其他数据集重复（排除当前数据集）
        if (datasetRepository.existsByDatasetNameAndIdNot(request.getDatasetName(), id)) {
            throw new InvalidInputException("数据集名称 '" + request.getDatasetName() + "' 已存在，请使用其他名称", true);
        }

        dataset.setDatasetName(request.getDatasetName());
        dataset.setDescription(request.getDescription());

        datasetRepository.save(dataset);
        return getDatasetDetails(id);
    }

    /**
     * 【新增】删除数据集
     */
    @Transactional
    public void deleteDataset(Long id) {
        QueryableDataset dataset = datasetRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("数据集", id));

        // 删除所有关联的字段配置
        // 通过级联删除或手动删除
        if (dataset.getColumns() != null) {
            datasetColumnRepository.deleteAll(dataset.getColumns());
        }

        // 删除数据集
        datasetRepository.delete(dataset);
    }

    @Transactional
    public void deleteColumn(Long columnId) {
        if (!datasetColumnRepository.existsById(columnId)) {
            throw new ResourceNotFoundException("数据列", columnId);
        }
        datasetColumnRepository.deleteById(columnId);
    }

    /**
     * 更新单个数据列的配置
     */
    @Transactional
    public ColumnConfigDTO updateColumn(Long columnId, ColumnConfigDTO dto) {
        DatasetColumn column = datasetColumnRepository.findById(columnId)
                .orElseThrow(() -> new ResourceNotFoundException("数据列", columnId));

        // 按需更新，仅当DTO中提供了值时才更新
        if (dto.getColumnName() != null) {
            column.setColumnName(dto.getColumnName());
        }
        if (dto.getDescription() != null) {
            column.setDescription(dto.getDescription());
        }
        if (dto.getDataType() != null) {
            column.setDataType(dto.getDataType());
        }
        if (dto.getSemanticType() != null) {
            column.setSemanticType(dto.getSemanticType());
        }
        if (dto.getStatus() != null) {
            column.setStatus(dto.getStatus());
        }

        // 处理同义词
        if (dto.getSynonyms() != null) {
            try {
                String synonymsJson = objectMapper.writeValueAsString(dto.getSynonyms());
                column.setSynonyms(synonymsJson);
            } catch (IOException e) {
                throw new RuntimeException("序列化同义词失败", e);
            }
        }

        // 处理可筛选和可分组属性
        column.setFilterable(dto.isFilterable());
        column.setGroupable(dto.isGroupable());

        // 处理字段类型（是否为计算字段）
        if (dto.getIsComputed() != null) {
            // 校验：如果技术定义包含表达式，则必须标记为计算字段
            String technicalDef = column.getTechnicalNameOrExpression();
            boolean isExpression = technicalDef != null &&
                    (technicalDef.contains("+") || technicalDef.contains("-") ||
                            technicalDef.contains("*") || technicalDef.contains("/") ||
                            technicalDef.contains("(") || technicalDef.contains(")") ||
                            technicalDef.matches(".*[a-zA-Z_][a-zA-Z0-9_]*\\s*\\(.*"));

            if (isExpression && !dto.getIsComputed()) {
                throw new IllegalArgumentException("技术定义包含表达式，字段类型必须设置为计算字段");
            }

            column.setComputed(dto.getIsComputed());
        }

        // 注意：通常不应允许用户直接修改技术名称或是否为计算字段

        DatasetColumn updatedColumn = datasetColumnRepository.save(column);
        return convertToColumnConfigDTO(updatedColumn);
    }

    /**
     * 【新增】校验一个SQL表达式的有效性。
     * 该方法会构造一个测试SQL，并尝试在实际的数据源上执行它。
     *
     * @param request 包含数据集ID和待校验表达式的请求体。
     * @return 包含校验结果（是否有效）和详细信息（成功或失败消息）的响应对象。
     */
    public ValidateExpressionResponse validateExpression(ValidateExpressionRequest request) {
        QueryableDataset dataset = datasetRepository.findById(request.getDatasetId())
                .orElseThrow(() -> new ResourceNotFoundException("数据集", request.getDatasetId()));

        DatabaseSourceConfig sourceConfig = dataset.getDataSource();
        String jdbcUrl = buildJdbcUrl(sourceConfig);

        String fromClause;
        BaseObjectType objectType = BaseObjectType.valueOf(dataset.getBaseObjectType());

        if (objectType == BaseObjectType.TABLE || objectType == BaseObjectType.VIEW) {
            fromClause = dataset.getBaseObjectName();
        } else if (objectType == BaseObjectType.QUERY) {
            fromClause = "(" + dataset.getTechnicalDefinition() + ")";
        } else {
            return new ValidateExpressionResponse(false, "不支持的数据集类型: " + dataset.getBaseObjectType());
        }

        String testSql = String.format("SELECT %s AS test_expr FROM %s AS t LIMIT 1", request.getExpression(),
                fromClause);

        try (Connection connection = DriverManager.getConnection(jdbcUrl, sourceConfig.getUsername(),
                sourceConfig.getPassword());
                Statement statement = connection.createStatement()) {
            statement.execute(testSql);
            return new ValidateExpressionResponse(true, "表达式校验通过");
        } catch (SQLException e) {
            // 将具体的数据库错误信息返回给前端，便于用户排查表达式问题
            return new ValidateExpressionResponse(false, "校验失败: " + e.getMessage());
        } catch (Exception e) {
            // 捕获其他可能的异常，例如驱动加载失败等
            return new ValidateExpressionResponse(false, "发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 【新增】为数据集添加一个计算指标。
     *
     * @param request 包含计算指标详细信息的请求体。
     * @return 创建成功后，返回该计算指标的DTO。
     */
    @Transactional
    public ColumnConfigDTO createComputedColumn(CreateComputedColumnRequest request) {
        // 1. 在创建前，必须先对表达式进行严格校验
        ValidateExpressionResponse validation = validateExpression(
                new ValidateExpressionRequest(request.getDatasetId(), request.getExpression()));
        if (!validation.isValid()) {
            // 如果校验失败，则直接抛出异常，终止创建流程
            throw new IllegalArgumentException("表达式校验失败: " + validation.getMessage());
        }

        QueryableDataset dataset = datasetRepository.findById(request.getDatasetId())
                .orElseThrow(() -> new ResourceNotFoundException("数据集", request.getDatasetId()));

        // 2. 创建一个新的 DatasetColumn 实体
        DatasetColumn newColumn = new DatasetColumn();
        newColumn.setDataset(dataset);
        newColumn.setColumnName(request.getColumnName()); // 业务名称
        newColumn.setTechnicalNameOrExpression(request.getExpression()); // SQL表达式
        newColumn.setSemanticType(request.getSemanticType().name()); // 语义类型 (Enum -> String)
        newColumn.setDataType(request.getDataType()); // 数据类型
        newColumn.setDescription(request.getDescription());
        newColumn.setComputed(true); // 核心标记：这是一个计算字段
        newColumn.setStatus("active"); // 计算字段默认启用
        newColumn.setFilterable(false); // 计算指标通常不可作为筛选条件
        newColumn.setGroupable(false); // 计算指标不可用于分组

        // 3. 保存该实体到数据库
        DatasetColumn savedColumn = datasetColumnRepository.save(newColumn);

        // 4. 将保存后的实体转换为DTO并返回
        return convertToColumnConfigDTO(savedColumn);
    }

    @Transactional
    public void updatePhysicalColumns(Long datasetId, List<String> columnsToKeep) {
        QueryableDataset dataset = datasetRepository.findById(datasetId)
                .orElseThrow(() -> new ResourceNotFoundException("数据集", datasetId));

        List<DatasetColumn> existingColumns = datasetColumnRepository.findByDatasetId(datasetId)
                .stream()
                .filter(c -> !c.isComputed())
                .collect(Collectors.toList());

        Map<String, DatasetColumn> existingColumnsMap = existingColumns.stream()
                .collect(Collectors.toMap(DatasetColumn::getTechnicalNameOrExpression, c -> c));

        List<DatasetColumn> columnsToDelete = existingColumns.stream()
                .filter(c -> !columnsToKeep.contains(c.getTechnicalNameOrExpression()))
                .collect(Collectors.toList());

        PreviewSourceRequest previewRequest = new PreviewSourceRequest();
        previewRequest.setDataSourceId(dataset.getDataSource().getId());
        previewRequest.setBaseObjectType(BaseObjectType.valueOf(dataset.getBaseObjectType()));
        previewRequest.setSourceDefinition(dataset.getBaseObjectName());

        List<ColumnInitialConfig> sourceColumns = previewColumns(previewRequest);
        Map<String, ColumnInitialConfig> sourceColumnsMap = sourceColumns.stream()
                .collect(Collectors.toMap(ColumnInitialConfig::getColumnName, c -> c));

        // 新增字段
        List<DatasetColumn> columnsToAdd = columnsToKeep.stream()
                .filter(name -> !existingColumnsMap.containsKey(name))
                .map(name -> {
                    ColumnInitialConfig sourceCol = sourceColumnsMap.get(name);
                    DatasetColumn newColumn = new DatasetColumn();
                    newColumn.setDataset(dataset);
                    newColumn.setColumnName(name);
                    newColumn.setTechnicalNameOrExpression(name);
                    newColumn.setDataType(sourceCol != null ? sourceCol.getDataType() : "UNKNOWN");
                    newColumn.setComputed(false);
                    newColumn.setStatus("active");
                    newColumn.setSemanticType(sourceCol != null ? sourceCol.getSemanticType().name() : "DIMENSION");
                    return newColumn;
                })
                .collect(Collectors.toList());

        // 更新已存在字段的数据类型（从数据源同步最新的类型信息）
        List<DatasetColumn> columnsToUpdate = columnsToKeep.stream()
                .filter(name -> existingColumnsMap.containsKey(name))
                .map(name -> {
                    DatasetColumn existingColumn = existingColumnsMap.get(name);
                    ColumnInitialConfig sourceCol = sourceColumnsMap.get(name);
                    if (sourceCol != null && !Objects.equals(existingColumn.getDataType(), sourceCol.getDataType())) {
                        existingColumn.setDataType(sourceCol.getDataType());
                        return existingColumn;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!columnsToDelete.isEmpty()) {
            datasetColumnRepository.deleteAll(columnsToDelete);
        }
        if (!columnsToAdd.isEmpty()) {
            datasetColumnRepository.saveAll(columnsToAdd);
        }
        if (!columnsToUpdate.isEmpty()) {
            datasetColumnRepository.saveAll(columnsToUpdate);
        }
    }

    @Transactional
    public List<ColumnConfigDTO> syncColumnsFromSource(Long datasetId) {
        QueryableDataset dataset = datasetRepository.findById(datasetId)
                .orElseThrow(() -> new ResourceNotFoundException("数据集", datasetId));

        // In a real scenario, dataSyncService would connect to the source and get
        // columns.
        // Here we'll just return the existing columns as a placeholder.
        // List<ColumnDefinition> newColumns =
        // dataSyncService.getColumnsFor(dataset.getTechnicalDefinition());

        // Placeholder logic:
        // dataset.getColumns().clear(); // Clear old columns
        // // Create some dummy columns for demonstration
        // DatasetColumn col1 = new DatasetColumn();
        // col1.setColumnName("user_id");
        // col1.setDataType("NUMBER");
        // dataset.getColumns().add(col1);

        // DatasetColumn col2 = new DatasetColumn();
        // col2.setColumnName("order_amount");
        // col2.setDataType("NUMBER");
        // dataset.getColumns().add(col2);

        // datasetRepository.save(dataset);

        return dataset.getColumns().stream()
                .map(this::convertToColumnConfigDTO)
                .collect(Collectors.toList());
    }

    // =================================================================
    // 私有辅助方法 (Private Helper Methods)
    // =================================================================

    private DataSourceDTO convertToDataSourceDTO(DatabaseSourceConfig config) {
        DataSourceDTO dto = new DataSourceDTO();
        BeanUtils.copyProperties(config, dto);
        return dto;
    }

    private DatasetSummaryDTO convertToDatasetSummaryDTO(QueryableDataset dataset) {
        DatasetSummaryDTO dto = new DatasetSummaryDTO();
        dto.setId(dataset.getId());
        dto.setDatasetName(dataset.getDatasetName());
        dto.setDescription(dataset.getDescription());

        try {
            dto.setBaseObjectType(BaseObjectType.valueOf(dataset.getBaseObjectType().toUpperCase()));
        } catch (Exception e) {
            // 安全转换，如果失败则不设置或设置默认值
        }

        if (dataset.getUpdatedAt() != null) {
            dto.setUpdatedAt(
                    dataset.getUpdatedAt().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        }

        if (dataset.getDataSource() != null) {
            dto.setSourceName(dataset.getDataSource().getSourceName());
        } else if (dataset.getDatabaseSourceId() != null) {
            dataSourceRepository.findById(dataset.getDatabaseSourceId())
                    .ifPresent(dataSource -> dto.setSourceName(dataSource.getSourceName()));
        }

        return dto;
    }

    private DatasetDetailDTO convertToDatasetDetailDTO(QueryableDataset dataset) {
        DatasetDetailDTO dto = new DatasetDetailDTO();
        dto.setId(dataset.getId());
        dto.setDatasetName(dataset.getDatasetName());
        dto.setDescription(dataset.getDescription());
        dto.setSourceId(dataset.getDatabaseSourceId());
        dto.setBaseObjectName(dataset.getBaseObjectName());
        dto.setSourceDefinition(dataset.getTechnicalDefinition());
        try {
            dto.setBaseObjectType(BaseObjectType.valueOf(dataset.getBaseObjectType()));
        } catch (Exception e) {
            // ignore
        }

        // 设置数据源名称
        if (dataset.getDataSource() != null) {
            dto.setSourceName(dataset.getDataSource().getSourceName());
        } else if (dataset.getDatabaseSourceId() != null) {
            dataSourceRepository.findById(dataset.getDatabaseSourceId())
                    .ifPresent(dataSource -> dto.setSourceName(dataSource.getSourceName()));
        }

        dto.setColumns(dataset.getColumns().stream().map(this::convertToColumnConfigDTO).collect(Collectors.toList()));
        return dto;
    }

    private ColumnConfigDTO convertToColumnConfigDTO(DatasetColumn column) {
        ColumnConfigDTO dto = new ColumnConfigDTO();
        dto.setId(column.getId());
        dto.setColumnName(column.getColumnName());
        dto.setTechnicalNameOrExpression(column.getTechnicalNameOrExpression());
        dto.setDescription(column.getDescription());
        dto.setDataType(column.getDataType());
        dto.setSemanticType(column.getSemanticType());
        dto.setStatus(column.getStatus());
        dto.setIsComputed(column.isComputed());
        dto.setFilterable(column.isFilterable());
        dto.setGroupable(column.isGroupable());

        // 解析JSON字符串格式的synonyms
        if (column.getSynonyms() != null && !column.getSynonyms().isEmpty()) {
            try {
                dto.setSynonyms(objectMapper.readValue(column.getSynonyms(), new TypeReference<List<String>>() {
                }));
            } catch (IOException e) {
                // 如果解析失败，可以记录日志，并设置为空列表或保持null
                // log.error("Failed to parse synonyms JSON: {}", column.getSynonyms(), e);
                dto.setSynonyms(new ArrayList<>());
            }
        } else {
            dto.setSynonyms(new ArrayList<>());
        }

        return dto;
    }
}
