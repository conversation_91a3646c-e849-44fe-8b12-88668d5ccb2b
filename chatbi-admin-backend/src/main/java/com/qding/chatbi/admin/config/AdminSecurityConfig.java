package com.qding.chatbi.admin.config;

import com.qding.chatbi.admin.filter.JwtAuthenticationFilter;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security configuration for the admin backend.
 * 使用JWT Token认证，完全无状态
 */
@Configuration
@EnableWebSecurity
@Order(1) // 确保这个配置有最高优先级
public class AdminSecurityConfig {

    @Resource
    private CorsProperties corsProperties;

    @Resource
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    public AdminSecurityConfig() {
        System.out.println("==================== AdminSecurityConfig 被加载 ====================");
        System.out.println("==================== admin-backend模块正在初始化 ====================");
    }

    @Bean
    public SecurityFilterChain adminSecurityFilterChain(HttpSecurity http) throws Exception {
        System.out.println("==================== 配置 SecurityFilterChain ====================");
        
        return http
            .securityMatcher("/api/admin/**")
            .cors(cors -> cors.configurationSource(corsConfigurationSource())) // 启用CORS
            .csrf(csrf -> csrf.disable()) // 禁用CSRF
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/admin/login", "/api/admin/test", "/api/admin/permissions/roles/all").permitAll() // 允许这些端点无需认证
                .requestMatchers("/api/admin/**").hasAnyRole("ADMIN", "SUPER_ADMIN") // 其他 /api/admin/** 下的请求需要认证
                .anyRequest().denyAll() // 拒绝所有其他请求
            )
            .httpBasic(httpBasic -> httpBasic.disable()) // 禁用HTTP Basic认证
            .formLogin(form -> form.disable()) // 禁用表单登录
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            .build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(corsProperties.getAllowedOrigins());
                // Arrays.asList("http://localhost:*", "http://127.0.0.1:*", "http://*************:*","http://***********:*"));
        configuration.setAllowedMethods(corsProperties.getAllowedMethods());
        //Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS","PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/api/admin/**", configuration);
        return source;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        System.out.println("==================== 创建 PasswordEncoder Bean ====================");
        return new BCryptPasswordEncoder();
    }
}