package com.qding.chatbi.admin.config;

import com.qding.chatbi.common.exception.ChatBiException;
import com.qding.chatbi.common.exception.InvalidInputException;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import org.apache.catalina.connector.ClientAbortException;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理应用中的异常，返回标准化的错误响应
 */
@Slf4j
@ControllerAdvice
@ResponseBody
public class GlobalExceptionHandler {

    /**
     * 处理静态资源未找到异常
     * 这通常发生在前端请求API时被误认为是静态资源请求
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<Map<String, Object>> handleNoResourceFoundException(NoResourceFoundException e) {
        String path = e.getResourcePath();
        log.warn("No resource found for path: {}", path);

        // 如果是API路径被误认为静态资源，提供更友好的错误信息
        if (path != null && path.startsWith("api/")) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "API端点未找到或请求方法不正确: /" + path);
            errorResponse.put("errorCode", "BIE404");
            errorResponse.put("suggestion", "请检查请求URL和HTTP方法是否正确");

            return ResponseEntity.status(HttpStatus.NOT_FOUND).contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
        }

        // 普通的静态资源未找到
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", "请求的资源未找到: " + path);
        errorResponse.put("errorCode", "BIE404");

        return ResponseEntity.status(HttpStatus.NOT_FOUND).contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    /**
     * 处理无效输入异常
     */
    @ExceptionHandler(InvalidInputException.class)
    public ResponseEntity<Map<String, Object>> handleInvalidInputException(InvalidInputException e) {
        log.warn("Invalid input: {}", e.getMessage());

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", e.getMessage());
        errorResponse.put("errorCode", e.getErrorCode());

        return ResponseEntity.badRequest().contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    /**
     * 处理资源未找到异常
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.warn("Resource not found: {}", e.getMessage());

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", e.getMessage());
        errorResponse.put("errorCode", e.getErrorCode());

        return ResponseEntity.status(HttpStatus.NOT_FOUND).contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    /**
     * 处理通用ChatBi异常
     */
    @ExceptionHandler(ChatBiException.class)
    public ResponseEntity<Map<String, Object>> handleChatBiException(ChatBiException e) {
        log.error("ChatBi exception: {}", e.getMessage(), e);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", e.getMessage());
        errorResponse.put("errorCode", e.getErrorCode());

        // 根据错误码决定HTTP状态码
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        if (e.getErrorCode() != null) {
            switch (e.getErrorCode()) {
                case "BIE001": // INVALID_INPUT
                    status = HttpStatus.BAD_REQUEST;
                    break;
                case "BIE002": // UNAUTHENTICATED
                    status = HttpStatus.UNAUTHORIZED;
                    break;
                case "BIE003": // PERMISSION_DENIED
                    status = HttpStatus.FORBIDDEN;
                    break;
                case "BIE004": // RESOURCE_NOT_FOUND
                    status = HttpStatus.NOT_FOUND;
                    break;
                default:
                    status = HttpStatus.INTERNAL_SERVER_ERROR;
                    break;
            }
        }

        return ResponseEntity.status(status).contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({ MethodArgumentNotValidException.class, BindException.class })
    public ResponseEntity<Map<String, Object>> handleValidationException(Exception e) {
        log.warn("Validation error: {}", e.getMessage());

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);

        StringBuilder messageBuilder = new StringBuilder("参数校验失败: ");

        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validException = (MethodArgumentNotValidException) e;
            for (FieldError fieldError : validException.getBindingResult().getFieldErrors()) {
                messageBuilder.append(fieldError.getField())
                        .append(" ")
                        .append(fieldError.getDefaultMessage())
                        .append("; ");
            }
        } else if (e instanceof BindException) {
            BindException bindException = (BindException) e;
            for (FieldError fieldError : bindException.getBindingResult().getFieldErrors()) {
                messageBuilder.append(fieldError.getField())
                        .append(" ")
                        .append(fieldError.getDefaultMessage())
                        .append("; ");
            }
        }

        errorResponse.put("message", messageBuilder.toString());
        errorResponse.put("errorCode", "BIE001");

        return ResponseEntity.badRequest().contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    /**
     * 处理IllegalArgumentException
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("Illegal argument: {}", e.getMessage());

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", "参数错误: " + e.getMessage());
        errorResponse.put("errorCode", "BIE001");

        return ResponseEntity.badRequest().contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    /**
     * 处理所有其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        log.error("Unexpected error: {}", e.getMessage(), e);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", "系统内部错误，请稍后重试");
        errorResponse.put("errorCode", "BIE999");

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).contentType(MediaType.APPLICATION_JSON)
                .body(errorResponse);
    }
}