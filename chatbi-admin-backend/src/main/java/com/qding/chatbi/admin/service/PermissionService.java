package com.qding.chatbi.admin.service;

import com.qding.chatbi.admin.vo.permission.*;
import com.qding.chatbi.admin.vo.dataset.DatasetSummaryDTO;
import com.qding.chatbi.common.exception.ResourceNotFoundException;
import com.qding.chatbi.metadata.entity.DatasetAccessPermission;
import com.qding.chatbi.metadata.entity.QueryableDataset;
import com.qding.chatbi.metadata.entity.Role;
import com.qding.chatbi.metadata.repository.DatasetAccessPermissionRepository;
import com.qding.chatbi.metadata.repository.QueryableDatasetRepository;
import com.qding.chatbi.metadata.repository.RoleRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class PermissionService {
    
    @Autowired private RoleRepository roleRepository;
    @Autowired private QueryableDatasetRepository datasetRepository;
    @Autowired private DatasetAccessPermissionRepository permissionRepository;

    // 角色管理方法
    public Page<RoleDTO> searchOrListRoles(String searchTerm, Pageable pageable) {
        Page<Role> page;
        if (searchTerm == null || searchTerm.isBlank()) {
            page = roleRepository.findAll(pageable);
        } else {
            page = roleRepository.findByRoleNameContainingIgnoreCase(searchTerm, pageable);
        }
        return page.map(this::convertRoleToDto);
    }

    @Transactional
    public RoleDTO createRole(CreateOrUpdateRoleRequest request) {
        Role role = new Role();
        BeanUtils.copyProperties(request, role);
        return convertRoleToDto(roleRepository.save(role));
    }
    
    @Transactional
    public RoleDTO updateRole(Long id, CreateOrUpdateRoleRequest request) {
        Role role = roleRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("角色", id));
        BeanUtils.copyProperties(request, role);
        return convertRoleToDto(roleRepository.save(role));
    }
    
    @Transactional
    public void deleteRole(Long id) {
        if (!roleRepository.existsById(id)) {
            throw new ResourceNotFoundException("角色", id);
        }
        roleRepository.deleteById(id);
    }

    /**
     * 获取所有角色的名称列表
     * @return 角色名称字符串列表
     */
    public List<String> getAllRoleNames() {
        return roleRepository.findAll().stream()
                .map(Role::getRoleName)
                .collect(Collectors.toList());
    }

    // --- 新的、以数据集为中心的权限分配方法 ---

    public List<DatasetPermissionDTO> getAllDatasetPermissions() {
        List<QueryableDataset> allDatasets = datasetRepository.findAll();
        List<DatasetAccessPermission> allPermissions = permissionRepository.findAll();

        // 将所有权限按数据集ID分组
        Map<Long, List<Role>> rolesByDatasetId = allPermissions.stream()
                .collect(Collectors.groupingBy(
                        p -> p.getDataset().getId(),
                        Collectors.mapping(DatasetAccessPermission::getRole, Collectors.toList())
                ));

        // 构建最终的DTO列表
        return allDatasets.stream()
                .map(dataset -> {
                    DatasetPermissionDTO dto = new DatasetPermissionDTO();
                    dto.setDatasetId(dataset.getId());
                    dto.setDatasetName(dataset.getDatasetName());
                    dto.setDescription(dataset.getDescription());
                    // 如果有domain字段，也进行设置
                    // dto.setDomain(dataset.getDomain()); 
                    
                    List<Role> roles = rolesByDatasetId.getOrDefault(dataset.getId(), List.of());
                    dto.setAccessibleRoles(roles.stream().map(this::convertRoleToDto).collect(Collectors.toList()));
                    
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Transactional
    public void updateRolesForDataset(Long datasetId, List<Long> newRoleIds) {
        // 1. 验证数据集存在
        QueryableDataset dataset = datasetRepository.findById(datasetId)
                .orElseThrow(() -> new ResourceNotFoundException("数据集", datasetId));

        // 2. 删除该数据集所有旧的权限
        permissionRepository.deleteByDatasetId(datasetId);

        // 3. 验证所有新的角色ID都存在
        List<Role> newRoles = roleRepository.findAllById(newRoleIds);
        if (newRoles.size() != newRoleIds.size()) {
            throw new ResourceNotFoundException("一个或多个角色ID无效");
        }

        // 4. 创建并保存新的权限
        if (!newRoleIds.isEmpty()) {
            List<DatasetAccessPermission> newPermissions = newRoles.stream()
                    .map(role -> {
                        DatasetAccessPermission perm = new DatasetAccessPermission();
                        perm.setDataset(dataset);
                        perm.setRole(role);
                        return perm;
                    })
                    .collect(Collectors.toList());
            permissionRepository.saveAll(newPermissions);
        }
    }

    // --- 旧的权限分配方法 ---
    public PermissionAssignmentDTO getPermissionAssignments() {
        List<Role> allRoles = roleRepository.findAll();
        List<QueryableDataset> allDatasets = datasetRepository.findAll();
        List<DatasetAccessPermission> allPermissions = permissionRepository.findAll();

        Map<Long, Set<Long>> assignments = allPermissions.stream()
            .collect(Collectors.groupingBy(
                p -> p.getRole().getId(),
                Collectors.mapping(p -> p.getDataset().getId(), Collectors.toSet())
            ));
            
        PermissionAssignmentDTO dto = new PermissionAssignmentDTO();
        dto.setAllRoles(allRoles.stream().map(this::convertRoleToDto).collect(Collectors.toList()));
        dto.setAllDatasets(allDatasets.stream().map(this::convertDatasetToDto).collect(Collectors.toList()));
        dto.setAssignments(assignments);
        
        return dto;
    }
    
    @Transactional
    public void updateAssignments(List<UpdateAssignmentsRequest> requests) {
        for (UpdateAssignmentsRequest request : requests) {
            Long roleId = request.getRoleId();
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("角色", roleId));

            permissionRepository.deleteByRoleId(roleId);

            List<DatasetAccessPermission> newPermissions = request.getDatasetIds().stream()
                .map(datasetId -> {
                    QueryableDataset dataset = datasetRepository.findById(datasetId)
                        .orElseThrow(() -> new ResourceNotFoundException("数据集", datasetId));
                    DatasetAccessPermission newPerm = new DatasetAccessPermission();
                    newPerm.setRole(role);
                    newPerm.setDataset(dataset);
                    return newPerm;
                })
                .collect(Collectors.toList());
            
            if (!newPermissions.isEmpty()) {
                permissionRepository.saveAll(newPermissions);
            }
        }
    }
    
    // DTO转换方法
    private RoleDTO convertRoleToDto(Role entity) {
        RoleDTO dto = new RoleDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
    
    private DatasetSummaryDTO convertDatasetToDto(QueryableDataset entity) {
        DatasetSummaryDTO dto = new DatasetSummaryDTO();
        BeanUtils.copyProperties(entity, dto);
        // 如果有domain字段，也进行设置
        // dto.setDomain(entity.getDomain());
        return dto;
    }
} 