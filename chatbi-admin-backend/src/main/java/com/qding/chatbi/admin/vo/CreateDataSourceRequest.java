package com.qding.chatbi.admin.vo;

import com.qding.chatbi.common.enums.DatabaseType;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

@Data
public class CreateDataSourceRequest {
    @NotBlank(message = "数据源名称不能为空")
    private String sourceName;
    @NotNull(message = "数据库类型不能为空")
    private DatabaseType databaseType;
    @NotBlank(message = "主机地址不能为空")
    private String host;
    @NotNull(message = "端口号不能为空")
    @Min(value = 1, message = "端口号必须大于0")
    private Integer port;
    @NotBlank(message = "用户名不能为空")
    private String username;
    private String password;
    @NotBlank(message = "数据库名称不能为空")
    private String databaseName;
    private String description;
    private String status;
} 