package com.qding.chatbi.admin.controller;

import com.qding.chatbi.admin.service.ModelManagementService;
import com.qding.chatbi.admin.vo.PagedResponse;
import com.qding.chatbi.admin.vo.dataset.DatasetSummaryDTO;
import com.qding.chatbi.admin.vo.dataset.CreateDatasetRequest;
import com.qding.chatbi.admin.vo.dataset.DatasetDetailDTO;
import com.qding.chatbi.admin.vo.dataset.CreateComputedColumnRequest;
import com.qding.chatbi.admin.vo.dataset.ValidateExpressionRequest;
import com.qding.chatbi.admin.vo.dataset.ValidateExpressionResponse;
import com.qding.chatbi.admin.vo.dataset.ColumnConfigDTO;
import com.qding.chatbi.admin.vo.dataset.PreviewSourceRequest;
import com.qding.chatbi.admin.vo.dataset.ColumnInitialConfig;
import com.qding.chatbi.admin.vo.dataset.UpdateDatasetRequest;
import com.qding.chatbi.common.exception.InvalidInputException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/admin/datasets")
public class DatasetAdminController {

    private final ModelManagementService modelService;

    @Autowired
    public DatasetAdminController(ModelManagementService modelService) {
        this.modelService = modelService;
    }

    @GetMapping("")
    public ResponseEntity<Page<DatasetSummaryDTO>> listDatasets(
            @PageableDefault(size = 20, sort = "updatedAt", direction = Sort.Direction.DESC) Pageable pageable) {
        return ResponseEntity.ok(modelService.getAllDatasets(pageable));
    }

    @PostMapping("")
    public ResponseEntity<DatasetDetailDTO> createDataset(@Valid @RequestBody CreateDatasetRequest request) {
        DatasetDetailDTO createdDataset = modelService.createDataset(request);
        return ResponseEntity.ok(createdDataset);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<DatasetDetailDTO> getDatasetDetails(@PathVariable("id") Long id) {
        DatasetDetailDTO dataset = modelService.getDatasetDetails(id);
        return ResponseEntity.ok(dataset);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<DatasetDetailDTO> updateDataset(
            @PathVariable("id") Long id, 
            @Valid @RequestBody UpdateDatasetRequest request) {
        DatasetDetailDTO updatedDataset = modelService.updateDataset(id, request);
        return ResponseEntity.ok(updatedDataset);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDataset(@PathVariable("id") Long id) {
        modelService.deleteDataset(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * 更新单个字段的配置
     */
    @PutMapping("/columns/{columnId}")
    public ResponseEntity<ColumnConfigDTO> updateColumn(
            @PathVariable("columnId") Long columnId,
            @Valid @RequestBody ColumnConfigDTO request) {
        ColumnConfigDTO updatedColumn = modelService.updateColumn(columnId, request);
        return ResponseEntity.ok(updatedColumn);
    }
    
    /**
     * 检查数据集名称是否已存在
     */
    @GetMapping("/check-name")
    public ResponseEntity<Map<String, Object>> checkDatasetName(
            @RequestParam("name") String datasetName,
            @RequestParam(value = "excludeId", required = false) Long excludeId) {
        boolean exists;
        if (excludeId != null) {
            // 更新时检查，排除当前数据集
            exists = modelService.isDatasetNameExists(datasetName, excludeId);
        } else {
            // 创建时检查
            exists = modelService.isDatasetNameExists(datasetName);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("exists", exists);
        result.put("available", !exists);
        if (exists) {
            result.put("message", "数据集名称 '" + datasetName + "' 已存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 【新增】预览数据源的字段信息
     */
    @PostMapping("/preview-columns")
    public ResponseEntity<List<ColumnInitialConfig>> previewColumns(
            @Valid @RequestBody PreviewSourceRequest request) {
        List<ColumnInitialConfig> columns = modelService.previewColumns(request);
        return ResponseEntity.ok(columns);
    }
    
    @PostMapping("/{id}/columns/sync")
    public ResponseEntity<List<ColumnConfigDTO>> syncColumns(@PathVariable("id") Long datasetId) {
        List<ColumnConfigDTO> columns = modelService.syncColumnsFromSource(datasetId);
        return ResponseEntity.ok(columns);
    }

    /**
     * 【新增】为数据集添加一个计算指标
     */
    @PostMapping("/{id}/columns/computed")
    public ResponseEntity<ColumnConfigDTO> createComputedColumn(
            @PathVariable("id") Long datasetId,
            @Valid @RequestBody CreateComputedColumnRequest request) {
        request.setDatasetId(datasetId); // 确保使用路径中的 datasetId
        ColumnConfigDTO createdColumn = modelService.createComputedColumn(request);
        return ResponseEntity.ok(createdColumn);
    }

    /**
     * 【新增】校验一个SQL表达式的有效性
     */
    @PostMapping("/{id}/columns/validate")
    public ResponseEntity<ValidateExpressionResponse> validateExpression(
            @PathVariable("id") Long datasetId,
            @Valid @RequestBody ValidateExpressionRequest request) {
        request.setDatasetId(datasetId); // 确保使用路径中的 datasetId
        ValidateExpressionResponse response = modelService.validateExpression(request);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/physical-columns")
    public ResponseEntity<Void> updatePhysicalColumns(
            @PathVariable Long id, 
            @RequestBody Map<String, List<String>> requestBody) {
        List<String> columnNames = requestBody.get("columnNames");
        if (columnNames == null) {
            throw new InvalidInputException("Request body must contain 'columnNames' key.");
        }
        modelService.updatePhysicalColumns(id, columnNames);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/columns/{id}")
    public ResponseEntity<Void> deleteColumn(@PathVariable Long id) {
        modelService.deleteColumn(id);
        return ResponseEntity.noContent().build();
    }
} 