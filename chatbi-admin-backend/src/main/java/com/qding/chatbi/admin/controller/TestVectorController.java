package com.qding.chatbi.admin.controller;

import com.qding.chatbi.knowledge.service.KnowledgePersistenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/admin/test")
public class TestVectorController {

    private static final Logger log = LoggerFactory.getLogger(TestVectorController.class);

    @Autowired
    private KnowledgePersistenceService knowledgeService;

    @GetMapping("/vector-logs")
    public ResponseEntity<Map<String, Object>> testVectorLogs() {
        log.info("========== 测试向量搜索日志功能 ==========");

        try {
            Map<String, Object> result = knowledgeService.testMilvusConnection();
            log.info("测试完成，结果: {}", result);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("测试失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "error", e.getMessage()));
        }
    }
}