package com.qding.chatbi.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description CorsProperties
 * @date 2025/7/15
 */
@Component
@ConfigurationProperties(prefix = "spring.web.cors")
@Data
public class CorsProperties {
    private List<String> allowedOrigins;
    private List<String> allowedMethods;

}
