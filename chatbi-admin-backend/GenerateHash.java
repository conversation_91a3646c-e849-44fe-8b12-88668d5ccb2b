import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder; public class GenerateHash { public static void main(String[] args) { BCryptPasswordEncoder encoder = new BCryptPasswordEncoder(); String password = "111111"; String hash = encoder.encode(password); System.out.println("Password: " + password); System.out.println("Generated Hash: " + hash); System.out.println("Verification: " + encoder.matches(password, hash)); } }
