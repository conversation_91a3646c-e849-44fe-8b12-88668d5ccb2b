<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qding.chatbi</groupId>
        <artifactId>chatbi-service</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-admin-backend</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 假设 metadata-service 和 knowledge-service 的逻辑作为库被直接依赖 -->
        <!-- 如果它们作为独立服务，这里会是 feign-client 或类似的依赖 -->
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-metadata-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-knowledge-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.qding.chatbi</groupId>
            <artifactId>chatbi-data-access-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- JWT dependencies -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 移除Spring Boot插件，因为这是一个库模块，不是可执行应用程序 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
